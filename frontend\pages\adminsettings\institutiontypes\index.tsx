//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import InstitutionTypeTable from "./institutionTypeTable";
import { useTranslation } from 'next-i18next';
import { canAddOrganisationTypes } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";



const InstitutionTypeIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowInstitutionTypeIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title={t("adminsetting.Organisationtypes.OrganisationType")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_institution_type"
              >
              <Button variant="secondary" size="sm">
              {t("adminsetting.Organisationtypes.AddOrganisationType")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <InstitutionTypeTable />
          </Col>
        </Row>
      </Container>
    );
  }
  
  const ShowAddOrganisationTypes = canAddOrganisationTypes(() => <ShowInstitutionTypeIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.institution_type?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddOrganisationTypes />
  )
}
export default InstitutionTypeIndex;