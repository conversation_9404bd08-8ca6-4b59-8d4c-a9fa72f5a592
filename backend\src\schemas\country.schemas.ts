//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const CountrySchema = new mongoose.Schema({
  code: { type: String, required: true, unique: true },
  code3: { type: String },
  title: { type: String, required: true, unique: true },
  title_de: { type: String, required: true },
  first_letter: { type: Object },
  coordinates: { type: Object },
  world_region: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'WorldRegion',
    autopopulate: true,
  },
  health_profile: { type: String },
  security_advice: { type: String },
  label: { type: Array },
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users' },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
  dial_code: { type: String },
});

CountrySchema.plugin(mongoosePaginate);
