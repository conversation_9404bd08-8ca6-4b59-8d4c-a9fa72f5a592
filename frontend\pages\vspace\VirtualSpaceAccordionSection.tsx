//Import Library
import { Accordion, Card } from "react-bootstrap";
import { faPlus, faMinus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";

//Import services/components
import DocumentsAccordian from './DocumentAccordian';
import AnnouncementsAccordian from "./AnnouncementsAccordian";
import MediaGalleryAccordian from "./MediaGalleryAccordian";
import { canViewDiscussionUpdate} from "./permission";
import Discussion from "../../components/common/disussion";
import { useTranslation } from 'next-i18next';

const VspaceAccordianSection = (props: any) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);

    const DiscussionComponent = () => {
        return (
          <Accordion.Item eventKey="3">
            <Accordion.Header onClick={() => setSection(!section)}>
              <div className="cardTitle">{t("vspace.Discussions")}</div>
              <div className="cardArrow">
                {section ? <FontAwesomeIcon icon={faMinus} color="#fff" /> :
                  <FontAwesomeIcon icon={faPlus} color="#fff" />}
              </div>
            </Accordion.Header>
            <Accordion.Body>
              <Discussion
                type="vspace"
                id={props && props.routes ? props.routes[1] : null}
              />
            </Accordion.Body>
          </Accordion.Item>
        )
    };
    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionComponent />)
    return(
        <>
            <Accordion>
                <DocumentsAccordian {...props} />
            </Accordion>
            <Accordion>
                <AnnouncementsAccordian {...props} />
            </Accordion>
            <Accordion>
                <MediaGalleryAccordian {...props} />
            </Accordion>
            <Accordion>
                <CanViewDiscussionUpdate />
            </Accordion>
        </>
    )
};

export default VspaceAccordianSection;