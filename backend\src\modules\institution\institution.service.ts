//Import Library
import { PaginateModel } from 'mongoose-paginate-v2';
import * as mongoose from 'mongoose';
import {
  Injectable,
  NotFoundException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { v4 as uuidv4 } from 'uuid';
import { map } from 'rxjs/operators';

//Import services/components
import { InstitutionInterface } from '../../interfaces/institution.interface';
import { UsersService } from './../../users/users.service';
import { EmailService } from './../../email.service';
import { CreateInstitutionDto } from './dto/create-institution.dto';
import { UpdateInstitutionDto } from './dto/update-institution.dto';
import { CountryService } from '../country/country.service';
import { FlagService } from '../flag/flag.service';
// SCHEMAS
import { UsersSchema } from 'src/schemas/users.schemas';
import { HttpService } from '@nestjs/axios';

const RequestPending = 'Request Pending';
const InstitutionType = 'Could not find Institution Type.';
const Message = mongoose.model('Message', UsersSchema);
@Injectable()
export class InstitutionService {
  constructor(
    @InjectModel('Institution')
    private institutionModel: PaginateModel<InstitutionInterface>,
    private readonly _usersService: UsersService,
    private readonly _emailService: EmailService,
    private httpService: HttpService,
    private readonly _countryService: CountryService,
    private readonly _flagService: FlagService,
  ) {}

  randomString(length) {
    let result = '';
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      const crypto = require('crypto');
      const buf = crypto.randomBytes(1);
      result += characters.charAt(Math.floor(buf * charactersLength));
    }
    return result;
  }

  async bulkCreate(
    institutionDto: Array<CreateInstitutionDto>,
  ): Promise<InstitutionInterface[]> {
    const createdInstitutions = await this.institutionModel.insertMany(
      institutionDto,
    );
    return createdInstitutions;
  }

  async findExisting(institutionDto: Array<any>): Promise<any> {
    const titles = institutionDto.map((d) => d.title);
    const existingCount = await this.institutionModel.count({
      title: { $in: titles },
    });
    return existingCount;
  }

  async find(query): Promise<Array<any>> {
    const foundDocs = await this.institutionModel.find(query).exec();
    return foundDocs;
  }

  async getGoogleGeocode(address: string): Promise<Array<any>> {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${process.env.GOOGLE_API_KEY}`;
    const geocode = await this.httpService
      .get(url)
      .pipe(map((response) => response.data))
      .toPromise();
    return geocode;
  }

  async create(
    createInstitutionDto: CreateInstitutionDto,
  ): Promise<InstitutionInterface> {
    if (
      createInstitutionDto['focal_points'] &&
      createInstitutionDto['focal_points'].length > 0
    ) {
      const newUsers = createInstitutionDto['focal_points'].filter(
        (d) => !d._id,
      );
      let oldUsers = createInstitutionDto['focal_points']
        .filter((d) => d._id)
        .map((d) => d._id);
      const query = {
        $or: [
          {
            email: {
              $in: newUsers.map((d) => {
                const { email } = d;
                const messageToSearchWith: any = new Message({ email });
                messageToSearchWith.encryptFieldsSync();
                return messageToSearchWith['email'];
              }),
            },
          },
          {
            username: {
              $in: newUsers.map((d) => {
                const { username } = d;
                const messageToSearchWith: any = new Message({ username });
                messageToSearchWith.encryptFieldsSync();
                return messageToSearchWith['username'];
              }),
            },
          },
        ],
      };
      const existingCount = await this._usersService.getCount(query);
      this.focalUsers(existingCount);
      // }
      // if (createInstitutionDto['focal_points'] && createInstitutionDto['focal_points'].length > 0) {
      const usersList = [];
      const newInviteUser = [];
      newUsers.forEach((user) => {
        this.focalUsername(user, usersList, newInviteUser);
      });
      const userDetails = await this._usersService.bulkCreate(usersList);
      const languageCode = createInstitutionDto['language']
        ? createInstitutionDto['language']
        : 'en';
      //Inviting users as focal points to organisation
      //Inviting new users
      newInviteUser.forEach((d) => {
        this._emailService.focalPointInviteNewUser(
          d,
          createInstitutionDto,
          languageCode,
        );
      });

      //Inviting exisisting users to Organizations
      oldUsers.forEach((d) => {
        this._emailService.focalPointInviteExistingUser(
          d,
          createInstitutionDto,
          languageCode,
        );
      });

      oldUsers = [
        ...oldUsers,
        ...(userDetails ? userDetails.map((d) => d['_id']) : []),
      ];
      createInstitutionDto['focal_points'] = oldUsers;

      //Convert Address to geocode
      await this.convertAdres(createInstitutionDto);
    }
    const createdInstitution = new this.institutionModel(createInstitutionDto);
    if (
      createdInstitution.focal_points &&
      createdInstitution.focal_points.length > 0
    ) {
      const userIds = createdInstitution.focal_points.map((d) => d._id);
      await this._usersService.bulkUpdate(
        { _id: { $in: userIds } },
        { institution: createdInstitution._id, status: RequestPending },
      );
    }

    return createdInstitution.save();
  }

  private focalUsername(user: any, usersList: any[], newInviteUser: any[]) {
    if (user.username && user.email) {
      const { username, email } = user;
      const messageToSearchWith: any = new Message({ username, email });
      messageToSearchWith.encryptFieldsSync();
      usersList.push({
        username: messageToSearchWith['username'],
        email: messageToSearchWith['email'],
        password: this.randomString(16),
        // institution: createdInstitution._id,
        enabled: true,
        status: RequestPending,
        is_focal_point: true,
        dial_code: user.focal_dial_code,
        mobile_number: user.mobile_number,
        roles: ['AUTHENTICATED'],
        emailActivateToken: uuidv4(),
        by_instution: true,
        __enc_username: true,
        __enc_email: true,
        _enc_mobile_number: true,
      });
      const { emailActivateToken } = usersList[0];
      newInviteUser.push({
        username: username,
        email: email,
        emailActivateToken: emailActivateToken,
      });
    }
  }

  private async convertAdres(createInstitutionDto: CreateInstitutionDto) {
    if (createInstitutionDto['address'].country) {
      const countryName: any = await this._countryService.get(
        createInstitutionDto['address'].country,
      );
      let address =
        createInstitutionDto['address'].line_1 +
        ',' +
        createInstitutionDto['address'].line_2 +
        ',' +
        createInstitutionDto['address'].city +
        ',' +
        countryName.title;
      address = address.split(' ').join('+');
      const ext: any = await this.getGoogleGeocode(address);
      if (ext && ext.status === 'OK') {
        if (ext.results && ext.results.length > 0) {
          const coordinates = {
            latitude: ext.results[0].geometry.location.lat,
            longitude: ext.results[0].geometry.location.lng,
          };
          createInstitutionDto['address'].coordinates = coordinates;
        }
      }
    }
  }

  private focalUsers(existingCount: any) {
    if (existingCount > 0) {
      throw new HttpException(
        {
          status: HttpStatus.FORBIDDEN,
          message: ['toast.usernameoremailusedalready'],
        },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  async getTotalCount(filter: any) {
    let _filter = {};
    try {
      _filter = filter.query ? filter.query : {};
    } catch (e) {}
    return this.institutionModel.count(_filter).exec();
  }

  async findAll(query): Promise<InstitutionInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = this.instutionInterface(query, myCustomLabels);

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    if (_filter.title) {
      _filter.title = _filter.title
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(`^${_filter.title}`, 'gi');
      _filter['title'] = regex;
    }

    if (options.sort && (options.sort.country || options.sort.type)) {
      const sortOrder = options.sort.country
        ? options.sort.country
        : options.sort.type;
      const sortNumber = sortOrder === 'asc' ? 1 : -1;
      const skipValue = (options.page - 1) * options.limit;
      const sortArray = options.sort.country
        ? { 'countryArray.title': sortNumber }
        : { 'typeArray.title': sortNumber };
      const list = await this.getSortedList(
        _filter,
        sortArray,
        skipValue,
        options.limit,
        options.page,
      );
      return list;
    } else {
      return this.institutionModel.paginate(_filter, options);
    }
  }

  private instutionInterface(
    query: any,
    myCustomLabels: { totalDocs: string; docs: string },
  ) {
    return {
      lean: query.lean ? query.lean : false,
      populate: query.populate ? query.populate : '',
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
      collation: { locale: 'en' },
    };
  }

  async getSortedList(filter, arr, skip, limit, page) {
    const typeArray = [];
    let filterType;
    const worldregionArray = [];
    this.adressWorldregion(filter, worldregionArray, typeArray);
    filterType = this.instutionFilter(
      filter,
      filterType,
      typeArray,
      worldregionArray,
    );
    const list = await this.institutionModel.aggregate([
      {
        $match: filterType,
      },
      {
        $lookup: {
          from: 'countries',
          localField: 'address.country',
          foreignField: '_id',
          as: 'countryArray',
        },
      },
      { $unwind: '$countryArray' },
      { $set: { 'address.country': '$countryArray' } },
      {
        $lookup: {
          from: 'institutionnetworks',
          localField: 'networks',
          foreignField: '_id',
          as: 'networkArray',
        },
      },
      { $set: { networks: '$networkArray' } },
      {
        $lookup: {
          from: 'institutiontypes',
          localField: 'type',
          foreignField: '_id',
          as: 'typeArray',
        },
      },
      { $unwind: { path: '$typeArray', preserveNullAndEmptyArrays: true } },
      { $set: { type: '$typeArray' } },
      { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
      { $sort: arr },
      {
        $project: {
          address: {
            coordinates: 1,
            country: {
              title: 1,
              _id: 1,
            },
          },
          networks: {
            $map: {
              input: '$networks',
              as: 'networks',
              in: {
                _id: '$$networks._id',
                title: '$$networks.title',
              },
            },
          },
          expertise: {
            $map: {
              input: '$expertise',
              as: 'expertise',
              in: {
                _id: '$$expertise._id',
                title: '$$expertise.title',
              },
            },
          },
          _id: 1,
          title: 1,
          type: 1,
        },
      },
      {
        $facet: {
          totalData: [
            { $match: {} },
            { $limit: limit + skip },
            { $skip: skip },
          ],
          totalCount: [
            {
              $group: {
                _id: null,
                count: { $sum: 1 },
              },
            },
          ],
        },
      },
    ]);
    const institutionList: any = this.instutionServicescount(list, limit, page);
    return institutionList;
  }

  private instutionFilter(
    filter: any,
    filterType: any,
    typeArray: any[],
    worldregionArray: any[],
  ) {
    if (filter.networks && filter.type && filter.title) {
      filterType = {
        $and: [
          { networks: new mongoose.Types.ObjectId(filter.networks) },
          { type: { $in: typeArray } },
          { 'address.world_region': { $in: worldregionArray } },
          { title: filter['title'] },
        ],
      };
    } else if (filter.networks && filter.type && !filter.title) {
      filterType = {
        $and: [
          { networks: new mongoose.Types.ObjectId(filter.networks) },
          { type: { $in: typeArray } },
          { 'address.world_region': { $in: worldregionArray } },
        ],
      };
    } else if ((filter.networks || filter.type) && filter.title) {
      const filterTitle = filter.type
        ? { $and: [{ type: { $in: typeArray } }, { title: filter['title'] }] }
        : null;
      filterType = filter.networks
        ? {
            $and: [
              { networks: new mongoose.Types.ObjectId(filter.networks) },
              { title: filter['title'] },
              { 'address.world_region': { $in: worldregionArray } },
            ],
          }
        : filterTitle;
    } else if ((filter.networks || filter.type) && !filter.title) {
      filterType = this.getFilterArrayDetails(
        filter,
        typeArray,
        worldregionArray,
        filterType,
      );
    } else {
      filterType = { 'address.world_region': { $in: worldregionArray } };
    }
    filterType.status = filter.status;
    return filterType;
  }

  getFilterArrayDetails(
    filter: any,
    typeArray: any,
    worldregionArray: any,
    filterType: any,
  ) {
    if ((filter.networks || filter.type) && !filter.title) {
      const filterArray = filter.type
        ? {
            $and: [
              { type: { $in: typeArray } },
              { 'address.world_region': { $in: worldregionArray } },
            ],
          }
        : null;
      filterType = filter.networks
        ? {
            $and: [
              { networks: new mongoose.Types.ObjectId(filter.networks) },
              { 'address.world_region': { $in: worldregionArray } },
            ],
          }
        : filterArray;
    } else if (filter.title) {
      filterType = filter.title
        ? {
            $and: [
              { title: filter['title'] },
              { 'address.world_region': { $in: worldregionArray } },
            ],
          }
        : {};
    }
    return filterType;
  }

  private instutionServicescount(list: any, limit: any, page: any) {
    const totalCount = list[0].totalCount[0].count;
    const institutionList: any = {};
    institutionList.data = list[0].totalData;
    institutionList.totalCount = totalCount;
    institutionList.limit = limit;
    institutionList.totalPages = Math.ceil(totalCount / limit);
    institutionList.page = page;
    institutionList.pagingCounter = page;
    institutionList.hasNextPage = page !== institutionList.totalPages;
    institutionList.hasPrevPage = !(
      page === 1 && page === institutionList.totalPages
    );
    institutionList.prevPage =
      page === 1 && page === institutionList.totalPages ? null : page - 1;
    institutionList.nextPage =
      page === institutionList.totalPages ? null : page + 1;
    return institutionList;
  }

  private adressWorldregion(
    filter: any,
    worldregionArray: any[],
    typeArray: any[],
  ) {
    if (filter['address.world_region']) {
      filter['address.world_region'].forEach((element) => {
        const val = element.value ? element.value : element;
        worldregionArray.push(new mongoose.Types.ObjectId(val));
      });
    }
    if (filter.type) {
      filter.type.forEach((element) => {
        const val = element.value ? element.value : element;
        typeArray.push(new mongoose.Types.ObjectId(val));
      });
    }
  }

  async get(institutionId): Promise<InstitutionInterface[]> {
    let _result;
    try {
      _result = await this.institutionModel
        .findOne({ _id: institutionId })
        .populate('type', 'title')
        .populate({ path: 'address.country', select: 'title' })
        .populate({ path: 'address.region', select: 'title' })
        .populate({ path: 'expertise', select: 'title' })
        .populate({ path: 'networks', select: 'title' })
        .populate({ path: 'partners', select: 'title' })
        .populate({ path: 'hazards', select: 'title' })
        .populate({ path: 'header', select: '_id' })
        .populate({ path: 'images' })
        // .populate('focal_points', '_id')
        .populate({
          path: 'focal_points',
          select: ['_id', 'username', 'email', 'status'],
          match: { status: 'Approved' },
        })
        .lean();
    } catch (error) {
      throw new NotFoundException(InstitutionType);
    }
    if (!_result) {
      throw new NotFoundException(InstitutionType);
    }
    return _result;
  }
  async getUserByInstitution(institutionId): Promise<number> {
    let _result = await this.institutionModel.count(institutionId);
    return _result;
  }
  async update(institutionId: any, updateInstitutionDto: UpdateInstitutionDto) {
    let oldUsers = [];
    const getById: any = await this.institutionModel
      .findById(institutionId)
      .exec();
    const existingFocalPoints = getById['focal_points'].map((item) => item._id);
    if (
      updateInstitutionDto['focal_points'] &&
      updateInstitutionDto['focal_points'].length > 0
    ) {
      oldUsers = await Promise.all(
        await updateInstitutionDto['focal_points']
          .filter((d) => d._id)
          .map(async (d) => {
            const _query = {
              _id: institutionId,
              focal_points: { $in: [Object(d._id)] },
            };
            const existingCount = await this.getUserByInstitution(_query);
            if (existingCount == 0) {
              return d._id;
            }
          }),
      );
      oldUsers = oldUsers.filter((d) => d != undefined);
      const newUsersList = [];
      let fullUsersIds = [];
      const _query = {
        $or: [],
      };
      const _userobj = {
        emails: [],
        usernames: [],
        uniqueIds: [],
      };
      const userInvitedetails = [];
      updateInstitutionDto['focal_points'].forEach((user) => {
        this.focalRandomlist(
          user,
          newUsersList,
          institutionId,
          _userobj,
          userInvitedetails,
        );
      });
      fullUsersIds = getById['focal_points'];

      _query['$or'].push({
        email: { $in: _userobj.emails },
      });
      _query['$or'].push({
        username: { $in: _userobj.usernames },
      });
      const existingCount = await this._usersService.getCount(_query);
      this.focalUsers(existingCount);

      fullUsersIds = await this.inviteBulk(
        newUsersList,
        fullUsersIds,
        updateInstitutionDto,
        userInvitedetails,
      );

      const newExistingUsers = oldUsers.filter(
        (x) => existingFocalPoints.indexOf(x) === -1,
      );

      await this._usersService.bulkUpdate(
        { _id: { $in: oldUsers } },
        { institution: institutionId, status: RequestPending },
      );

      //Inviting exisisting users to Organizations
      this.existingUser(newExistingUsers, updateInstitutionDto);

      updateInstitutionDto['focal_points'] =
        fullUsersIds.concat(newExistingUsers);

      //Convert Address to geocode
      await this.institudeAdd(updateInstitutionDto);
    }
    const updatedData = new this.institutionModel(updateInstitutionDto);

    try {
      const previouStatus = getById.status;
      const instUser = getById.user;
      Object.keys(updateInstitutionDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
      const languageupdateCode = updateInstitutionDto['language']
        ? updateInstitutionDto['language']
        : 'en';
      if (
        previouStatus === RequestPending &&
        updatedData.status === 'Approved'
      ) {
        await this._emailService.institutionApproval(
          instUser,
          getById,
          languageupdateCode,
        );
      }
      await this._flagService.alertSubscribers(
        getById.title,
        getById._id,
        'institution',
        languageupdateCode,
      );
    } catch (e) {
      throw new NotFoundException('Could not update Institution Type.');
    }
    return getById;
  }

  async updateFocalPoints(
    institutionId: any,
    updateInstitutionDto: UpdateInstitutionDto,
  ) {
    const institution: any = await this.institutionModel
      .findById(institutionId)
      .exec();
    let focalPoints = [...institution['focal_points']];
    updateInstitutionDto['focal_points'].forEach((payLoadFp) => {
      focalPoints = focalPoints.filter(
        (fp) => !`${fp}`.includes(payLoadFp._id),
      );
    });
    institution['focal_points'] = focalPoints;
    institution.save();
    return institution;
  }

  private focalRandomlist(
    user: any,
    newUsersList: any[],
    institutionId: any,
    _userobj: { emails: any[]; usernames: any[]; uniqueIds: any[] },
    userInvitedetails: any[],
  ) {
    if (!user._id) {
      if (user.username && user.email) {
        const { username, email } = user;
        const messageToSearchWith: any = new Message({ username, email });
        const randomIds = uuidv4();
        messageToSearchWith.encryptFieldsSync();
        newUsersList.push({
          username: messageToSearchWith['username'],
          email: messageToSearchWith['email'],
          password: this.randomString(16),
          institution: institutionId,
          enabled: true,
          status: RequestPending,
          roles: user.roles ? user.roles : 'AUTHENTICATED',
          is_focal_point: true,
          mobile_number: user.mobile_number,
          dial_code: user.focal_dial_code,
          emailActivateToken: randomIds,
          by_instution: true,
          __enc_username: true,
          __enc_email: true,
          _enc_mobile_number: true,
        });
        _userobj.emails.push(messageToSearchWith['email']);
        _userobj.usernames.push(messageToSearchWith['username']);
        userInvitedetails.push({
          username: user.username,
          email: user.email,
          emailActivateToken: randomIds,
        });
      }
    }
  }

  private async inviteBulk(
    newUsersList: any[],
    fullUsersIds: any[],
    updateInstitutionDto: UpdateInstitutionDto,
    userInvitedetails: any[],
  ) {
    if (newUsersList.length > 0) {
      const resp = await this._usersService.bulkCreate(newUsersList);
      fullUsersIds = [...fullUsersIds, ...resp.map((d) => d['_id'])];
      //Inviting new users
      if (resp && resp.length > 0) {
        userInvitedetails.forEach((d) => {
          const languageCode = updateInstitutionDto['language']
            ? updateInstitutionDto['language']
            : 'en';
          this._emailService.focalPointInviteNewUser(
            d,
            updateInstitutionDto,
            languageCode,
          );
        });
      }
    }
    return fullUsersIds;
  }

  private existingUser(
    newExistingUsers: any[],
    updateInstitutionDto: UpdateInstitutionDto,
  ) {
    if (newExistingUsers && newExistingUsers.length > 0) {
      newExistingUsers.forEach((d) => {
        const languageCode = updateInstitutionDto['language']
          ? updateInstitutionDto['language']
          : 'en';
        this._emailService.focalPointInviteExistingUser(
          d,
          updateInstitutionDto,
          languageCode,
        );
      });
    }
  }

  private async institudeAdd(updateInstitutionDto: any) {
    if (updateInstitutionDto?.address?.country) {
      const countryName: any = await this._countryService.get(
        updateInstitutionDto['address'].country,
      );
      let address =
        updateInstitutionDto['address'].line_1 +
        ',' +
        updateInstitutionDto['address'].line_2 +
        ',' +
        updateInstitutionDto['address'].city +
        ',' +
        countryName.title;
      address = address.split(' ').join('+');
      const ext: any = await this.getGoogleGeocode(address);
      if (ext && ext.status === 'OK') {
        if (ext.results && ext.results.length > 0) {
          const coordinates = {
            latitude: ext.results[0].geometry.location.lat,
            longitude: ext.results[0].geometry.location.lng,
          };
          updateInstitutionDto['address'].coordinates = coordinates;
        }
      }
    }
  }

  async getfilteredInstitution(
    institutionTypeList,
  ): Promise<InstitutionInterface[][]> {
    let _result;
    try {
      _result = await this.institutionModel
        .find({ type: { $in: institutionTypeList } })
        .exec();
    } catch (error) {
      throw new NotFoundException('Could not find Organisation.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Organisation.');
    }
    return _result;
  }

  async onSave(institutionId): Promise<InstitutionInterface[]> {
    const institution: any = await this.get(institutionId);
    return institution;
  }

  async delete(institutionId: string) {
    const result = await this.institutionModel
      .findOneAndDelete({ _id: institutionId })
      .exec();
    if (!result) {
      throw new NotFoundException(InstitutionType);
    }
    return result;
  }

  async bulkUpdate(query, data) {
    const updatedData = await this.institutionModel
      .updateMany(query, data)
      .exec();
    return updatedData;
  }
}
