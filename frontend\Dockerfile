FROM node:16

# Update packages and install dependency packages for services
RUN apt-get update \
 && apt-get dist-upgrade -y \
 && apt-get clean \
 && echo 'Finished installing dependencies'

# Don't run as root
RUN mkdir /app && chown -R node:node /app
USER node
WORKDIR /app

# Copy package.json and package-lock.json
COPY --chown=node:node package*.json ./

# Install npm production packages 
RUN npm install react react-dom next

COPY --chown=node:node ./ .

ENV NODE_ENV production

# Set environment variables from build arguments
ARG API_SERVER
ARG MAP_KEY
ARG SEARCH_DEBOUNCE_TIME
ENV API_SERVER ${API_SERVER:-/api/v1}
ENV MAP_KEY ${MAP_KEY}
ENV SEARCH_DEBOUNCE_TIME ${SEARCH_DEBOUNCE_TIME:-500}

# Building app
RUN npm run build

CMD ["npm", "start"]

EXPOSE 3000
