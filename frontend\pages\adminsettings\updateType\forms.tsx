//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { UpdateTypeInterface } from "../../../components/interfaces/updateType.interface";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const UpdateTypeForm = (props: any) => {
    const _initialupdateType = {
        title: "",
        icon: "",
    };
    const { t } = useTranslation('common');
    const [initialVal, setInitialVal] = useState<UpdateTypeInterface>(_initialupdateType);

    const editform = props.routes && props.routes[0] === "edit_update_type" && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialupdateType);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
            icon: initialVal.icon,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.updatestype.Updatetypeisupdatedsuccessfully";
            response = await apiService.patch(`/updatetype/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.updatestype.Updatetypeisaddedsuccessfully";
            response = await apiService.post("/updatetype", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/update_type");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const updateTypeParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getUpdateTypeData = async () => {
                const response = await apiService.get(`/updatetype/${props.routes[1]}`, updateTypeParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getUpdateTypeData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.updatestype.UpdateType")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.updatestype.UpdateType")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => value.trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.updatestype.PleaseAddtheUpdateType"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label>{t("adminsetting.updatestype.Icon")}</Form.Label>
                                        <TextInput
                                            name="icon"
                                            id="icon"
                                            value={initialVal.icon}
                                            errorMessage={t("adminsetting.updatestype.PleaseAddtheicon")}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.updatestype.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.updatestype.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/update_type`}
                                        >
                                        <Button variant="secondary">{t("adminsetting.updatestype.Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default UpdateTypeForm;
