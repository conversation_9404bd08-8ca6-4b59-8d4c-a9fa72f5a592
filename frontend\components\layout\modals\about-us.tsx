//Import Library
import { Modal } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

interface AboutUsModalProps {
  show: boolean;
  onHide: () => void;
  [key: string]: any;
}

export default function AboutUsModal(props: AboutUsModalProps) {
  const { t } = useTranslation('common');
  return (
    <Modal
      {...props}
      size="lg"
      aria-labelledby="about-us-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title id="about-us-modal">
          {t("about")}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p>{t("abouttext")}</p>
      </Modal.Body>
    </Modal>
  )
}

