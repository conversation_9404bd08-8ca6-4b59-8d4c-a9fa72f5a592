//Import Library
import { Col, Form } from "react-bootstrap";

interface SortProps {
  sortHazard: number;
  handleSortChange: (e: any) => void;
}

const Sort = ({ sortHazard, handleSortChange }: SortProps) => {
  return (
    <>
      <Col lg sm md={1}>
        <Form.Control className="border border-primary" as="select" value={sortHazard} onChange={handleSortChange}>
          <option value="1">A-Z</option>
          <option value="2">Z-A</option>
        </Form.Control>
      </Col>
    </>

  )
};

export default Sort;
