//Import Library
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";
import _ from "lodash";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import HazardTableFilter from "./hazardTableFilter";
import { useTranslation } from 'next-i18next';

const HazardTable = () => {
    const { t, i18n } = useTranslation("common");
    const currentLang = i18n.language === "fr" ? "en" : i18n.language;
    const titleSearch = currentLang ? `title.${currentLang}` : "title.en";

    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [filterText, setFilterText] = React.useState("");
    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);
    const [isModalShow, setModal] = useState(false);
    const [selectHazard, setSelectHazard] = useState({});
    const [currLang] = useState(titleSearch);


    const handleMonitored = async (e) => {
        const index = _.findIndex(tabledata, { _id: e.target.name });
        if (index > -1) {
            tabledata[index].enabled = !tabledata[index].enabled;
            setDataToTable([...tabledata]);
            const response = await apiService.patch(`/hazard/${e.target.name}`, tabledata[index]);
            if (response && response._id) {
                toast.success(`${response.title[currentLang]} ${t("updatedSuccessfully")}`);
            } else {
                toast.error(response);
            }
        } else {
            toast.error(t("indexNotFound"));
        }
    };

    const Toggle = ({ _id, enabled }) => (
        <Form.Check
            className="ms-4"
            type="switch"
            name={_id}
            id={_id}
            label=""
            checked={enabled}
            onChange={(e) => handleMonitored(e)}
        />
    );
    const columns = [
        {
            name: t("menu.hazards"),
            selector: currLang,
            cell: (d) => (d && d.title && d.title[currentLang] ? d.title[currentLang] : ""),
        },
        {
            name: t("hazardType"),
            selector: "hazard_type",
            cell: (d) => (d && d.hazard_type && d.hazard_type.title ? d.hazard_type.title : ""),
        },
        {
            name: t("published"),
            selector: "enabled",
            cell: (row) => <Toggle {...row} />,
        },
        {
            name: t("action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_hazard/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <span onClick={() => userAction(d)} style={{ cursor: "pointer" }}>
                        <i className="icon fas fa-trash-alt" />
                    </span>
                </div>
            ),
        },
    ];

    useEffect(() => {
        getHazardsData();
    }, []);

    const hazardParams = {
        sort: { [currLang]: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const getHazardsData = async () => {
        setLoading(true);
        const response = await apiService.get("/hazard", hazardParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        hazardParams.limit = perPage;
        hazardParams.page = page;
        getHazardsData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        hazardParams.limit = newPerPage;
        hazardParams.page = page;
        setLoading(true);
        const response = await apiService.get("/hazard", hazardParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectHazard(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/hazard/${selectHazard}`);
            getHazardsData();
            setModal(false);
            toast.success(t("adminsetting.hazard.Table.hazardDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.hazard.Table.errorDeletingHazard"));
        }
    };

    const modalHide = () => setModal(false);

    const subHeaderComponentMemo = React.useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText("");
            }
        };

        const sendQuery = (q) => {
            if (q) {
                hazardParams.query = { [currLang]: q };
            }
            getHazardsData();
        };

        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            setFilterText(e.target.value);
            handleSearchTitle(e.target.value);
        };

        return <HazardTableFilter onFilter={handleChange} onClear={handleClear} filterText={filterText} />;
    }, [filterText]);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("deleteHazard")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("areYouSureWantToDeleteThisHazard")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                subheader
                resetPaginationToggle={resetPaginationToggle}
                subHeaderComponent={subHeaderComponentMemo}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default HazardTable;
