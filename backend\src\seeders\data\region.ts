export const Regions = [
  {
      "country_code": "AR",
      "title": "Buenos Aires"
  },
  {
      "country_code": "AR",
      "title": "Catamarca"
  },
  {
      "country_code": "AR",
      "title": "Chaco"
  },
  {
      "country_code": "AR",
      "title": "Chubut"
  },
  {
      "country_code": "AR",
      "title": "Cordoba"
  },
  {
      "country_code": "AR",
      "title": "Corrientes"
  },
  {
      "country_code": "AR",
      "title": "Entre Rios"
  },
  {
      "country_code": "AR",
      "title": "Formosa"
  },
  {
      "country_code": "AR",
      "title": "Jujuy"
  },
  {
      "country_code": "AR",
      "title": "La Pampa"
  },
  {
      "country_code": "AR",
      "title": "La Rioja"
  },
  {
      "country_code": "AR",
      "title": "Buenos Aires"
  },
  {
      "country_code": "AR",
      "title": "Mendoza"
  },
  {
      "country_code": "AR",
      "title": "Misiones"
  },
  {
      "country_code": "AR",
      "title": "Neuquen"
  },
  {
      "country_code": "AR",
      "title": "Rio Negro"
  },
  {
      "country_code": "AR",
      "title": "Salta"
  },
  {
      "country_code": "AR",
      "title": "San Juan"
  },
  {
      "country_code": "AR",
      "title": "San Luis"
  },
  {
      "country_code": "AR",
      "title": "Santa Cruz"
  },
  {
      "country_code": "AR",
      "title": "Santa Fe"
  },
  {
      "country_code": "AR",
      "title": "Santiago Del Estero"
  },
  {
      "country_code": "AR",
      "title": "Tierra Del Fuego"
  },
  {
      "country_code": "AR",
      "title": "Tucuman"
  },
  {
      "country_code": "AT",
      "title": "Burgenland"
  },
  {
      "country_code": "AT",
      "title": "Kärnten"
  },
  {
      "country_code": "AT",
      "title": "Niederösterreich"
  },
  {
      "country_code": "AT",
      "title": "Oberösterreich"
  },
  {
      "country_code": "AT",
      "title": "Salzburg"
  },
  {
      "country_code": "AT",
      "title": "Steiermark"
  },
  {
      "country_code": "AT",
      "title": "Tirol"
  },
  {
      "country_code": "AT",
      "title": "Vorarlberg"
  },
  {
      "country_code": "AT",
      "title": "Wien"
  },
  {
      "country_code": "AU",
      "title": "Australian Capital Territory"
  },
  {
      "country_code": "AU",
      "title": "New South Wales"
  },
  {
      "country_code": "AU",
      "title": "Northern Territory"
  },
  {
      "country_code": "AU",
      "title": "Queensland"
  },
  {
      "country_code": "AU",
      "title": "South Australia"
  },
  {
      "country_code": "AU",
      "title": "Tasmania"
  },
  {
      "country_code": "AU",
      "title": "Victoria"
  },
  {
      "country_code": "AU",
      "title": "Western Australia"
  },
  {
      "country_code": "AX",
      "title": "Ålands landsbygd"
  },
  {
      "country_code": "AX",
      "title": "Ålands skärgård"
  },
  {
      "country_code": "AX",
      "title": "Mariehamns stad"
  },
  {
      "country_code": "AZ",
      "title": "Ağcabədi"
  },
  {
      "country_code": "AZ",
      "title": "Ağdaş"
  },
  {
      "country_code": "AZ",
      "title": "Ağdam"
  },
  {
      "country_code": "AZ",
      "title": "Ağstafa"
  },
  {
      "country_code": "AZ",
      "title": "Ağsu"
  },
  {
      "country_code": "AZ",
      "title": "Abşeron"
  },
  {
      "country_code": "AZ",
      "title": "Astara"
  },
  {
      "country_code": "AZ",
      "title": "Şabran"
  },
  {
      "country_code": "AZ",
      "title": "Şamaxi"
  },
  {
      "country_code": "AZ",
      "title": "Şəki"
  },
  {
      "country_code": "AZ",
      "title": "Şəmkir"
  },
  {
      "country_code": "AZ",
      "title": "Şirvan"
  },
  {
      "country_code": "AZ",
      "title": "Baki"
  },
  {
      "country_code": "AZ",
      "title": "Balakən"
  },
  {
      "country_code": "AZ",
      "title": "Bərdə"
  },
  {
      "country_code": "AZ",
      "title": "Beyləqan"
  },
  {
      "country_code": "AZ",
      "title": "Biləsuvar"
  },
  {
      "country_code": "AZ",
      "title": "Cəlilabad"
  },
  {
      "country_code": "AZ",
      "title": "Daşkəsən"
  },
  {
      "country_code": "AZ",
      "title": "Füzuli"
  },
  {
      "country_code": "AZ",
      "title": "Göyçay"
  },
  {
      "country_code": "AZ",
      "title": "Göy-Göl"
  },
  {
      "country_code": "AZ",
      "title": "Gədəbəy"
  },
  {
      "country_code": "AZ",
      "title": "Gəncə"
  },
  {
      "country_code": "AZ",
      "title": "Goranboy"
  },
  {
      "country_code": "AZ",
      "title": "Haciqabul"
  },
  {
      "country_code": "AZ",
      "title": "Imişli"
  },
  {
      "country_code": "AZ",
      "title": "Ismayilli"
  },
  {
      "country_code": "AZ",
      "title": "Kürdəmir"
  },
  {
      "country_code": "AZ",
      "title": "Kəlbəcər"
  },
  {
      "country_code": "AZ",
      "title": "Lerik"
  },
  {
      "country_code": "AZ",
      "title": "Lənkəran"
  },
  {
      "country_code": "AZ",
      "title": "Masalli"
  },
  {
      "country_code": "AZ",
      "title": "Mingəçevir"
  },
  {
      "country_code": "AZ",
      "title": "Naftalan"
  },
  {
      "country_code": "AZ",
      "title": "Neftçala"
  },
  {
      "country_code": "AZ",
      "title": "Oğuz"
  },
  {
      "country_code": "AZ",
      "title": "Qax"
  },
  {
      "country_code": "AZ",
      "title": "Qazax"
  },
  {
      "country_code": "AZ",
      "title": "Qəbələ"
  },
  {
      "country_code": "AZ",
      "title": "Qobustan"
  },
  {
      "country_code": "AZ",
      "title": "Quba"
  },
  {
      "country_code": "AZ",
      "title": "Qusar"
  },
  {
      "country_code": "AZ",
      "title": "Saatli"
  },
  {
      "country_code": "AZ",
      "title": "Sabirabad"
  },
  {
      "country_code": "AZ",
      "title": "Salyan"
  },
  {
      "country_code": "AZ",
      "title": "Samux"
  },
  {
      "country_code": "AZ",
      "title": "Siyəzən"
  },
  {
      "country_code": "AZ",
      "title": "Sumqayit"
  },
  {
      "country_code": "AZ",
      "title": "Tər-Tər"
  },
  {
      "country_code": "AZ",
      "title": "Tovuz"
  },
  {
      "country_code": "AZ",
      "title": "Ucar"
  },
  {
      "country_code": "AZ",
      "title": "Xaçmaz"
  },
  {
      "country_code": "AZ",
      "title": "Xizi"
  },
  {
      "country_code": "AZ",
      "title": "Yardimli"
  },
  {
      "country_code": "AZ",
      "title": "Yevlax"
  },
  {
      "country_code": "AZ",
      "title": "Zaqatala"
  },
  {
      "country_code": "AZ",
      "title": "Zərdab"
  },
  {
      "country_code": "BD",
      "title": "Barisal Division"
  },
  {
      "country_code": "BD",
      "title": "Chittagong"
  },
  {
      "country_code": "BD",
      "title": "Dhaka Division"
  },
  {
      "country_code": "BD",
      "title": "Khulna Division"
  },
  {
      "country_code": "BD",
      "title": "Rājshāhi Division"
  },
  {
      "country_code": "BD",
      "title": "Rangpur Division"
  },
  {
      "country_code": "BD",
      "title": "Sylhet Division"
  },
  {
      "country_code": "BE",
      "title": "Bruxelles-Capitale"
  },
  {
      "country_code": "BE",
      "title": "Vlaanderen"
  },
  {
      "country_code": "BE",
      "title": "Wallonie"
  },
  {
      "country_code": "BG",
      "title": "Разград / Razgrad"
  },
  {
      "country_code": "BG",
      "title": "Русе / Ruse"
  },
  {
      "country_code": "BG",
      "title": "Силистра / Silistra"
  },
  {
      "country_code": "BG",
      "title": "Сливен / Sliven"
  },
  {
      "country_code": "BG",
      "title": "Смолян / Smoljan"
  },
  {
      "country_code": "BG",
      "title": "София (столица) / Sofija (stolica)"
  },
  {
      "country_code": "BG",
      "title": "София / Sofija"
  },
  {
      "country_code": "BG",
      "title": "Стара Загора / Stara Zagora"
  },
  {
      "country_code": "BG",
      "title": "Шумен / Shumen"
  },
  {
      "country_code": "BG",
      "title": "Ямбол / Jambol"
  },
  {
      "country_code": "BG",
      "title": "Благоевград / Blagoevgrad"
  },
  {
      "country_code": "BG",
      "title": "Бургас / Burgas"
  },
  {
      "country_code": "BG",
      "title": "Видин / Vidin"
  },
  {
      "country_code": "BG",
      "title": "Варна / Varna"
  },
  {
      "country_code": "BG",
      "title": "Велико Търново / Veliko Turnovo"
  },
  {
      "country_code": "BG",
      "title": "Враца / Vraca"
  },
  {
      "country_code": "BG",
      "title": "Габрово / Gabrovo"
  },
  {
      "country_code": "BG",
      "title": "Добрич / Dobrich"
  },
  {
      "country_code": "BG",
      "title": "Ловеч / Lovech"
  },
  {
      "country_code": "BG",
      "title": "Търговище / Turgovishhe"
  },
  {
      "country_code": "BG",
      "title": "Хасково / Khaskovo"
  },
  {
      "country_code": "BG",
      "title": "Монтана / Montana"
  },
  {
      "country_code": "BG",
      "title": "Кърджали / Kurdzhali"
  },
  {
      "country_code": "BG",
      "title": "Кюстендил / Kjustendil"
  },
  {
      "country_code": "BG",
      "title": "Плевен / Pleven"
  },
  {
      "country_code": "BG",
      "title": "Пловдив / Plovdiv"
  },
  {
      "country_code": "BG",
      "title": "Пазарджик / Pazardzhik"
  },
  {
      "country_code": "BG",
      "title": "Перник / Pernik"
  },
  {
      "country_code": "BM",
      "title": "Devonshire Parish"
  },
  {
      "country_code": "BM",
      "title": "Hamilton"
  },
  {
      "country_code": "BM",
      "title": "Paget Parish"
  },
  {
      "country_code": "BM",
      "title": "Pembroke Parish"
  },
  {
      "country_code": "BM",
      "title": "Saint George"
  },
  {
      "country_code": "BM",
      "title": "Saint George’s Parish"
  },
  {
      "country_code": "BM",
      "title": "Sandys Parish"
  },
  {
      "country_code": "BM",
      "title": "Smith’s Parish"
  },
  {
      "country_code": "BM",
      "title": "Southampton Parish"
  },
  {
      "country_code": "BM",
      "title": "Warwick Parish"
  },
  {
      "country_code": "BR",
      "title": "Acre"
  },
  {
      "country_code": "BR",
      "title": "Alagoas"
  },
  {
      "country_code": "BR",
      "title": "Amapa"
  },
  {
      "country_code": "BR",
      "title": "Amazonas"
  },
  {
      "country_code": "BR",
      "title": "Bahia"
  },
  {
      "country_code": "BR",
      "title": "Ceara"
  },
  {
      "country_code": "BR",
      "title": "Distrito Federal"
  },
  {
      "country_code": "BR",
      "title": "Espirito Santo"
  },
  {
      "country_code": "BR",
      "title": "Goias"
  },
  {
      "country_code": "BR",
      "title": "Maranhao"
  },
  {
      "country_code": "BR",
      "title": "Mato Grosso"
  },
  {
      "country_code": "BR",
      "title": "Mato Grosso do Sul"
  },
  {
      "country_code": "BR",
      "title": "Minas Gerais"
  },
  {
      "country_code": "BR",
      "title": "Para"
  },
  {
      "country_code": "BR",
      "title": "Paraiba"
  },
  {
      "country_code": "BR",
      "title": "Parana"
  },
  {
      "country_code": "BR",
      "title": "Pernambuco"
  },
  {
      "country_code": "BR",
      "title": "Piaui"
  },
  {
      "country_code": "BR",
      "title": "Rio de Janeiro"
  },
  {
      "country_code": "BR",
      "title": "Rio Grande do Norte"
  },
  {
      "country_code": "BR",
      "title": "Rio Grande do Sul"
  },
  {
      "country_code": "BR",
      "title": "Rondonia"
  },
  {
      "country_code": "BR",
      "title": "Roraima"
  },
  {
      "country_code": "BR",
      "title": "Santa Catarina"
  },
  {
      "country_code": "BR",
      "title": "Sao Paulo"
  },
  {
      "country_code": "BR",
      "title": "Sergipe"
  },
  {
      "country_code": "BR",
      "title": "Tocantins"
  },
  {
      "country_code": "BY",
      "title": "Brest"
  },
  {
      "country_code": "BY",
      "title": "Gomel"
  },
  {
      "country_code": "BY",
      "title": "Grodno"
  },
  {
      "country_code": "BY",
      "title": "Minsk"
  },
  {
      "country_code": "BY",
      "title": "Moghilev"
  },
  {
      "country_code": "BY",
      "title": "Vitebsk"
  },
  {
      "country_code": "CA",
      "title": "Alberta"
  },
  {
      "country_code": "CA",
      "title": "British Columbia"
  },
  {
      "country_code": "CA",
      "title": "Manitoba"
  },
  {
      "country_code": "CA",
      "title": "New Brunswick"
  },
  {
      "country_code": "CA",
      "title": "Newfoundland and Labrador"
  },
  {
      "country_code": "CA",
      "title": "Northwest Territory"
  },
  {
      "country_code": "CA",
      "title": "Nova Scotia"
  },
  {
      "country_code": "CA",
      "title": "Nunavut Territory"
  },
  {
      "country_code": "CA",
      "title": "Ontario"
  },
  {
      "country_code": "CA",
      "title": "Prince Edward Island"
  },
  {
      "country_code": "CA",
      "title": "Quebec"
  },
  {
      "country_code": "CA",
      "title": "Saskatchewan"
  },
  {
      "country_code": "CA",
      "title": "Yukon"
  },
  {
      "country_code": "CH",
      "title": "Canton de Berne"
  },
  {
      "country_code": "CH",
      "title": "Canton de Fribourg"
  },
  {
      "country_code": "CH",
      "title": "Canton de Vaud"
  },
  {
      "country_code": "CH",
      "title": "Canton du Valais"
  },
  {
      "country_code": "CH",
      "title": "Genève"
  },
  {
      "country_code": "CH",
      "title": "Jura"
  },
  {
      "country_code": "CH",
      "title": "Kanton Aargau"
  },
  {
      "country_code": "CH",
      "title": "Kanton Appenzell Ausserrhoden"
  },
  {
      "country_code": "CH",
      "title": "Kanton Appenzell Innerrhoden"
  },
  {
      "country_code": "CH",
      "title": "Kanton Basel-Landschaft"
  },
  {
      "country_code": "CH",
      "title": "Kanton Basel-Stadt"
  },
  {
      "country_code": "CH",
      "title": "Kanton Glarus"
  },
  {
      "country_code": "CH",
      "title": "Kanton Graubünden"
  },
  {
      "country_code": "CH",
      "title": "Kanton Luzern"
  },
  {
      "country_code": "CH",
      "title": "Kanton Nidwalden"
  },
  {
      "country_code": "CH",
      "title": "Kanton Obwalden"
  },
  {
      "country_code": "CH",
      "title": "Kanton Schaffhausen"
  },
  {
      "country_code": "CH",
      "title": "Kanton Schwyz"
  },
  {
      "country_code": "CH",
      "title": "Kanton Solothurn"
  },
  {
      "country_code": "CH",
      "title": "Kanton St. Gallen"
  },
  {
      "country_code": "CH",
      "title": "Kanton Thurgau"
  },
  {
      "country_code": "CH",
      "title": "Kanton Uri"
  },
  {
      "country_code": "CH",
      "title": "Kanton Zürich"
  },
  {
      "country_code": "CH",
      "title": "Kanton Zug"
  },
  {
      "country_code": "CH",
      "title": "Neuchâtel"
  },
  {
      "country_code": "CH",
      "title": "Ticino"
  },
  {
      "country_code": "CL",
      "title": "Región Aysén"
  },
  {
      "country_code": "CL",
      "title": "Región de Antofagasta"
  },
  {
      "country_code": "CL",
      "title": "Región de Arica y Parinacota"
  },
  {
      "country_code": "CL",
      "title": "Región de Atacama"
  },
  {
      "country_code": "CL",
      "title": "Región de Coquimbo"
  },
  {
      "country_code": "CL",
      "title": "Región de la Araucanía"
  },
  {
      "country_code": "CL",
      "title": "Región de los Lagos"
  },
  {
      "country_code": "CL",
      "title": "Región de los Ríos"
  },
  {
      "country_code": "CL",
      "title": "Región de Magallanes y Antártica Chilena"
  },
  {
      "country_code": "CL",
      "title": "Región de Tarapacá"
  },
  {
      "country_code": "CL",
      "title": "Región de Valparaíso"
  },
  {
      "country_code": "CL",
      "title": "Región del Biobío"
  },
  {
      "country_code": "CL",
      "title": "Región del Libertador General Bernardo O’Higgins"
  },
  {
      "country_code": "CL",
      "title": "Región del Maule"
  },
  {
      "country_code": "CL",
      "title": "Región Metropolitana"
  },
  {
      "country_code": "CO",
      "title": "Amazonas"
  },
  {
      "country_code": "CO",
      "title": "Antioquia"
  },
  {
      "country_code": "CO",
      "title": "Arauca"
  },
  {
      "country_code": "CO",
      "title": "Archipielago De San Andres"
  },
  {
      "country_code": "CO",
      "title": "Atlantico"
  },
  {
      "country_code": "CO",
      "title": "Bogota, D.C."
  },
  {
      "country_code": "CO",
      "title": "Bolivar"
  },
  {
      "country_code": "CO",
      "title": "Boyaca"
  },
  {
      "country_code": "CO",
      "title": "Caldas"
  },
  {
      "country_code": "CO",
      "title": "Caqueta"
  },
  {
      "country_code": "CO",
      "title": "Casanare"
  },
  {
      "country_code": "CO",
      "title": "Cauca"
  },
  {
      "country_code": "CO",
      "title": "Cesar"
  },
  {
      "country_code": "CO",
      "title": "Choco"
  },
  {
      "country_code": "CO",
      "title": "Cordoba"
  },
  {
      "country_code": "CO",
      "title": "Cundinamarca"
  },
  {
      "country_code": "CO",
      "title": "Guainia"
  },
  {
      "country_code": "CO",
      "title": "Guaviare"
  },
  {
      "country_code": "CO",
      "title": "Huila"
  },
  {
      "country_code": "CO",
      "title": "La Guajira"
  },
  {
      "country_code": "CO",
      "title": "Magdalena"
  },
  {
      "country_code": "CO",
      "title": "Meta"
  },
  {
      "country_code": "CO",
      "title": "Nariño"
  },
  {
      "country_code": "CO",
      "title": "Norte De Santander"
  },
  {
      "country_code": "CO",
      "title": "Putumayo"
  },
  {
      "country_code": "CO",
      "title": "Quindio"
  },
  {
      "country_code": "CO",
      "title": "Risaralda"
  },
  {
      "country_code": "CO",
      "title": "Santander"
  },
  {
      "country_code": "CO",
      "title": "Sucre"
  },
  {
      "country_code": "CO",
      "title": "Tolima"
  },
  {
      "country_code": "CO",
      "title": "Valle Del Cauca"
  },
  {
      "country_code": "CO",
      "title": "Vaupes"
  },
  {
      "country_code": "CO",
      "title": "Vichada"
  },
  {
      "country_code": "CR",
      "title": "Provincia de Alajuela"
  },
  {
      "country_code": "CR",
      "title": "Provincia de Cartago"
  },
  {
      "country_code": "CR",
      "title": "Provincia de Guanacaste"
  },
  {
      "country_code": "CR",
      "title": "Provincia de Heredia"
  },
  {
      "country_code": "CR",
      "title": "Provincia de Limón"
  },
  {
      "country_code": "CR",
      "title": "Provincia de Puntarenas"
  },
  {
      "country_code": "CR",
      "title": "Provincia de San José"
  },
  {
      "country_code": "CZ",
      "title": "Ústecký kraj"
  },
  {
      "country_code": "CZ",
      "title": "Hlavní město Praha"
  },
  {
      "country_code": "CZ",
      "title": "Jihočeský kraj"
  },
  {
      "country_code": "CZ",
      "title": "Jihomoravský kraj"
  },
  {
      "country_code": "CZ",
      "title": "Karlovarský kraj"
  },
  {
      "country_code": "CZ",
      "title": "Královéhradecký kraj"
  },
  {
      "country_code": "CZ",
      "title": "Kraj Vysočina"
  },
  {
      "country_code": "CZ",
      "title": "Liberecký kraj"
  },
  {
      "country_code": "CZ",
      "title": "Moravskoslezský kraj"
  },
  {
      "country_code": "CZ",
      "title": "Olomoucký kraj"
  },
  {
      "country_code": "CZ",
      "title": "Pardubický kraj"
  },
  {
      "country_code": "CZ",
      "title": "Plzeňský kraj"
  },
  {
      "country_code": "CZ",
      "title": "Středočeský kraj"
  },
  {
      "country_code": "CZ",
      "title": "Zlínský kraj"
  },
  {
      "country_code": "DE",
      "title": "Baden-Württemberg"
  },
  {
      "country_code": "DE",
      "title": "Bayern"
  },
  {
      "country_code": "DE",
      "title": "Berlin"
  },
  {
      "country_code": "DE",
      "title": "Brandenburg"
  },
  {
      "country_code": "DE",
      "title": "Bremen"
  },
  {
      "country_code": "DE",
      "title": "Hamburg"
  },
  {
      "country_code": "DE",
      "title": "Hessen"
  },
  {
      "country_code": "DE",
      "title": "Mecklenburg-Vorpommern"
  },
  {
      "country_code": "DE",
      "title": "Niedersachsen"
  },
  {
      "country_code": "DE",
      "title": "Nordrhein-Westfalen"
  },
  {
      "country_code": "DE",
      "title": "Rheinland-Pfalz"
  },
  {
      "country_code": "DE",
      "title": "Saarland"
  },
  {
      "country_code": "DE",
      "title": "Sachsen"
  },
  {
      "country_code": "DE",
      "title": "Sachsen-Anhalt"
  },
  {
      "country_code": "DE",
      "title": "Schleswig-Holstein"
  },
  {
      "country_code": "DE",
      "title": "Thüringen"
  },
  {
      "country_code": "DK",
      "title": "Region Hovedstaden"
  },
  {
      "country_code": "DK",
      "title": "Region Midtjylland"
  },
  {
      "country_code": "DK",
      "title": "Region Nordjylland"
  },
  {
      "country_code": "DK",
      "title": "Region Sjælland"
  },
  {
      "country_code": "DK",
      "title": "Region Syddanmark"
  },
  {
      "country_code": "DZ",
      "title": "Adrar"
  },
  {
      "country_code": "DZ",
      "title": "Ain-Defla"
  },
  {
      "country_code": "DZ",
      "title": "Ain-Temouchent"
  },
  {
      "country_code": "DZ",
      "title": "Alger"
  },
  {
      "country_code": "DZ",
      "title": "Annaba"
  },
  {
      "country_code": "DZ",
      "title": "Batna"
  },
  {
      "country_code": "DZ",
      "title": "Bechar"
  },
  {
      "country_code": "DZ",
      "title": "Bejaia"
  },
  {
      "country_code": "DZ",
      "title": "Biskra"
  },
  {
      "country_code": "DZ",
      "title": "Blida"
  },
  {
      "country_code": "DZ",
      "title": "Bordj-Bou-Arreridj"
  },
  {
      "country_code": "DZ",
      "title": "Bouira"
  },
  {
      "country_code": "DZ",
      "title": "Boumerdes"
  },
  {
      "country_code": "DZ",
      "title": "Chlef"
  },
  {
      "country_code": "DZ",
      "title": "Constantine"
  },
  {
      "country_code": "DZ",
      "title": "Djelfa"
  },
  {
      "country_code": "DZ",
      "title": "El-Bayadh"
  },
  {
      "country_code": "DZ",
      "title": "El-Oued"
  },
  {
      "country_code": "DZ",
      "title": "El-Taref"
  },
  {
      "country_code": "DZ",
      "title": "Ghardaia"
  },
  {
      "country_code": "DZ",
      "title": "Guelma"
  },
  {
      "country_code": "DZ",
      "title": "Illizi"
  },
  {
      "country_code": "DZ",
      "title": "Jijel"
  },
  {
      "country_code": "DZ",
      "title": "Khenchela"
  },
  {
      "country_code": "DZ",
      "title": "L.Aghouat"
  },
  {
      "country_code": "DZ",
      "title": "Mascara"
  },
  {
      "country_code": "DZ",
      "title": "Medea"
  },
  {
      "country_code": "DZ",
      "title": "Mila"
  },
  {
      "country_code": "DZ",
      "title": "Mostaganem"
  },
  {
      "country_code": "DZ",
      "title": "M'Sila"
  },
  {
      "country_code": "DZ",
      "title": "Naama"
  },
  {
      "country_code": "DZ",
      "title": "Oran"
  },
  {
      "country_code": "DZ",
      "title": "Ouargla"
  },
  {
      "country_code": "DZ",
      "title": "Oum-El-Bouaghi"
  },
  {
      "country_code": "DZ",
      "title": "Relizane"
  },
  {
      "country_code": "DZ",
      "title": "Saida"
  },
  {
      "country_code": "DZ",
      "title": "Setif"
  },
  {
      "country_code": "DZ",
      "title": "Sidi-Bel-Abbes"
  },
  {
      "country_code": "DZ",
      "title": "Skikda"
  },
  {
      "country_code": "DZ",
      "title": "Souk-Ahras"
  },
  {
      "country_code": "DZ",
      "title": "Tamanrasset"
  },
  {
      "country_code": "DZ",
      "title": "Tebessa"
  },
  {
      "country_code": "DZ",
      "title": "Tiaret"
  },
  {
      "country_code": "DZ",
      "title": "Tindouf"
  },
  {
      "country_code": "DZ",
      "title": "Tipaza"
  },
  {
      "country_code": "DZ",
      "title": "Tissemsilt"
  },
  {
      "country_code": "DZ",
      "title": "Tizi-Ouzou"
  },
  {
      "country_code": "DZ",
      "title": "Tlemcen"
  },
  {
      "country_code": "EE",
      "title": "Harju maakond"
  },
  {
      "country_code": "EE",
      "title": "Hiiu maakond"
  },
  {
      "country_code": "EE",
      "title": "Ida-Viru maakond"
  },
  {
      "country_code": "EE",
      "title": "Järva maakond"
  },
  {
      "country_code": "EE",
      "title": "Jõgeva maakond"
  },
  {
      "country_code": "EE",
      "title": "Lääne maakond"
  },
  {
      "country_code": "EE",
      "title": "Lääne-Viru maakond"
  },
  {
      "country_code": "EE",
      "title": "Pärnu maakond"
  },
  {
      "country_code": "EE",
      "title": "Põlva maakond"
  },
  {
      "country_code": "EE",
      "title": "Rapla maakond"
  },
  {
      "country_code": "EE",
      "title": "Saare maakond"
  },
  {
      "country_code": "EE",
      "title": "Tartu maakond"
  },
  {
      "country_code": "EE",
      "title": "Võru maakond"
  },
  {
      "country_code": "EE",
      "title": "Valga maakond"
  },
  {
      "country_code": "EE",
      "title": "Viljandi maakond"
  },
  {
      "country_code": "ES",
      "title": "Andalucia"
  },
  {
      "country_code": "ES",
      "title": "Aragon"
  },
  {
      "country_code": "ES",
      "title": "Asturias"
  },
  {
      "country_code": "ES",
      "title": "Baleares"
  },
  {
      "country_code": "ES",
      "title": "Canarias"
  },
  {
      "country_code": "ES",
      "title": "Cantabria"
  },
  {
      "country_code": "ES",
      "title": "Castilla - La Mancha"
  },
  {
      "country_code": "ES",
      "title": "Castilla - Leon"
  },
  {
      "country_code": "ES",
      "title": "Cataluna"
  },
  {
      "country_code": "ES",
      "title": "Ceuta"
  },
  {
      "country_code": "ES",
      "title": "Comunidad Valenciana"
  },
  {
      "country_code": "ES",
      "title": "Extremadura"
  },
  {
      "country_code": "ES",
      "title": "Galicia"
  },
  {
      "country_code": "ES",
      "title": "La Rioja"
  },
  {
      "country_code": "ES",
      "title": "Madrid"
  },
  {
      "country_code": "ES",
      "title": "Melilla"
  },
  {
      "country_code": "ES",
      "title": "Murcia"
  },
  {
      "country_code": "ES",
      "title": "Navarra"
  },
  {
      "country_code": "ES",
      "title": "Pais Vasco"
  },
  {
      "country_code": "FI",
      "title": "Central Finland Region"
  },
  {
      "country_code": "FI",
      "title": "Central Ostrobothnia Region"
  },
  {
      "country_code": "FI",
      "title": "Kainuu"
  },
  {
      "country_code": "FI",
      "title": "Kanta-Häme"
  },
  {
      "country_code": "FI",
      "title": "Kymenlaakso"
  },
  {
      "country_code": "FI",
      "title": "Lapland"
  },
  {
      "country_code": "FI",
      "title": "North Karelia"
  },
  {
      "country_code": "FI",
      "title": "North Ostrobothnia Region"
  },
  {
      "country_code": "FI",
      "title": "Northern Savo"
  },
  {
      "country_code": "FI",
      "title": "Ostrobothnia Region"
  },
  {
      "country_code": "FI",
      "title": "Päijänne Tavastia"
  },
  {
      "country_code": "FI",
      "title": "Pirkanmaa"
  },
  {
      "country_code": "FI",
      "title": "Satakunta"
  },
  {
      "country_code": "FI",
      "title": "South Karelia"
  },
  {
      "country_code": "FI",
      "title": "South Ostrobothnia Region"
  },
  {
      "country_code": "FI",
      "title": "Southern Savonia"
  },
  {
      "country_code": "FI",
      "title": "Southwest Finland"
  },
  {
      "country_code": "FI",
      "title": "Uusimaa"
  },
  {
      "country_code": "FR",
      "title": "Auvergne-Rhône-Alpes"
  },
  {
      "country_code": "FR",
      "title": "Île-de-France"
  },
  {
      "country_code": "FR",
      "title": "Bourgogne-Franche-Comté"
  },
  {
      "country_code": "FR",
      "title": "Bretagne"
  },
  {
      "country_code": "FR",
      "title": "Centre-Val de Loire"
  },
  {
      "country_code": "FR",
      "title": "Corse"
  },
  {
      "country_code": "FR",
      "title": "Grand Est"
  },
  {
      "country_code": "FR",
      "title": "Hauts-de-France"
  },
  {
      "country_code": "FR",
      "title": "Normandie"
  },
  {
      "country_code": "FR",
      "title": "Nouvelle-Aquitaine"
  },
  {
      "country_code": "FR",
      "title": "Occitanie"
  },
  {
      "country_code": "FR",
      "title": "Pays de la Loire"
  },
  {
      "country_code": "FR",
      "title": "Provence-Alpes-Côte d'Azur"
  },
  {
      "country_code": "GB",
      "title": "England"
  },
  {
      "country_code": "GB",
      "title": "Northern Ireland"
  },
  {
      "country_code": "GB",
      "title": "Scotland"
  },
  {
      "country_code": "GB",
      "title": "Wales"
  },
  {
      "country_code": "GF",
      "title": "Guyane"
  },
  {
      "country_code": "GP",
      "title": "Guadeloupe"
  },
  {
      "country_code": "GT",
      "title": "Ciudad de Guatemala"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE ALTA VERAPAZ"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE BAJA VERAPAZ"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE CHIMALTENANGO"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE CHIQUIMULA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE EL PROGRESO"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE ESCUINTLA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE GUATEMALA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE HUEHUETENANGO"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE IZABAL"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE JALAPA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE JUTIAPA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE PETEN"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE QUETZALTENANGO"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE RETALHULEU"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE SACATEPEQUEZ"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE SAN MARCOS"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE SANTA ROSA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE SOLOLA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE SUCHITEPEQUEZ"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE TOTONICAPAN"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DE ZACAPA"
  },
  {
      "country_code": "GT",
      "title": "DEPTO DEL QUICHE"
  },
  {
      "country_code": "GU",
      "title": "Gu"
  },
  {
      "country_code": "HR",
      "title": "Šibensko-Kninska"
  },
  {
      "country_code": "HR",
      "title": "Bjelovarsko-Bilogorska"
  },
  {
      "country_code": "HR",
      "title": "Brodsko-Posavska"
  },
  {
      "country_code": "HR",
      "title": "Dubrovačko-Neretvanska"
  },
  {
      "country_code": "HR",
      "title": "Grad Zagreb"
  },
  {
      "country_code": "HR",
      "title": "Istarska"
  },
  {
      "country_code": "HR",
      "title": "Karlovačka"
  },
  {
      "country_code": "HR",
      "title": "Koprivničko-Križevačka"
  },
  {
      "country_code": "HR",
      "title": "Krapinsko-Zagorska"
  },
  {
      "country_code": "HR",
      "title": "Ličko-Senjska"
  },
  {
      "country_code": "HR",
      "title": "Međimurska"
  },
  {
      "country_code": "HR",
      "title": "Osječko-Baranjska"
  },
  {
      "country_code": "HR",
      "title": "Požeško-Slavonska"
  },
  {
      "country_code": "HR",
      "title": "Primorsko-Goranska"
  },
  {
      "country_code": "HR",
      "title": "Sisačko-Moslavačka"
  },
  {
      "country_code": "HR",
      "title": "Splitsko-Dalmatinska"
  },
  {
      "country_code": "HR",
      "title": "Varaždinska"
  },
  {
      "country_code": "HR",
      "title": "Virovitičko-Podravska"
  },
  {
      "country_code": "HR",
      "title": "Vukovarsko-Srijemska"
  },
  {
      "country_code": "HR",
      "title": "Zadarska"
  },
  {
      "country_code": "HR",
      "title": "Zagrebačka"
  },
  {
      "country_code": "HU",
      "title": "Bács-Kiskun"
  },
  {
      "country_code": "HU",
      "title": "Békés"
  },
  {
      "country_code": "HU",
      "title": "Baranya"
  },
  {
      "country_code": "HU",
      "title": "Borsod-Abaúj-Zemplén"
  },
  {
      "country_code": "HU",
      "title": "Budapest"
  },
  {
      "country_code": "HU",
      "title": "Csongrád"
  },
  {
      "country_code": "HU",
      "title": "Fejér"
  },
  {
      "country_code": "HU",
      "title": "Győr-Moson-Sopron"
  },
  {
      "country_code": "HU",
      "title": "Hajdú-Bihar"
  },
  {
      "country_code": "HU",
      "title": "Heves"
  },
  {
      "country_code": "HU",
      "title": "Jász-Nagykun-Szolnok"
  },
  {
      "country_code": "HU",
      "title": "Komárom-Esztergom"
  },
  {
      "country_code": "HU",
      "title": "Nógrád"
  },
  {
      "country_code": "HU",
      "title": "Pest"
  },
  {
      "country_code": "HU",
      "title": "Somogy"
  },
  {
      "country_code": "HU",
      "title": "Szabolcs-Szatmár-Bereg"
  },
  {
      "country_code": "HU",
      "title": "Tolna"
  },
  {
      "country_code": "HU",
      "title": "Vas"
  },
  {
      "country_code": "HU",
      "title": "Veszprém"
  },
  {
      "country_code": "HU",
      "title": "Zala"
  },
  {
      "country_code": "IN",
      "title": "Andaman & Nicobar Islands"
  },
  {
      "country_code": "IN",
      "title": "Andhra Pradesh"
  },
  {
      "country_code": "IN",
      "title": "Arunachal Pradesh"
  },
  {
      "country_code": "IN",
      "title": "Assam"
  },
  {
      "country_code": "IN",
      "title": "Bihar"
  },
  {
      "country_code": "IN",
      "title": "Chandigarh"
  },
  {
      "country_code": "IN",
      "title": "Chattisgarh"
  },
  {
      "country_code": "IN",
      "title": "Dadra & Nagar Haveli"
  },
  {
      "country_code": "IN",
      "title": "Daman & Diu"
  },
  {
      "country_code": "IN",
      "title": "Delhi"
  },
  {
      "country_code": "IN",
      "title": "Goa"
  },
  {
      "country_code": "IN",
      "title": "Gujarat"
  },
  {
      "country_code": "IN",
      "title": "Haryana"
  },
  {
      "country_code": "IN",
      "title": "Himachal Pradesh"
  },
  {
      "country_code": "IN",
      "title": "Jammu & Kashmir"
  },
  {
      "country_code": "IN",
      "title": "Jharkhand"
  },
  {
      "country_code": "IN",
      "title": "Karnataka"
  },
  {
      "country_code": "IN",
      "title": "Kerala"
  },
  {
      "country_code": "IN",
      "title": "Lakshadweep"
  },
  {
      "country_code": "IN",
      "title": "Madhya Pradesh"
  },
  {
      "country_code": "IN",
      "title": "Maharashtra"
  },
  {
      "country_code": "IN",
      "title": "Manipur"
  },
  {
      "country_code": "IN",
      "title": "Meghalaya"
  },
  {
      "country_code": "IN",
      "title": "Mizoram"
  },
  {
      "country_code": "IN",
      "title": "Nagaland"
  },
  {
      "country_code": "IN",
      "title": "Odisha"
  },
  {
      "country_code": "IN",
      "title": "Pondicherry"
  },
  {
      "country_code": "IN",
      "title": "Punjab"
  },
  {
      "country_code": "IN",
      "title": "Rajasthan"
  },
  {
      "country_code": "IN",
      "title": "Sikkim"
  },
  {
      "country_code": "IN",
      "title": "Tamil Nadu"
  },
  {
      "country_code": "IN",
      "title": "Telangana"
  },
  {
      "country_code": "IN",
      "title": "Tripura"
  },
  {
      "country_code": "IN",
      "title": "Uttar Pradesh"
  },
  {
      "country_code": "IN",
      "title": "Uttarakhand"
  },
  {
      "country_code": "IN",
      "title": "West Bengal"
  },
  {
      "country_code": "IT",
      "title": "Abruzzi"
  },
  {
      "country_code": "IT",
      "title": "Basilicata"
  },
  {
      "country_code": "IT",
      "title": "Calabria"
  },
  {
      "country_code": "IT",
      "title": "Campania"
  },
  {
      "country_code": "IT",
      "title": "Emilia-Romagna"
  },
  {
      "country_code": "IT",
      "title": "Friuli-Venezia Giulia"
  },
  {
      "country_code": "IT",
      "title": "Lazio"
  },
  {
      "country_code": "IT",
      "title": "Liguria"
  },
  {
      "country_code": "IT",
      "title": "Lombardia"
  },
  {
      "country_code": "IT",
      "title": "Marche"
  },
  {
      "country_code": "IT",
      "title": "Molise"
  },
  {
      "country_code": "IT",
      "title": "Piemonte"
  },
  {
      "country_code": "IT",
      "title": "Puglia"
  },
  {
      "country_code": "IT",
      "title": "Sardegna"
  },
  {
      "country_code": "IT",
      "title": "Sicilia"
  },
  {
      "country_code": "IT",
      "title": "Toscana"
  },
  {
      "country_code": "IT",
      "title": "Trentino-Alto Adige"
  },
  {
      "country_code": "IT",
      "title": "Umbria"
  },
  {
      "country_code": "IT",
      "title": "Valle D'Aosta"
  },
  {
      "country_code": "IT",
      "title": "Veneto"
  },
  {
      "country_code": "JP",
      "title": "Aichi Ken"
  },
  {
      "country_code": "JP",
      "title": "Akita Ken"
  },
  {
      "country_code": "JP",
      "title": "Aomori Ken"
  },
  {
      "country_code": "JP",
      "title": "Chiba Ken"
  },
  {
      "country_code": "JP",
      "title": "Ehime Ken"
  },
  {
      "country_code": "JP",
      "title": "Fukui Ken"
  },
  {
      "country_code": "JP",
      "title": "Fukuoka Ken"
  },
  {
      "country_code": "JP",
      "title": "Fukushima Ken"
  },
  {
      "country_code": "JP",
      "title": "Gifu Ken"
  },
  {
      "country_code": "JP",
      "title": "Gumma Ken"
  },
  {
      "country_code": "JP",
      "title": "Hiroshima Ken"
  },
  {
      "country_code": "JP",
      "title": "Hokkaido"
  },
  {
      "country_code": "JP",
      "title": "Hyogo Ken"
  },
  {
      "country_code": "JP",
      "title": "Ibaraki Ken"
  },
  {
      "country_code": "JP",
      "title": "Ishikawa Ken"
  },
  {
      "country_code": "JP",
      "title": "Iwate Ken"
  },
  {
      "country_code": "JP",
      "title": "Kagawa Ken"
  },
  {
      "country_code": "JP",
      "title": "Kagoshima Ken"
  },
  {
      "country_code": "JP",
      "title": "Kanagawa Ken"
  },
  {
      "country_code": "JP",
      "title": "Kochi Ken"
  },
  {
      "country_code": "JP",
      "title": "Kumamoto Ken"
  },
  {
      "country_code": "JP",
      "title": "Kyoto Fu"
  },
  {
      "country_code": "JP",
      "title": "Mie Ken"
  },
  {
      "country_code": "JP",
      "title": "Miyagi Ken"
  },
  {
      "country_code": "JP",
      "title": "Miyazaki Ken"
  },
  {
      "country_code": "JP",
      "title": "Nagano Ken"
  },
  {
      "country_code": "JP",
      "title": "Nagasaki Ken"
  },
  {
      "country_code": "JP",
      "title": "Nara Ken"
  },
  {
      "country_code": "JP",
      "title": "Niigata Ken"
  },
  {
      "country_code": "JP",
      "title": "Oita Ken"
  },
  {
      "country_code": "JP",
      "title": "Okayama Ken"
  },
  {
      "country_code": "JP",
      "title": "Okinawa Ken"
  },
  {
      "country_code": "JP",
      "title": "Osaka Fu"
  },
  {
      "country_code": "JP",
      "title": "Saga Ken"
  },
  {
      "country_code": "JP",
      "title": "Saitama Ken"
  },
  {
      "country_code": "JP",
      "title": "Shiga Ken"
  },
  {
      "country_code": "JP",
      "title": "Shimane Ken"
  },
  {
      "country_code": "JP",
      "title": "Shizuoka Ken"
  },
  {
      "country_code": "JP",
      "title": "Tochigi Ken"
  },
  {
      "country_code": "JP",
      "title": "Tokushima Ken"
  },
  {
      "country_code": "JP",
      "title": "Tokyo To"
  },
  {
      "country_code": "JP",
      "title": "Tottori Ken"
  },
  {
      "country_code": "JP",
      "title": "Toyama Ken"
  },
  {
      "country_code": "JP",
      "title": "Wakayama Ken"
  },
  {
      "country_code": "JP",
      "title": "Yamagata Ken"
  },
  {
      "country_code": "JP",
      "title": "Yamaguchi Ken"
  },
  {
      "country_code": "JP",
      "title": "Yamanashi Ken"
  },
  {
      "country_code": "KR",
      "title": "광주광역시"
  },
  {
      "country_code": "KR",
      "title": "강원도"
  },
  {
      "country_code": "KR",
      "title": "부산광역시"
  },
  {
      "country_code": "KR",
      "title": "경기도"
  },
  {
      "country_code": "KR",
      "title": "경상남도"
  },
  {
      "country_code": "KR",
      "title": "경상북도"
  },
  {
      "country_code": "KR",
      "title": "대구광역시"
  },
  {
      "country_code": "KR",
      "title": "대전광역시"
  },
  {
      "country_code": "KR",
      "title": "전라남도"
  },
  {
      "country_code": "KR",
      "title": "전라북도"
  },
  {
      "country_code": "KR",
      "title": "제주특별자치도"
  },
  {
      "country_code": "KR",
      "title": "인천광역시"
  },
  {
      "country_code": "KR",
      "title": "세종특별자치시"
  },
  {
      "country_code": "KR",
      "title": "서울특별시"
  },
  {
      "country_code": "KR",
      "title": "충청남도"
  },
  {
      "country_code": "KR",
      "title": "충청북도"
  },
  {
      "country_code": "KR",
      "title": "울산광역시"
  },
  {
      "country_code": "LI",
      "title": "Eschen"
  },
  {
      "country_code": "LI",
      "title": "Mauren"
  },
  {
      "country_code": "LK",
      "title": "Central Province"
  },
  {
      "country_code": "LK",
      "title": "Eastern Province"
  },
  {
      "country_code": "LK",
      "title": "North Central Province"
  },
  {
      "country_code": "LK",
      "title": "North Western Province"
  },
  {
      "country_code": "LK",
      "title": "Northern Province"
  },
  {
      "country_code": "LK",
      "title": "Province of Uva"
  },
  {
      "country_code": "LK",
      "title": "Sabaragamuwa Province"
  },
  {
      "country_code": "LK",
      "title": "Southern Province"
  },
  {
      "country_code": "LK",
      "title": "Western Province"
  },
  {
      "country_code": "LT",
      "title": "Šiauliai County"
  },
  {
      "country_code": "LT",
      "title": "Alytus County"
  },
  {
      "country_code": "LT",
      "title": "Kaunas County"
  },
  {
      "country_code": "LT",
      "title": "Klaipėda County"
  },
  {
      "country_code": "LT",
      "title": "Marijampolė County"
  },
  {
      "country_code": "LT",
      "title": "Panevėžys"
  },
  {
      "country_code": "LT",
      "title": "Tauragė County"
  },
  {
      "country_code": "LT",
      "title": "Telšių apskritis"
  },
  {
      "country_code": "LT",
      "title": "Utena County"
  },
  {
      "country_code": "LT",
      "title": "Vilniaus apskritis"
  },
  {
      "country_code": "LU",
      "title": "Capellen"
  },
  {
      "country_code": "LU",
      "title": "Clervaux"
  },
  {
      "country_code": "LU",
      "title": "Diekirch"
  },
  {
      "country_code": "LU",
      "title": "Echternach"
  },
  {
      "country_code": "LU",
      "title": "Esch-sur-Alzette"
  },
  {
      "country_code": "LU",
      "title": "Grevenmacher"
  },
  {
      "country_code": "LU",
      "title": "Luxembourg"
  },
  {
      "country_code": "LU",
      "title": "Mersch"
  },
  {
      "country_code": "LU",
      "title": "Redange"
  },
  {
      "country_code": "LU",
      "title": "Remich"
  },
  {
      "country_code": "LU",
      "title": "Vianden"
  },
  {
      "country_code": "LU",
      "title": "Wiltz"
  },
  {
      "country_code": "LV",
      "title": "Ērgļu nov."
  },
  {
      "country_code": "LV",
      "title": "Ķeguma nov."
  },
  {
      "country_code": "LV",
      "title": "Ķekavas nov."
  },
  {
      "country_code": "LV",
      "title": "Ādažu nov."
  },
  {
      "country_code": "LV",
      "title": "Aglonas nov."
  },
  {
      "country_code": "LV",
      "title": "Aizkraukles nov."
  },
  {
      "country_code": "LV",
      "title": "Aizputes nov."
  },
  {
      "country_code": "LV",
      "title": "Aknīstes nov."
  },
  {
      "country_code": "LV",
      "title": "Alūksnes nov."
  },
  {
      "country_code": "LV",
      "title": "Alojas nov."
  },
  {
      "country_code": "LV",
      "title": "Alsungas nov."
  },
  {
      "country_code": "LV",
      "title": "Amatas nov."
  },
  {
      "country_code": "LV",
      "title": "Apes nov."
  },
  {
      "country_code": "LV",
      "title": "Auces nov."
  },
  {
      "country_code": "LV",
      "title": "Babītes nov."
  },
  {
      "country_code": "LV",
      "title": "Baldones nov."
  },
  {
      "country_code": "LV",
      "title": "Baltinavas nov."
  },
  {
      "country_code": "LV",
      "title": "Balvu nov."
  },
  {
      "country_code": "LV",
      "title": "Bauskas nov."
  },
  {
      "country_code": "LV",
      "title": "Beverīnas nov."
  },
  {
      "country_code": "LV",
      "title": "Brocēnu nov."
  },
  {
      "country_code": "LV",
      "title": "Burtnieku nov."
  },
  {
      "country_code": "LV",
      "title": "Cēsu nov."
  },
  {
      "country_code": "LV",
      "title": "Carnikavas nov."
  },
  {
      "country_code": "LV",
      "title": "Cesvaines nov."
  },
  {
      "country_code": "LV",
      "title": "Ciblas nov."
  },
  {
      "country_code": "LV",
      "title": "Dagdas nov."
  },
  {
      "country_code": "LV",
      "title": "Daugavpils nov."
  },
  {
      "country_code": "LV",
      "title": "Dobeles nov."
  },
  {
      "country_code": "LV",
      "title": "Dundagas nov."
  },
  {
      "country_code": "LV",
      "title": "Durbes nov."
  },
  {
      "country_code": "LV",
      "title": "Engures nov."
  },
  {
      "country_code": "LV",
      "title": "Garkalnes nov."
  },
  {
      "country_code": "LV",
      "title": "Grobiņas nov."
  },
  {
      "country_code": "LV",
      "title": "Gulbenes nov."
  },
  {
      "country_code": "LV",
      "title": "Iecavas nov."
  },
  {
      "country_code": "LV",
      "title": "Ikšķiles nov."
  },
  {
      "country_code": "LV",
      "title": "Ilūkstes nov."
  },
  {
      "country_code": "LV",
      "title": "Inčukalna nov."
  },
  {
      "country_code": "LV",
      "title": "Jēkabpils nov."
  },
  {
      "country_code": "LV",
      "title": "Jaunjelgavas nov."
  },
  {
      "country_code": "LV",
      "title": "Jaunpiebalgas nov."
  },
  {
      "country_code": "LV",
      "title": "Jaunpils nov."
  },
  {
      "country_code": "LV",
      "title": "Jelgavas nov."
  },
  {
      "country_code": "LV",
      "title": "Kandavas nov."
  },
  {
      "country_code": "LV",
      "title": "Kārsavas nov."
  },
  {
      "country_code": "LV",
      "title": "Kocēnu nov."
  },
  {
      "country_code": "LV",
      "title": "Kokneses nov."
  },
  {
      "country_code": "LV",
      "title": "Krāslavas nov."
  },
  {
      "country_code": "LV",
      "title": "Krimuldas nov."
  },
  {
      "country_code": "LV",
      "title": "Krustpils nov."
  },
  {
      "country_code": "LV",
      "title": "Kuldīgas nov."
  },
  {
      "country_code": "LV",
      "title": "Līgatnes nov."
  },
  {
      "country_code": "LV",
      "title": "Līvānu nov."
  },
  {
      "country_code": "LV",
      "title": "Lielvārdes nov."
  },
  {
      "country_code": "LV",
      "title": "Limbažu nov."
  },
  {
      "country_code": "LV",
      "title": "Lubānas nov."
  },
  {
      "country_code": "LV",
      "title": "Ludzas nov."
  },
  {
      "country_code": "LV",
      "title": "Mērsraga nov."
  },
  {
      "country_code": "LV",
      "title": "Madonas nov."
  },
  {
      "country_code": "LV",
      "title": "Mālpils nov."
  },
  {
      "country_code": "LV",
      "title": "Mārupes nov."
  },
  {
      "country_code": "LV",
      "title": "Mazsalacas nov."
  },
  {
      "country_code": "LV",
      "title": "Nīcas nov."
  },
  {
      "country_code": "LV",
      "title": "Naukšēnu nov."
  },
  {
      "country_code": "LV",
      "title": "Neretas nov."
  },
  {
      "country_code": "LV",
      "title": "Ogres nov."
  },
  {
      "country_code": "LV",
      "title": "Olaines nov."
  },
  {
      "country_code": "LV",
      "title": "Ozolnieku nov."
  },
  {
      "country_code": "LV",
      "title": "Pļaviņu nov."
  },
  {
      "country_code": "LV",
      "title": "Pārgaujas nov."
  },
  {
      "country_code": "LV",
      "title": "Pāvilostas nov."
  },
  {
      "country_code": "LV",
      "title": "Preiļu nov."
  },
  {
      "country_code": "LV",
      "title": "Priekuļu nov."
  },
  {
      "country_code": "LV",
      "title": "Priekules nov."
  },
  {
      "country_code": "LV",
      "title": "Rēzeknes nov."
  },
  {
      "country_code": "LV",
      "title": "Rūjienas nov."
  },
  {
      "country_code": "LV",
      "title": "Raunas nov."
  },
  {
      "country_code": "LV",
      "title": "Riebiņu nov."
  },
  {
      "country_code": "LV",
      "title": "Rojas nov."
  },
  {
      "country_code": "LV",
      "title": "Ropažu nov."
  },
  {
      "country_code": "LV",
      "title": "Rucavas nov."
  },
  {
      "country_code": "LV",
      "title": "Rugāju nov."
  },
  {
      "country_code": "LV",
      "title": "Rundāles nov."
  },
  {
      "country_code": "LV",
      "title": "Sējas nov."
  },
  {
      "country_code": "LV",
      "title": "Salas nov."
  },
  {
      "country_code": "LV",
      "title": "Salaspils nov."
  },
  {
      "country_code": "LV",
      "title": "Saldus nov."
  },
  {
      "country_code": "LV",
      "title": "Saulkrastu nov."
  },
  {
      "country_code": "LV",
      "title": "Siguldas nov."
  },
  {
      "country_code": "LV",
      "title": "Skrīveru nov."
  },
  {
      "country_code": "LV",
      "title": "Skrundas nov."
  },
  {
      "country_code": "LV",
      "title": "Smiltenes nov."
  },
  {
      "country_code": "LV",
      "title": "Stopiņu nov."
  },
  {
      "country_code": "LV",
      "title": "Strenču nov."
  },
  {
      "country_code": "LV",
      "title": "Tērvetes nov."
  },
  {
      "country_code": "LV",
      "title": "Talsu nov."
  },
  {
      "country_code": "LV",
      "title": "Tukuma nov."
  },
  {
      "country_code": "LV",
      "title": "Vaiņodes nov."
  },
  {
      "country_code": "LV",
      "title": "Valkas nov."
  },
  {
      "country_code": "LV",
      "title": "Varakļānu nov."
  },
  {
      "country_code": "LV",
      "title": "Vārkavas nov."
  },
  {
      "country_code": "LV",
      "title": "Vecpiebalgas nov."
  },
  {
      "country_code": "LV",
      "title": "Vecumnieku nov."
  },
  {
      "country_code": "LV",
      "title": "Ventspils nov."
  },
  {
      "country_code": "LV",
      "title": "Viļakas nov."
  },
  {
      "country_code": "LV",
      "title": "Viļānu nov."
  },
  {
      "country_code": "LV",
      "title": "Viesītes nov."
  },
  {
      "country_code": "LV",
      "title": "Zilupes nov."
  },
  {
      "country_code": "MC",
      "title": "Monaco"
  },
  {
      "country_code": "MD",
      "title": "Anenii Noi"
  },
  {
      "country_code": "MD",
      "title": "Bender Tr."
  },
  {
      "country_code": "MD",
      "title": "Briceni"
  },
  {
      "country_code": "MD",
      "title": "Cahul"
  },
  {
      "country_code": "MD",
      "title": "Calarasi"
  },
  {
      "country_code": "MD",
      "title": "Camenca Tr."
  },
  {
      "country_code": "MD",
      "title": "Cantemir"
  },
  {
      "country_code": "MD",
      "title": "Causeni"
  },
  {
      "country_code": "MD",
      "title": "Cimislia"
  },
  {
      "country_code": "MD",
      "title": "Comrat"
  },
  {
      "country_code": "MD",
      "title": "Criuleni"
  },
  {
      "country_code": "MD",
      "title": "Criuleni- Dub."
  },
  {
      "country_code": "MD",
      "title": "Criuleni-Dub."
  },
  {
      "country_code": "MD",
      "title": "Donduseni"
  },
  {
      "country_code": "MD",
      "title": "Drochia"
  },
  {
      "country_code": "MD",
      "title": "Dubasari Cr."
  },
  {
      "country_code": "MD",
      "title": "Dubasari Tr."
  },
  {
      "country_code": "MD",
      "title": "Edinet"
  },
  {
      "country_code": "MD",
      "title": "Falesti"
  },
  {
      "country_code": "MD",
      "title": "Floresti"
  },
  {
      "country_code": "MD",
      "title": "Glodeni"
  },
  {
      "country_code": "MD",
      "title": "Grigoriopol Tr."
  },
  {
      "country_code": "MD",
      "title": "Hincesti"
  },
  {
      "country_code": "MD",
      "title": "Ialoveni"
  },
  {
      "country_code": "MD",
      "title": "Leova"
  },
  {
      "country_code": "MD",
      "title": "Mun.Balti"
  },
  {
      "country_code": "MD",
      "title": "Mun.Chisinau"
  },
  {
      "country_code": "MD",
      "title": "Nisporeni"
  },
  {
      "country_code": "MD",
      "title": "Ocnita"
  },
  {
      "country_code": "MD",
      "title": "Orhei"
  },
  {
      "country_code": "MD",
      "title": "Rezina"
  },
  {
      "country_code": "MD",
      "title": "Ribnita Tr."
  },
  {
      "country_code": "MD",
      "title": "Riscani"
  },
  {
      "country_code": "MD",
      "title": "Singerei"
  },
  {
      "country_code": "MD",
      "title": "Slobozia Tr."
  },
  {
      "country_code": "MD",
      "title": "Soldanesti"
  },
  {
      "country_code": "MD",
      "title": "Soroca"
  },
  {
      "country_code": "MD",
      "title": "Stefan-Voda"
  },
  {
      "country_code": "MD",
      "title": "Straseni"
  },
  {
      "country_code": "MD",
      "title": "Taraclia"
  },
  {
      "country_code": "MD",
      "title": "Telenesti"
  },
  {
      "country_code": "MD",
      "title": "Tiraspol Tr."
  },
  {
      "country_code": "MD",
      "title": "Ungheni"
  },
  {
      "country_code": "MH",
      "title": "Mh"
  },
  {
      "country_code": "MQ",
      "title": "Martinique"
  },
  {
      "country_code": "MT",
      "title": "Għajnsielem"
  },
  {
      "country_code": "MT",
      "title": "Iż-Żebbuġ"
  },
  {
      "country_code": "MT",
      "title": "Il-Kalkara"
  },
  {
      "country_code": "MT",
      "title": "Il-Marsa"
  },
  {
      "country_code": "MT",
      "title": "Il-Munxar"
  },
  {
      "country_code": "MW",
      "title": "Central Region"
  },
  {
      "country_code": "MW",
      "title": "Northern Region"
  },
  {
      "country_code": "MW",
      "title": "Southern Region"
  },
  {
      "country_code": "MX",
      "title": "Aguascalientes"
  },
  {
      "country_code": "MX",
      "title": "Baja California"
  },
  {
      "country_code": "MX",
      "title": "Baja California Sur"
  },
  {
      "country_code": "MX",
      "title": "Campeche"
  },
  {
      "country_code": "MX",
      "title": "Chiapas"
  },
  {
      "country_code": "MX",
      "title": "Chihuahua"
  },
  {
      "country_code": "MX",
      "title": "Coahuila de Zaragoza"
  },
  {
      "country_code": "MX",
      "title": "Colima"
  },
  {
      "country_code": "MX",
      "title": "Distrito Federal"
  },
  {
      "country_code": "MX",
      "title": "Durango"
  },
  {
      "country_code": "MX",
      "title": "Guanajuato"
  },
  {
      "country_code": "MX",
      "title": "Guerrero"
  },
  {
      "country_code": "MX",
      "title": "Hidalgo"
  },
  {
      "country_code": "MX",
      "title": "Jalisco"
  },
  {
      "country_code": "MX",
      "title": "México"
  },
  {
      "country_code": "MX",
      "title": "Michoacán de Ocampo"
  },
  {
      "country_code": "MX",
      "title": "Morelos"
  },
  {
      "country_code": "MX",
      "title": "Nayarit"
  },
  {
      "country_code": "MX",
      "title": "Nuevo León"
  },
  {
      "country_code": "MX",
      "title": "Oaxaca"
  },
  {
      "country_code": "MX",
      "title": "Puebla"
  },
  {
      "country_code": "MX",
      "title": "Querétaro"
  },
  {
      "country_code": "MX",
      "title": "Quintana Roo"
  },
  {
      "country_code": "MX",
      "title": "San Luis Potosí"
  },
  {
      "country_code": "MX",
      "title": "Sinaloa"
  },
  {
      "country_code": "MX",
      "title": "Sonora"
  },
  {
      "country_code": "MX",
      "title": "Tabasco"
  },
  {
      "country_code": "MX",
      "title": "Tamaulipas"
  },
  {
      "country_code": "MX",
      "title": "Tlaxcala"
  },
  {
      "country_code": "MX",
      "title": "Veracruz de Ignacio de la Llave"
  },
  {
      "country_code": "MX",
      "title": "Yucatán"
  },
  {
      "country_code": "MX",
      "title": "Zacatecas"
  },
  {
      "country_code": "MY",
      "title": "Johor"
  },
  {
      "country_code": "MY",
      "title": "Kedah"
  },
  {
      "country_code": "MY",
      "title": "Kelantan"
  },
  {
      "country_code": "MY",
      "title": "Kuala Lumpur"
  },
  {
      "country_code": "MY",
      "title": "Labuan"
  },
  {
      "country_code": "MY",
      "title": "Melaka"
  },
  {
      "country_code": "MY",
      "title": "Negeri Sembilan"
  },
  {
      "country_code": "MY",
      "title": "Pahang"
  },
  {
      "country_code": "MY",
      "title": "Perak"
  },
  {
      "country_code": "MY",
      "title": "Perlis"
  },
  {
      "country_code": "MY",
      "title": "Pulau Pinang"
  },
  {
      "country_code": "MY",
      "title": "Putrajaya"
  },
  {
      "country_code": "MY",
      "title": "Sabah"
  },
  {
      "country_code": "MY",
      "title": "Sarawak"
  },
  {
      "country_code": "MY",
      "title": "Selangor"
  },
  {
      "country_code": "MY",
      "title": "Terengganu"
  },
  {
      "country_code": "NC",
      "title": "Îles Loyauté"
  },
  {
      "country_code": "NC",
      "title": "Province Nord"
  },
  {
      "country_code": "NC",
      "title": "Province Sud"
  },
  {
      "country_code": "NL",
      "title": "Drenthe"
  },
  {
      "country_code": "NL",
      "title": "Flevoland"
  },
  {
      "country_code": "NL",
      "title": "Friesland"
  },
  {
      "country_code": "NL",
      "title": "Gelderland"
  },
  {
      "country_code": "NL",
      "title": "Groningen"
  },
  {
      "country_code": "NL",
      "title": "Limburg"
  },
  {
      "country_code": "NL",
      "title": "Noord-Brabant"
  },
  {
      "country_code": "NL",
      "title": "Noord-Holland"
  },
  {
      "country_code": "NL",
      "title": "Overijssel"
  },
  {
      "country_code": "NL",
      "title": "Utrecht"
  },
  {
      "country_code": "NL",
      "title": "Zeeland"
  },
  {
      "country_code": "NL",
      "title": "Zuid-Holland"
  },
  {
      "country_code": "NO",
      "title": "Agder"
  },
  {
      "country_code": "NO",
      "title": "Innlandet"
  },
  {
      "country_code": "NO",
      "title": "Møre og Romsdal"
  },
  {
      "country_code": "NO",
      "title": "Nordland"
  },
  {
      "country_code": "NO",
      "title": "Oslo County"
  },
  {
      "country_code": "NO",
      "title": "Rogaland"
  },
  {
      "country_code": "NO",
      "title": "Trøndelag"
  },
  {
      "country_code": "NO",
      "title": "Troms og Finnmark"
  },
  {
      "country_code": "NO",
      "title": "Vestfold og Telemark"
  },
  {
      "country_code": "NO",
      "title": "Vestland"
  },
  {
      "country_code": "NO",
      "title": "Viken"
  },
  {
      "country_code": "NZ",
      "title": "Auckland"
  },
  {
      "country_code": "NZ",
      "title": "Bay of Plenty"
  },
  {
      "country_code": "NZ",
      "title": "Canterbury"
  },
  {
      "country_code": "NZ",
      "title": "Gisborne"
  },
  {
      "country_code": "NZ",
      "title": "Hawke's Bay"
  },
  {
      "country_code": "NZ",
      "title": "Manawatu-Wanganui"
  },
  {
      "country_code": "NZ",
      "title": "Marlborough"
  },
  {
      "country_code": "NZ",
      "title": "Nelson"
  },
  {
      "country_code": "NZ",
      "title": "Northland"
  },
  {
      "country_code": "NZ",
      "title": "Otago"
  },
  {
      "country_code": "NZ",
      "title": "Southland"
  },
  {
      "country_code": "NZ",
      "title": "Taranaki"
  },
  {
      "country_code": "NZ",
      "title": "Tasman"
  },
  {
      "country_code": "NZ",
      "title": "Waikato"
  },
  {
      "country_code": "NZ",
      "title": "Wellington"
  },
  {
      "country_code": "NZ",
      "title": "West Coast"
  },
  {
      "country_code": "PK",
      "title": "Balochisan"
  },
  {
      "country_code": "PK",
      "title": "Federal Capial &AJK"
  },
  {
      "country_code": "PK",
      "title": "Hyderabad"
  },
  {
      "country_code": "PK",
      "title": "Lahore"
  },
  {
      "country_code": "PK",
      "title": "Norhern Punajb Rawalpindi"
  },
  {
      "country_code": "PK",
      "title": "NWFP Peshawar"
  },
  {
      "country_code": "PK",
      "title": "Souhern Punajb Mulan"
  },
  {
      "country_code": "PL",
      "title": "Łódź Voivodeship"
  },
  {
      "country_code": "PL",
      "title": "Świętokrzyskie"
  },
  {
      "country_code": "PL",
      "title": "Greater Poland"
  },
  {
      "country_code": "PL",
      "title": "Kujawsko-Pomorskie"
  },
  {
      "country_code": "PL",
      "title": "Lesser Poland"
  },
  {
      "country_code": "PL",
      "title": "Lower Silesia"
  },
  {
      "country_code": "PL",
      "title": "Lublin"
  },
  {
      "country_code": "PL",
      "title": "Lubusz"
  },
  {
      "country_code": "PL",
      "title": "Mazovia"
  },
  {
      "country_code": "PL",
      "title": "Opole Voivodeship"
  },
  {
      "country_code": "PL",
      "title": "Podlasie"
  },
  {
      "country_code": "PL",
      "title": "Pomerania"
  },
  {
      "country_code": "PL",
      "title": "Silesia"
  },
  {
      "country_code": "PL",
      "title": "Subcarpathia"
  },
  {
      "country_code": "PL",
      "title": "Warmia-Masuria"
  },
  {
      "country_code": "PL",
      "title": "West Pomerania"
  },
  {
      "country_code": "PR",
      "title": "Aguadilla"
  },
  {
      "country_code": "PR",
      "title": "Aibonito"
  },
  {
      "country_code": "PR",
      "title": "Arecibo"
  },
  {
      "country_code": "PR",
      "title": "Bayamon"
  },
  {
      "country_code": "PR",
      "title": "Cabo Rojo"
  },
  {
      "country_code": "PR",
      "title": "Caguas"
  },
  {
      "country_code": "PR",
      "title": "Carolina"
  },
  {
      "country_code": "PR",
      "title": "Catano"
  },
  {
      "country_code": "PR",
      "title": "Cayey"
  },
  {
      "country_code": "PR",
      "title": "Ceiba"
  },
  {
      "country_code": "PR",
      "title": "Fajardo"
  },
  {
      "country_code": "PR",
      "title": "Guanica"
  },
  {
      "country_code": "PR",
      "title": "Guayama"
  },
  {
      "country_code": "PR",
      "title": "Guayanilla"
  },
  {
      "country_code": "PR",
      "title": "Guaynabo"
  },
  {
      "country_code": "PR",
      "title": "Humacao"
  },
  {
      "country_code": "PR",
      "title": "Lares"
  },
  {
      "country_code": "PR",
      "title": "Mayaguez"
  },
  {
      "country_code": "PR",
      "title": "Naguabo"
  },
  {
      "country_code": "PR",
      "title": "Ponce"
  },
  {
      "country_code": "PR",
      "title": "Rio Grande"
  },
  {
      "country_code": "PR",
      "title": "San German"
  },
  {
      "country_code": "PR",
      "title": "San Juan"
  },
  {
      "country_code": "PR",
      "title": "Toa Alta"
  },
  {
      "country_code": "PR",
      "title": "Toa Baja"
  },
  {
      "country_code": "PR",
      "title": "Trujillo Alto"
  },
  {
      "country_code": "PR",
      "title": "Utuado"
  },
  {
      "country_code": "PR",
      "title": "Vega Baja"
  },
  {
      "country_code": "PT",
      "title": "Évora"
  },
  {
      "country_code": "PT",
      "title": "Aveiro"
  },
  {
      "country_code": "PT",
      "title": "Azores"
  },
  {
      "country_code": "PT",
      "title": "Beja"
  },
  {
      "country_code": "PT",
      "title": "Braga"
  },
  {
      "country_code": "PT",
      "title": "Bragança"
  },
  {
      "country_code": "PT",
      "title": "Castelo Branco"
  },
  {
      "country_code": "PT",
      "title": "Coimbra"
  },
  {
      "country_code": "PT",
      "title": "Faro"
  },
  {
      "country_code": "PT",
      "title": "Guarda"
  },
  {
      "country_code": "PT",
      "title": "Leiria"
  },
  {
      "country_code": "PT",
      "title": "Lisboa"
  },
  {
      "country_code": "PT",
      "title": "Madeira"
  },
  {
      "country_code": "PT",
      "title": "Portalegre"
  },
  {
      "country_code": "PT",
      "title": "Porto"
  },
  {
      "country_code": "PT",
      "title": "Santarém"
  },
  {
      "country_code": "PT",
      "title": "Setúbal"
  },
  {
      "country_code": "PT",
      "title": "Viana do Castelo"
  },
  {
      "country_code": "PT",
      "title": "Vila Real"
  },
  {
      "country_code": "PT",
      "title": "Viseu"
  },
  {
      "country_code": "RE",
      "title": "Réunion"
  },
  {
      "country_code": "RE",
      "title": "Reunion (general)"
  },
  {
      "country_code": "RO",
      "title": "Alba"
  },
  {
      "country_code": "RO",
      "title": "Arad"
  },
  {
      "country_code": "RO",
      "title": "Argeş"
  },
  {
      "country_code": "RO",
      "title": "Bacău"
  },
  {
      "country_code": "RO",
      "title": "Bihor"
  },
  {
      "country_code": "RO",
      "title": "Bistriţa-Năsăud"
  },
  {
      "country_code": "RO",
      "title": "Botoşani"
  },
  {
      "country_code": "RO",
      "title": "Braşov"
  },
  {
      "country_code": "RO",
      "title": "Brăila"
  },
  {
      "country_code": "RO",
      "title": "Bucureşti"
  },
  {
      "country_code": "RO",
      "title": "Buzău"
  },
  {
      "country_code": "RO",
      "title": "Călăraşi"
  },
  {
      "country_code": "RO",
      "title": "Caraş-Severin"
  },
  {
      "country_code": "RO",
      "title": "Cluj"
  },
  {
      "country_code": "RO",
      "title": "Constanţa"
  },
  {
      "country_code": "RO",
      "title": "Covasna"
  },
  {
      "country_code": "RO",
      "title": "Dâmboviţa"
  },
  {
      "country_code": "RO",
      "title": "Dolj"
  },
  {
      "country_code": "RO",
      "title": "Galaţi"
  },
  {
      "country_code": "RO",
      "title": "Giurgiu"
  },
  {
      "country_code": "RO",
      "title": "Gorj"
  },
  {
      "country_code": "RO",
      "title": "Harghita"
  },
  {
      "country_code": "RO",
      "title": "Hunedoara"
  },
  {
      "country_code": "RO",
      "title": "Iaşi"
  },
  {
      "country_code": "RO",
      "title": "Ialomiţa"
  },
  {
      "country_code": "RO",
      "title": "Ilfov"
  },
  {
      "country_code": "RO",
      "title": "Maramureş"
  },
  {
      "country_code": "RO",
      "title": "Mehedinţi"
  },
  {
      "country_code": "RO",
      "title": "Mureş"
  },
  {
      "country_code": "RO",
      "title": "Neamţ"
  },
  {
      "country_code": "RO",
      "title": "Olt"
  },
  {
      "country_code": "RO",
      "title": "Prahova"
  },
  {
      "country_code": "RO",
      "title": "Sălaj"
  },
  {
      "country_code": "RO",
      "title": "Satu Mare"
  },
  {
      "country_code": "RO",
      "title": "Sibiu"
  },
  {
      "country_code": "RO",
      "title": "Suceava"
  },
  {
      "country_code": "RO",
      "title": "Teleorman"
  },
  {
      "country_code": "RO",
      "title": "Timiş"
  },
  {
      "country_code": "RO",
      "title": "Tulcea"
  },
  {
      "country_code": "RO",
      "title": "Vâlcea"
  },
  {
      "country_code": "RO",
      "title": "Vaslui"
  },
  {
      "country_code": "RO",
      "title": "Vrancea"
  },
  {
      "country_code": "RU",
      "title": "Ростовская Область"
  },
  {
      "country_code": "RU",
      "title": "Рязанская Область"
  },
  {
      "country_code": "RU",
      "title": "Самарская Область"
  },
  {
      "country_code": "RU",
      "title": "Санкт-Петербург"
  },
  {
      "country_code": "RU",
      "title": "Саха (Якутия) Республика"
  },
  {
      "country_code": "RU",
      "title": "Сахалинская Область"
  },
  {
      "country_code": "RU",
      "title": "Саратовская Область"
  },
  {
      "country_code": "RU",
      "title": "Северная Осетия-Алания Республика"
  },
  {
      "country_code": "RU",
      "title": "Смоленская Область"
  },
  {
      "country_code": "RU",
      "title": "Свердловская Область"
  },
  {
      "country_code": "RU",
      "title": "Ставропольский Край"
  },
  {
      "country_code": "RU",
      "title": "Ярославская Область"
  },
  {
      "country_code": "RU",
      "title": "Ингушетия Республика"
  },
  {
      "country_code": "RU",
      "title": "Ивановская Область"
  },
  {
      "country_code": "RU",
      "title": "Иркутская Область"
  },
  {
      "country_code": "RU",
      "title": "Байконур"
  },
  {
      "country_code": "RU",
      "title": "Башкортостан Республика"
  },
  {
      "country_code": "RU",
      "title": "Белгородская Область"
  },
  {
      "country_code": "RU",
      "title": "Брянская Область"
  },
  {
      "country_code": "RU",
      "title": "Бурятия Республика"
  },
  {
      "country_code": "RU",
      "title": "Владимирская Область"
  },
  {
      "country_code": "RU",
      "title": "Вологодская Область"
  },
  {
      "country_code": "RU",
      "title": "Волгоградская Область"
  },
  {
      "country_code": "RU",
      "title": "Воронежская Область"
  },
  {
      "country_code": "RU",
      "title": "Дагестан Республика"
  },
  {
      "country_code": "RU",
      "title": "Липецкая Область"
  },
  {
      "country_code": "RU",
      "title": "Ленинградская Область"
  },
  {
      "country_code": "RU",
      "title": "Тамбовская Область"
  },
  {
      "country_code": "RU",
      "title": "Татарстан Республика"
  },
  {
      "country_code": "RU",
      "title": "Томская Область"
  },
  {
      "country_code": "RU",
      "title": "Тверская Область"
  },
  {
      "country_code": "RU",
      "title": "Тыва Республика"
  },
  {
      "country_code": "RU",
      "title": "Тульская Область"
  },
  {
      "country_code": "RU",
      "title": "Тюменская Область"
  },
  {
      "country_code": "RU",
      "title": "Удмуртская Республика"
  },
  {
      "country_code": "RU",
      "title": "Ульяновская Область"
  },
  {
      "country_code": "RU",
      "title": "Хабаровский Край"
  },
  {
      "country_code": "RU",
      "title": "Хакасия Республика"
  },
  {
      "country_code": "RU",
      "title": "Читинская Область"
  },
  {
      "country_code": "RU",
      "title": "Челябинская Область"
  },
  {
      "country_code": "RU",
      "title": "Чеченская Республика"
  },
  {
      "country_code": "RU",
      "title": "Чувашская Республика"
  },
  {
      "country_code": "RU",
      "title": "Адыгея Республика"
  },
  {
      "country_code": "RU",
      "title": "Нижегородская Область"
  },
  {
      "country_code": "RU",
      "title": "Алтай Республика"
  },
  {
      "country_code": "RU",
      "title": "Алтайский Край"
  },
  {
      "country_code": "RU",
      "title": "Амурская Область"
  },
  {
      "country_code": "RU",
      "title": "Новосибирская Область"
  },
  {
      "country_code": "RU",
      "title": "Новгородская Область"
  },
  {
      "country_code": "RU",
      "title": "Архангельская Область"
  },
  {
      "country_code": "RU",
      "title": "Астраханская Область"
  },
  {
      "country_code": "RU",
      "title": "Магаданская Область"
  },
  {
      "country_code": "RU",
      "title": "Марий Эл Республика"
  },
  {
      "country_code": "RU",
      "title": "Мордовия Республика"
  },
  {
      "country_code": "RU",
      "title": "Московская Область"
  },
  {
      "country_code": "RU",
      "title": "Москва"
  },
  {
      "country_code": "RU",
      "title": "Мурманская Область"
  },
  {
      "country_code": "RU",
      "title": "Кировская Область"
  },
  {
      "country_code": "RU",
      "title": "Кабардино-Балкарская Республика"
  },
  {
      "country_code": "RU",
      "title": "Калининградская Область"
  },
  {
      "country_code": "RU",
      "title": "Калмыкия Республика"
  },
  {
      "country_code": "RU",
      "title": "Калужская Область"
  },
  {
      "country_code": "RU",
      "title": "Камчатская Область"
  },
  {
      "country_code": "RU",
      "title": "Карачаево-Черкесская Республика"
  },
  {
      "country_code": "RU",
      "title": "Карелия Республика"
  },
  {
      "country_code": "RU",
      "title": "Кемеровская Область"
  },
  {
      "country_code": "RU",
      "title": "Коми Республика"
  },
  {
      "country_code": "RU",
      "title": "Костромская Область"
  },
  {
      "country_code": "RU",
      "title": "Краснодарский Край"
  },
  {
      "country_code": "RU",
      "title": "Красноярский Край"
  },
  {
      "country_code": "RU",
      "title": "Курганская Область"
  },
  {
      "country_code": "RU",
      "title": "Курская Область"
  },
  {
      "country_code": "RU",
      "title": "Пензенская Область"
  },
  {
      "country_code": "RU",
      "title": "Пермский Край"
  },
  {
      "country_code": "RU",
      "title": "Приморский Край"
  },
  {
      "country_code": "RU",
      "title": "Псковская Область"
  },
  {
      "country_code": "RU",
      "title": "Омская Область"
  },
  {
      "country_code": "RU",
      "title": "Орловская Область"
  },
  {
      "country_code": "RU",
      "title": "Оренбургская Область"
  },
  {
      "country_code": "SE",
      "title": "Örebro"
  },
  {
      "country_code": "SE",
      "title": "Östergötland"
  },
  {
      "country_code": "SE",
      "title": "Blekinge"
  },
  {
      "country_code": "SE",
      "title": "Dalarna"
  },
  {
      "country_code": "SE",
      "title": "Gävleborg"
  },
  {
      "country_code": "SE",
      "title": "Gotland"
  },
  {
      "country_code": "SE",
      "title": "Halland"
  },
  {
      "country_code": "SE",
      "title": "Jämtland"
  },
  {
      "country_code": "SE",
      "title": "Jönköping"
  },
  {
      "country_code": "SE",
      "title": "Kalmar"
  },
  {
      "country_code": "SE",
      "title": "Kronoberg"
  },
  {
      "country_code": "SE",
      "title": "Norrbotten"
  },
  {
      "country_code": "SE",
      "title": "Södermanland"
  },
  {
      "country_code": "SE",
      "title": "Skåne"
  },
  {
      "country_code": "SE",
      "title": "Stockholm"
  },
  {
      "country_code": "SE",
      "title": "Uppsala"
  },
  {
      "country_code": "SE",
      "title": "Värmland"
  },
  {
      "country_code": "SE",
      "title": "Västerbotten"
  },
  {
      "country_code": "SE",
      "title": "Västernorrland"
  },
  {
      "country_code": "SE",
      "title": "Västmanland"
  },
  {
      "country_code": "SE",
      "title": "Västra Götaland"
  },
  {
      "country_code": "SJ",
      "title": "Svalbard"
  },
  {
      "country_code": "SK",
      "title": "Žilinský kraj"
  },
  {
      "country_code": "SK",
      "title": "Banskobystrický kraj"
  },
  {
      "country_code": "SK",
      "title": "Bratislavský kraj"
  },
  {
      "country_code": "SK",
      "title": "Košický kraj"
  },
  {
      "country_code": "SK",
      "title": "Nitriansky kraj"
  },
  {
      "country_code": "SK",
      "title": "Prešovský kraj"
  },
  {
      "country_code": "SK",
      "title": "Trenčiansky kraj"
  },
  {
      "country_code": "SK",
      "title": "Trnavský kraj"
  },
  {
      "country_code": "TH",
      "title": "Amnat Charoen"
  },
  {
      "country_code": "TH",
      "title": "Ang Thong"
  },
  {
      "country_code": "TH",
      "title": "Bangkok"
  },
  {
      "country_code": "TH",
      "title": "Buri Ram"
  },
  {
      "country_code": "TH",
      "title": "Chachoengsao"
  },
  {
      "country_code": "TH",
      "title": "Chai Nat"
  },
  {
      "country_code": "TH",
      "title": "Chaiyaphum"
  },
  {
      "country_code": "TH",
      "title": "Chanthaburi"
  },
  {
      "country_code": "TH",
      "title": "Chiang Mai"
  },
  {
      "country_code": "TH",
      "title": "Chiang Rai"
  },
  {
      "country_code": "TH",
      "title": "Chon Buri"
  },
  {
      "country_code": "TH",
      "title": "Chumphon"
  },
  {
      "country_code": "TH",
      "title": "Kalasin"
  },
  {
      "country_code": "TH",
      "title": "Kamphaeng Phet"
  },
  {
      "country_code": "TH",
      "title": "Kanchanaburi"
  },
  {
      "country_code": "TH",
      "title": "Khon Kaen"
  },
  {
      "country_code": "TH",
      "title": "Krabi"
  },
  {
      "country_code": "TH",
      "title": "Lampang"
  },
  {
      "country_code": "TH",
      "title": "Lamphun"
  },
  {
      "country_code": "TH",
      "title": "Loei"
  },
  {
      "country_code": "TH",
      "title": "Lopburi"
  },
  {
      "country_code": "TH",
      "title": "Mae Hong Son"
  },
  {
      "country_code": "TH",
      "title": "Maha Sarakham"
  },
  {
      "country_code": "TH",
      "title": "Mukdahan"
  },
  {
      "country_code": "TH",
      "title": "Nakhon Nayok"
  },
  {
      "country_code": "TH",
      "title": "Nakhon Pathom"
  },
  {
      "country_code": "TH",
      "title": "Nakhon Phanom"
  },
  {
      "country_code": "TH",
      "title": "Nakhon Ratchasima"
  },
  {
      "country_code": "TH",
      "title": "Nakhon Sawan"
  },
  {
      "country_code": "TH",
      "title": "Nakhon Si Thammarat"
  },
  {
      "country_code": "TH",
      "title": "Nan"
  },
  {
      "country_code": "TH",
      "title": "Narathiwat"
  },
  {
      "country_code": "TH",
      "title": "Nong Bua Lam Phu"
  },
  {
      "country_code": "TH",
      "title": "Nong Khai"
  },
  {
      "country_code": "TH",
      "title": "Nonthaburi"
  },
  {
      "country_code": "TH",
      "title": "Pathum Thani"
  },
  {
      "country_code": "TH",
      "title": "Pattani"
  },
  {
      "country_code": "TH",
      "title": "Phang Nga"
  },
  {
      "country_code": "TH",
      "title": "Phatthalung"
  },
  {
      "country_code": "TH",
      "title": "Phayao"
  },
  {
      "country_code": "TH",
      "title": "Phetchaburi"
  },
  {
      "country_code": "TH",
      "title": "Phichit"
  },
  {
      "country_code": "TH",
      "title": "Phitsanulok"
  },
  {
      "country_code": "TH",
      "title": "Phra Nakhon Si Ayutthaya"
  },
  {
      "country_code": "TH",
      "title": "Phrae"
  },
  {
      "country_code": "TH",
      "title": "Phuket"
  },
  {
      "country_code": "TH",
      "title": "Prachin Buri"
  },
  {
      "country_code": "TH",
      "title": "Prachuap Khiri Khan"
  },
  {
      "country_code": "TH",
      "title": "Ranong"
  },
  {
      "country_code": "TH",
      "title": "Ratchaburi"
  },
  {
      "country_code": "TH",
      "title": "Rayong"
  },
  {
      "country_code": "TH",
      "title": "Roi Et"
  },
  {
      "country_code": "TH",
      "title": "Sa Kaeo"
  },
  {
      "country_code": "TH",
      "title": "Sakon Nakhon"
  },
  {
      "country_code": "TH",
      "title": "Samut Prakan"
  },
  {
      "country_code": "TH",
      "title": "Samut Sakhon"
  },
  {
      "country_code": "TH",
      "title": "Samut Songkhram"
  },
  {
      "country_code": "TH",
      "title": "Saraburi"
  },
  {
      "country_code": "TH",
      "title": "Satun"
  },
  {
      "country_code": "TH",
      "title": "Si Sa Ket"
  },
  {
      "country_code": "TH",
      "title": "Sing Buri"
  },
  {
      "country_code": "TH",
      "title": "Songkhla"
  },
  {
      "country_code": "TH",
      "title": "Sukhothai"
  },
  {
      "country_code": "TH",
      "title": "Suphanburi"
  },
  {
      "country_code": "TH",
      "title": "Surat Thani"
  },
  {
      "country_code": "TH",
      "title": "Tak"
  },
  {
      "country_code": "TH",
      "title": "Trang"
  },
  {
      "country_code": "TH",
      "title": "Trat"
  },
  {
      "country_code": "TH",
      "title": "Ubon Ratchathani"
  },
  {
      "country_code": "TH",
      "title": "Udon Thani"
  },
  {
      "country_code": "TH",
      "title": "Uthai Thani"
  },
  {
      "country_code": "TH",
      "title": "Uttaradit"
  },
  {
      "country_code": "TH",
      "title": "Yala"
  },
  {
      "country_code": "TH",
      "title": "Yasothon"
  },
  {
      "country_code": "TR",
      "title": "İstanbul"
  },
  {
      "country_code": "TR",
      "title": "Çanakkale"
  },
  {
      "country_code": "TR",
      "title": "Çankiri"
  },
  {
      "country_code": "TR",
      "title": "Çorum"
  },
  {
      "country_code": "TR",
      "title": "Ağri"
  },
  {
      "country_code": "TR",
      "title": "Adana"
  },
  {
      "country_code": "TR",
      "title": "Adiyaman"
  },
  {
      "country_code": "TR",
      "title": "Afyonkarahisar"
  },
  {
      "country_code": "TR",
      "title": "Aksaray"
  },
  {
      "country_code": "TR",
      "title": "Amasya"
  },
  {
      "country_code": "TR",
      "title": "Ankara"
  },
  {
      "country_code": "TR",
      "title": "Antalya"
  },
  {
      "country_code": "TR",
      "title": "Ardahan"
  },
  {
      "country_code": "TR",
      "title": "Artvin"
  },
  {
      "country_code": "TR",
      "title": "Aydin"
  },
  {
      "country_code": "TR",
      "title": "Şanliurfa"
  },
  {
      "country_code": "TR",
      "title": "Şirnak"
  },
  {
      "country_code": "TR",
      "title": "Balikesir"
  },
  {
      "country_code": "TR",
      "title": "Bartin"
  },
  {
      "country_code": "TR",
      "title": "Batman"
  },
  {
      "country_code": "TR",
      "title": "Bayburt"
  },
  {
      "country_code": "TR",
      "title": "Bilecik"
  },
  {
      "country_code": "TR",
      "title": "Bingöl"
  },
  {
      "country_code": "TR",
      "title": "Bitlis"
  },
  {
      "country_code": "TR",
      "title": "Bolu"
  },
  {
      "country_code": "TR",
      "title": "Burdur"
  },
  {
      "country_code": "TR",
      "title": "Bursa"
  },
  {
      "country_code": "TR",
      "title": "Düzce"
  },
  {
      "country_code": "TR",
      "title": "Denizli"
  },
  {
      "country_code": "TR",
      "title": "Diyarbakir"
  },
  {
      "country_code": "TR",
      "title": "Edirne"
  },
  {
      "country_code": "TR",
      "title": "Elaziğ"
  },
  {
      "country_code": "TR",
      "title": "Erzincan"
  },
  {
      "country_code": "TR",
      "title": "Erzurum"
  },
  {
      "country_code": "TR",
      "title": "Eskişehir"
  },
  {
      "country_code": "TR",
      "title": "Gümüşhane"
  },
  {
      "country_code": "TR",
      "title": "Gaziantep"
  },
  {
      "country_code": "TR",
      "title": "Giresun"
  },
  {
      "country_code": "TR",
      "title": "Hakkari"
  },
  {
      "country_code": "TR",
      "title": "Hatay"
  },
  {
      "country_code": "TR",
      "title": "Iğdir"
  },
  {
      "country_code": "TR",
      "title": "Isparta"
  },
  {
      "country_code": "TR",
      "title": "Kütahya"
  },
  {
      "country_code": "TR",
      "title": "Kahramanmaraş"
  },
  {
      "country_code": "TR",
      "title": "Karabük"
  },
  {
      "country_code": "TR",
      "title": "Karaman"
  },
  {
      "country_code": "TR",
      "title": "Kars"
  },
  {
      "country_code": "TR",
      "title": "Kastamonu"
  },
  {
      "country_code": "TR",
      "title": "Kayseri"
  },
  {
      "country_code": "TR",
      "title": "Kilis"
  },
  {
      "country_code": "TR",
      "title": "Kirşehir"
  },
  {
      "country_code": "TR",
      "title": "Kirikkale"
  },
  {
      "country_code": "TR",
      "title": "Kirklareli"
  },
  {
      "country_code": "TR",
      "title": "Kktc"
  },
  {
      "country_code": "TR",
      "title": "Kocaeli"
  },
  {
      "country_code": "TR",
      "title": "Konya"
  },
  {
      "country_code": "TR",
      "title": "Malatya"
  },
  {
      "country_code": "TR",
      "title": "Manisa"
  },
  {
      "country_code": "TR",
      "title": "Mardin"
  },
  {
      "country_code": "TR",
      "title": "Mersin(İçel)"
  },
  {
      "country_code": "TR",
      "title": "Muş"
  },
  {
      "country_code": "TR",
      "title": "Muğla"
  },
  {
      "country_code": "TR",
      "title": "Nevşehir"
  },
  {
      "country_code": "TR",
      "title": "Niğde"
  },
  {
      "country_code": "TR",
      "title": "Ordu"
  },
  {
      "country_code": "TR",
      "title": "Osmaniye"
  },
  {
      "country_code": "TR",
      "title": "Rize"
  },
  {
      "country_code": "TR",
      "title": "Sakarya"
  },
  {
      "country_code": "TR",
      "title": "Samsun"
  },
  {
      "country_code": "TR",
      "title": "Siirt"
  },
  {
      "country_code": "TR",
      "title": "Sinop"
  },
  {
      "country_code": "TR",
      "title": "Sivas"
  },
  {
      "country_code": "TR",
      "title": "Tekirdağ"
  },
  {
      "country_code": "TR",
      "title": "Tokat"
  },
  {
      "country_code": "TR",
      "title": "Trabzon"
  },
  {
      "country_code": "TR",
      "title": "Tunceli"
  },
  {
      "country_code": "TR",
      "title": "Uşak"
  },
  {
      "country_code": "TR",
      "title": "Van"
  },
  {
      "country_code": "TR",
      "title": "Yalova"
  },
  {
      "country_code": "TR",
      "title": "Yozgat"
  },
  {
      "country_code": "TR",
      "title": "Zonguldak"
  },
  {
      "country_code": "UA",
      "title": "Cherkaska"
  },
  {
      "country_code": "UA",
      "title": "Chernihivska"
  },
  {
      "country_code": "UA",
      "title": "Chernivetska"
  },
  {
      "country_code": "UA",
      "title": "Dnipropetrovska"
  },
  {
      "country_code": "UA",
      "title": "Donetska"
  },
  {
      "country_code": "UA",
      "title": "Ivano-Frankivska"
  },
  {
      "country_code": "UA",
      "title": "Kharkivska"
  },
  {
      "country_code": "UA",
      "title": "Khersonska"
  },
  {
      "country_code": "UA",
      "title": "Khmelnytska"
  },
  {
      "country_code": "UA",
      "title": "Kirovohradska"
  },
  {
      "country_code": "UA",
      "title": "Kyiv"
  },
  {
      "country_code": "UA",
      "title": "Kyivska"
  },
  {
      "country_code": "UA",
      "title": "Luhanska"
  },
  {
      "country_code": "UA",
      "title": "Lvivska"
  },
  {
      "country_code": "UA",
      "title": "Mykolaivska"
  },
  {
      "country_code": "UA",
      "title": "Odeska"
  },
  {
      "country_code": "UA",
      "title": "Poltavska"
  },
  {
      "country_code": "UA",
      "title": "Rivnenska"
  },
  {
      "country_code": "UA",
      "title": "Sumska"
  },
  {
      "country_code": "UA",
      "title": "Ternopilska"
  },
  {
      "country_code": "UA",
      "title": "Vinnytska"
  },
  {
      "country_code": "UA",
      "title": "Volynska"
  },
  {
      "country_code": "UA",
      "title": "Zakarpatska"
  },
  {
      "country_code": "UA",
      "title": "Zaporizka"
  },
  {
      "country_code": "UA",
      "title": "Zhytomyrska"
  },
  {
      "country_code": "US",
      "title": "Alabama"
  },
  {
      "country_code": "US",
      "title": "Alaska"
  },
  {
      "country_code": "US",
      "title": "Arizona"
  },
  {
      "country_code": "US",
      "title": "Arkansas"
  },
  {
      "country_code": "US",
      "title": "California"
  },
  {
      "country_code": "US",
      "title": "Colorado"
  },
  {
      "country_code": "US",
      "title": "Connecticut"
  },
  {
      "country_code": "US",
      "title": "Delaware"
  },
  {
      "country_code": "US",
      "title": "District of Columbia"
  },
  {
      "country_code": "US",
      "title": "Florida"
  },
  {
      "country_code": "US",
      "title": "Georgia"
  },
  {
      "country_code": "US",
      "title": "Hawaii"
  },
  {
      "country_code": "US",
      "title": "Idaho"
  },
  {
      "country_code": "US",
      "title": "Illinois"
  },
  {
      "country_code": "US",
      "title": "Indiana"
  },
  {
      "country_code": "US",
      "title": "Iowa"
  },
  {
      "country_code": "US",
      "title": "Kansas"
  },
  {
      "country_code": "US",
      "title": "Kentucky"
  },
  {
      "country_code": "US",
      "title": "Louisiana"
  },
  {
      "country_code": "US",
      "title": "Maine"
  },
  {
      "country_code": "US",
      "title": "Maryland"
  },
  {
      "country_code": "US",
      "title": "Massachusetts"
  },
  {
      "country_code": "US",
      "title": "Michigan"
  },
  {
      "country_code": "US",
      "title": "Minnesota"
  },
  {
      "country_code": "US",
      "title": "Mississippi"
  },
  {
      "country_code": "US",
      "title": "Missouri"
  },
  {
      "country_code": "US",
      "title": "Montana"
  },
  {
      "country_code": "US",
      "title": "Nebraska"
  },
  {
      "country_code": "US",
      "title": "Nevada"
  },
  {
      "country_code": "US",
      "title": "New Hampshire"
  },
  {
      "country_code": "US",
      "title": "New Jersey"
  },
  {
      "country_code": "US",
      "title": "New Mexico"
  },
  {
      "country_code": "US",
      "title": "New York"
  },
  {
      "country_code": "US",
      "title": "North Carolina"
  },
  {
      "country_code": "US",
      "title": "North Dakota"
  },
  {
      "country_code": "US",
      "title": "Ohio"
  },
  {
      "country_code": "US",
      "title": "Oklahoma"
  },
  {
      "country_code": "US",
      "title": "Oregon"
  },
  {
      "country_code": "US",
      "title": "Pennsylvania"
  },
  {
      "country_code": "US",
      "title": "Rhode Island"
  },
  {
      "country_code": "US",
      "title": "South Carolina"
  },
  {
      "country_code": "US",
      "title": "South Dakota"
  },
  {
      "country_code": "US",
      "title": "Tennessee"
  },
  {
      "country_code": "US",
      "title": "Texas"
  },
  {
      "country_code": "US",
      "title": "Utah"
  },
  {
      "country_code": "US",
      "title": "Vermont"
  },
  {
      "country_code": "US",
      "title": "Virginia"
  },
  {
      "country_code": "US",
      "title": "Washington"
  },
  {
      "country_code": "US",
      "title": "West Virginia"
  },
  {
      "country_code": "US",
      "title": "Wisconsin"
  },
  {
      "country_code": "US",
      "title": "Wyoming"
  },
  {
      "country_code": "UY",
      "title": "Artigas"
  },
  {
      "country_code": "UY",
      "title": "Canelones"
  },
  {
      "country_code": "UY",
      "title": "Cerro Largo"
  },
  {
      "country_code": "UY",
      "title": "Colonia"
  },
  {
      "country_code": "UY",
      "title": "Durazno"
  },
  {
      "country_code": "UY",
      "title": "Flores"
  },
  {
      "country_code": "UY",
      "title": "Florida"
  },
  {
      "country_code": "UY",
      "title": "Lavalleja"
  },
  {
      "country_code": "UY",
      "title": "Maldonado"
  },
  {
      "country_code": "UY",
      "title": "Montevideo"
  },
  {
      "country_code": "UY",
      "title": "Paysandu"
  },
  {
      "country_code": "UY",
      "title": "Rio Negro"
  },
  {
      "country_code": "UY",
      "title": "Rivera"
  },
  {
      "country_code": "UY",
      "title": "Rocha"
  },
  {
      "country_code": "UY",
      "title": "Salto"
  },
  {
      "country_code": "UY",
      "title": "San Jose"
  },
  {
      "country_code": "UY",
      "title": "Soriano"
  },
  {
      "country_code": "UY",
      "title": "Tacuarembo"
  },
  {
      "country_code": "UY",
      "title": "Treinta y Tres"
  },
  {
      "country_code": "VI",
      "title": "Vi"
  },
  {
      "country_code": "YT",
      "title": "Mamoudzou"
  },
  {
      "country_code": "AD",
      "title": "South"
  },
  {
      "country_code": "AD",
      "title": "North"
  },
  {
      "country_code": "AD",
      "title": "East"
  },
  {
      "country_code": "AD",
      "title": "West"
  },
  {
      "country_code": "AD",
      "title": "Central"
  },
  {
      "country_code": "AE",
      "title": "South"
  },
  {
      "country_code": "AE",
      "title": "North"
  },
  {
      "country_code": "AE",
      "title": "East"
  },
  {
      "country_code": "AE",
      "title": "West"
  },
  {
      "country_code": "AE",
      "title": "Central"
  },
  {
      "country_code": "AF",
      "title": "South"
  },
  {
      "country_code": "AF",
      "title": "North"
  },
  {
      "country_code": "AF",
      "title": "East"
  },
  {
      "country_code": "AF",
      "title": "West"
  },
  {
      "country_code": "AF",
      "title": "Central"
  },
  {
      "country_code": "AG",
      "title": "South"
  },
  {
      "country_code": "AG",
      "title": "North"
  },
  {
      "country_code": "AG",
      "title": "East"
  },
  {
      "country_code": "AG",
      "title": "West"
  },
  {
      "country_code": "AG",
      "title": "Central"
  },
  {
      "country_code": "AI",
      "title": "South"
  },
  {
      "country_code": "AI",
      "title": "North"
  },
  {
      "country_code": "AI",
      "title": "East"
  },
  {
      "country_code": "AI",
      "title": "West"
  },
  {
      "country_code": "AI",
      "title": "Central"
  },
  {
      "country_code": "AL",
      "title": "South"
  },
  {
      "country_code": "AL",
      "title": "North"
  },
  {
      "country_code": "AL",
      "title": "East"
  },
  {
      "country_code": "AL",
      "title": "West"
  },
  {
      "country_code": "AL",
      "title": "Central"
  },
  {
      "country_code": "AM",
      "title": "South"
  },
  {
      "country_code": "AM",
      "title": "North"
  },
  {
      "country_code": "AM",
      "title": "East"
  },
  {
      "country_code": "AM",
      "title": "West"
  },
  {
      "country_code": "AM",
      "title": "Central"
  },
  {
      "country_code": "AN",
      "title": "South"
  },
  {
      "country_code": "AN",
      "title": "North"
  },
  {
      "country_code": "AN",
      "title": "East"
  },
  {
      "country_code": "AN",
      "title": "West"
  },
  {
      "country_code": "AN",
      "title": "Central"
  },
  {
      "country_code": "AO",
      "title": "South"
  },
  {
      "country_code": "AO",
      "title": "North"
  },
  {
      "country_code": "AO",
      "title": "East"
  },
  {
      "country_code": "AO",
      "title": "West"
  },
  {
      "country_code": "AO",
      "title": "Central"
  },
  {
      "country_code": "AS",
      "title": "South"
  },
  {
      "country_code": "AS",
      "title": "North"
  },
  {
      "country_code": "AS",
      "title": "East"
  },
  {
      "country_code": "AS",
      "title": "West"
  },
  {
      "country_code": "AS",
      "title": "Central"
  },
  {
      "country_code": "AW",
      "title": "South"
  },
  {
      "country_code": "AW",
      "title": "North"
  },
  {
      "country_code": "AW",
      "title": "East"
  },
  {
      "country_code": "AW",
      "title": "West"
  },
  {
      "country_code": "AW",
      "title": "Central"
  },
  {
      "country_code": "BA",
      "title": "South"
  },
  {
      "country_code": "BA",
      "title": "North"
  },
  {
      "country_code": "BA",
      "title": "East"
  },
  {
      "country_code": "BA",
      "title": "West"
  },
  {
      "country_code": "BA",
      "title": "Central"
  },
  {
      "country_code": "BB",
      "title": "South"
  },
  {
      "country_code": "BB",
      "title": "North"
  },
  {
      "country_code": "BB",
      "title": "East"
  },
  {
      "country_code": "BB",
      "title": "West"
  },
  {
      "country_code": "BB",
      "title": "Central"
  },
  {
      "country_code": "BF",
      "title": "South"
  },
  {
      "country_code": "BF",
      "title": "North"
  },
  {
      "country_code": "BF",
      "title": "East"
  },
  {
      "country_code": "BF",
      "title": "West"
  },
  {
      "country_code": "BF",
      "title": "Central"
  },
  {
      "country_code": "BH",
      "title": "South"
  },
  {
      "country_code": "BH",
      "title": "North"
  },
  {
      "country_code": "BH",
      "title": "East"
  },
  {
      "country_code": "BH",
      "title": "West"
  },
  {
      "country_code": "BH",
      "title": "Central"
  },
  {
      "country_code": "BI",
      "title": "South"
  },
  {
      "country_code": "BI",
      "title": "North"
  },
  {
      "country_code": "BI",
      "title": "East"
  },
  {
      "country_code": "BI",
      "title": "West"
  },
  {
      "country_code": "BI",
      "title": "Central"
  },
  {
      "country_code": "BJ",
      "title": "South"
  },
  {
      "country_code": "BJ",
      "title": "North"
  },
  {
      "country_code": "BJ",
      "title": "East"
  },
  {
      "country_code": "BJ",
      "title": "West"
  },
  {
      "country_code": "BJ",
      "title": "Central"
  },
  {
      "country_code": "BN",
      "title": "South"
  },
  {
      "country_code": "BN",
      "title": "North"
  },
  {
      "country_code": "BN",
      "title": "East"
  },
  {
      "country_code": "BN",
      "title": "West"
  },
  {
      "country_code": "BN",
      "title": "Central"
  },
  {
      "country_code": "BO",
      "title": "South"
  },
  {
      "country_code": "BO",
      "title": "North"
  },
  {
      "country_code": "BO",
      "title": "East"
  },
  {
      "country_code": "BO",
      "title": "West"
  },
  {
      "country_code": "BO",
      "title": "Central"
  },
  {
      "country_code": "BS",
      "title": "South"
  },
  {
      "country_code": "BS",
      "title": "North"
  },
  {
      "country_code": "BS",
      "title": "East"
  },
  {
      "country_code": "BS",
      "title": "West"
  },
  {
      "country_code": "BS",
      "title": "Central"
  },
  {
      "country_code": "BT",
      "title": "South"
  },
  {
      "country_code": "BT",
      "title": "North"
  },
  {
      "country_code": "BT",
      "title": "East"
  },
  {
      "country_code": "BT",
      "title": "West"
  },
  {
      "country_code": "BT",
      "title": "Central"
  },
  {
      "country_code": "BW",
      "title": "South"
  },
  {
      "country_code": "BW",
      "title": "North"
  },
  {
      "country_code": "BW",
      "title": "East"
  },
  {
      "country_code": "BW",
      "title": "West"
  },
  {
      "country_code": "BW",
      "title": "Central"
  },
  {
      "country_code": "BZ",
      "title": "South"
  },
  {
      "country_code": "BZ",
      "title": "North"
  },
  {
      "country_code": "BZ",
      "title": "East"
  },
  {
      "country_code": "BZ",
      "title": "West"
  },
  {
      "country_code": "BZ",
      "title": "Central"
  },
  {
      "country_code": "CD",
      "title": "South"
  },
  {
      "country_code": "CD",
      "title": "North"
  },
  {
      "country_code": "CD",
      "title": "East"
  },
  {
      "country_code": "CD",
      "title": "West"
  },
  {
      "country_code": "CD",
      "title": "Central"
  },
  {
      "country_code": "CF",
      "title": "South"
  },
  {
      "country_code": "CF",
      "title": "North"
  },
  {
      "country_code": "CF",
      "title": "East"
  },
  {
      "country_code": "CF",
      "title": "West"
  },
  {
      "country_code": "CF",
      "title": "Central"
  },
  {
      "country_code": "CG",
      "title": "South"
  },
  {
      "country_code": "CG",
      "title": "North"
  },
  {
      "country_code": "CG",
      "title": "East"
  },
  {
      "country_code": "CG",
      "title": "West"
  },
  {
      "country_code": "CG",
      "title": "Central"
  },
  {
      "country_code": "CI",
      "title": "South"
  },
  {
      "country_code": "CI",
      "title": "North"
  },
  {
      "country_code": "CI",
      "title": "East"
  },
  {
      "country_code": "CI",
      "title": "West"
  },
  {
      "country_code": "CI",
      "title": "Central"
  },
  {
      "country_code": "CK",
      "title": "South"
  },
  {
      "country_code": "CK",
      "title": "North"
  },
  {
      "country_code": "CK",
      "title": "East"
  },
  {
      "country_code": "CK",
      "title": "West"
  },
  {
      "country_code": "CK",
      "title": "Central"
  },
  {
      "country_code": "CM",
      "title": "South"
  },
  {
      "country_code": "CM",
      "title": "North"
  },
  {
      "country_code": "CM",
      "title": "East"
  },
  {
      "country_code": "CM",
      "title": "West"
  },
  {
      "country_code": "CM",
      "title": "Central"
  },
  {
      "country_code": "CN",
      "title": "South"
  },
  {
      "country_code": "CN",
      "title": "North"
  },
  {
      "country_code": "CN",
      "title": "East"
  },
  {
      "country_code": "CN",
      "title": "West"
  },
  {
      "country_code": "CN",
      "title": "Central"
  },
  {
      "country_code": "CU",
      "title": "South"
  },
  {
      "country_code": "CU",
      "title": "North"
  },
  {
      "country_code": "CU",
      "title": "East"
  },
  {
      "country_code": "CU",
      "title": "West"
  },
  {
      "country_code": "CU",
      "title": "Central"
  },
  {
      "country_code": "CV",
      "title": "South"
  },
  {
      "country_code": "CV",
      "title": "North"
  },
  {
      "country_code": "CV",
      "title": "East"
  },
  {
      "country_code": "CV",
      "title": "West"
  },
  {
      "country_code": "CV",
      "title": "Central"
  },
  {
      "country_code": "CX",
      "title": "South"
  },
  {
      "country_code": "CX",
      "title": "North"
  },
  {
      "country_code": "CX",
      "title": "East"
  },
  {
      "country_code": "CX",
      "title": "West"
  },
  {
      "country_code": "CX",
      "title": "Central"
  },
  {
      "country_code": "CY",
      "title": "South"
  },
  {
      "country_code": "CY",
      "title": "North"
  },
  {
      "country_code": "CY",
      "title": "East"
  },
  {
      "country_code": "CY",
      "title": "West"
  },
  {
      "country_code": "CY",
      "title": "Central"
  },
  {
      "country_code": "DJ",
      "title": "South"
  },
  {
      "country_code": "DJ",
      "title": "North"
  },
  {
      "country_code": "DJ",
      "title": "East"
  },
  {
      "country_code": "DJ",
      "title": "West"
  },
  {
      "country_code": "DJ",
      "title": "Central"
  },
  {
      "country_code": "DM",
      "title": "South"
  },
  {
      "country_code": "DM",
      "title": "North"
  },
  {
      "country_code": "DM",
      "title": "East"
  },
  {
      "country_code": "DM",
      "title": "West"
  },
  {
      "country_code": "DM",
      "title": "Central"
  },
  {
      "country_code": "DO",
      "title": "South"
  },
  {
      "country_code": "DO",
      "title": "North"
  },
  {
      "country_code": "DO",
      "title": "East"
  },
  {
      "country_code": "DO",
      "title": "West"
  },
  {
      "country_code": "DO",
      "title": "Central"
  },
  {
      "country_code": "EC",
      "title": "South"
  },
  {
      "country_code": "EC",
      "title": "North"
  },
  {
      "country_code": "EC",
      "title": "East"
  },
  {
      "country_code": "EC",
      "title": "West"
  },
  {
      "country_code": "EC",
      "title": "Central"
  },
  {
      "country_code": "EG",
      "title": "South"
  },
  {
      "country_code": "EG",
      "title": "North"
  },
  {
      "country_code": "EG",
      "title": "East"
  },
  {
      "country_code": "EG",
      "title": "West"
  },
  {
      "country_code": "EG",
      "title": "Central"
  },
  {
      "country_code": "ER",
      "title": "South"
  },
  {
      "country_code": "ER",
      "title": "North"
  },
  {
      "country_code": "ER",
      "title": "East"
  },
  {
      "country_code": "ER",
      "title": "West"
  },
  {
      "country_code": "ER",
      "title": "Central"
  },
  {
      "country_code": "ET",
      "title": "South"
  },
  {
      "country_code": "ET",
      "title": "North"
  },
  {
      "country_code": "ET",
      "title": "East"
  },
  {
      "country_code": "ET",
      "title": "West"
  },
  {
      "country_code": "ET",
      "title": "Central"
  },
  {
      "country_code": "FJ",
      "title": "South"
  },
  {
      "country_code": "FJ",
      "title": "North"
  },
  {
      "country_code": "FJ",
      "title": "East"
  },
  {
      "country_code": "FJ",
      "title": "West"
  },
  {
      "country_code": "FJ",
      "title": "Central"
  },
  {
      "country_code": "FK",
      "title": "South"
  },
  {
      "country_code": "FK",
      "title": "North"
  },
  {
      "country_code": "FK",
      "title": "East"
  },
  {
      "country_code": "FK",
      "title": "West"
  },
  {
      "country_code": "FK",
      "title": "Central"
  },
  {
      "country_code": "FM",
      "title": "South"
  },
  {
      "country_code": "FM",
      "title": "North"
  },
  {
      "country_code": "FM",
      "title": "East"
  },
  {
      "country_code": "FM",
      "title": "West"
  },
  {
      "country_code": "FM",
      "title": "Central"
  },
  {
      "country_code": "FO",
      "title": "South"
  },
  {
      "country_code": "FO",
      "title": "North"
  },
  {
      "country_code": "FO",
      "title": "East"
  },
  {
      "country_code": "FO",
      "title": "West"
  },
  {
      "country_code": "FO",
      "title": "Central"
  },
  {
      "country_code": "GA",
      "title": "South"
  },
  {
      "country_code": "GA",
      "title": "North"
  },
  {
      "country_code": "GA",
      "title": "East"
  },
  {
      "country_code": "GA",
      "title": "West"
  },
  {
      "country_code": "GA",
      "title": "Central"
  },
  {
      "country_code": "GD",
      "title": "South"
  },
  {
      "country_code": "GD",
      "title": "North"
  },
  {
      "country_code": "GD",
      "title": "East"
  },
  {
      "country_code": "GD",
      "title": "West"
  },
  {
      "country_code": "GD",
      "title": "Central"
  },
  {
      "country_code": "GE",
      "title": "South"
  },
  {
      "country_code": "GE",
      "title": "North"
  },
  {
      "country_code": "GE",
      "title": "East"
  },
  {
      "country_code": "GE",
      "title": "West"
  },
  {
      "country_code": "GE",
      "title": "Central"
  },
  {
      "country_code": "GH",
      "title": "South"
  },
  {
      "country_code": "GH",
      "title": "North"
  },
  {
      "country_code": "GH",
      "title": "East"
  },
  {
      "country_code": "GH",
      "title": "West"
  },
  {
      "country_code": "GH",
      "title": "Central"
  },
  {
      "country_code": "GI",
      "title": "South"
  },
  {
      "country_code": "GI",
      "title": "North"
  },
  {
      "country_code": "GI",
      "title": "East"
  },
  {
      "country_code": "GI",
      "title": "West"
  },
  {
      "country_code": "GI",
      "title": "Central"
  },
  {
      "country_code": "GL",
      "title": "South"
  },
  {
      "country_code": "GL",
      "title": "North"
  },
  {
      "country_code": "GL",
      "title": "East"
  },
  {
      "country_code": "GL",
      "title": "West"
  },
  {
      "country_code": "GL",
      "title": "Central"
  },
  {
      "country_code": "GM",
      "title": "South"
  },
  {
      "country_code": "GM",
      "title": "North"
  },
  {
      "country_code": "GM",
      "title": "East"
  },
  {
      "country_code": "GM",
      "title": "West"
  },
  {
      "country_code": "GM",
      "title": "Central"
  },
  {
      "country_code": "GN",
      "title": "South"
  },
  {
      "country_code": "GN",
      "title": "North"
  },
  {
      "country_code": "GN",
      "title": "East"
  },
  {
      "country_code": "GN",
      "title": "West"
  },
  {
      "country_code": "GN",
      "title": "Central"
  },
  {
      "country_code": "GQ",
      "title": "South"
  },
  {
      "country_code": "GQ",
      "title": "North"
  },
  {
      "country_code": "GQ",
      "title": "East"
  },
  {
      "country_code": "GQ",
      "title": "West"
  },
  {
      "country_code": "GQ",
      "title": "Central"
  },
  {
      "country_code": "GR",
      "title": "South"
  },
  {
      "country_code": "GR",
      "title": "North"
  },
  {
      "country_code": "GR",
      "title": "East"
  },
  {
      "country_code": "GR",
      "title": "West"
  },
  {
      "country_code": "GR",
      "title": "Central"
  },
  {
      "country_code": "GW",
      "title": "South"
  },
  {
      "country_code": "GW",
      "title": "North"
  },
  {
      "country_code": "GW",
      "title": "East"
  },
  {
      "country_code": "GW",
      "title": "West"
  },
  {
      "country_code": "GW",
      "title": "Central"
  },
  {
      "country_code": "GY",
      "title": "South"
  },
  {
      "country_code": "GY",
      "title": "North"
  },
  {
      "country_code": "GY",
      "title": "East"
  },
  {
      "country_code": "GY",
      "title": "West"
  },
  {
      "country_code": "GY",
      "title": "Central"
  },
  {
      "country_code": "HK",
      "title": "South"
  },
  {
      "country_code": "HK",
      "title": "North"
  },
  {
      "country_code": "HK",
      "title": "East"
  },
  {
      "country_code": "HK",
      "title": "West"
  },
  {
      "country_code": "HK",
      "title": "Central"
  },
  {
      "country_code": "HN",
      "title": "South"
  },
  {
      "country_code": "HN",
      "title": "North"
  },
  {
      "country_code": "HN",
      "title": "East"
  },
  {
      "country_code": "HN",
      "title": "West"
  },
  {
      "country_code": "HN",
      "title": "Central"
  },
  {
      "country_code": "HT",
      "title": "South"
  },
  {
      "country_code": "HT",
      "title": "North"
  },
  {
      "country_code": "HT",
      "title": "East"
  },
  {
      "country_code": "HT",
      "title": "West"
  },
  {
      "country_code": "HT",
      "title": "Central"
  },
  {
      "country_code": "ID",
      "title": "South"
  },
  {
      "country_code": "ID",
      "title": "North"
  },
  {
      "country_code": "ID",
      "title": "East"
  },
  {
      "country_code": "ID",
      "title": "West"
  },
  {
      "country_code": "ID",
      "title": "Central"
  },
  {
      "country_code": "IE",
      "title": "South"
  },
  {
      "country_code": "IE",
      "title": "North"
  },
  {
      "country_code": "IE",
      "title": "East"
  },
  {
      "country_code": "IE",
      "title": "West"
  },
  {
      "country_code": "IE",
      "title": "Central"
  },
  {
      "country_code": "IL",
      "title": "South"
  },
  {
      "country_code": "IL",
      "title": "North"
  },
  {
      "country_code": "IL",
      "title": "East"
  },
  {
      "country_code": "IL",
      "title": "West"
  },
  {
      "country_code": "IL",
      "title": "Central"
  },
  {
      "country_code": "IM",
      "title": "South"
  },
  {
      "country_code": "IM",
      "title": "North"
  },
  {
      "country_code": "IM",
      "title": "East"
  },
  {
      "country_code": "IM",
      "title": "West"
  },
  {
      "country_code": "IM",
      "title": "Central"
  },
  {
      "country_code": "IQ",
      "title": "South"
  },
  {
      "country_code": "IQ",
      "title": "North"
  },
  {
      "country_code": "IQ",
      "title": "East"
  },
  {
      "country_code": "IQ",
      "title": "West"
  },
  {
      "country_code": "IQ",
      "title": "Central"
  },
  {
      "country_code": "IR",
      "title": "South"
  },
  {
      "country_code": "IR",
      "title": "North"
  },
  {
      "country_code": "IR",
      "title": "East"
  },
  {
      "country_code": "IR",
      "title": "West"
  },
  {
      "country_code": "IR",
      "title": "Central"
  },
  {
      "country_code": "IS",
      "title": "South"
  },
  {
      "country_code": "IS",
      "title": "North"
  },
  {
      "country_code": "IS",
      "title": "East"
  },
  {
      "country_code": "IS",
      "title": "West"
  },
  {
      "country_code": "IS",
      "title": "Central"
  },
  {
      "country_code": "JM",
      "title": "South"
  },
  {
      "country_code": "JM",
      "title": "North"
  },
  {
      "country_code": "JM",
      "title": "East"
  },
  {
      "country_code": "JM",
      "title": "West"
  },
  {
      "country_code": "JM",
      "title": "Central"
  },
  {
      "country_code": "JO",
      "title": "South"
  },
  {
      "country_code": "JO",
      "title": "North"
  },
  {
      "country_code": "JO",
      "title": "East"
  },
  {
      "country_code": "JO",
      "title": "West"
  },
  {
      "country_code": "JO",
      "title": "Central"
  },
  {
      "country_code": "KE",
      "title": "South"
  },
  {
      "country_code": "KE",
      "title": "North"
  },
  {
      "country_code": "KE",
      "title": "East"
  },
  {
      "country_code": "KE",
      "title": "West"
  },
  {
      "country_code": "KE",
      "title": "Central"
  },
  {
      "country_code": "KG",
      "title": "South"
  },
  {
      "country_code": "KG",
      "title": "North"
  },
  {
      "country_code": "KG",
      "title": "East"
  },
  {
      "country_code": "KG",
      "title": "West"
  },
  {
      "country_code": "KG",
      "title": "Central"
  },
  {
      "country_code": "KH",
      "title": "South"
  },
  {
      "country_code": "KH",
      "title": "North"
  },
  {
      "country_code": "KH",
      "title": "East"
  },
  {
      "country_code": "KH",
      "title": "West"
  },
  {
      "country_code": "KH",
      "title": "Central"
  },
  {
      "country_code": "KI",
      "title": "South"
  },
  {
      "country_code": "KI",
      "title": "North"
  },
  {
      "country_code": "KI",
      "title": "East"
  },
  {
      "country_code": "KI",
      "title": "West"
  },
  {
      "country_code": "KI",
      "title": "Central"
  },
  {
      "country_code": "KM",
      "title": "South"
  },
  {
      "country_code": "KM",
      "title": "North"
  },
  {
      "country_code": "KM",
      "title": "East"
  },
  {
      "country_code": "KM",
      "title": "West"
  },
  {
      "country_code": "KM",
      "title": "Central"
  },
  {
      "country_code": "KN",
      "title": "South"
  },
  {
      "country_code": "KN",
      "title": "North"
  },
  {
      "country_code": "KN",
      "title": "East"
  },
  {
      "country_code": "KN",
      "title": "West"
  },
  {
      "country_code": "KN",
      "title": "Central"
  },
  {
      "country_code": "KO",
      "title": "South"
  },
  {
      "country_code": "KO",
      "title": "North"
  },
  {
      "country_code": "KO",
      "title": "East"
  },
  {
      "country_code": "KO",
      "title": "West"
  },
  {
      "country_code": "KO",
      "title": "Central"
  },
  {
      "country_code": "KP",
      "title": "South"
  },
  {
      "country_code": "KP",
      "title": "North"
  },
  {
      "country_code": "KP",
      "title": "East"
  },
  {
      "country_code": "KP",
      "title": "West"
  },
  {
      "country_code": "KP",
      "title": "Central"
  },
  {
      "country_code": "KW",
      "title": "South"
  },
  {
      "country_code": "KW",
      "title": "North"
  },
  {
      "country_code": "KW",
      "title": "East"
  },
  {
      "country_code": "KW",
      "title": "West"
  },
  {
      "country_code": "KW",
      "title": "Central"
  },
  {
      "country_code": "KY",
      "title": "South"
  },
  {
      "country_code": "KY",
      "title": "North"
  },
  {
      "country_code": "KY",
      "title": "East"
  },
  {
      "country_code": "KY",
      "title": "West"
  },
  {
      "country_code": "KY",
      "title": "Central"
  },
  {
      "country_code": "KZ",
      "title": "South"
  },
  {
      "country_code": "KZ",
      "title": "North"
  },
  {
      "country_code": "KZ",
      "title": "East"
  },
  {
      "country_code": "KZ",
      "title": "West"
  },
  {
      "country_code": "KZ",
      "title": "Central"
  },
  {
      "country_code": "LA",
      "title": "South"
  },
  {
      "country_code": "LA",
      "title": "North"
  },
  {
      "country_code": "LA",
      "title": "East"
  },
  {
      "country_code": "LA",
      "title": "West"
  },
  {
      "country_code": "LA",
      "title": "Central"
  },
  {
      "country_code": "LB",
      "title": "South"
  },
  {
      "country_code": "LB",
      "title": "North"
  },
  {
      "country_code": "LB",
      "title": "East"
  },
  {
      "country_code": "LB",
      "title": "West"
  },
  {
      "country_code": "LB",
      "title": "Central"
  },
  {
      "country_code": "LC",
      "title": "South"
  },
  {
      "country_code": "LC",
      "title": "North"
  },
  {
      "country_code": "LC",
      "title": "East"
  },
  {
      "country_code": "LC",
      "title": "West"
  },
  {
      "country_code": "LC",
      "title": "Central"
  },
  {
      "country_code": "LR",
      "title": "South"
  },
  {
      "country_code": "LR",
      "title": "North"
  },
  {
      "country_code": "LR",
      "title": "East"
  },
  {
      "country_code": "LR",
      "title": "West"
  },
  {
      "country_code": "LR",
      "title": "Central"
  },
  {
      "country_code": "LS",
      "title": "South"
  },
  {
      "country_code": "LS",
      "title": "North"
  },
  {
      "country_code": "LS",
      "title": "East"
  },
  {
      "country_code": "LS",
      "title": "West"
  },
  {
      "country_code": "LS",
      "title": "Central"
  },
  {
      "country_code": "LY",
      "title": "South"
  },
  {
      "country_code": "LY",
      "title": "North"
  },
  {
      "country_code": "LY",
      "title": "East"
  },
  {
      "country_code": "LY",
      "title": "West"
  },
  {
      "country_code": "LY",
      "title": "Central"
  },
  {
      "country_code": "MA",
      "title": "South"
  },
  {
      "country_code": "MA",
      "title": "North"
  },
  {
      "country_code": "MA",
      "title": "East"
  },
  {
      "country_code": "MA",
      "title": "West"
  },
  {
      "country_code": "MA",
      "title": "Central"
  },
  {
      "country_code": "ME",
      "title": "South"
  },
  {
      "country_code": "ME",
      "title": "North"
  },
  {
      "country_code": "ME",
      "title": "East"
  },
  {
      "country_code": "ME",
      "title": "West"
  },
  {
      "country_code": "ME",
      "title": "Central"
  },
  {
      "country_code": "MG",
      "title": "South"
  },
  {
      "country_code": "MG",
      "title": "North"
  },
  {
      "country_code": "MG",
      "title": "East"
  },
  {
      "country_code": "MG",
      "title": "West"
  },
  {
      "country_code": "MG",
      "title": "Central"
  },
  {
      "country_code": "MK",
      "title": "South"
  },
  {
      "country_code": "MK",
      "title": "North"
  },
  {
      "country_code": "MK",
      "title": "East"
  },
  {
      "country_code": "MK",
      "title": "West"
  },
  {
      "country_code": "MK",
      "title": "Central"
  },
  {
      "country_code": "ML",
      "title": "South"
  },
  {
      "country_code": "ML",
      "title": "North"
  },
  {
      "country_code": "ML",
      "title": "East"
  },
  {
      "country_code": "ML",
      "title": "West"
  },
  {
      "country_code": "ML",
      "title": "Central"
  },
  {
      "country_code": "MM",
      "title": "South"
  },
  {
      "country_code": "MM",
      "title": "North"
  },
  {
      "country_code": "MM",
      "title": "East"
  },
  {
      "country_code": "MM",
      "title": "West"
  },
  {
      "country_code": "MM",
      "title": "Central"
  },
  {
      "country_code": "MN",
      "title": "South"
  },
  {
      "country_code": "MN",
      "title": "North"
  },
  {
      "country_code": "MN",
      "title": "East"
  },
  {
      "country_code": "MN",
      "title": "West"
  },
  {
      "country_code": "MN",
      "title": "Central"
  },
  {
      "country_code": "MO",
      "title": "South"
  },
  {
      "country_code": "MO",
      "title": "North"
  },
  {
      "country_code": "MO",
      "title": "East"
  },
  {
      "country_code": "MO",
      "title": "West"
  },
  {
      "country_code": "MO",
      "title": "Central"
  },
  {
      "country_code": "MR",
      "title": "South"
  },
  {
      "country_code": "MR",
      "title": "North"
  },
  {
      "country_code": "MR",
      "title": "East"
  },
  {
      "country_code": "MR",
      "title": "West"
  },
  {
      "country_code": "MR",
      "title": "Central"
  },
  {
      "country_code": "MS",
      "title": "South"
  },
  {
      "country_code": "MS",
      "title": "North"
  },
  {
      "country_code": "MS",
      "title": "East"
  },
  {
      "country_code": "MS",
      "title": "West"
  },
  {
      "country_code": "MS",
      "title": "Central"
  },
  {
      "country_code": "MU",
      "title": "South"
  },
  {
      "country_code": "MU",
      "title": "North"
  },
  {
      "country_code": "MU",
      "title": "East"
  },
  {
      "country_code": "MU",
      "title": "West"
  },
  {
      "country_code": "MU",
      "title": "Central"
  },
  {
      "country_code": "MV",
      "title": "South"
  },
  {
      "country_code": "MV",
      "title": "North"
  },
  {
      "country_code": "MV",
      "title": "East"
  },
  {
      "country_code": "MV",
      "title": "West"
  },
  {
      "country_code": "MV",
      "title": "Central"
  },
  {
      "country_code": "MZ",
      "title": "South"
  },
  {
      "country_code": "MZ",
      "title": "North"
  },
  {
      "country_code": "MZ",
      "title": "East"
  },
  {
      "country_code": "MZ",
      "title": "West"
  },
  {
      "country_code": "MZ",
      "title": "Central"
  },
  {
      "country_code": "NA",
      "title": "South"
  },
  {
      "country_code": "NA",
      "title": "North"
  },
  {
      "country_code": "NA",
      "title": "East"
  },
  {
      "country_code": "NA",
      "title": "West"
  },
  {
      "country_code": "NA",
      "title": "Central"
  },
  {
      "country_code": "NE",
      "title": "South"
  },
  {
      "country_code": "NE",
      "title": "North"
  },
  {
      "country_code": "NE",
      "title": "East"
  },
  {
      "country_code": "NE",
      "title": "West"
  },
  {
      "country_code": "NE",
      "title": "Central"
  },
  {
      "country_code": "NF",
      "title": "South"
  },
  {
      "country_code": "NF",
      "title": "North"
  },
  {
      "country_code": "NF",
      "title": "East"
  },
  {
      "country_code": "NF",
      "title": "West"
  },
  {
      "country_code": "NF",
      "title": "Central"
  },
  {
      "country_code": "NG",
      "title": "South"
  },
  {
      "country_code": "NG",
      "title": "North"
  },
  {
      "country_code": "NG",
      "title": "East"
  },
  {
      "country_code": "NG",
      "title": "West"
  },
  {
      "country_code": "NG",
      "title": "Central"
  },
  {
      "country_code": "NI",
      "title": "South"
  },
  {
      "country_code": "NI",
      "title": "North"
  },
  {
      "country_code": "NI",
      "title": "East"
  },
  {
      "country_code": "NI",
      "title": "West"
  },
  {
      "country_code": "NI",
      "title": "Central"
  },
  {
      "country_code": "NP",
      "title": "South"
  },
  {
      "country_code": "NP",
      "title": "North"
  },
  {
      "country_code": "NP",
      "title": "East"
  },
  {
      "country_code": "NP",
      "title": "West"
  },
  {
      "country_code": "NP",
      "title": "Central"
  },
  {
      "country_code": "NR",
      "title": "South"
  },
  {
      "country_code": "NR",
      "title": "North"
  },
  {
      "country_code": "NR",
      "title": "East"
  },
  {
      "country_code": "NR",
      "title": "West"
  },
  {
      "country_code": "NR",
      "title": "Central"
  },
  {
      "country_code": "NU",
      "title": "South"
  },
  {
      "country_code": "NU",
      "title": "North"
  },
  {
      "country_code": "NU",
      "title": "East"
  },
  {
      "country_code": "NU",
      "title": "West"
  },
  {
      "country_code": "NU",
      "title": "Central"
  },
  {
      "country_code": "OM",
      "title": "South"
  },
  {
      "country_code": "OM",
      "title": "North"
  },
  {
      "country_code": "OM",
      "title": "East"
  },
  {
      "country_code": "OM",
      "title": "West"
  },
  {
      "country_code": "OM",
      "title": "Central"
  },
  {
      "country_code": "PA",
      "title": "South"
  },
  {
      "country_code": "PA",
      "title": "North"
  },
  {
      "country_code": "PA",
      "title": "East"
  },
  {
      "country_code": "PA",
      "title": "West"
  },
  {
      "country_code": "PA",
      "title": "Central"
  },
  {
      "country_code": "PE",
      "title": "South"
  },
  {
      "country_code": "PE",
      "title": "North"
  },
  {
      "country_code": "PE",
      "title": "East"
  },
  {
      "country_code": "PE",
      "title": "West"
  },
  {
      "country_code": "PE",
      "title": "Central"
  },
  {
      "country_code": "PF",
      "title": "South"
  },
  {
      "country_code": "PF",
      "title": "North"
  },
  {
      "country_code": "PF",
      "title": "East"
  },
  {
      "country_code": "PF",
      "title": "West"
  },
  {
      "country_code": "PF",
      "title": "Central"
  },
  {
      "country_code": "PG",
      "title": "South"
  },
  {
      "country_code": "PG",
      "title": "North"
  },
  {
      "country_code": "PG",
      "title": "East"
  },
  {
      "country_code": "PG",
      "title": "West"
  },
  {
      "country_code": "PG",
      "title": "Central"
  },
  {
      "country_code": "PH",
      "title": "South"
  },
  {
      "country_code": "PH",
      "title": "North"
  },
  {
      "country_code": "PH",
      "title": "East"
  },
  {
      "country_code": "PH",
      "title": "West"
  },
  {
      "country_code": "PH",
      "title": "Central"
  },
  {
      "country_code": "PM",
      "title": "South"
  },
  {
      "country_code": "PM",
      "title": "North"
  },
  {
      "country_code": "PM",
      "title": "East"
  },
  {
      "country_code": "PM",
      "title": "West"
  },
  {
      "country_code": "PM",
      "title": "Central"
  },
  {
      "country_code": "PN",
      "title": "South"
  },
  {
      "country_code": "PN",
      "title": "North"
  },
  {
      "country_code": "PN",
      "title": "East"
  },
  {
      "country_code": "PN",
      "title": "West"
  },
  {
      "country_code": "PN",
      "title": "Central"
  },
  {
      "country_code": "PW",
      "title": "South"
  },
  {
      "country_code": "PW",
      "title": "North"
  },
  {
      "country_code": "PW",
      "title": "East"
  },
  {
      "country_code": "PW",
      "title": "West"
  },
  {
      "country_code": "PW",
      "title": "Central"
  },
  {
      "country_code": "PY",
      "title": "South"
  },
  {
      "country_code": "PY",
      "title": "North"
  },
  {
      "country_code": "PY",
      "title": "East"
  },
  {
      "country_code": "PY",
      "title": "West"
  },
  {
      "country_code": "PY",
      "title": "Central"
  },
  {
      "country_code": "QA",
      "title": "South"
  },
  {
      "country_code": "QA",
      "title": "North"
  },
  {
      "country_code": "QA",
      "title": "East"
  },
  {
      "country_code": "QA",
      "title": "West"
  },
  {
      "country_code": "QA",
      "title": "Central"
  },
  {
      "country_code": "RS",
      "title": "South"
  },
  {
      "country_code": "RS",
      "title": "North"
  },
  {
      "country_code": "RS",
      "title": "East"
  },
  {
      "country_code": "RS",
      "title": "West"
  },
  {
      "country_code": "RS",
      "title": "Central"
  },
  {
      "country_code": "RW",
      "title": "South"
  },
  {
      "country_code": "RW",
      "title": "North"
  },
  {
      "country_code": "RW",
      "title": "East"
  },
  {
      "country_code": "RW",
      "title": "West"
  },
  {
      "country_code": "RW",
      "title": "Central"
  },
  {
      "country_code": "SA",
      "title": "South"
  },
  {
      "country_code": "SA",
      "title": "North"
  },
  {
      "country_code": "SA",
      "title": "East"
  },
  {
      "country_code": "SA",
      "title": "West"
  },
  {
      "country_code": "SA",
      "title": "Central"
  },
  {
      "country_code": "SB",
      "title": "South"
  },
  {
      "country_code": "SB",
      "title": "North"
  },
  {
      "country_code": "SB",
      "title": "East"
  },
  {
      "country_code": "SB",
      "title": "West"
  },
  {
      "country_code": "SB",
      "title": "Central"
  },
  {
      "country_code": "SC",
      "title": "South"
  },
  {
      "country_code": "SC",
      "title": "North"
  },
  {
      "country_code": "SC",
      "title": "East"
  },
  {
      "country_code": "SC",
      "title": "West"
  },
  {
      "country_code": "SC",
      "title": "Central"
  },
  {
      "country_code": "SD",
      "title": "South"
  },
  {
      "country_code": "SD",
      "title": "North"
  },
  {
      "country_code": "SD",
      "title": "East"
  },
  {
      "country_code": "SD",
      "title": "West"
  },
  {
      "country_code": "SD",
      "title": "Central"
  },
  {
      "country_code": "SG",
      "title": "South"
  },
  {
      "country_code": "SG",
      "title": "North"
  },
  {
      "country_code": "SG",
      "title": "East"
  },
  {
      "country_code": "SG",
      "title": "West"
  },
  {
      "country_code": "SG",
      "title": "Central"
  },
  {
      "country_code": "SI",
      "title": "South"
  },
  {
      "country_code": "SI",
      "title": "North"
  },
  {
      "country_code": "SI",
      "title": "East"
  },
  {
      "country_code": "SI",
      "title": "West"
  },
  {
      "country_code": "SI",
      "title": "Central"
  },
  {
      "country_code": "SL",
      "title": "South"
  },
  {
      "country_code": "SL",
      "title": "North"
  },
  {
      "country_code": "SL",
      "title": "East"
  },
  {
      "country_code": "SL",
      "title": "West"
  },
  {
      "country_code": "SL",
      "title": "Central"
  },
  {
      "country_code": "SM",
      "title": "South"
  },
  {
      "country_code": "SM",
      "title": "North"
  },
  {
      "country_code": "SM",
      "title": "East"
  },
  {
      "country_code": "SM",
      "title": "West"
  },
  {
      "country_code": "SM",
      "title": "Central"
  },
  {
      "country_code": "SN",
      "title": "South"
  },
  {
      "country_code": "SN",
      "title": "North"
  },
  {
      "country_code": "SN",
      "title": "East"
  },
  {
      "country_code": "SN",
      "title": "West"
  },
  {
      "country_code": "SN",
      "title": "Central"
  },
  {
      "country_code": "SO",
      "title": "South"
  },
  {
      "country_code": "SO",
      "title": "North"
  },
  {
      "country_code": "SO",
      "title": "East"
  },
  {
      "country_code": "SO",
      "title": "West"
  },
  {
      "country_code": "SO",
      "title": "Central"
  },
  {
      "country_code": "SR",
      "title": "South"
  },
  {
      "country_code": "SR",
      "title": "North"
  },
  {
      "country_code": "SR",
      "title": "East"
  },
  {
      "country_code": "SR",
      "title": "West"
  },
  {
      "country_code": "SR",
      "title": "Central"
  },
  {
      "country_code": "ST",
      "title": "South"
  },
  {
      "country_code": "ST",
      "title": "North"
  },
  {
      "country_code": "ST",
      "title": "East"
  },
  {
      "country_code": "ST",
      "title": "West"
  },
  {
      "country_code": "ST",
      "title": "Central"
  },
  {
      "country_code": "SV",
      "title": "South"
  },
  {
      "country_code": "SV",
      "title": "North"
  },
  {
      "country_code": "SV",
      "title": "East"
  },
  {
      "country_code": "SV",
      "title": "West"
  },
  {
      "country_code": "SV",
      "title": "Central"
  },
  {
      "country_code": "SY",
      "title": "South"
  },
  {
      "country_code": "SY",
      "title": "North"
  },
  {
      "country_code": "SY",
      "title": "East"
  },
  {
      "country_code": "SY",
      "title": "West"
  },
  {
      "country_code": "SY",
      "title": "Central"
  },
  {
      "country_code": "SZ",
      "title": "South"
  },
  {
      "country_code": "SZ",
      "title": "North"
  },
  {
      "country_code": "SZ",
      "title": "East"
  },
  {
      "country_code": "SZ",
      "title": "West"
  },
  {
      "country_code": "SZ",
      "title": "Central"
  },
  {
      "country_code": "TC",
      "title": "South"
  },
  {
      "country_code": "TC",
      "title": "North"
  },
  {
      "country_code": "TC",
      "title": "East"
  },
  {
      "country_code": "TC",
      "title": "West"
  },
  {
      "country_code": "TC",
      "title": "Central"
  },
  {
      "country_code": "TD",
      "title": "South"
  },
  {
      "country_code": "TD",
      "title": "North"
  },
  {
      "country_code": "TD",
      "title": "East"
  },
  {
      "country_code": "TD",
      "title": "West"
  },
  {
      "country_code": "TD",
      "title": "Central"
  },
  {
      "country_code": "TG",
      "title": "South"
  },
  {
      "country_code": "TG",
      "title": "North"
  },
  {
      "country_code": "TG",
      "title": "East"
  },
  {
      "country_code": "TG",
      "title": "West"
  },
  {
      "country_code": "TG",
      "title": "Central"
  },
  {
      "country_code": "TJ",
      "title": "South"
  },
  {
      "country_code": "TJ",
      "title": "North"
  },
  {
      "country_code": "TJ",
      "title": "East"
  },
  {
      "country_code": "TJ",
      "title": "West"
  },
  {
      "country_code": "TJ",
      "title": "Central"
  },
  {
      "country_code": "TK",
      "title": "South"
  },
  {
      "country_code": "TK",
      "title": "North"
  },
  {
      "country_code": "TK",
      "title": "East"
  },
  {
      "country_code": "TK",
      "title": "West"
  },
  {
      "country_code": "TK",
      "title": "Central"
  },
  {
      "country_code": "TL",
      "title": "South"
  },
  {
      "country_code": "TL",
      "title": "North"
  },
  {
      "country_code": "TL",
      "title": "East"
  },
  {
      "country_code": "TL",
      "title": "West"
  },
  {
      "country_code": "TL",
      "title": "Central"
  },
  {
      "country_code": "TM",
      "title": "South"
  },
  {
      "country_code": "TM",
      "title": "North"
  },
  {
      "country_code": "TM",
      "title": "East"
  },
  {
      "country_code": "TM",
      "title": "West"
  },
  {
      "country_code": "TM",
      "title": "Central"
  },
  {
      "country_code": "TN",
      "title": "South"
  },
  {
      "country_code": "TN",
      "title": "North"
  },
  {
      "country_code": "TN",
      "title": "East"
  },
  {
      "country_code": "TN",
      "title": "West"
  },
  {
      "country_code": "TN",
      "title": "Central"
  },
  {
      "country_code": "TO",
      "title": "South"
  },
  {
      "country_code": "TO",
      "title": "North"
  },
  {
      "country_code": "TO",
      "title": "East"
  },
  {
      "country_code": "TO",
      "title": "West"
  },
  {
      "country_code": "TO",
      "title": "Central"
  },
  {
      "country_code": "TT",
      "title": "South"
  },
  {
      "country_code": "TT",
      "title": "North"
  },
  {
      "country_code": "TT",
      "title": "East"
  },
  {
      "country_code": "TT",
      "title": "West"
  },
  {
      "country_code": "TT",
      "title": "Central"
  },
  {
      "country_code": "TV",
      "title": "South"
  },
  {
      "country_code": "TV",
      "title": "North"
  },
  {
      "country_code": "TV",
      "title": "East"
  },
  {
      "country_code": "TV",
      "title": "West"
  },
  {
      "country_code": "TV",
      "title": "Central"
  },
  {
      "country_code": "TW",
      "title": "South"
  },
  {
      "country_code": "TW",
      "title": "North"
  },
  {
      "country_code": "TW",
      "title": "East"
  },
  {
      "country_code": "TW",
      "title": "West"
  },
  {
      "country_code": "TW",
      "title": "Central"
  },
  {
      "country_code": "TZ",
      "title": "South"
  },
  {
      "country_code": "TZ",
      "title": "North"
  },
  {
      "country_code": "TZ",
      "title": "East"
  },
  {
      "country_code": "TZ",
      "title": "West"
  },
  {
      "country_code": "TZ",
      "title": "Central"
  },
  {
      "country_code": "UG",
      "title": "South"
  },
  {
      "country_code": "UG",
      "title": "North"
  },
  {
      "country_code": "UG",
      "title": "East"
  },
  {
      "country_code": "UG",
      "title": "West"
  },
  {
      "country_code": "UG",
      "title": "Central"
  },
  {
      "country_code": "UZ",
      "title": "South"
  },
  {
      "country_code": "UZ",
      "title": "North"
  },
  {
      "country_code": "UZ",
      "title": "East"
  },
  {
      "country_code": "UZ",
      "title": "West"
  },
  {
      "country_code": "UZ",
      "title": "Central"
  },
  {
      "country_code": "VA",
      "title": "South"
  },
  {
      "country_code": "VA",
      "title": "North"
  },
  {
      "country_code": "VA",
      "title": "East"
  },
  {
      "country_code": "VA",
      "title": "West"
  },
  {
      "country_code": "VA",
      "title": "Central"
  },
  {
      "country_code": "VC",
      "title": "South"
  },
  {
      "country_code": "VC",
      "title": "North"
  },
  {
      "country_code": "VC",
      "title": "East"
  },
  {
      "country_code": "VC",
      "title": "West"
  },
  {
      "country_code": "VC",
      "title": "Central"
  },
  {
      "country_code": "VE",
      "title": "South"
  },
  {
      "country_code": "VE",
      "title": "North"
  },
  {
      "country_code": "VE",
      "title": "East"
  },
  {
      "country_code": "VE",
      "title": "West"
  },
  {
      "country_code": "VE",
      "title": "Central"
  },
  {
      "country_code": "VG",
      "title": "South"
  },
  {
      "country_code": "VG",
      "title": "North"
  },
  {
      "country_code": "VG",
      "title": "East"
  },
  {
      "country_code": "VG",
      "title": "West"
  },
  {
      "country_code": "VG",
      "title": "Central"
  },
  {
      "country_code": "VN",
      "title": "South"
  },
  {
      "country_code": "VN",
      "title": "North"
  },
  {
      "country_code": "VN",
      "title": "East"
  },
  {
      "country_code": "VN",
      "title": "West"
  },
  {
      "country_code": "VN",
      "title": "Central"
  },
  {
      "country_code": "VU",
      "title": "South"
  },
  {
      "country_code": "VU",
      "title": "North"
  },
  {
      "country_code": "VU",
      "title": "East"
  },
  {
      "country_code": "VU",
      "title": "West"
  },
  {
      "country_code": "VU",
      "title": "Central"
  },
  {
      "country_code": "WF",
      "title": "South"
  },
  {
      "country_code": "WF",
      "title": "North"
  },
  {
      "country_code": "WF",
      "title": "East"
  },
  {
      "country_code": "WF",
      "title": "West"
  },
  {
      "country_code": "WF",
      "title": "Central"
  },
  {
      "country_code": "WS",
      "title": "South"
  },
  {
      "country_code": "WS",
      "title": "North"
  },
  {
      "country_code": "WS",
      "title": "East"
  },
  {
      "country_code": "WS",
      "title": "West"
  },
  {
      "country_code": "WS",
      "title": "Central"
  },
  {
      "country_code": "YE",
      "title": "South"
  },
  {
      "country_code": "YE",
      "title": "North"
  },
  {
      "country_code": "YE",
      "title": "East"
  },
  {
      "country_code": "YE",
      "title": "West"
  },
  {
      "country_code": "YE",
      "title": "Central"
  },
  {
      "country_code": "ZA",
      "title": "South"
  },
  {
      "country_code": "ZA",
      "title": "North"
  },
  {
      "country_code": "ZA",
      "title": "East"
  },
  {
      "country_code": "ZA",
      "title": "West"
  },
  {
      "country_code": "ZA",
      "title": "Central"
  },
  {
      "country_code": "ZM",
      "title": "South"
  },
  {
      "country_code": "ZM",
      "title": "North"
  },
  {
      "country_code": "ZM",
      "title": "East"
  },
  {
      "country_code": "ZM",
      "title": "West"
  },
  {
      "country_code": "ZM",
      "title": "Central"
  },
  {
      "country_code": "ZW",
      "title": "South"
  },
  {
      "country_code": "ZW",
      "title": "North"
  },
  {
      "country_code": "ZW",
      "title": "East"
  },
  {
      "country_code": "ZW",
      "title": "West"
  },
  {
      "country_code": "ZW",
      "title": "Central"
  },
  {
      "country_code": "XH",
      "title": "South"
  },
  {
      "country_code": "XH",
      "title": "North"
  },
  {
      "country_code": "XH",
      "title": "East"
  },
  {
      "country_code": "XH",
      "title": "West"
  },
  {
      "country_code": "XH",
      "title": "Central"
  },
  {
      "country_code": "XK",
      "title": "South"
  },
  {
      "country_code": "XK",
      "title": "North"
  },
  {
      "country_code": "XK",
      "title": "East"
  },
  {
      "country_code": "XK",
      "title": "West"
  },
  {
      "country_code": "XK",
      "title": "Central"
  },
  {
      "country_code": "XS",
      "title": "South"
  },
  {
      "country_code": "XS",
      "title": "North"
  },
  {
      "country_code": "XS",
      "title": "East"
  },
  {
      "country_code": "XS",
      "title": "West"
  },
  {
      "country_code": "XS",
      "title": "Central"
  },
  {
      "country_code": "MP",
      "title": "South"
  },
  {
      "country_code": "MP",
      "title": "North"
  },
  {
      "country_code": "MP",
      "title": "East"
  },
  {
      "country_code": "MP",
      "title": "West"
  },
  {
      "country_code": "MP",
      "title": "Central"
  },
  {
      "country_code": "GS",
      "title": "South"
  },
  {
      "country_code": "GS",
      "title": "North"
  },
  {
      "country_code": "GS",
      "title": "East"
  },
  {
      "country_code": "GS",
      "title": "West"
  },
  {
      "country_code": "GS",
      "title": "Central"
  },
  {
      "country_code": "PS",
      "title": "South"
  },
  {
      "country_code": "PS",
      "title": "North"
  },
  {
      "country_code": "PS",
      "title": "East"
  },
  {
      "country_code": "PS",
      "title": "West"
  },
  {
      "country_code": "PS",
      "title": "Central"
  },
  {
      "country_code": "BL",
      "title": "South"
  },
  {
      "country_code": "BL",
      "title": "North"
  },
  {
      "country_code": "BL",
      "title": "East"
  },
  {
      "country_code": "BL",
      "title": "West"
  },
  {
      "country_code": "BL",
      "title": "Central"
  },
  {
      "country_code": "MF",
      "title": "South"
  },
  {
      "country_code": "MF",
      "title": "North"
  },
  {
      "country_code": "MF",
      "title": "East"
  },
  {
      "country_code": "MF",
      "title": "West"
  },
  {
      "country_code": "MF",
      "title": "Central"
  },
  {
      "country_code": "IO",
      "title": "South"
  },
  {
      "country_code": "IO",
      "title": "North"
  },
  {
      "country_code": "IO",
      "title": "East"
  },
  {
      "country_code": "IO",
      "title": "West"
  },
  {
      "country_code": "IO",
      "title": "Central"
  },
  {
      "country_code": "SH",
      "title": "South"
  },
  {
      "country_code": "SH",
      "title": "North"
  },
  {
      "country_code": "SH",
      "title": "East"
  },
  {
      "country_code": "SH",
      "title": "West"
  },
  {
      "country_code": "SH",
      "title": "Central"
  },
  {
      "country_code": "SS",
      "title": "South"
  },
  {
      "country_code": "SS",
      "title": "North"
  },
  {
      "country_code": "SS",
      "title": "East"
  },
  {
      "country_code": "SS",
      "title": "West"
  },
  {
      "country_code": "SS",
      "title": "Central"
  }
]