//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { RegionService } from "./region.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('region')
@UseGuards(SessionGuard)
export class RegionController {

  constructor(
    private readonly _regionService: RegionService
  ) { }

  @Post()
  create(@Body() createRegionDto: CreateRegionDto) {
    const resp = this._regionService.create(createRegionDto);
    return resp;
  }

  @Get()
  async findAll(@Query() query: any) {
    return this._regionService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') regionId: string) {
    return this._regionService.get(regionId);
  }

  @Patch(':id')
  update(@Param('id') regionId: string, @Body() updateRegionDto: UpdateRegionDto) {
    const resp = this._regionService.update(regionId, updateRegionDto);
    return resp;
  }

  @Delete(':id')
  remove(@Param('id') regionId: string) {
    const deletedData = this._regionService.delete(regionId);
    return deletedData;
  }
}
