//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateVspaceTopicDto } from './dto/create-vspace-topic.dto';
import { UpdateVspaceTopicDto } from './dto/update-vspace-topic.dto';
import { VspaceTopicService } from "./vspace-topic.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('vspacetopic')
@UseGuards(SessionGuard)
export class VspaceTopicController {

  constructor(
    private readonly _vspaceTopicService: VspaceTopicService
  ) { }

  @Post()
  create(@Body() createVspaceTopicDto: CreateVspaceTopicDto) {
    const resp = this._vspaceTopicService.create(createVspaceTopicDto);
    return resp;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._vspaceTopicService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') vspaceTopicId: string) {
    return this._vspaceTopicService.get(vspaceTopicId);
  }

  @Patch(':id')
  update(@Param('id') vspaceTopicId: string, @Body() updateVspaceTopicDto: UpdateVspaceTopicDto) {
    const resp = this._vspaceTopicService.update(vspaceTopicId, updateVspaceTopicDto);
    return resp;
  }

  @Delete(':id')
  remove(@Param('id') vspaceTopicId: string) {
    const deletedData = this._vspaceTopicService.delete(vspaceTopicId);
    return deletedData;
  }
}
