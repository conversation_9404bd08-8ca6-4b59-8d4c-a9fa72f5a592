//Import Library
import { useEffect, useState } from "react";
import Link from 'next/link';

//Import services/components
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

interface VspaceTableProps {
  vspaceData: any;
  vspaceDataLoading: boolean;
  vspaceDataTotalRows: number;
  vspaceDataPerPage: number;
  vspaceDataCurrentPage: number;
  type: string;
  id: string;
}

const VspaceTable = (props: VspaceTableProps) => {
  const { t } = useTranslation('common');
  const { type, id } = props;
  const [tabledata, setDataToTable] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [resetPaginationToggle] = useState(false);

  const vSpaceParams = {
    sort: { created_at: "asc" },
    limit: perPage,
    page: 1,
    query: {},
  };

  const columns = [
    {
      name: t("Title"),
      selector: "title",
      cell: (d: any) => d && d.title && d._id ? <Link href="/vspace/[...routes]" as={`/vspace/show/${d._id}`} >{d.title}</Link> : "",
    },
    {
      name: t("Owner"),
      selector: "users",
      cell: (d: any) => d && d.user && d.user.firstname ? `${d.user.firstname} ${d.user.lastname}` : ""
    },
    {
      name: t("PublicPrivate"),
      selector: "visibility",
      cell: (d: any) => d && d.visibility ? "Public" : "Private",

    },
    {
      name: t("NumberofMembers"),
      selector: "members",
      cell: (d: any) => d && d.members ? d.members.length : "-",
    }
  ];

  const getLinkedVspace = async (vSpaceParams1: any) => {
    setLoading(true);
    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);
    if (response) {
      type === "Operation" ? setDataToTable(response.operation) : setDataToTable(response.project);
      setTotalRows(response.totalCount);
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    vSpaceParams.limit = perPage;
    vSpaceParams.page = page;
    getLinkedVspace(vSpaceParams);
  };

  const handlePerRowsChange = async (newPerPage: number, page: number) => {
    vSpaceParams.limit = newPerPage;
    vSpaceParams.page = page;
    setLoading(true);
    const response = await apiService.get(`stats/get${type}WithVspace/${id}`, vSpaceParams);
    if (response) {
      type === "Operation" ? setDataToTable(response.operation) : setDataToTable(response.project);
      setPerPage(newPerPage);
      setLoading(false);
    }
  };

  useEffect(() => {
    getLinkedVspace(vSpaceParams);
  }, []);



  return (
    <div>
      <RKITable
        columns={columns}
        data={tabledata}
        totalRows={totalRows}
        loading={loading}
        resetPaginationToggle={resetPaginationToggle}
        handlePerRowsChange={handlePerRowsChange}
        handlePageChange={handlePageChange}
      />
    </div>
  );
}

export default VspaceTable;
