//Import Library
import React from 'react'
import DataTable  from 'react-data-table-component';

//Import services/components
import { useTranslation } from 'next-i18next';
import CustomLoader from './CustomLoader';

interface RKITableProps {
  columns: any[];
  data: any[];
  totalRows?: number;
  resetPaginationToggle?: boolean;
  subheader?: boolean;
  subHeaderComponent?: React.ReactNode;
  handlePerRowsChange?: (newPerPage: number, page: number) => void;
  handlePageChange?: (page: number) => void;
  rowsPerPage?: number[];
  defaultRowsPerPage?: number;
  selectableRows?: boolean;
  loading?: boolean;
  pagServer?: boolean;
  onSelectedRowsChange?: (selectedRows: any) => void;
  clearSelectedRows?: boolean;
  sortServer?: boolean;
  onSort?: (column: any, sortDirection: string) => void;
  persistTableHead?: boolean;
  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];
  noHeader?: boolean;
  dense?: boolean;
  pagination?: boolean;
  paginationServer?: boolean;
  paginationTotalRows?: number;
  subHeaderAlign?: string;
  [key: string]: any;
}

function RKITable(props: RKITableProps) {
  const { t } = useTranslation('common');
  const paginationComponentOptions = {
    rowsPerPageText: t('Rowsperpage'),
    };
  const {
    columns,
    data,
    totalRows,
    resetPaginationToggle,
    subheader,
    subHeaderComponent,
    handlePerRowsChange,
    handlePageChange,
    rowsPerPage,
    defaultRowsPerPage,
    selectableRows,
    loading,
    pagServer,
    onSelectedRowsChange,
    clearSelectedRows,
    sortServer,
    onSort,
    persistTableHead,
    sortFunction,
    // Filter out any unknown props that might cause DOM warnings
    ...otherProps
  } = props;

  // Only pass known DataTable props to avoid DOM warnings
  const dataTableProps = {
    paginationComponentOptions,
    noDataComponent: t("NoData"),
    noHeader: true,
    columns,
    data: data || [],
    dense: true,
    paginationResetDefaultPage: resetPaginationToggle,
    subHeader: subheader,
    progressPending: loading,
    subHeaderComponent,
    pagination: true,
    paginationServer: pagServer,
    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,
    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],
    paginationTotalRows: totalRows,
    onChangeRowsPerPage: handlePerRowsChange,
    onChangePage: handlePageChange,
    selectableRows,
    onSelectedRowsChange,
    clearSelectedRows,
    progressComponent: <CustomLoader/>,
    sortIcon: <i className="sort-icon fas fa-exchange-alt" />,
    sortServer,
    onSort,
    sortFunction,
    persistTableHead,
    className: "rki-table"
  };
  return (
    <DataTable {...dataTableProps} />
  )
};

RKITable.defaultProps = {
  subHeader: false,
  pagination: true,
  totalRows: null,
  pagServer: true,
  onSelectedRowsChange: null,
  clearSelectedRows: false,
  sortServer: false,
  persistTableHead: false
}

export default RKITable;

