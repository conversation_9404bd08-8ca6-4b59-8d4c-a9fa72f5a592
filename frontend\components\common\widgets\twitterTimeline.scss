//Import services/components
@import "../../../styles/variables";

.twitter__timeline {
  z-index: 101;
  position: relative;
  background-color: #fff;
  height: 44vh;
  overflow-y: auto;

  .timeline-InformationCircle-widgetParent {
    position: relative;
  }
  .timeline-Header {
    background-color: $sidebar-bg;
    padding: 10px;
    h1 {
      margin: 0;
      padding: 0;
      list-style: none;
      border: none;
    }
    .timeline-Header-title {
      font-size: 21px;
      font-weight: 300;
      line-height: 24px;
      color: #fff;
    }
    .u-inlineBlock {
      display: inline-block !important;
      max-width: 100%;
    }
    .timeline-Header-byline {
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      color: #fff;
    }
    a {
      color: #fff;
      text-decoration: none;
    }
    a:visited {
      color: #fff;
      text-decoration: none;
      outline: 0;
    }
  }
}
