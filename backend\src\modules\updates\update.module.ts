//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { UpdateController } from './update.controller';
import { UpdateService } from './update.service';
import { ImageService } from './../image/image.service';
import { FilesService } from './../files/files.service';
// SCHEMAS
import { UpdateSchema } from '../../schemas/update.schemas';
import { ImageSchema } from '../../schemas/image.schemas';
import { FilesSchema } from '../../schemas/files.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Update', schema: UpdateSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Files', schema: FilesSchema }
    ])
  ],
  controllers: [UpdateController],
  providers: [UpdateService, ImageService, FilesService],
})

export class UpdateModule { }