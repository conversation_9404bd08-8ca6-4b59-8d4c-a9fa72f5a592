//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { SyndromeInterface } from '../../interfaces/syndrome.interface';
import { CreateSyndromeDto } from './dto/create-syndrome.dto';
import { UpdateSyndromeDto } from './dto/update-syndrome.dto';
const FindSyndrome = 'Could not find Syndrome.'
@Injectable()
export class SyndromeService {
  constructor(
    @InjectModel('Syndrome') private syndromeModel: Model<SyndromeInterface>
  ) { }

  async create(createSyndromeDto: CreateSyndromeDto): Promise<SyndromeInterface> {
    const createdSyndrome = new this.syndromeModel(createSyndromeDto);
    return createdSyndrome.save();
  }

  async findAll(query): Promise<SyndromeInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.syndromeModel.paginate(_filter, options);
  }

  async get(syndromeId): Promise<SyndromeInterface[]> {
    let syndrome;
    try {
      syndrome = await this.syndromeModel.findById(syndromeId).exec();
    } catch (error) {
      throw new NotFoundException(FindSyndrome);
    }
    if (!syndrome) {
      throw new NotFoundException(FindSyndrome);
    }
    return syndrome;
  }

  async update(syndromeId: any, updateSyndromeDto: UpdateSyndromeDto) {
    const getSyndromeById: any = await this.syndromeModel.findById(syndromeId).exec();
    const updatedSyndrome = new this.syndromeModel(updateSyndromeDto);
    if (getSyndromeById.title) {
      getSyndromeById.title = updatedSyndrome.title;
    }
    getSyndromeById.updatedAt = new Date();
    getSyndromeById.save();
    return getSyndromeById;
  }

  async delete(syndromeId: string) {
    const result = await this.syndromeModel.deleteOne({ _id: syndromeId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindSyndrome);
    }
  }
}
