module.exports = {
  async up(db, client) {

    const jsonData = require('./missing-hazardtypes.json');
    const dbHazards = await db.collection('hazards').aggregate().toArray();
    const dbHazardTypes = await db.collection('hazardtypes').aggregate().toArray();

    async function updateHazardData(hazardData, type) {
      return await db.collection('hazards').updateOne({ title_en: hazardData.title_en }, { $set: { hazard_type: type._id } });
    }

    async function reccUpdate(_array, _count, _res, _rej) {
      if (_count < _array.length) {
        const ele = _array[_count]
        const matchedData = dbHazards.filter(x => x.title_en == ele.hazard);
        const matchedHazardType = dbHazardTypes.filter(x => x.title == ele.type);
        if (matchedData && matchedHazardType && matchedData.length > 0 && matchedHazardType.length > 0) {
          const res = await updateHazardData(matchedData[0], matchedHazardType[0]);
          if (res.acknowledged) {
            reccUpdate(_array, _count + 1, _res, _rej)
          } else {
            _rej("unable to update hazard")
          }
        }
      } else {
        _res(true);
      }
    }

    return new Promise(function (res, rej) {
      reccUpdate(jsonData, 0, res, rej)
    })

  },

  async down(db, client) {

    const jsonData = require('./missing-hazardtypes.json');
    const dbHazards = await db.collection('hazards').aggregate().toArray();

    async function updateHazardData(hazardData) {
      return await db.collection('hazards').updateOne({ title_en: hazardData.title_en }, { $set: { hazard_type: null } });
    }

    async function reccUpdate(_array, _count, _res, _rej) {
      if (_count < _array.length) {
        const ele = _array[_count]
        const matchedData = dbHazards.filter(x => x.title_en == ele.hazard);
        if (matchedData && matchedData.length > 0) {
          const res = await updateHazardData(matchedData[0])
          if (res.acknowledged) {
            reccUpdate(_array, _count + 1, _res, _rej)
          } else {
            _rej("unable to update hazard")
          }
        }
      } else {
        _res(true);
      }
    }

    return new Promise(function (res, rej) {
      reccUpdate(jsonData, 0, res, rej)
    })
  }
};
