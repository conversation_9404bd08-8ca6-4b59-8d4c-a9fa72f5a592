//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as fs from 'fs';
const mv = require('mv');

//Import services/components
import { ImageInterface } from '../../interfaces/image.interface';
import { CreateImageDto } from './dto/create-image.dto';
import { UpdateImageDto } from './dto/update-image.dto';

const FindImage='Could not find Image.'
@Injectable()
export class ImageService {
  constructor(
    @InjectModel('Image') private imageModel: Model<ImageInterface>
  ) { }

  async create(createImageDto: CreateImageDto): Promise<ImageInterface> {
    const createdImage = new this.imageModel(createImageDto);
    return createdImage.save();
  }

  async bulkCreate(createImageDto: Array<CreateImageDto>): Promise<ImageInterface[]> {
    const createdImages = this.imageModel.insertMany(createImageDto);
    return createdImages;
  }

  async findAll(query): Promise<ImageInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.imageModel.paginate(_filter, options);
  }

  async get(imageId): Promise<ImageInterface[]> {
    let _result;
    try {
      _result = await this.imageModel.findById(imageId).exec();
    } catch (error) {
      throw new NotFoundException(FindImage);
    }
    if (!_result) {
      throw new NotFoundException(FindImage);
    }
    return _result;
  }

  async update(imageId: any, updateImageDto: UpdateImageDto) {
    const getById: any = await this.imageModel.findById(imageId).exec();
    const updatedData = new this.imageModel(updateImageDto);
    try {
      Object.keys(updateImageDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Image.');
    }
    return getById;
  }

  async bulkUpdate(imageIds) {
    const foundData = await this.imageModel.find({ _id: { $in: imageIds } });

    if (foundData && foundData.length > 0) {
      foundData.forEach((data) => {
        if (fs.existsSync(`${process.cwd()}/temp/${data.name}`)) {
          mv(
            `${process.cwd()}/temp/${data.name}`,
            `${process.cwd()}/upload/${data.name}`,
            function (err) {
              // done. it tried fs.rename first, and then falls back to
              // piping the source file to the dest file and then unlinking
              // the source file.
            },
          );
        }
        data.is_temp = false;
        data.save();
      });
    }
    return foundData;
  }



  async delete(imageId: string) {
    const result: any = await this.imageModel.findOneAndRemove({ _id: imageId }).exec();
    if (!result.is_temp && fs.existsSync(`${process.cwd()}/upload/${result.name}`)) {
      fs.unlinkSync(`${process.cwd()}/upload/${result.name}`);
    }
    if (result.is_temp && fs.existsSync(`./temp/${result.name}`)) {
      fs.unlinkSync(`${process.cwd()}/temp/${result.name}`);
    }
    if (result.n === 0) {
      throw new NotFoundException(FindImage);
    }
    return result;
  }

  async bulkDelete(imageIds: Array<string>) {
    const results = await this.imageModel.find({ _id: { $in: imageIds } }).exec();
    if (results && results.length > 0) {
      results.forEach((result) => {
        if (!result.is_temp && fs.existsSync(`${process.cwd()}/upload/${result.name}`)) {
          fs.unlinkSync(`${process.cwd()}/upload/${result.name}`);
        }
        if (result.is_temp && fs.existsSync(`./temp/${result.name}`)) {
          fs.unlinkSync(`${process.cwd()}/temp/${result.name}`);
        }
        result.remove();
      });
    }

    return results;
  }
}
