//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ExpertiseSeederService } from './expertise-seeder.services';
// SCHEMAS
import { ExpertiseSchema } from 'src/schemas/expertise.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Expertise', schema: ExpertiseSchema }
      ]
    )
  ],
  providers: [ExpertiseSeederService],
  exports: [ExpertiseSeederService],
})

export class ExpertiseSeederModule { }
