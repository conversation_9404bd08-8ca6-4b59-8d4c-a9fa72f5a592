//Import Library
import React, { useEffect, useState } from 'react';
import { Container, Row } from "react-bootstrap";
import Col from "react-bootstrap/Col";
import { useRouter } from "next/router";

//Import services/components
import _ from 'lodash';
import AnnouncementItem from "./AnnouncementItem";
import Carousel from 'react-bootstrap/Carousel'
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';


//TODO: Need to use RKISingleItemCarousel component to reuse our exisiting component
interface ListOfAnnouncementItemProps {
  announcements: any[][];
}

function ListOfAnnouncementItem(props: ListOfAnnouncementItemProps) {
  const { announcements } = props;
  return (
    <div>
      {announcements.map((item, index) => {
        return (
          <Row className="announcementItem" key={index}>
            <AnnouncementItem item={item} />
          </Row>
        )
      })}
    </div>
  )
}

function Announcement() {
  const { t } = useTranslation('common');
  const router = useRouter();
  const routes: any = router.query.routes || [];
  const [announcements, setAnnouncements] = useState<any[][]>([]);

  const [cindex, setCindex] = useState(0);

  const [carouselItemCount] = useState(3);

  const setEmptyNotice = () => {
    setAnnouncements([]);
  };

  const updatesParams = {
    query: { show_as_announcement: true, parent_vspace: routes[1] },
    sort: { created_at: "desc" },
    limit: "~",
    select: "-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at"
  };

  const fetchAnnouncements = async (params = updatesParams) => {
    const response = await apiService.get('/updates', params);
    if (response && response.data && response.data.length > 0) {
      const partition = _.chunk(response.data, 3);
      setAnnouncements(partition)
    } else {
      setEmptyNotice()
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [])

  const toggleCarousel = (direction: 'next' | 'prev') => {
    let index = cindex;
    const [min, max] = [0, carouselItemCount - 1];

    if (direction === 'next') {
      index++
    }
    else if (direction === 'prev') {
      index--
    }

    if (index > max) {
      index = 0
    }

    if (index < min) {
      index = max
    }


    if ((announcements.length - index) === 1) {
      index = announcements.length - 1;
    }

    if ((announcements.length - index) === 0) {
      index = 1;
    }
    setCindex(index);
  };

  return (
    <div className="announcements mt-0">
      {announcements && announcements.length > 0 ? (
        <>
          <Container fluid>
            <Row>
              <Col xs={10} className="p-0">
              </Col>
              {announcements && announcements.length > 1 ?
                <Col xs={2} className="text-end carousel-control p-0">
                  <div className="carousel-navigation">
                    <a className="left carousel-control" onClick={() => toggleCarousel('prev')}>
                      <i className="fa fa-chevron-left" />
                    </a>
                    <a className="right carousel-control" onClick={() => toggleCarousel('next')}>
                      <i className="fa fa-chevron-right" />
                    </a>
                  </div>
                </Col>
                : null}
            </Row>
          </Container>
          <Container fluid>
            <Row>
              <Col xs={12} className="p-0">
                <Carousel indicators={false} controls={false} interval={null} activeIndex={cindex}>
                  {announcements.map((item, index) => {
                    return (
                      <Carousel.Item key={index}>
                        <ListOfAnnouncementItem announcements={item} />
                      </Carousel.Item>
                    )
                  })}
                </Carousel>
              </Col>
            </Row>
          </Container>
        </>
      ) : (
          <Container fluid={true}>
            <Row>
              <Col xs={12} className="p-0">
                <div className="border border-info m-3">
                <p className="d-flex d-flex justify-content-center p-2 m-0">{t("NoAnnouncementFound!")}</p>
                </div>
              </Col>
            </Row>
          </Container>
        )}
    </div>
  )
}

export default Announcement;