//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { CountryController } from './country.controller';
import { CountryService } from './country.service';
import { ProjectService } from "../project/project.service";
import { InstitutionService } from "../institution/institution.service";
import { EmailService } from './../../email.service';
import { UsersService } from './../../users/users.service';
import { RolesService } from '../roles/roles.service';
import { FlagService } from '../flag/flag.service';
import { VspaceService } from '../vspace/vspace.service';
import { UpdateService } from '../updates/update.service';
// SCHEMAS
import { CountrySchema } from '../../schemas/country.schemas';
import { UsersSchema } from '../../schemas/users.schemas';
import { ImageSchema } from '../../schemas/image.schemas';
import { ProjectSchema } from '../../schemas/project.schemas';
import { RolesSchema } from '../../schemas/roles.schemas';
import { FlagSchema } from "../../schemas/flag.schemas";
import { InstitutionSchema } from 'src/schemas/institution.schemas';
import { VspaceSchema } from 'src/schemas/vspace.schemas';
import { UpdateSchema } from 'src/schemas/update.schemas';
import { OperationSchema } from 'src/schemas/operation.schemas';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Country', schema: CountrySchema },
      { name: 'Institution', schema: InstitutionSchema },
      { name: 'Update', schema: UpdateSchema},
      { name: 'Project', schema: ProjectSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Users', schema: UsersSchema },
      { name: 'Roles', schema: RolesSchema },
      { name: 'Flag', schema: FlagSchema},
      { name: 'Operation', schema: OperationSchema },
      { name: 'Vspace', schema: VspaceSchema}
    ]),HttpModule
  ],
  controllers: [CountryController],
  providers: [CountryService, ProjectService , InstitutionService, EmailService, UsersService, RolesService, FlagService, VspaceService, UpdateService],
})

export class CountryModule { }