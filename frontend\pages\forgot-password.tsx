//Import Library
import React, {useEffect, useState} from 'react';
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";
import {
  faCheckCircle,
  faArrowCircleLeft
} from "@fortawesome/free-solid-svg-icons";
import toast from 'react-hot-toast';
import Link from "next/link";
import { connect } from "react-redux";

//Import services/components
import apiService from "../services/apiService";
import authService from "../services/authService";
import { Iuser } from '../shared/interfaces/user.interface';
import { getItem, getUser } from "../shared/services/local-storage"

const responseMessage = {
  "LOGIN.EMAIL_RESENT": "Password reset successfully. Please check email",
  "REGISTRATION.ERROR.MAIL_NOT_SENT": "Error resetting password. Unable to find account",
  "LOGIN.ERROR.SEND_EMAIL": "You have entered a invalid e-mail",
  "LOGIN.USER_NOT_FOUND": "Error Resetting Password. Please enter validate E-mail",
  "REGISTER.USER_NOT_REGISTERED": "Unable to reset password. Contact Administrator",
  "LOGIN.ERROR.GENERIC_ERROR": "Unable to reset password. Contact Administrator"
};

const ForgetPassword = (props) => {
  const initUser: Iuser = { email: "", username: "" };
  const [user, setUser] = useState(initUser);

  const [successPage, setSuccessPage] = useState(true);
  const [userInfo, setUserInfo] = useState("")
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleChange = (e) => {
    setUserInfo(e.target.value)
  }

   const getresponse = async (email) =>{
    const response = await apiService.get(`/email/forgot-password/${email}`);
    if (response && response.success) {
      toast.success(responseMessage[response.message]);
      setSuccessPage(false);
      if (user && user.email) {
        const { logout } = authService;
        await logout();
        setIsLoggedIn(false);
      }
    } else {
      if (response.data && response.data.message) {
        toast.error(responseMessage[response.message]);
      } else {
        toast.error("Error Resetting Password. Please enter validate E-mail");
      }
    }
   }


  const submitHandler = async (e) => {
    e.preventDefault();
    let email = userInfo;
    if (user && user.email) {
      email = user.email;
    }
    if (email) {

      getresponse(email)

    } else {
      toast.error("Error reseting password. Contact administrator");
    }
  }

  useEffect(() => {
    const user = getUser();
    if (user && user.username) {
      setUser(user)
      setIsLoggedIn(true)
    }
  },[])

  return (
    <div className="loginContainer ">
      <div className='section'>
        <div className='container'>
          <div className='columns'>
            <div className='column  is-two-thirds'>
              <div className='column loginForm'>
                <div className="imgBanner">
                  <img src="/images/login-banner.jpg" alt="RKI Login Banner Image"/>
                </div>
                <form className="formContainer" onSubmit={submitHandler}>
                  <div className="logoContainer">
                    <Link href={'/'}>

                      <img src="/images/logo.jpg" alt="Rohert Koch Institut - Logo"/>

                    </Link>
                  </div>
                  {!isLoggedIn ? (<>
                    {success_func()
                    }
                  </>):(<div>
                    <section>
                      <p>Password reset instructions will be mailed to {user.email}. You must log out to use the password reset link in the email.</p>
                      <div className='field is-grouped'>
                        <div className='control'>
                          <button className='button is-primary' type='submit'>
                            Reset Password
                          </button>
                        </div>
                      </div>
                    </section>
                  </div>)}
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  function success_func() {
    return successPage ? (<div>
      <div className='mb-3'>
        <label className='label'>Enter your email id</label>
        <input
          className='form-control'
          type='text'
          name='username'
          onChange={handleChange}
          required />
      </div>
      <div className='field is-grouped'>
        <div className='control'>
          <button className='button is-primary' type='submit'>
            Reset Password
          </button>
        </div>
      </div>
    </div>) : (<div className="d-flex flex-column justify-content-center align-items-center">
      <div>
        <FontAwesomeIcon icon={faCheckCircle} color="#1a273a" size="5x" className="success-icon" />
      </div>
      <p className="text-center lead mt-3 infotext">Your one time password reset link is sent to your
        email. Please use that and reset your password.</p>
      <Link href="/home" as="/home" >
        <button className="button is-primary"><FontAwesomeIcon icon={faArrowCircleLeft} color="#ffff"
          size="1x" /> Back to RKI Home
        </button>
      </Link>
    </div>);
  }
}

export default connect((state) => state)(ForgetPassword);