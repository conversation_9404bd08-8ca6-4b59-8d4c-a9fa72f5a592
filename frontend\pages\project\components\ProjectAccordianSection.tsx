//Import Library
import React from "react";
import { Accordion, Col, Row } from "react-bootstrap";

//Import services/components
import { canViewDiscussionUpdate } from "../permission";
import ProjectDetailsAccordion from "./ProjectDetailsAccordion";
import VirtualSpaceAccordion from "./VirtualSpaceAccordion";
import DiscussionAccordion from "./DiscussionAccordion";


interface ProjectAccordianSectionProps {
  projectData: any;
  routeData: {
    routes: string[];
  };
  editAccess: boolean;
}

const ProjectAccordianSection = (props: ProjectAccordianSectionProps) => {

    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionAccordion routeData = {props.routeData} />)

    return (
        <>
            <Row>
                <Col className="projectAccordion" xs={12}>
                    <Accordion>
                        <ProjectDetailsAccordion project = {props.projectData} />
                    </Accordion>
                    <Accordion>
                        <CanViewDiscussionUpdate />
                    </Accordion>
                    <Accordion>
                        <VirtualSpaceAccordion routeData = {props.routeData} />
                    </Accordion>
                </Col>
            </Row>
        </>
    )
}

export default ProjectAccordianSection;