<p align="center">
  <img src="backend/src/assets/img/rki-logo.jpg" alt="RKI" />
</p>

The Robert Koch Institut is a German federal government agency and research institute responsible for disease control and prevention. It is located in Berlin and Wernigerode. As an upper federal agency, it is subordinate to the Federal Ministry of Health

### Requirements
```bash
Nginx 1.22 or later (Install default method)
Mongo 4.2.24 or later (Install default method)
Node 16.13.1
```
## Installation and application setup process in Linux Machine

### Nginx installation
```bash
sudo yum install epel-release
sudo yum update
sudo yum install nginx
sudo nginx -v

sudo systemctl start nginx.service
sudo systemctl enable nginx.service
sudo systemctl status nginx.service
```
### Nginx configuration
```bash
sudo nano /etc/nginx/conf.d/<FILENAME>.conf
```

### Copy the configuration and save it as per requirement
```bash
server {
    server_name <SITE URL>;
    client_max_body_size 30M;

#    location / {
#        root   /usr/share/nginx/html;
#        index  index.html index.htm;
#    }

    location / {
        proxy_pass http://localhost:3000;
        proxy_read_timeout 120;
    }

#    auth_basic "Restricted Access";
#    auth_basic_user_file /etc/nginx/htpasswd.users;

    server_tokens off;
#    ssl_protocols       TLSv1.2;


    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version  1.1;
        proxy_read_timeout 120;
        proxy_cache_bypass  $http_upgrade;
        proxy_set_header Upgrade    $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP  $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  $scheme;
        proxy_set_header X-Forwarded-Host   $host;
        proxy_set_header X-Forwarded-Port   $server_port;
    }
}
```
### To check the nginx configuration syntax status
```bash
sudo nginx -t
```
### Mongo installation
```bash
sudo tee /etc/yum.repos.d/mongodb-org-4.2.repo<<EOL
[mongodb-org-4.2]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/amazon/2/mongodb-org/4.2/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-4.2.asc
EOL

sudo yum install -y mongodb-org
systemctl start mongod.service
systemctl enable mongod.service
systemctl status mongod.service
```
### Login to mongo
```bash 
sudo mongo
```
### To list the databases
```bash 
show dbs;
```
### To create database
```bash 
use <DATABASE NAME>
```
### To create User for the database access
```bash 
db.createUser(
   {
     user: '<USERNAME>',
     pwd: '<PASSWORD>',
     roles: [ { role: 'readWrite', db: '<DATABASE NAME>' } ],
     mechanisms: [ 'SCRAM-SHA-256'],
   }
 );
 ```
### To add existing mongo database
```bash 
mongorestore -d <DATABASE NAME> --username <USERNAME> --password <PASSWORD> <PATH OF DATABASE FILE>
```
### Node installation
```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash
source ~/.bashrc
nvm ls
nvm ls-remote --lts
(Install node version as per the requirement.)
nvm install 16.13.1
nvm alias default 16.13.1
node -v
```
### PM2 installation steps
```bash
npm install -g pm2
pm2 ls
```
### Application Configuration
```bash
git clone <REPO URL>
cd <REPO NAME>
cd backend
(Rename .env file to .env.default and change the settings as per your requirement.)
npm install
npm install -g rimraf
npm install rimraf
npm run build
pm2 start dist/main.js --name rki-backend -x -- --prod
cd ..

cd frontend
(Rename .env file to .env.default and change the settings as per your requirement.)
npm install
npm install -g next
npm install next
npm run dev
npm run build
pm2 start dist/server.js --name rki-frontend -x -- --prod

#update 17 July 2024 https://rki-dev.adapptlabs.com/
npm run build
pm2 start npm --name rki-frontend -- run start

pm2 startup
(Copy the path and run the cmd, refer below cmd.)
sudo env PATH=$PATH:/home/<USER>/.nvm/versions/node/v16.13.1/bin /home/<USER>/.nvm/versions/node/v16.13.1/lib/node_modules/pm2/bin/pm2 startup systemd -u ec2-user --hp /home/<USER>
pm2 save

pm2 restart rki-backend
pm2 restart rki-frontend
```
