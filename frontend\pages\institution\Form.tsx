//Import Library
import React, { useState, useRef, useEffect } from "react";
import Router, { useRouter } from "next/router";
import _ from "lodash";
import Link from "next/link";
import { MultiSelect } from "react-multi-select-component";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col, Tabs, Tab, InputGroup } from "react-bootstrap";
import { TextInput, SelectGroup } from "../../components/common/FormikTextInput";
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";

import toast from 'react-hot-toast';

//Import services/components
import ReactDropZone from "../../components/common/ReactDropZone";
import apiService from "../../services/apiService";
import invitationService from "../../services/invitation.service";
import { useTranslation } from 'next-i18next';
import InstitutionImageHandler from "./InstitutionImageHandler";
import { EditorComponent } from "../../shared/quill-editor/quill-editor.component"

interface InstitutionFormProps {
  routes: string[];
  institution: any;
}

const InstitutionForm = ({ routes, institution }: InstitutionFormProps) => {
    const buttonRef = useRef(null);
    const formRef = useRef(null);
    const { t, i18n } = useTranslation("common");
    const _initialState: any = {
        organisationName: "",
        title: "",
        country: "",
        world_region: "",
        website: "",
        organisationType: null,
        telephone: "",
        dial_code: t("DialCode"),
        contact_name: "",
        twitter: "",
        addressL1: "",
        addressL2: "",
        city: "",
        region: [],
        users: [],
        institution: "",
        expertise: [],
        network: [],
        hazards: [],
        description: "",
        images: [],
        header: null,
        department: "",
        unit: "",
        use_default_header: true,
        images_src: [],
    };
    const { query } = useRouter();
    const currentLang = i18n.language;
    const titleSearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };

    const [countryList, setcountryList]: any = useState([]);
    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);
    const [srcCollection, setSrcCollection] = useState<any[]>([]);
    const [headerDropCollection, setHeaderDropCollection] = useState<any>(null);
    const [hazards, setHazards] = useState<any[]>([]);
    const [expertise, setExpertise] = useState<any[]>([]);
    const [network, setNetwork] = useState<any[]>([]);
    const [regions, setRegion] = useState<any[]>([]);
    const [usersList, setUsersList] = useState<any[]>([]);
    const [defaultActiveKey, setdefaultActiveKey] = useState<number>(1);
    const [, setHazardInfiniteList] = useState<any[]>([]);
    const [tab, setTab] = useState<any[]>([
        {
            username: "",
            email: "",
            institution: "",
            focal_dial_code: t("DialCode"),
            mobile_number: "",
            _id: null,
        },
    ]);
    const [organizationList, setOrganizationList] = useState<any[]>([]);
    const [expertiseList, setexpertiseList] = useState<any[]>([]);
    const [networkList, setnetworkList] = useState<any[]>([]);
    const [hazardList, sethazardList] = useState<any[]>([]);
    const editform: boolean = routes && routes[0] == "edit" && !!routes[1];
    const [initialVal, setinitialVal] = useState<any>(_initialState);
    const [existingFpUsers, setExistingFpUsers] = useState<any[]>([]);
    const [partners, setPartners] = useState<any[]>([]);

    const institutionParams = {
        query: {},
        sort: { title: "asc" },
        limit: "~",
    };

    const partnersParams = {
        query: {},
        sort: { title: "asc" },
        select: "-networks -expertise -focal_points -hazard_types -description -twitter -telephone -images -header -partners -type -user -website -hazards -contact_name -updated_at -created_at -__v",
        limit: "~",
        lean: true,
    };

    /***For modal Show & Hide */
    /***End */

    const filterOptions = (options: any[], filter: string) => {
        if (!filter) {
            return options;
        }
        const regex = new RegExp(`^${filter}`, "gi");
        return options.filter(({ label }: { label: string }) => label && label.match(regex));
    };
    useEffect(() => {
        /* Start Update data for form */
        if (routes[0] == "edit" && institution) {
            const response = institution;
            if (response) {
                response_institution_Func(response);
                const currentLang = i18n.language === "fr" ? "en" : i18n.language;
                const { _expertise, _network, _partners, _hazards } = response_methods_func(response, currentLang);
                getRegion(response.country, institutionParams); // update region based on the country
                setExpertise(_expertise);

                setNetwork(_network);
                setSelectedPartners(_partners);
                setHazards(_hazards);
                setHazardInfiniteList(_hazards);

                setDropZoneCollection(response.images ? response.images : []);
                setSrcCollection(response.images_src ? response.images_src : []);
                setHeaderDropCollection(response.header && response.header._id ? response.header._id : null);
                setinitialVal((prevState: any) => ({ ...prevState, ...response }));
            }
        }

        getHazard();
        getUsers();
        getCountries();
        getOrganization(institutionParams);
        getExpertise(institutionParams);
        getNetwork(institutionParams);
        getPartners(partnersParams);
    }, []);

    const getCountries = async () => {
        const institutionParamslist = {
            sort: titleSearch,
            limit: "~",
            select: "-code -code3 -coordinates -created_at -first_letter -health_profile -security_advice -updated_at",
            lean: true,
            languageCode: currentLang,
        };
        const response = await apiService.get("/country", institutionParamslist);
        if (response && Array.isArray(response.data)) {
            setcountryList(response.data);
        }
    };

    const setApprovedUsers = (users: any) => {
        if (users && users.data && users.data.length > 0) {
            const tempStorage: any = [];
            users.data.forEach((user: any) => {
                let foundInstituteInvites = user.institutionInvites.filter(
                    (invite: any) => invite.institutionId === routes[1] && invite.status === "Approved"
                );
                if (foundInstituteInvites.length) {
                    tempStorage.push({
                        label: user.username,
                        value: user._id,
                        email: user.email,
                    });
                }
            });
            setExistingFpUsers(tempStorage);
            setinitialVal((prevState: any) => ({
                ...prevState,
                users: tempStorage,
            }));
        }
    };

    const isUserReqPendingForInstitute = (_users: any[], currentUser: any) => {
        let result = false;
        _users.forEach((user: any) => {
            user.institutionInvites.forEach((invite: any) => {
                if (
                    invite.institutionId === routes[1] &&
                    invite.status === "Request Pending" &&
                    currentUser._id === user._id
                ) {
                    result = true;
                    return;
                }
            });
        });
        return result;
    };

    const filterUsers = (_users: any[]) => {
        let filteredUsers: any[] = [];
        _users = _users.filter((user: any) => {
            return isUserReqPendingForInstitute(_users, user) ? false : true;
        });
        _users.forEach((user: any) => {
            filteredUsers.push({ label: user.username, value: user._id });
        });
        return filteredUsers;
    };

    const getUsers = async () => {
        const institutionParamslist = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
            select: "-firstname -lastname -email -password -role -country -region -institution -is_focal_point -image -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken",
        };
        const users = await apiService.get("/users", institutionParamslist);
        if (editform) {
            setApprovedUsers(users);
        }
        if (users && Array.isArray(users.data)) {
            let _users = [];
            if (editform) {
                let _filteredUsers = filterUsers(users.data);
                users.data.forEach((user) => {
                    _users.push({ label: user.username, value: user._id });
                });
                setUsersList(_filteredUsers);
            } else {
                users.data.forEach((user) => {
                    _users.push({ label: user.username, value: user._id });
                });
                setUsersList(_users);
            }
        }
    };

    const getOrganization = async (institutionParamsinit: any) => {
        const response = await apiService.get("/InstitutionType", institutionParamsinit);
        if (response && Array.isArray(response.data)) {
            setOrganizationList(response.data);
        }
    };

    /***Get partner Organisation***/
    const getPartners = async (partnersParamsinit: any) => {
        const response = await apiService.get("/institution", partnersParamsinit);
        if (response && Array.isArray(response.data)) {
            const partner = response.data
                .filter((item: any) => item._id != query.routes[1])
                .map((item: any, _i: any) => ({ label: item.title, value: item._id }));
            setPartners(partner);
        }
    };
    /***End***/

    const getExpertise = async (institutionParamsinit: any) => {
        const response = await apiService.get("/Expertise", institutionParamsinit);
        if (response && Array.isArray(response.data)) {
            const _expertise = response.data.map((item: any, _i: any) => {
                return { label: item.title, value: item._id };
            });
            setexpertiseList(_expertise);
        }
    };

    const getNetwork = async (institutionParamsinit: any) => {
        const response = await apiService.get("/InstitutionNetwork", institutionParamsinit);
        if (response && Array.isArray(response.data)) {
            const _network = response.data.map((item: any, _i: any) => {
                return { label: item.title, value: item._id };
            });
            setnetworkList(_network);
        }
    };

    const getHazard = async () => {
        const RegionParams = {
            query: {},
            limit: "~",
            sort: { title: "asc" },
        };

        const allHazardType: any[] = [];
        const responsetype = await apiService.get("/hazardtype", RegionParams);
        if (responsetype && Array.isArray(responsetype.data)) {
            const finalHazard: any[] = [];
            _.each(responsetype.data, (item: any, _i: any) => {
                allHazardType.push(item._id);
            });
        }

        const searchParams = {
            query: { hazard_type: allHazardType },
            limit: "~",
            sort: { title: "asc" },
        };
        const response = await apiService.get("/hazard", searchParams);
        if (response && Array.isArray(response.data)) {
            const _hazard = response.data.map((item: any, _i: any) => {
                return { label: item.title[currentLang], value: item._id };
            });
            sethazardList(_hazard);
        }
    };

    const getRegion = async (id: any, institutionParamsinit: any) => {
        let _regions: any[] = [];
        if (id) {
            const response = await apiService.get(`/country_region/${id}`, institutionParamsinit);
            if (response && response.data) {
                _regions = response.data.map((item: any, _i: any) => {
                    return { label: item.title, value: item._id };
                });
                _regions.sort((a: any, b: any) => a.label.localeCompare(b.label));
            }
        }
        setRegion(_regions);
    };

    const clearValue = (obj: any) => {
        setinitialVal((prevState: any) => ({
            ...prevState,
            ...obj,
        }));
    };

    const handleChange = (e: any) => {
        const { name, value } = e.target;
        setinitialVal((prevState: any) => ({
            ...prevState,
            [name]: value,
        }));

        if (name == "country") {
            const selectedIndex = e.target.selectedIndex;
            if (e.target && selectedIndex && selectedIndex != null) {
                const worldRegion = e.target[selectedIndex].getAttribute("data-worldregion");
                setinitialVal((prevState: any) => ({
                    ...prevState,
                    world_region: worldRegion,
                }));
            }
        }

        if (name == "country") {
            getRegion(value, institutionParams);
            clearValue({ region: [] });
        }
    };

    const bindCountryRegions = (e: any, name: any) => {
        setinitialVal((prevState: any) => ({
            ...prevState,
            [name]: e,
        }));
    };

    const handleDescription = (value: any) => {
        setinitialVal((prevState: any) => ({ ...prevState, description: value }));
    };

    const handleHazard = (e: any) => {
        setHazards(e);
    };

    const [hazardExpertiseVal] = useState<string>("");
    const handleExpertise = (e: any) => {
        setExpertise(e);
    };

    const [networkVal] = useState<string>("");
    const [selctedPartners, setSelectedPartners] = useState<any[]>([]);
    const handleNetwork = (e: any) => {
        setNetwork(e);
    };

    const handlePartners = (e: any) => {
        setSelectedPartners(e);
    };

    const tabAdd = () => {
        const _temp = [...tab];
        _temp.push({
            username: "",
            email: "",
            focal_dial_code: t("DialCode"),
            mobile_number: "",
            institution: "",
            _id: null,
        });
        setTab(_temp);
        setdefaultActiveKey(_temp.length);
    };
    const removeTab = (_e: any, i: any) => {
        tab.splice(i, 1);
        const _temp = [...tab];
        setTab(_temp);
        setdefaultActiveKey(_temp.length);
        if (tab.length === 0) {
            tabAdd();
        }
    };
    const handleTab = (e: any, i: any) => {
        const _tempCosts = [...tab];
        _tempCosts[i][e.target.name] = e.target.value;
        setTab(_tempCosts);
    };

    const resetHandler = () => {
        setinitialVal(_initialState);
        setDropZoneCollection([]);
        setSrcCollection([]);
        setHeaderDropCollection(null);
        setTab([]);

        setExpertise([]);
        setNetwork([]);
        setOrganizationList([]);
        sethazardList([]);
        setHazardInfiniteList([]);
        // Reset Expertise with Hazards field
        setHazards([])
        // Reset Partners field
        setSelectedPartners([])
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };



    const inviteFpUsers = (users: any, institutionData: any) => {
        if (editform) {
            //delete invitations which are removed from dropdown
            const removedUsers = invitationService.arrayDifference(existingFpUsers, users);
            invitationService.deleteInvitations(removedUsers, routes[1]);
            // send invitations from dropdowns
            invitationService.sendInvitations(users, routes[1], institutionData.title);
        } else {
            users.forEach(async (user: any) => {
                let userId = user._id;
                let userData: any;
                if (userId) {
                    userData = await apiService.get(`/users/${userId}`);
                } else if (user.email && user.username) {
                    userData = await apiService.get(`/users`, {
                        query: {
                            email: user.email,
                            username: user.username,
                        },
                    });
                    userData = userData?.data?.length ? userData.data[0] : {};
                    userId = userData._id;
                }
                if (userData && userData._id) {
                    let newInInvites: any[] = [];
                    if (userData?.institutionInvites?.length) {
                        newInInvites = [...userData.institutionInvites];
                        let dupInvite = newInInvites.filter((invite: any) => invite.institutionId === institutionData._id);
                        if (dupInvite.length <= 0) {
                            newInInvites.push({
                                institutionName: institutionData.title,
                                institutionId: institutionData._id,
                                status: "Request Pending",
                            });
                        } else {
                            newInInvites = newInInvites.map((invite: any) => {
                                if (invite.institutionId === institutionData._id && invite.status === "Rejected") {
                                    invite.status = "Request Pending";
                                }
                                return invite;
                            });
                        }
                    } else {
                        newInInvites = [
                            {
                                institutionName: institutionData.title,
                                institutionId: institutionData._id,
                                status: "Request Pending",
                            },
                        ];
                    }
                    userData.institutionInvites = newInInvites;
                    const res = await apiService.patch(`/users/${userId}`, userData);
                }
            });
        }
    };

    const handleSubmit = async (e: any) => {
        e.preventDefault();
        const extuser = initialVal.users.map((item: any, _i: any) => {
            return { _id: item.value };
        });
        for (let i = 0; i < tab.length; i++) itertion_func(tab, i);
        const isuniqueUsername = _.uniqBy(tab, "username");
        const isuniqueEmail = _.uniqBy(tab, "email");
        if (isuniqueUsername.length != tab.length || isuniqueEmail.length != tab.length) {
            toast.error(t("toast.UniqueFocalpointIssue"));
            return;
        }

        const obj = {
            title: initialVal.title.trim(),
            contact_name: initialVal.contact_name,

            networks: network
                ? network.map((item: any, _i: any) => {
                    return item.value;
                })
                : [],
            partners: selctedPartners ? selctedPartners.map((item: any, _i: any) => item.value) : [],
            expertise: expertise
                ? expertise.map((item: any, _i: any) => {
                    return item.value;
                })
                : [],

            hazards: initialVal.hazards,

            address: {
                country: initialVal.country,
                world_region: initialVal.world_region,
                region: initialVal.region
                    ? initialVal.region.map((item: any, _i: any) => {
                        return item.value;
                    })
                    : [],
                line_1: initialVal.addressL1,
                line_2: initialVal.addressL2,
                city: initialVal.city,
            },

            focal_points: [...tab, ...extuser],
            website: initialVal.website,
            telephone: initialVal.telephone,
            dial_code: initialVal.dial_code,
            twitter: initialVal.twitter,
            type: initialVal.organisationType,
            description: initialVal.description,
            images: initialVal.images,
            header: initialVal.header,
            department: initialVal.department,
            unit: initialVal.unit,
            use_default_header: initialVal.header == null ? true : false,
            images_src: initialVal.images_src,
            primary_focal_point: initialVal.primary_focal_point,
        };

        if (hazards && hazards.length) {
            obj.hazards = hazards.map((_hazard: any) => _hazard.value);
        }

        let response: any;
        let toastMsg: string;
        if (editform) {
            if (obj?.focal_points?.length > 0) {
                if (!obj.focal_points.find((_fp: any) => _fp._id === initialVal.primary_focal_point)) {
                    obj.primary_focal_point = "";
                }
            }
            toastMsg = "toast.Organisationupdatedsuccessfully";
            response = await apiService.patch(`/institution/${routes[1]}`, obj);
        } else {
            toastMsg = "toast.Organisationaddedsuccessfully";
            response = await apiService.post("/institution", obj);
        }
        if (response && response._id) {
            obj.focal_points?.length ? inviteFpUsers(obj.focal_points, response) : "";
            {
                t("addOrganisation");
            }
            toast.success(t(toastMsg))
            Router.push("/institution/[...routes]", `/institution/show/${response._id}`);
        } else {
            if (editform) { toastMsg = "toast.OrganisationNotupdatedsuccessfully";}
            else { toastMsg = "toast.OrganisationNotaddedsuccessfully"; }

            if (response == 'toast.usernameoremailusedalready') {
                toastMsg = "toast.OrganisationNameShouldUnique";
            }
            toast.error(t(toastMsg));
        }
    };



    const getID = (id: any, index: any) => {
        let imageData: any = {};
        const filterIndex0 = id.filter((item: any) => item.index == index).map((item: any) => item.serverID);
        switch (index) {
            case 0:
                imageData = { header: String(filterIndex0) };
                break;
            case 1:
                /*****It's for Focal Point ******/
                break;
            case 2:
                imageData = { images: filterIndex0 };
                break;
        }
        setinitialVal((prevState: any) => ({ ...prevState, ...imageData }));
    };

    const getSource = (imgSrcArr: any) => {
        setinitialVal((prevState: any) => ({ ...prevState, images_src: imgSrcArr }));
    };

    //Get Id, for sending the server
    const getIdHandler = (id: any) => {
        setinitialVal((prevState: any) => ({
            ...prevState,
            header: id,
            use_default_header: id == null ? true : false,
        }));
    };
    return (
        <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
            <Container className="formCard" fluid>
                <Card>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>
                                    {routes[0] === "edit" ? t("editOrganisation") : t("addOrganisation")}
                                </Card.Title>
                            </Col>
                        </Row>
                        {/* <hr /> */}
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("OrganisationName")}</Form.Label>
                                    <TextInput
                                        name="title"
                                        id="title"
                                        required={true}
                                        value={initialVal.title}
                                        onChange={handleChange}
                                        validator={(value: any) => value.trim() != ""}
                                        errorMessage={{
                                            validator: t("PleaseAddtheOrganisationName"),
                                        }}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label> {t("Description")}</Form.Label>
                                    <EditorComponent initContent={initialVal.description} onChange={(evt: any) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="d-inline switch-right">
                                        {t("HeaderImage")}
                                        <Form.Check
                                            className="ms-2 d-inline institution_switch"
                                            type="switch"
                                            checked={initialVal.use_default_header}
                                            id="custom-switch"
                                            label={t("UseDefaultHeader")}
                                        />
                                    </Form.Label>
                                    <InstitutionImageHandler
                                        getId={(id: any) => getIdHandler(id)}
                                        header={headerDropCollection}
                                        type={"image"}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col lg={4} sm={6}>
                                <Form.Group>
                                    <Form.Label>{t("Website")}</Form.Label>
                                    <TextInput
                                        name="website"
                                        id="website"
                                        value={initialVal.website}
                                        pattern="^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+$"
                                        errorMessage={{
                                            pattern: t("Pleaseentervalidwebsite"),
                                        }}
                                        onChange={handleChange}
                                        required={false}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                    />
                                </Form.Group>
                            </Col>

                            <Col lg={4} sm={6}>
                                <InputGroup className="row ms-0 me-0">
                                    <Form.Group className="institutionDialCode col-5 col-lg-4 ps-0 pe-0">
                                        <Form.Label>{t("Telephone")}</Form.Label>
                                        <SelectGroup
                                            type="numbers"
                                            name="dial_code"
                                            id="dialCode"
                                            value={initialVal.dial_code}
                                            onChange={handleChange}
                                            required={false}
                                            errorMessage=""
                                        >
                                            <option value={initialVal.dial_code}>{initialVal.dial_code}</option>
                                            {countryList.map((citem: any, _i: any) => {
                                                return (
                                                    <option
                                                        key={_i}
                                                        value={citem.dial_code}
                                                    >{`(${citem.dial_code}) ${citem.title}`}</option>
                                                );
                                            })}
                                        </SelectGroup>
                                    </Form.Group>

                                    <Form.Group className="institutionTelephone col-7 col-lg-8 pe-0">
                                        <Form.Label>&nbsp;</Form.Label>
                                        <TextInput
                                            name="telephone"
                                            id="telephone"
                                            value={initialVal.telephone}
                                            onChange={handleChange}
                                            required={false}
                                            as="input"
                                            multiline={false}
                                            rows={1}
                                            errorMessage={{}}
                                        />
                                    </Form.Group>
                                </InputGroup>
                            </Col>

                            <Col lg={4} sm={6}>
                                <Form.Group>
                                    <Form.Label>{t("Twitter")}</Form.Label>
                                    <TextInput
                                        name="twitter"
                                        id="twitter"
                                        value={initialVal.twitter}
                                        onChange={handleChange}
                                        required={false}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                        errorMessage={{}}
                                        validator={() => true}
                                        pattern=""
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Card.Text>
                            <b>{t("Address")}</b>
                            {/* <hr /> */}
                        </Card.Text>
                        <Row className="mb-3">
                            <Col lg md={2}>
                                <Form.Group>
                                    <Form.Label>{t("Address1")}</Form.Label>
                                    <TextInput
                                        name="addressL1"
                                        id="addressL1"
                                        value={initialVal.addressL1}
                                        onChange={handleChange}
                                        required={false}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                        errorMessage={{}}
                                        validator={() => true}
                                        pattern=""
                                    />
                                </Form.Group>
                            </Col>
                            <Col lg md={2}>
                                <Form.Group>
                                    <Form.Label>{t("Address2")}</Form.Label>
                                    <TextInput
                                        name="addressL2"
                                        id="addressL2"
                                        value={initialVal.addressL2}
                                        onChange={handleChange}
                                        required={false}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                        errorMessage={{}}
                                        validator={() => true}
                                        pattern=""
                                    />
                                </Form.Group>
                            </Col>
                            <Col lg md={2}>
                                <Form.Group>
                                    <Form.Label>{t("City")}</Form.Label>
                                    <TextInput
                                        name="city"
                                        id="city"
                                        value={initialVal.city}
                                        onChange={handleChange}
                                        required={false}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                        errorMessage={{}}
                                        validator={() => true}
                                        pattern=""
                                    />
                                </Form.Group>
                            </Col>
                            <Col lg md={3}>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("Country")}</Form.Label>
                                    <SelectGroup
                                        name="country"
                                        id="country"
                                        value={initialVal.country}
                                        onChange={handleChange}
                                        errorMessage={t("thisfieldisrequired")}
                                        required
                                    >
                                        <option value="">{t("SelectCountry")}</option>
                                        {countryList.map((item: any, i: any) => {
                                            return (
                                                <option data-worldregion={item.world_region} key={i} value={item._id}>
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col lg md={3}>
                                <Form.Group style={{ maxWidth: "250px" }}>
                                    <Form.Label>{t("CountryRegions")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectRegions"),
                                            allItemsAreSelected: "All Regions are Selected",
                                        }}
                                        options={regions}
                                        value={initialVal.region}
                                        className={"region"}
                                        onChange={(e: any) => bindCountryRegions(e, "region")}
                                        labelledBy={t("SelectRegions")}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Card.Text>
                            <b>{t("FocalPoints")}</b>
                        </Card.Text>
                        <hr />
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Tabs
                                        activeKey={defaultActiveKey}
                                        onSelect={(k: any) => setdefaultActiveKey(k)}
                                        id="uncontrolled-tab-example"
                                    >
                                        <Tab
                                            eventKey={""}
                                            title={
                                                <div>
                                                    <p>{t("ExistingUser")}</p>
                                                </div>
                                            }
                                        >
                                            <Row >
                                                <Col md lg={4}>
                                                    <Form.Group style={{ paddingTop: "20px" }}>
                                                        <Form.Label>{t("ExistingUser")}</Form.Label>
                                                        <MultiSelect
                                                            overrideStrings={{
                                                                selectSomeItems: "Select Users",
                                                                allItemsAreSelected: "All Users are Selected",
                                                            }}
                                                            options={usersList}
                                                            onChange={(e: any) => bindCountryRegions(e, "users")}
                                                            className={"user"}
                                                            value={initialVal.users}
                                                            labelledBy={t("SelectUsers")}
                                                        />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </Tab>
                                        {tab.map((item: any, i: number) => {
                                            return (
                                                <Tab
                                                    eventKey={`${i + 1}`}
                                                    title={`${t("FocalPoints")} ${i + 1}`}
                                                >
                                                    <Row className="mb-3">
                                                        <Col lg={4} sm={6}>
                                                            <Form.Group style={{ paddingTop: "20px" }}>
                                                                <Col className="p-0">
                                                                    <Form.Label>{t("Name")}</Form.Label>
                                                                </Col>
                                                                <label className="w-100">
                                                                    <TextInput
                                                                        name="username"
                                                                        id="username"
                                                                        value={item.username}
                                                                        onChange={(e: any) => handleTab(e, i)}
                                                                    ></TextInput>
                                                                </label>
                                                            </Form.Group>
                                                        </Col>

                                                        <Col lg={4} sm={6}>
                                                            <Form.Group style={{ paddingTop: "20px" }}>
                                                                <Col className="p-0">
                                                                    <Form.Label>{t("Email")}</Form.Label>
                                                                </Col>
                                                                <label className="w-100">
                                                                    <TextInput
                                                                        name="email"
                                                                        id="email"
                                                                        value={item.email}
                                                                        onChange={(e: any) => handleTab(e, i)}
                                                                        pattern="^[^@]+@[^@]+\.[^@]+$"
                                                                        errorMessage={{
                                                                            pattern: "Please enter a valid email",
                                                                        }}
                                                                    ></TextInput>
                                                                </label>
                                                            </Form.Group>
                                                        </Col>

                                                        <Col lg={4} sm={6}>
                                                            <InputGroup className="row ms-0 me-0">
                                                                <Form.Group
                                                                    style={{ paddingTop: "20px" }}
                                                                    className="institutionDialCode col-5 col-lg-4 ps-0 pe-0"
                                                                >
                                                                    <Form.Label>{t("Telephone")}</Form.Label>
                                                                    <SelectGroup
                                                                        type="numbers"
                                                                        name="focal_dial_code"
                                                                        id="focal_dial_Code"
                                                                        value={item.focal_dial_code}
                                                                        onChange={(e: any) => handleTab(e, i)}
                                                                    >
                                                                        <option value={item.focal_dial_code}>
                                                                            {item.focal_dial_code}
                                                                        </option>
                                                                        {countryList.map((item: any, i: any) => {
                                                                            return (
                                                                                <option
                                                                                    key={i}
                                                                                    value={item.dial_code}
                                                                                >{`(${item.dial_code}) ${item.title}`}</option>
                                                                            );
                                                                        })}
                                                                    </SelectGroup>
                                                                </Form.Group>

                                                                <Form.Group
                                                                    style={{ paddingTop: "20px" }}
                                                                    className="institutionTelephone col-7 col-lg-8 pe-0"
                                                                >
                                                                    <Form.Label>&nbsp;</Form.Label>
                                                                    <TextInput
                                                                        name="mobile_number"
                                                                        id="mobile_number"
                                                                        value={item.mobile_number}
                                                                        onChange={(e: any) => handleTab(e, i)}
                                                                    ></TextInput>
                                                                </Form.Group>
                                                            </InputGroup>
                                                        </Col>

                                                        <Col>
                                                            <Form.Group className="m-0">
                                                                <input
                                                                    name="institution"
                                                                    id="institution"
                                                                    value={initialVal.title}
                                                                    type="hidden"
                                                                ></input>
                                                            </Form.Group>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        {i === 0 ? (
                                                            <span></span>
                                                        ) : (
                                                            <Col xs lg="4">
                                                                <Button
                                                                    onSelect={(k: any) => setdefaultActiveKey(k)}
                                                                    variant="secondary"
                                                                    onClick={(e) => removeTab(e, i)}
                                                                >
                                                                    {t("Remove")}
                                                                </Button>
                                                            </Col>
                                                        )}
                                                    </Row>
                                                </Tab>
                                            );
                                        })}
                                        <Tab
                                            title={
                                                <div>
                                                    <span onClick={tabAdd}>
                                                        {" "}
                                                        <p>{t("AddnewFocalPoint")}</p>
                                                    </span>
                                                </div>
                                            }
                                        ></Tab>
                                    </Tabs>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Card.Text>
                            <b>{t("MoreInfo")}</b>
                        </Card.Text>
                        <Row className="mb-3">
                            <Col lg md={4}>
                                <Form.Group>
                                    <Form.Label>{t("OrganisationType")}</Form.Label>

                                    <SelectGroup
                                        name="organisationType"
                                        id="organisationType"
                                        value={initialVal.organisationType === null ? "" : initialVal.organisationType}
                                        onChange={handleChange}
                                        required={false}
                                        errorMessage=""
                                    >
                                        <option value="">{t("SelectOrganisationType")}</option>
                                        {organizationList.map((item, i) => {
                                            return (
                                                <option key={i} value={item._id}>
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col lg md={4}>
                                <Form.Group>
                                    <Form.Label>{t("Network")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectNetwork"),
                                            allItemsAreSelected: "All Networks are Selected",
                                        }}
                                        options={networkList}
                                        value={network}
                                        onChange={handleNetwork}
                                        className={"net-work"}
                                        labelledBy={t("SelectNetwork")}
                                    />
                                    <span>{networkVal}</span>
                                </Form.Group>
                            </Col>
                            <Col lg md={4}>
                                <Form.Group>
                                    <Form.Label>{t("Expertise")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectExpertise"),
                                            allItemsAreSelected: "All Expertise are Selected",
                                        }}
                                        options={expertiseList}
                                        value={expertise}
                                        onChange={handleExpertise}
                                        className={"expertise"}
                                        labelledBy={t("SelectExpertise")}
                                    />
                                    {hazardExpertiseVal}
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md lg={6}>
                                <Form.Group style={{ maxWidth: "600px" }}>
                                    <Form.Label>{t("ExpertisewithHazards")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("ExpertisewithHazards"),
                                            allItemsAreSelected: "All Hazards are Selected",
                                        }}
                                        options={hazardList}
                                        filterOptions={filterOptions}
                                        onChange={handleHazard}
                                        value={hazards}
                                        className={"partners"}
                                        labelledBy={t("SelectPartners")}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md lg={6}>
                                <Form.Group style={{ maxWidth: "600px" }}>
                                    <Form.Label>{t("Partners")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectPartners"),
                                            allItemsAreSelected: "All Partners are Selected",
                                        }}
                                        options={partners}
                                        onChange={handlePartners}
                                        value={selctedPartners}
                                        className={"partners"}
                                        labelledBy={t("SelectPartners")}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col sm={12} md={6} lg={6}>
                                <Form.Group>
                                    <Form.Label>{t("Department")}</Form.Label>
                                    <TextInput
                                        name="department"
                                        id="department"
                                        value={initialVal.department}
                                        onChange={handleChange}
                                        required={false}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                        errorMessage={{}}
                                        validator={() => true}
                                        pattern=""
                                    ></TextInput>
                                </Form.Group>
                            </Col>
                            <Col sm={12} md={6} lg={6}>
                                <Form.Group>
                                    <Form.Label>{t("Unit")}</Form.Label>
                                    <TextInput
                                        name="unit"
                                        id="unit"
                                        value={initialVal.unit}
                                        onChange={handleChange}
                                        required={false}
                                        as="input"
                                        multiline={false}
                                        rows={1}
                                        errorMessage={{}}
                                        validator={() => true}
                                        pattern=""
                                    ></TextInput>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Card.Text>
                            <b>{t("MediaGallery")}</b>
                        </Card.Text>
                        <Row>
                            <Col>
                                <ReactDropZone
                                    index={2}
                                    datas={dropZoneCollection}
                                    srcText={srcCollection}
                                    getImgID={(id, index) => getID(id, index)}
                                    getImageSource={(imgSrcArr) => getSource(imgSrcArr)}
                                />
                            </Col>
                        </Row>
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary" ref={buttonRef}>
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("reset")}
                                </Button>
                                <Link href="/institution" as="/institution" >
                                    <Button variant="secondary">{t("Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>
            </Container>
        </ValidationFormWrapper>
    );
};

export default InstitutionForm;
function initialValues_func(setinitialVal: React.Dispatch<any>, initialVal: any) {
    setinitialVal(initialVal);
}

function itertion_func(tab: any, i: number) {
    {
        tab[i].username = tab[i].username.toLowerCase();
        tab[i].email = tab[i].email.toLowerCase();
    }
}

function response_methods_func(response: any, currentLang: string) {
    const _expertise = response.expertise
        ? response.expertise.map((item: any, _i: any) => {
            return { label: item.title, value: item._id };
        })
        : [];
    const _network = response.networks
        ? response.networks.map((item: any, _i: any) => {
            return { label: item.title, value: item._id };
        })
        : [];
    const _partners = response.partners
        ? response.partners.map((item: any, _i: any) => {
            return { label: item.title, value: item._id };
        })
        : [];
    //** Only get the index ****/
    const _hazards = response.hazards
        ? response.hazards.map((item: any) => {
            return { label: item.title[currentLang], value: item._id };
        })
        : [];
    response["users"] = response.focal_points
        ? response.focal_points.map((item: any, _i: any) => {
            return { label: item.username, value: item._id };
        })
        : [];
    return { _expertise, _network, _partners, _hazards };
}

function response_institution_Func(response: any) {
    response.organisationType = response && response.type ? response.type._id : "";
    response.addressL1 = response.address.line_1;
    response.addressL2 = response.address.line_2;
    response.city = response.address.city;
    response.country = response.address.country && response.address.country._id ? response.address.country._id : " "; // status value
    response.world_region = response.address && response.address.world_region ? response.address.world_region : "";
    response.region = response.address.region
        ? response.address.region.map((item, _i) => {
            return { label: item.title, value: item._id };
        })
        : [];
}
