"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
//Import Library
const express_1 = __importDefault(require("express"));
const next_1 = __importDefault(require("next"));
const helmet_1 = __importDefault(require("helmet"));
//Import services/components
// import nextI18next from './i18n';
const port = process.env.PORT || 3000;
const app = (0, next_1.default)({ dev: process.env.NODE_ENV !== 'production' });
const handle = app.getRequestHandler();
(async () => {
    await app.prepare();
    const server = (0, express_1.default)();
    server.use(helmet_1.default.hidePoweredBy());
    // await nextI18next.initPromise
    server.get('*', (req, res) => handle(req, res));
    server.listen(port);
    console.log(`> Ready on http://localhost:${port}`); // eslint-disable-line no-console
})();
