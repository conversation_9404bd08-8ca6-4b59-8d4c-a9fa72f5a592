//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
const mongoose = require("mongoose");

//Import services/components
import { UsersInterface } from 'src/interfaces/users.interface';
import { InstitutionInterface } from 'src/interfaces/institution.interface';
// SCHEMAS
const users_schemas_1 = require("../../schemas/users.schemas");
@Injectable()
export class UserInviteService {
  constructor(
    @InjectModel('User') private userModel: Model<UsersInterface>,
    @InjectModel('Institution') private institutionModel: Model<InstitutionInterface>,
  ) { }

  async getUserForInvite(query?: any): Promise<UsersInterface[]> {
    let _result;
    try {
      const { _filter, options } = this.optionFilter(query);
      if (query.query && query.query.type === "private") {
        await this.userQuerycountry(query);
      }
      else {
        await this.userInstutiontype(query);
        await this.userQuery(query);
        await this.userExpertises(query);
        _result = await this.userModel.find(_filter).sort(options.sort).limit(options.limit).exec();
      }

    } catch (error) {
      throw new NotFoundException('Could not find Country Region.');
    }
    this.notFindcountry(_result);
    return _result;
  }

  private async userQuerycountry(query: any) {
    if (query.query.country_region && query.query.country_region.length > 0 && query.query.institution && query.query.institution.length > 0) {
      const userInOrg = await this.userInstutionlist(query);
      return userInOrg;
    }
    else if (query.query.institution_type && query.query.institution.length === 0 && query.query.institution_type.length > 0) {
      const userInOrg = await this.userInstutionmodel(query);
      return userInOrg;
    }
    else if (query.query.institution && query.query.institution.length > 0) {
      const userInOrg = await this.userModel.find(
        { "institution": { "$in": query.query.institution } } ,
      );
      return userInOrg;
    }
    else if (query.query.country_region && query.query.country_region.length > 0) {
      const userInOrg = await this.userModel.find(
        { "region": { $elemMatch: { "$in": query.query.country_region } } },
      );
      return userInOrg;
    }
    else if (query.query.country && query.query.country.length > 0) {
      const userInOrg = await this.userModel.find(
        { "country": { "$in": query.query.country } },
      );
      return userInOrg;
    }
    else {
      const userInOrg = await this.userModel.find();
      return userInOrg;
    }
  }

  private optionFilter(query: any) {
    const options = this.optionUser(query);
    const _filter = query.query ? query.query : {};
    this.userMax(query, options);
    return { _filter, options };
  }

  private notFindcountry(_result: any) {
    if (!_result) {
      throw new NotFoundException('Could not find Country Region.');
    }
  }

  private async userInstutionlist(query: any) {
    const institutionList = query.query.institution;
    const regionList = query.query.country_region;
    const userInOrg = await this.userModel.find({
      $and: [
        { "region": { $elemMatch: { "$in": regionList } } },
        { "institution": { "$in": institutionList } }
      ]
    });
    return userInOrg;
  }

  private async userInstutionmodel(query: any) {
    const institutions = await this.institutionModel.find().where('type').in(query.query.institution_type);
    query.query.institution = institutions.map((e) => e._id);
    const userInOrg = await this.userModel.find(
      { "institution": { "$in": query.query.institution } }
    );
    return userInOrg;
  }

  private async userInstutiontype(query: any) {
    if (query?.query?.institution_type) {
      const institutions = await this.institutionModel.find().where('type').in(query.query.institution_type);
      query.query.institution = institutions.map((e) => e._id);
      delete query.query.institution_type;
    }
  }

  private async userExpertises(query: any) {
    if (query?.query?.expertises) {
      const institutions = await this.institutionModel.find().where('expertises').in(query.query.expertises);
      query.query.institution = institutions.map((e) => e._id);
      delete query.query.expertises;
    }
  }

  private async userQuery(query: any) {
    if (query?.query?.networks) {
      const institutions = await this.institutionModel.find().where('networks').in(query.query.networks);
      query.query.institution = institutions.map((e) => e._id);
      delete query.query.networks;
    }
  }

  private userMax(query: any, options: { sort: any; page: number; limit: number; select: string; }) {
    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }
  }

  private optionUser(query: any) {
    return {
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      select: query.select ? `-role -password ${query.select}` : '-role -password',
    };
  }

  async checkNonMemberList(email: string): Promise<any> {
    const _result = {};
    _result["message"] = "";
    _result["userList"] = [];
    try {
      const User = mongoose.model("Users", users_schemas_1.UsersSchema);
      const emailToSearch = new User({ email });
      emailToSearch.encryptFieldsSync();
      const userInList = await this.userModel.find({ email: emailToSearch['email'] });
      if (userInList.length > 0) {
        _result["userList"].push(userInList.email);
        _result["message"] = "user already exist, please add in platform member";
      }
    } catch (error) {
      console.log(error);
    }
    return _result;
  }
}
