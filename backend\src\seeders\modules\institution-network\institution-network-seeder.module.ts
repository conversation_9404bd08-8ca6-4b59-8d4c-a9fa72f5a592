//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { InstitutionNetworkSeederService } from './institution-network-seeder.services';
// SCHEMAS
import { InstitutionNetworkSchema } from 'src/schemas/institution_network.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'InstitutionNetwork', schema: InstitutionNetworkSchema }
      ]
    )
  ],
  providers: [InstitutionNetworkSeederService],
  exports: [InstitutionNetworkSeederService],
})

export class InstitutionNetworkSeederModule { }
