import { InfoWindow } from '@react-google-maps/api';

interface Props {
  position: google.maps.LatLngLiteral;
  onCloseClick?: () => void;
  children?: React.ReactNode;
}

const RKIMapInfowindow = ({ position, onCloseClick, children }: Props) => {
  return (
    <InfoWindow position={position} onCloseClick={onCloseClick}>
      <div>{children}</div>
    </InfoWindow>
  );
};

export default RKIMapInfowindow;
