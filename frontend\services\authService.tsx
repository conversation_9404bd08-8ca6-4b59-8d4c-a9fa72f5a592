//Import Library
import * as axiosLib from 'axios';
const axios = axiosLib.default;

const application = "application/json";
class AuthService {
  auth = async (params: { username: string; password: string }) => {
    try {
      let url = `${process.env.API_SERVER}/auth/login`;
      // if (isAdminLogin) {
      //   url = `${process.env.API_SERVER}/auth/admin/login`;
      // }
      const response = await axios.post(
        url,
        {
          username: params.username,
          password: params.password,
        },
        { headers: { "Content-Type": application }, withCredentials: true }
      );
      //if any one of these exist, then there is a field error
      if (response.status === 201 && response.data) {
        return response;
      }
    } catch (error: unknown) {
      const axiosError = error as { response?: any };
      return axiosError.response ? axiosError.response : {};
    }
  };

  logout = async () => {
    try {
      const _response = await axios.post(
        `${process.env.API_SERVER}/auth/logout`,
        {},
        { headers: { "Content-Type": application }, withCredentials: true }
      );
      localStorage.removeItem("persist:root");
    } catch (error: unknown) {
      const axiosError = error as { response?: any };
      return axiosError.response ? axiosError.response : {};
    }
  };

  getAuthHeader = async () => {
    return {
      "Content-Type": application,
    };
  };
}

export default new AuthService();
