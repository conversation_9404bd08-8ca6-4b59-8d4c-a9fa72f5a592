//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

//Import services/components
import { VspaceInterface } from '../../interfaces/vspace.interface';
import { CreateVspaceDto } from './dto/create-vspace.dto';
import { UpdateVspaceDto } from './dto/update-vspace.dto';
import { UsersService } from './../../users/users.service';
import { EmailService } from './../../email.service';
import { FlagService } from '../flag/flag.service';
import { UpdateInterface } from 'src/interfaces/update.interface';
const crypto = require('crypto');

const arrayIndex = '$arrayIndex';
const CouldnotfindVspace = 'Could not find Vspace.';
@Injectable()
export class VspaceService {
  constructor(
    @InjectModel('Vspace') private vspaceModel: Model<VspaceInterface>,
    private readonly _usersService: UsersService,
    private readonly _emailService: EmailService,
    private readonly _flagService: FlagService,
    @InjectModel('Update') private updateModel: Model<UpdateInterface>,
  ) {}

  randomString(length) {
    let result = '';
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      const randomVal = crypto.randomInt(0, 60);
      result += characters.charAt(Math.floor(randomVal * charactersLength));
    }
    return result;
  }

  async create(createVspaceDto: CreateVspaceDto): Promise<VspaceInterface> {
    const createdVspace = new this.vspaceModel(createVspaceDto);
    const usersList = [];
    /**inviting non-members */
    if (
      !createdVspace.visibility &&
      createVspaceDto['nonMembers'] &&
      createVspaceDto['nonMembers'].length
    ) {
      const ExistingMembersList = [];
      createVspaceDto['members'].forEach((element) => {
        ExistingMembersList.push(element.email);
      });
      const newNonMemberList = createVspaceDto['nonMembers'].filter(
        (x) => ExistingMembersList.indexOf(x) === -1,
      );

      for (const nonMember_email of newNonMemberList) {
        const _name = nonMember_email.match(/^([^@]*)@/)[1];
        const randomIds = uuidv4();
        const user = {
          username: _name,
          firstname: _name,
          roles: ['AUTHENTICATED'],
          email: nonMember_email,
          password: this.randomString(16),
          enabled: true,
          is_vspace: true,
          vspace_status: 'Request Pending',
          mobile_number: '',
          emailActivateToken: randomIds,
        };
        await this.vspaceServicesupdate(
          user,
          createVspaceDto,
          createdVspace['id'],
        );
      }
    }
    //Existing user invite mail
    if (
      !createdVspace.visibility &&
      createVspaceDto['members'] &&
      createVspaceDto['members'].length
    ) {
      createVspaceDto['members'].forEach(async (d) => {
        const user = await this._usersService.get(d);
        const languageCode = createVspaceDto['language']
          ? createVspaceDto['language']
          : 'en';
        this._emailService.vspaceOldUserInvite(
          user,
          createdVspace,
          languageCode,
        );
      });
    }
    return createdVspace.save();
  }

  private async vspaceServicesupdate(
    user: {
      username: any;
      firstname: any;
      roles: string[];
      email: any;
      password: string;
      enabled: boolean;
      is_vspace: boolean;
      vspace_status: string;
      mobile_number: string;
      emailActivateToken: string;
    },
    createVspaceDto: CreateVspaceDto,
    vspaceId: any,
  ) {
    try {
      let _update = await this._usersService.create(user);
      _update = await this._usersService.protectPassword(_update);
      const _user = await this._usersService.get(_update['_id']);
      const languageCode = createVspaceDto['language']
        ? createVspaceDto['language']
        : 'en';
      this._emailService.vspaceInviteNonExistingUser(
        _user,
        createVspaceDto,
        languageCode,
      );
    } catch (e) {
      if (e.response.user) {
        const _existuser = await this._usersService.findOne({
          _id: e.response.user,
        });
        if (_existuser) {
          const languageCode = createVspaceDto['language']
            ? createVspaceDto['language']
            : 'en';
          this._emailService.vspaceInvite(_existuser, languageCode, vspaceId);
        }
      }
    }
  }

  async findAll(query): Promise<VspaceInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      populate: query.populate ? query.populate : '',
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
      collation: { locale: 'en' },
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};

    if (_filter.title) {
      _filter.title = _filter.title
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(`^${_filter.title}`, 'gi');
      _filter['title'] = regex;
    }

    return this.vspaceModel.paginate(_filter, options);
  }

  async get(vspaceId): Promise<VspaceInterface[]> {
    let _result;
    try {
      _result = await this.vspaceModel.findById(vspaceId).exec();
    } catch (error) {
      throw new NotFoundException(CouldnotfindVspace);
    }
    if (!_result) {
      throw new NotFoundException(CouldnotfindVspace);
    }
    return _result;
  }

  async getSortedDocList(vspaceId, query): Promise<VspaceInterface[]> {
    let _result = [];
    try {
      const options = this.vspaceServicesoption(query);

      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }
      if (
        options.sort &&
        (options.sort.document_title || options.sort.doc_created_at)
      ) {
        const sortOrder = options.sort.document_title
          ? options.sort.document_title
          : options.sort.doc_created_at;
        const sortNumber = sortOrder === 'asc' ? 1 : -1;
        const sortArray = options.sort.document_title
          ? { 'docArray.original_name': sortNumber }
          : { 'docArray.created_at': sortNumber };
        const searchID = new mongoose.Types.ObjectId(vspaceId);
        const skip = (options.page - 1) * options.limit;
        const limit = options.limit;
        const list = await this.vspaceModel
          .aggregate([
            { $match: { _id: searchID } },
            {
              $unwind: {
                path: '$document',
                preserveNullAndEmptyArrays: true,
                includeArrayIndex: 'arrayIndex',
              },
            },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            { $set: { 'docArray.index': arrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', arrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
              },
            },
            {
              $facet: {
                totalData: [
                  { $match: {} },
                  { $limit: limit + skip },
                  { $skip: skip },
                ],
                totalCount: [
                  {
                    $group: {
                      _id: null,
                      count: { $sum: 1 },
                    },
                  },
                ],
              },
            },
          ])
          .collation({ locale: 'en', strength: 1 })
          .exec();
        const DocList: any = this.vspaceDoclist(list, limit, options);
        _result = DocList;
      }
    } catch (error) {
      throw new NotFoundException(CouldnotfindVspace);
    }
    if (!_result) {
      throw new NotFoundException(CouldnotfindVspace);
    }
    return _result;
  }

  private vspaceServicesoption(query: any) {
    return {
      sort: query.sort ? query.sort : {},
      collation: query.collation ? query.collation : 'en',
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
    };
  }

  private vspaceDoclist(
    list: any,
    limit: number,
    options: { sort: any; collation: any; page: number; limit: number },
  ) {
    const totalCount = list[0].totalCount[0].count;
    const DocList: any = {};
    DocList.data = list[0].totalData;
    DocList.totalCount = totalCount;
    DocList.limit = limit;
    DocList.totalPages = Math.ceil(totalCount / limit);
    DocList.page = options.page;
    DocList.pagingCounter = options.page;
    DocList.hasNextPage = options.page !== DocList.totalPages;
    DocList.hasPrevPage = !(
      options.page === 1 && options.page === DocList.totalPages
    );
    DocList.prevPage =
      options.page === 1 && options.page === DocList.totalPages
        ? null
        : options.page - 1;
    DocList.nextPage =
      options.page === DocList.totalPages ? null : options.page + 1;
    return DocList;
  }

  async getSortedUpdateDocList(vspaceId, query): Promise<VspaceInterface[]> {
    let _result = [];
    try {
      const options = {
        sort: query.sort ? query.sort : {},
        collation: query.collation ? query.collation : 'en',
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
      };
      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }

      if (
        options.sort &&
        (options.sort.document_title || options.sort.doc_created_at)
      ) {
        const { searchID, sortArray, limitnum, skip, collation_key } =
          this.vspaceCollation(
            options.sort,
            options.collation,
            options.page,
            options.limit,
            vspaceId,
          );

        const list = await this.updateModel
          .aggregate([
            { $match: { parent_vspace: searchID } },
            { $unwind: { path: '$document', includeArrayIndex: 'arrayIndex' } },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            { $set: { 'docArray.index': arrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', arrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
              },
            },
            {
              $facet: {
                totalData: [
                  { $match: {} },
                  { $limit: limitnum + skip },
                  { $skip: skip },
                ],
                totalCount: [
                  {
                    $group: {
                      _id: null,
                      count: { $sum: 1 },
                    },
                  },
                ],
              },
            },
          ])
          .collation({ locale: collation_key, strength: 1 })
          .exec();
        const DocList: any = this.vspaceCount(
          list,
          limitnum,
          options.page,
          options.limit,
        );
        _result = DocList;
      }
    } catch (error) {
      throw new NotFoundException('Could not find Documents.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Documents.');
    }
    return _result;
  }

  private vspaceCollation(
    sort: any,
    collation: any,
    page: number,
    limit: number,
    vspaceId: any,
  ) {
    const collation_key = collation ?? 'en';
    const sortOrder = sort.document_title ?? sort.doc_created_at;
    const sortNumber = sortOrder === 'asc' ? 1 : -1;
    const sortArray = sort.document_title
      ? { 'docArray.original_name': sortNumber }
      : { 'docArray.created_at': sortNumber };
    const searchID = new mongoose.Types.ObjectId(vspaceId);
    const skip = (page - 1) * limit;
    const limitnum = limit;
    return { searchID, sortArray, limitnum, skip, collation_key };
  }

  private vspaceCount(
    list: any,
    limitnum: number,
    page: number,
    limit: number,
  ) {
    const totalCount = list[0].totalCount[0].count;
    const DocList: any = {};
    DocList.data = list[0].totalData;
    DocList.totalCount = totalCount;
    DocList.limitnum = limit;
    DocList.totalPages = Math.ceil(totalCount / limitnum);
    DocList.page = page;
    DocList.pagingCounter = page;
    DocList.hasNextPage = page !== DocList.totalPages;
    DocList.hasPrevPage = !(page === 1 && page === DocList.totalPages);
    DocList.prevPage =
      page === 1 && page === DocList.totalPages ? null : page - 1;
    DocList.nextPage = page === DocList.totalPages ? null : page + 1;
    return DocList;
  }

  arrayDifference(array1, array2) {
    const difference = [];
    if (array1?.length && array2?.length) {
      array1.forEach((ar1El) => {
        if (array2.filter((ar2El) => ar2El === ar1El).length === 0) {
          difference.push(ar1El);
        }
      });
    }
    return difference;
  }

  processInvites = (existingMembers, newMembers) => {
    let addedUsers = [];
    let existingMembersIds = [];
    if (existingMembers?.length) {
      existingMembers.forEach((_member) => {
        existingMembersIds.push(_member._id.toString());
      });
    }
    addedUsers = this.arrayDifference(newMembers, existingMembersIds);
    return addedUsers;
  };

  async update(vspaceId: any, updateVspaceDto: UpdateVspaceDto) {
    const getById: any = await this.vspaceModel.findById(vspaceId).exec();
    const updatedData = new this.vspaceModel(updateVspaceDto);
    let addedUsers = this.processInvites(
      getById.members,
      updateVspaceDto['members'],
    );
    try {
      const existingFocalPoints = getById?.members
        ?.filter((d) => d._id)
        .map((d) => d._id);
      updateVspaceDto['members']?.filter(
        (x) => existingFocalPoints.indexOf(x) === -1,
      );

      if (!updatedData.visibility && updateVspaceDto['nonMembers']?.length) {
        const ExistingSubscribersList = [];
        this.vspaceMember(getById, ExistingSubscribersList);
        const newNonMemberList = updateVspaceDto['nonMembers'].filter(
          (x) => ExistingSubscribersList.indexOf(x) === -1,
        );
        for (const nonMember_email of newNonMemberList) {
          const _name = nonMember_email.match(/^([^@]*)@/)[1];

          const randomIds = uuidv4();
          const user = {
            username: _name,
            firstname: _name,
            roles: ['AUTHENTICATED'],
            email: nonMember_email,
            password: this.randomString(16),
            enabled: true,
            is_vspace: true,
            vspace_status: 'Request Pending',
            mobile_number: '',
            emailActivateToken: randomIds,
          };

          try {
            let _update = await this._usersService.create(user);
            _update = await this._usersService.protectPassword(_update);
            const _user = await this._usersService.get(_update['_id']);
            const _languageCode = updateVspaceDto['language']
              ? updateVspaceDto['language']
              : 'en';
            this._emailService.vspaceInviteNonExistingUser(
              _user,
              updateVspaceDto,
              _languageCode,
            );
          } catch (e) {
            await this.responceUser(e, updateVspaceDto);
          }
        }
      }

      //Inviting exisisting users to private vspace
      this.invitingExisting(addedUsers, updateVspaceDto, getById);
      Object.keys(updateVspaceDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Vspace.');
    }

    const languageCode = updateVspaceDto['language']
      ? updateVspaceDto['language']
      : 'en';
    await this._flagService.alertSubscribers(
      getById.title,
      getById._id,
      'vspace',
      languageCode,
    );
    return getById;
  }

  private async responceUser(e: any, updateVspaceDto: UpdateVspaceDto) {
    if (e.response.user) {
      const _existuser = await this._usersService.findOne({
        _id: e.response.user,
      });
      if (_existuser) {
        const _langCode = updateVspaceDto['language']
          ? updateVspaceDto['language']
          : 'en';
        this._emailService.vspaceInvite(
          _existuser,
          _langCode,
          updateVspaceDto['id'],
        );
      }
    }
  }

  private vspaceMember(getById: any, ExistingSubscribersList: any[]) {
    if (getById?.members?.length) {
      getById.members.forEach((element) => {
        ExistingSubscribersList.push(element.email);
      });
    }
  }

  private invitingExisting(
    newExistingUsers: any,
    updateVspaceDto: UpdateVspaceDto,
    getById: any,
  ) {
    if (newExistingUsers && newExistingUsers.length > 0) {
      newExistingUsers.forEach(async (d) => {
        const _TlanguageCode = updateVspaceDto['language']
          ? updateVspaceDto['language']
          : 'en';
        const user = await this._usersService.get(d);
        this._emailService.vspaceOldUserInvite(user, getById, _TlanguageCode);
      });
    }
  }

  async delete(vspaceId: string) {
    const result = await this.vspaceModel.deleteOne({ _id: vspaceId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(CouldnotfindVspace);
    }
  }

  async acceptSubscriptionRequest(
    vspaceId: any,
    updateVspaceDto: UpdateVspaceDto,
  ) {
    const getById: any = await this.vspaceModel.findById(vspaceId).exec();
    const updatedData = new this.vspaceModel(updateVspaceDto);
    getById.subscribers = updatedData.subscribers;
    getById.save();
    return getById;
  }
}
