//Import Library
import React from 'react';

// Creating Context
export interface UpdateContexts {
  updates: any[];
  setUpdates: (params: any[]) => void;
}

export const IntialValue = {
  updates: [],
  setUpdates: () => ""
};

export const UpdateContext = React.createContext<UpdateContexts>(IntialValue);

// Hooks
export const useUpdates = (): UpdateContexts => {
  const [updates, setUpdatesList] = React.useState<any[]>([]);

  const setUpdates = React.useCallback((currentUrl: any[]): void => {
    setUpdatesList(currentUrl);
  }, []);

  return {
    updates,
    setUpdates
  };
};
