//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { HazardHazardTypeController } from './hazard-hazardtype.controller';
import { HazardHazardTypeService } from './hazard-hazardtype.service';
// SCHEMAS
import { HazardSchema } from 'src/schemas/hazard.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Hazard', schema: HazardSchema }
    ])
  ],
  controllers: [HazardHazardTypeController],
  providers: [HazardHazardTypeService],
})

export class HazardHazardTypeModule { }