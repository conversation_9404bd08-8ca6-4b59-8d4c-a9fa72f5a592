//Import Library
import { Col } from "react-bootstrap";
import <PERSON> from "next/link";

const truncateLength = 260;

//TODO: Remove the maths random number for image after updates completed with image upload
export default function AnnouncementItem(props: any) {

  const { item } = props;

  const getTrimmedString = (html: any) => {
    const div = document.createElement("div");
    div.innerHTML = html;
    const string = div.textContent || div.innerText || "";
    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);
  }

  return (
    <Col className="p-0" xs={12}>
      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[Parent_func(item)]}/update/${item._id}`}>

        {(item.images && item.images[0]) ?
          <img src={`${process.env.API_SERVER}/image/show/${item.images[0]._id}`} alt="announcement"
            className="announceImg" />
          : <i className="fa fa-bullhorn announceImg" />}

      </Link>
      <div className="announceDesc">
        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[newFunction(item)]}/update/${item._id}`}>
          {item && item.title ? item.title : ''}
        </Link>
        <p>
          {item && item.description ? getTrimmedString(item.description) : null}
        </p>
      </div>
    </Col>
  );
}

function newFunction(item: any) {
  return `parent_${item.type}`;
}

function Parent_func(item: any) {
  return `parent_${item.type}`;
}
