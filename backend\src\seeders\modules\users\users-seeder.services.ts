//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';

//Import services/components
import { UsersInterface } from '../../../interfaces/users.interface';
import { RolesInterface } from '../../../interfaces/roles.interface';

const SALT_WORK_FACTOR = process.env.SALT_WORK_FACTOR ? parseInt(process.env.SALT_WORK_FACTOR) : 10;

/**
 * Service dealing with syndrome based operations.
 *
 * @class
 */
@Injectable()
export class UsersSeederService {

  constructor(
    @InjectModel('Users') private usersModel: Model<UsersInterface>,
    @InjectModel('Roles') private rolesModel: Model<RolesInterface>
  ) {}

  /**
   * Seed all syndrome.
   *
   * @function
   */
  async create(): Promise<UsersInterface> {
    const user = {
      username: process.env.SEED_USERNAME,
      email: process.env.SEED_EMAIL,
      password: process.env.SEED_PASSWORD,
      roles: [process.env.SEED_ROLE]
    };
    return await this.usersModel
      .findOne({ $or: [{ username: user.username }, { email: user.email }] })
      .exec()
      .then(async dbUser => {
        // We check if a syndrome already exists.
        // If it does don't create a new one.
        if (dbUser) {
        return Promise.resolve(null);
        }
        const salt = await bcrypt.genSaltSync(SALT_WORK_FACTOR);
        const hash = await bcrypt.hashSync(user.password, parseInt(salt));
        user.password = hash;
        return Promise.resolve(
          await this.usersModel.create(user)
        );
      })
      .catch(error => Promise.reject(error));
  }
}