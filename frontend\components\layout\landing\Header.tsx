//Import Library
import React from "react";
import { Row, Col } from "react-bootstrap";
import <PERSON> from "next/link";

//Import services/components
import LayoutHelpModal from "./../modals/layout-help";

type Header1 = {
  onScroll: (index: number) => void;
};

const Header: React.FunctionComponent<Header1> = ({ onScroll }): React.ReactElement => (
  <div className="header-block">
    <div className="header-container" style={{}}>
      <Row>
        <Col sm={3} className="logo-image">
          <img src="/images/home/<USER>" height="60px" />
        </Col>
        <Col sm={9}>
          <ul className="quick-menu">
            <li>
              <a onClick={() => onScroll(0)}>Home</a>
            </li>
            <li>
              <a onClick={() => onScroll(1)}>Our network</a>
            </li>
            <li>
              <a onClick={() => onScroll(2)}>About us</a>
            </li>
            <li>
              <a onClick={() => onScroll(3)}>Contact us</a>
            </li>
            <li>
              <LayoutHelpModal show={false} onHide={() => {}} />
            </li>
            <li className="login">
              <Link href="/login">
                Login
              </Link>
            </li>
          </ul>
        </Col>
      </Row>
    </div>
  </div>
);

export default Header;
