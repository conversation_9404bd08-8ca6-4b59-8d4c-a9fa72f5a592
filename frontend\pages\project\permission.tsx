//Import Library
import React from 'react';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

//Import services/components
import R403 from "../r403";

export const canAddProject = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddProject',
});

export const canAddProjectForm = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.project && state.permissions.project['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddProjectForm',
  FailureComponent: () => <R403/>
});

export const canEditProject = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.project) {
      if (state.permissions.project['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.project['update:own']) {
          if (props.project && props.project.user && props.project.user._id === state.user._id) {
            return true;
          }  
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditProject',
});

export const canEditProjectForm = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.project) {
      if (state.permissions.project['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.project['update:own']) {
          if (props.project && props.project.user && props.project.user._id === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditProjectForm',
  FailureComponent: () => <R403/>
});

export const canViewDiscussionUpdate = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewDiscussionUpdate',
});

export default canAddProject;