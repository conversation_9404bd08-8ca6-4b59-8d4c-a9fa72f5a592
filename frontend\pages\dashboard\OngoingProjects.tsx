//Import Library
import React, { useEffect, useState } from 'react';
import _ from "lodash";
import Link from "next/link";
import { ListGroup } from 'react-bootstrap';

//Import services/components
import RKICard from "../../components/common/RKICard";
import CardPlaceholder from "../../components/common/placeholders/CardPlaceholder";
import apiService from "../../services/apiService";


interface ListItemsProps {
  list: any[];
}

function ListItems(props: ListItemsProps) {
  const { list } = props;
  if (list.length > 0) {
    return (
      <ListGroup>
        {list.map((item: any, index: number) => {
          return (
            <ListGroup.Item
              key={index}>
              <Link href="/project/[...routes]" as={`/project/show/${item._id}`}>

                {item.title}

              </Link>
            </ListGroup.Item>
          );
        })}
      </ListGroup>
    );
  }
  return null;
}

interface CardDetailsProps {
  project: {
    body: string;
    id: string;
  };
}

function CardDetails(props: CardDetailsProps) {
  const { project } = props;
  return (
    <Link
      href='/project/[...routes]'
      as={`/project/show/${project.id}`}
      className='active-op-project'>

      <span className="project-title link">{project.body}</span>

    </Link>
  );
}

interface OngoingProjectsProps {
  t: (key: string) => string;
  fetchOngoingProjects: (projects: any[]) => void;
}

function OngoingProjects(props: OngoingProjectsProps) {
  const {t, fetchOngoingProjects} = props;
  const cardHeader = t("OngoingProjects");
  const [project, setProject] = useState<{ body: string; id: string; list: any[] }>({ body: "", id: "", list: [] });
  const [loading, setLoading] = useState(true);

  const setEmptyNotice = () => {
    setProject({ body: t("NoProjectavailable"), id: "", list: [] })
  };

  const fetchProjects = async () => {
    const projectParams = {
      query: { status: [] },
      sort: { created_at: "desc" },
      limit: 10,
      select: "-website -description -funded_by -status -start_date -end_date -region -area_of_work -institution_invites -vspace -vspace_visibility -user -created_at -updated_at -partner_institutions.partner_region -partner_institutions.partner_institution -partner_institutions.world_region"
    };
    const statusId = await fetchProjectStatus();
    if (statusId) {
      projectParams.query.status = statusId;

      try {
        setLoading(true);
        const projects = await apiService.get('/project', projectParams);
        setLoading(false);
        if (Array.isArray(projects.data) && projects.data.length > 0) {
          setProject({ body: projects.data[0].title, id: projects.data[0]._id, list: projects.data })
          fetchOngoingProjects(projects.data);
        } else {
          setEmptyNotice()
        }
      } catch (e) {
        setEmptyNotice()
      }
    } else {
      setEmptyNotice()
    }
  };

  const fetchProjectStatus = async () => {
    const response = await apiService.get('/projectStatus');
    if (response && response.data && response.data.length > 0) {
      const statusId = []
      _.forEach(response.data, function (item: any) {
        if (item.title === "Ongoing") {
          statusId.push(item._id);
        }
      });
      return statusId;
    }
    return false;
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const list = {
    heading: cardHeader,
    body: <ListItems list={project.list} />
  };

  return (
    <RKICard
      dialogClassName={"ongoing-project-list"}
      list={list}
      header={cardHeader}
      body={loading ? <CardPlaceholder /> : <CardDetails project={project} />}
    />
  )
}

export default OngoingProjects;