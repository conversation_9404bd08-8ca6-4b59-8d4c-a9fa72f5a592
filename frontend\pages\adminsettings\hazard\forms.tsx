//Import Library
import { Con<PERSON><PERSON>, <PERSON>, Row, Col, Form, Button, Dropdown, DropdownButton } from "react-bootstrap";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput, SelectGroup } from "../../../components/common/FormValidation";
import React, { useRef, useState, useEffect } from "react";
import toast from 'react-hot-toast';
import Router from "next/router";
import Link from "next/link";

//Import services/components
import apiService from "../../../services/apiService";
import HazardReactDropZone from "./hazardReactDropZone";
import { useTranslation } from 'next-i18next';
import { EditorComponent } from "../../../shared/quill-editor/quill-editor.component";
import { Hazard, HazardType, ApiResponse } from "../../../types";

interface HazardFormProps {
    [key: string]: any;
}

const HazardForm = (props: HazardFormProps) => {
    const { t, i18n } = useTranslation("common");
    const currentLang = i18n.language && i18n.language === "fr" ? "en" : i18n.language;
    const _initialHazard = {
        title: {
            en: "",
            fr: "",
            de: "",
        },
        hazard_type: "",
        description: {},
        enabled: true,
        picture: null,
        picture_source: "",
        images_src: [],
    };
    const getLang = async () => {
        const response: ApiResponse<any[]> = await apiService.get("/language", langParams);
        if (response) {
            setLang(response.data);
        }
    };

    const langParams = {
        query: {},
        sort: { title: "asc" },
        limit: "~",
        select: "-_id -created_at -updated_at",
    };

    const [initialVal, setInitialVal] = useState<any>(_initialHazard);
    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);
    const [, setSrcCollection] = useState<any[]>([]);
    const [hazardtype, setHazardType] = useState<HazardType[]>([]);
    const [lang, setLang] = useState<any[]>([]);
    const [locale, setLocale] = useState<string>(
        i18n.language && i18n.language === "fr" && currentLang === "en" ? "fr" : currentLang
    );

    const editform: boolean = props.routes && props.routes[0] === "edit_hazard" && props.routes[1];

    const handleSubmit = async (event: React.FormEvent, values?: any) => {
        event.preventDefault();
        // Use Formik values if available, otherwise fall back to initialVal
        const formValues = values || initialVal;
        let titleResult: any = {};
        let descResult: any = {};
        lang &&
            lang.map((item, _i) => {
                const langItem = item.abbr;
                titleResult = {
                    ...titleResult,
                    [langItem]: formValues.title[langItem],
                };
                descResult = {
                    ...descResult,
                    [langItem]: formValues.description[langItem],
                };
            });

        //Setting both the title as same
        // if (titleResult['de'] == "") {
        //     titleResult['de'] = titleResult['en'];
        // }

        // if (titleResult['en'] == "") {
        //     titleResult['en'] = titleResult['de'];
        // }
        // new conditions 
        if (titleResult['en']) {
            titleResult['de'] = titleResult['en'];
            titleResult['fr'] = titleResult['en'];
        }

        if (titleResult['de']) {
            titleResult['en'] = titleResult['de'];
            titleResult['fr'] = titleResult['de'];
        }

        if (titleResult['fr']) {
            titleResult['en'] = titleResult['fr'];
            titleResult['de'] = titleResult['fr'];
        }
        const obj = {
            title: {
                ...titleResult,
            },
            hazard_type: formValues.hazard_type,
            description: {
                ...descResult,
            },
            enabled: formValues.enabled,
            picture: formValues.picture,
            picture_source: formValues.picture_source,
            images_src: formValues.images_src,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "Hazardisupdatedsuccessfully";
            response = await apiService.patch(`/hazard/${props.routes[1]}`, obj);
        } else {
            toastMsg = "Hazardisaddedsuccessfully";
            response = await apiService.post("/hazard", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/hazard");
        } else {            
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    const onChangeLocale = (item: any): void => {
        setLocale(item.abbr);
    };

    const resetHandler = () => {
        setInitialVal(_initialHazard);
        setDropZoneCollection([]);
        setSrcCollection([]);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleTitle = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        const titleObj = {
            ...initialVal.title,
            [locale]: value,
        };
        setInitialVal((prevState) => ({
            ...prevState,
            [name]: titleObj,
        }));
    };

    const handleMonitored = (e: React.ChangeEvent<HTMLInputElement>) => {
        setInitialVal((prevState) => ({
            ...prevState,
            enabled: !prevState.enabled,
        }));
    };

    const getHazardRegion = async (hazardParams: any) => {
        const response: ApiResponse<HazardType[]> = await apiService.get("/hazardtype", hazardParams);
        if (response) {
            setHazardType(response.data);
        }
    };

    const handleDescription = (value: string) => {
        const descrObj = {
            ...initialVal.description,
            [locale]: value,
        };
        setInitialVal((prevState) => ({
            ...prevState,
            description: descrObj,
        }));
    };

    const getHazardData = async (hazardParams: any) => {
        const response: any = await apiService.get(`/hazard/${props.routes[1]}`, hazardParams);

        if (response) {
            const { hazard_type, picture } = response;
            response.hazard_type = hazard_type && hazard_type._id ? hazard_type._id : "";

            // Ensure title has the correct structure
            if (response.title && typeof response.title === 'object') {
                response.title = {
                    en: response.title.en || "",
                    fr: response.title.fr || "",
                    de: response.title.de || "",
                };
            } else {
                response.title = {
                    en: "",
                    fr: "",
                    de: "",
                };
            }

            // Ensure description has the correct structure
            if (response.description && typeof response.description === 'object') {
                response.description = {
                    en: response.description.en || "",
                    fr: response.description.fr || "",
                    de: response.description.de || "",
                    ...response.description
                };
            } else {
                response.description = {};
            }

            if (picture != null) {
                setDropZoneCollection([picture]);
            }
            setSrcCollection(response.images_src ? response.images_src : []);
            setInitialVal((prevState) => ({ ...prevState, ...response }));
        }
    };

    useEffect(() => {
        getLang();
        const hazardParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            getHazardData(hazardParams);
        }
        getHazardRegion(hazardParams);
    }, []);

    const formRef = useRef<any>(null);

    const getID = (id: any) => {
        const _id = id.map((item: any) => item.serverID);
        setInitialVal((prevState) => ({ ...prevState, picture: _id }));
    };

    return (
        <Container className="formCard" fluid>
            <Card
                style={{
                    marginTop: "5px",
                    boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                }}
            >
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>
                                    {props.routes && props.routes[0] === "edit_hazard"
                                        ? t("editHazard")
                                        : t("addHazard")}
                                </Card.Title>
                            </Col>
                        </Row>
                        <hr />
                        <Row className="mb-3">
                            <Col md lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("hazardName")}</Form.Label>
                                    <TextInput
                                        name="title"
                                        id="title"
                                        required={locale === "en"}
                                        value={locale === "fr" ? (initialVal.title["en"] || "") : (initialVal.title[locale] || "")}
                                        validator={(value: any) => String(initialVal.title[locale]  || '').trim() !== ""}
                                        errorMessage={{
                                            validator: t("adminsetting.hazard.add"),
                                        }}
                                        onChange={handleTitle}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field d-flex me-3">{t("hazardType")}</Form.Label>
                                    <SelectGroup
                                        name="hazard_type"
                                        id="hazard_type"
                                        required
                                        value={initialVal.hazard_type}
                                        errorMessage={{ validator: t("pleaseAddtheHazardType")}}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("hazardTypeCategory.selectHazardType")}</option>
                                        {hazardtype.length >= 1
                                            ? hazardtype.map((item, _i) => {
                                                return (
                                                    <option key={item._id} value={item._id}>
                                                        {item.title}
                                                    </option>
                                                );
                                            })
                                            : null}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={4} className="d-flex" style={{ marginTop: "10px" }}>
                                <Form.Label>{t("chooseLanguage")}</Form.Label>
                                <DropdownButton
                                    title={locale.toUpperCase()}
                                    variant="outline-secondary"
                                    id="basic-dropdown"
                                    className="ms-2"
                                >
                                    {lang &&
                                        lang.map((item, i) => (
                                            <div key={i}>
                                                <Dropdown.Item
                                                    active={item.abbr === locale}
                                                    eventKey={item._id}
                                                    onClick={() => onChangeLocale(item)}
                                                >
                                                    {item.abbr.toUpperCase()}-{item.title.toUpperCase()}
                                                </Dropdown.Item>
                                            </div>
                                        ))}
                                </DropdownButton>
                            </Col>
                        </Row>
                        <br />
                        <Row>
                            <Form.Check
                                className=" ms-4"
                                type="switch"
                                name="rki_monitored"
                                id="custom-switch"
                                onChange={handleMonitored}
                                label={t("published")}
                                checked={initialVal.enabled}
                            />
                        </Row>
                        <br />
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("description")}</Form.Label>
                                    <EditorComponent initContent={
                                            locale === "fr"
                                                ? (initialVal.description["en"] || "")
                                                : (initialVal.description[locale] || "")
                                        } onChange={(evt: any) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <HazardReactDropZone datas={dropZoneCollection} getImgID={(id: any) => getID(id)} />
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("imageSourceCredit")}</Form.Label>
                                    <TextInput
                                        name="picture_source"
                                        id="picture_source"
                                        value={initialVal.picture_source || ""}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary">
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("reset")}
                                </Button>
                                <Link
                                    href="/adminsettings/[...routes]"
                                    as={`/adminsettings/hazard`}
                                    >
                                    <Button variant="secondary">{t("Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
        </Container>
    );
};

export default HazardForm;
