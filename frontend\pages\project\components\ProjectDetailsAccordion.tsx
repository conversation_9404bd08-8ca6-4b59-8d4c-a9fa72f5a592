//Import Library
import React, { useState } from "react";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import moment from "moment";
import { Accordion, Card } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';


const ProjectDetailsAccordion = (props) => {
    const formatDate = "DD-MM-YYYY HH:mm:ss";
    const formatDateStartDate = "DD-MM-YYYY";
    const { t } = useTranslation('common');
    const [section, setSection] = useState(true);
    return (
        <>
                <Accordion.Item eventKey="0">
                    <Accordion.Header onClick={() => setSection(!section)}>
                        <div className="cardTitle">{t("ProjectDetails")}</div>
                        <div className="cardArrow">
                            {section ? <FontAwesomeIcon icon={faPlus} color="#fff" /> :
                                <FontAwesomeIcon icon={faMinus} color="#fff" />}
                        </div>
                    </Accordion.Header>
                    <Accordion.Body>
                            <Card.Text className="projectDetails ps-0">
                                <p><b>{t("Status")}</b>:<span> {props.project.status ? props.project.status["title"] : ""}</span></p>
                                <p><b>{t("Created")}</b>:<span> {props.project.created_at ? (moment(props.project.created_at).format(formatDate)) : null} </span></p>
                                <p><b>{t("EndDate")}</b>:<span> {props.project.end_date ? (moment(props.project.end_date).format(formatDateStartDate)) : null}</span></p>
                                <p><b>{t("LastModified")}</b>:<span> {props.project.updated_at ? (moment(props.project.updated_at).format(formatDate)) : null} </span></p>
                                <p><b>{t("WeblinktoProject")}</b>:
								<span>
									{props.project?.website && (
										<a href={props.project.website} target={"_blank"}>
										{props.project.website}
										</a>
									)}
								</span>
                                </p>
                            </Card.Text>
                    </Accordion.Body>
                </Accordion.Item>
        </>
    )
}

export default ProjectDetailsAccordion;