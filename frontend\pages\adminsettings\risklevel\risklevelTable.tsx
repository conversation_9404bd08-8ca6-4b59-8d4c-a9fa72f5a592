//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Button } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const RisklevelTable = (_props: any) => {
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectRisklevel, setSelectRisklevel] = useState({});
    const { t } = useTranslation('common');

    const risklevelParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("Title"),
            selector: row => row.title,
            sortable: true,
        },
        {
            name: t("adminsetting.RiskLevel.Level"),
            selector: row => row.level,
            sortable: true,
            cell: (d) => d.level,
        },
        {
            name: t("action"),
            selector: row => row._id,
            sortable: false,
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_risklevel/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    const getRisklevelData = async () => {
        setLoading(true);
        const response = await apiService.get("/risklevel", risklevelParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };
    const handlePageChange = (page) => {
        risklevelParams.limit = perPage;
        risklevelParams.page = page;
        getRisklevelData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        risklevelParams.limit = newPerPage;
        risklevelParams.page = page;
        setLoading(true);
        const response = await apiService.get("/risklevel", risklevelParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectRisklevel(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/risklevel/${selectRisklevel}`);
            getRisklevelData();
            setModal(false);
            toast.success(t("adminsetting.RiskLevel.Table.riskLevelDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.RiskLevel.Table.errorDeletingRiskLevel"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        getRisklevelData();
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.RiskLevel.DeleteRisklevel")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.RiskLevel.Areyousurewanttodeletethisrisklevel")} </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default RisklevelTable;
