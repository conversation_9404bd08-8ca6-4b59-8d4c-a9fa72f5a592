//Import Library
import React from "react";
import { Row, Col } from "react-bootstrap";

const AboutUs: React.FunctionComponent<{ innerRef: any }> = ({ innerRef }): React.ReactElement => (
  <div className="about-us" ref={innerRef}>
    <div className='aboutus-wrap' >
      <Row xs={12}>
        <Col sm={6}>
        <img src="/images/home/<USER>" alt="About" width="100%" />
        </Col>
        <Col sm={6}>
          <div className="content">
            <div className="title">About us</div>
            <div className="desc">The German Ministry of Health has commissioned the Robert Koch Institut (RKI) to develop this platform.
            Under the coordination of the Centre for International Health Protection, based at RKI in Berlin,
            we want to support our partners in exchanging information in order to better coordinate
            the activities of all of them and thus strengthen the response capacity of all. </div>
          </div>
        </Col>
      </Row>
    </div>
  </div>
);

export default AboutUs;
