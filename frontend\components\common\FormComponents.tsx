import React from 'react';
import { Form } from 'react-bootstrap';
import { useField } from 'formik';

// Text Input Component
export const TextInputField = ({ label, name, required = false, ...props }: any) => {
  const [field, meta] = useField(name);

  return (
    <Form.Group>
      <Form.Label className={required ? "required-field" : ""}>
        {label}
      </Form.Label>
      <Form.Control
        {...field}
        {...props}
        isInvalid={meta.touched && !!meta.error}
      />
      {meta.touched && meta.error ? (
        <Form.Control.Feedback type="invalid">
          {meta.error}
        </Form.Control.Feedback>
      ) : null}
    </Form.Group>
  );
};

// Select Group Component
export const SelectGroupField = ({ label, name, required = false, children, ...props }: any) => {
  const [field, meta] = useField(name);

  return (
    <Form.Group>
      <Form.Label className={required ? "required-field" : ""}>
        {label}
      </Form.Label>
      <Form.Control
        as="select"
        {...field}
        {...props}
        isInvalid={meta.touched && !!meta.error}
      >
        {children}
      </Form.Control>
      {meta.touched && meta.error ? (
        <Form.Control.Feedback type="invalid">
          {meta.error}
        </Form.Control.Feedback>
      ) : null}
    </Form.Group>
  );
};

// Date Picker Component
export const DatePickerField = ({ label, name, ...props }: any) => {
  const [field, meta, helpers] = useField(name);

  return (
    <Form.Group>
      <Form.Label>{label}</Form.Label>
      <div>
        <input
          {...field}
          {...props}
          type="date"
          className={`form-control ${meta.touched && meta.error ? 'is-invalid' : ''}`}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            helpers.setValue(e.target.value);
          }}
        />
        {meta.touched && meta.error ? (
          <div className="invalid-feedback">
            {meta.error}
          </div>
        ) : null}
      </div>
    </Form.Group>
  );
};

// Editor Component Wrapper
export const EditorField = ({ label, name, ...props }: any) => {
  const [field, meta, helpers] = useField(name);

  return (
    <Form.Group>
      <Form.Label>{label}</Form.Label>
      <div>
        {props.children({
          value: field.value || '',
          onChange: (value: any) => helpers.setValue(value),
        })}
        {meta.touched && meta.error ? (
          <div className="text-danger mt-1">
            {meta.error}
          </div>
        ) : null}
      </div>
    </Form.Group>
  );
};

// MultiSelect Component Wrapper
export const MultiSelectField = ({ label, name, options, ...props }: any) => {
  const [field, meta, helpers] = useField(name);

  return (
    <Form.Group>
      <Form.Label>{label}</Form.Label>
      <div>
        {props.children({
          value: field.value,
          onChange: (value: any) => helpers.setValue(value),
          options
        })}
        {meta.touched && meta.error ? (
          <div className="text-danger mt-1">
            {meta.error}
          </div>
        ) : null}
      </div>
    </Form.Group>
  );
};
