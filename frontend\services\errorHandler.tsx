//Import Library
import Router from 'next/router';

interface ErrorResponse {
  response?: {
    data?: any;
    status?: number;
    statusText?: string;
  };
  message?: string;
}

export default function errorResponseHandler(error: ErrorResponse | unknown) {

  //redirect to 404 page

  // session expired
  if ((error as any)?.status === 401) {
    Router.push('/home');
  }

  if ((error as any)?.status === 400 || (error as any)?.status === 403) {
    return (error as any)?.data && (error as any)?.data.message ? (error as any).data.message[0] : [];
  }

  return{};
}

