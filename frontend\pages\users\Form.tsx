//Import Library
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col } from "react-bootstrap";
import {
  ValidationForm,
  TextInput,
  SelectGroup,
  Radio
} from "../../components/common/FormValidation";
import Router, { useRouter } from 'next/router';
import validator from 'validator';
import {MultiSelect} from "react-multi-select-component";

//Import services/components
import apiService from "../../services/apiService";

const initialState = {
  username: '',
  email: '',
  role: '',
  password: '',
  confirm_password: '',
  institution: '',
  region: [],
  country: '',
  enabled: ''
};

const userParams = {
  query: {},
  sort: { title: "asc" },
  limit: "~",
};

const UserForm = (_props: any) => {
  const [formState, setFormState] = useState<any>(initialState);
  const [roles, setRoles] = useState<any[]>([]);
  const [institutions, setInstitutions] = useState<any[]>([]);
  const [regions, setRegions] = useState<any[]>([]);
  const [countryList, setcountryList] = useState<any[]>([]);
  const [userDetails, setUserDetails] = useState<any>({});
  const router = useRouter();
  const routes: any = router.query.routes || [];

  const getRoles = async () => {
    const userRoles = await apiService.get("roles");
    if (userRoles && userRoles.data && userRoles.data.length > 0) {
      setRoles(userRoles.data);
    }
  };


  const get_response = (response) =>{
    setUserDetails(response);
    setFormState(prev => ({
      ...prev,
      username: response.username,
      email: response.email,
      role: response.role ? response.role._id : '',
      institution: response.institution ? response.institution._id : '',
      region: response.region ? response.region._id.map((item, _i) => {
        return { label: item.title, value: item._id };
      })
      : [],
      country: response.country ? response.country._id : '',
      enabled: response.enabled ? 'true' : 'false'
    }));

  }
  const getUser = async () => {
    if (routes[1]) {
      const response = await apiService.get(`users/${routes[1]}`);
      if (response && response._id) {
        get_response(response)
      }
      getRegions(response.country);
    }
  };

  const getInsitutions = async () => {
    const insitutionsList = await apiService.get("institution");
    if (insitutionsList && insitutionsList.data && insitutionsList.data.length > 0) {
      setInstitutions(insitutionsList.data);
    }
  };

  const getCountries = async () => {
    const response = await apiService.get("/country", userParams);
    if (response && response.data && response.data.length > 0) {
      setcountryList(response.data);
    }
  };

  useEffect(() => {

    getRoles();
    getUser();
    getInsitutions();
    getCountries();
  }, []);

  const formRef = useRef(null);

  const clearValue = (obj) => {
    setFormState((prevState) => ({
      ...prevState,
      ...obj,
    }));
  };

  const bindCountryRegions = (e) => {
    setFormState((prevState) => ({
      ...prevState,
      region: e,
    }));
  };

  const getRegions = async (id) => {
    let _regions = [];
    if (id) {
      const response = await apiService.get(`/country_region/${id}`, {});
      if (response && response.data) {
        _regions = response.data.map((item, _i) => {
          return { label: item.title, value: item._id };
        });
        _regions.sort((a, b) => a.label.localeCompare(b.label));
      }
    }
    setRegions(_regions);
  };

  const handleChange = e => {
    const { name, value } = e.currentTarget;
    setFormState(prevState => ({
      ...prevState,
      [name]: value
    }));
    if (name === "country"){
      getRegions(value);
      clearValue({ region: [] });
    }
  };

  const resetHandler = () => {
    setFormState(initialState);
    // Reset validation state (Formik handles this automatically)
    window.scrollTo(0, 0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const data = {
      username: formState.username,
      email: formState.email,
      role: formState.role,
      institution: formState.institution ? formState.institution : null,
      region: formState.region ? formState.region.map((item, _i) => { return item.value; }) : [],
      country: formState.country ? formState.country : null,
      enabled: formState.enabled === 'true' ? true : false
    };
    if (userDetails && userDetails['_id']) {
      if (formState.password !== "") {
        data['password'] = formState.password;
      }
      await apiService.patch(`/users/${userDetails['_id']}`, data);
    } else {
      data['password'] = formState.password;
      await apiService.post('/users', data);
    }
    Router.push('/users');
  };

  const matchPassword = (value) => {
    return value === formState.password;
  };

  return (
    <Container className="formCard" fluid>
      <Card>
        <ValidationForm onSubmit={handleSubmit} ref={formRef}>
          <Card.Body>
            <Row>
              <Col>
                <Card.Title>{userDetails && userDetails['_id'] ? "Edit User" : "Create User"}</Card.Title>
              </Col>
            </Row>
            <hr />

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label className="required-field">Username</Form.Label>
                  <TextInput
                    name="username"
                    id="username"
                    required
                    minLength="3"
                    value={formState.username}
                    errorMessage="You don't have a Username?"
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label className="required-field">Email</Form.Label>
                  <TextInput name="email" id="email" type="email"
                    validator={validator.isEmail}
                    required
                    errorMessage={{ validator: "Please enter a valid email" }}
                    value={formState.email}
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label>Role</Form.Label>
                  <SelectGroup
                    name="role"
                    value={formState.role}
                    errorMessage="Please select a role"
                    onChange={handleChange}>
                    <option value="">Select Role</option>
                    {
                      roles.map((item, index) => {
                        return (<option key={index} value={item._id}>{item.title}</option>)
                      })
                    }
                  </SelectGroup>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label>Institution</Form.Label>
                  <SelectGroup
                    name="institution"
                    value={formState.institution}
                    errorMessage="Please select a Institution."
                    onChange={handleChange}
                  ><option value="">Select Institution</option>
                    {
                      institutions.map((item, index) => {
                        return (<option key={index} value={item._id}>{item.title}</option>)
                      })
                    }
                  </SelectGroup>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label>Country</Form.Label>
                  <SelectGroup
                    name="country"
                    id="country"
                    value={formState.country}
                    onChange={handleChange}
                  >
                    <option value="">Select Country</option>
                    {countryList.map((item, _i) => {
                      return <option key={item._id} value={item._id}>{item.title}</option>;
                    })}
                  </SelectGroup>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label>Region</Form.Label>
                  <MultiSelect overrideStrings={{ selectSomeItems: "Select Country Regions", allItemsAreSelected: "All Regions are Selected", }} options={regions} value={formState.region} onChange={bindCountryRegions} className={"region"} labelledBy={"Select Country Regions"} />

                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label className="required-field">Password</Form.Label>
                  {userDetails && userDetails['_id'] ?
                    <TextInput name="password" id="password" type="password"
                      pattern="(?=.*[A-Z]).{8,}"
                      errorMessage={{ pattern: "Password should be at least 8 characters and contains at least one upper case letter" }}
                      value={formState.password}
                      onChange={handleChange}
                    /> :
                    <TextInput name="password" id="password" type="password"
                      required
                      pattern="(?=.*[A-Z]).{8,}"
                      errorMessage={{ required: "Password is required", pattern: "Password should be at least 8 characters and contains at least one upper case letter" }}
                      value={formState.password}
                      onChange={handleChange}
                    />
                  }
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <Form.Label className="required-field">Confirm Password</Form.Label>
                  {userDetails && userDetails['_id'] ?
                    <TextInput
                      name="confirm_password"
                      id="confirm_password"
                      type="password"
                      validator={matchPassword}
                      errorMessage={{ required: "Confirm password is required", validator: "Password does not match" }}
                      value={formState.confirm_password}
                      onChange={handleChange}
                    />
                    :
                    <TextInput
                      name="confirm_password"
                      id="confirm_password"
                      type="password"
                      required
                      validator={matchPassword}
                      errorMessage={{ required: "Confirm password is required", validator: "Password does not match" }}
                      value={formState.confirm_password}
                      onChange={handleChange}
                    />
                  }
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col>
                <Form.Group>
                  <label>Enabled</label>
                  <Radio.RadioGroup name="enabled" errorMessage="It is required" valueSelected={formState.enabled} onChange={handleChange}>
                    <Radio.RadioItem id="yes" label="Yes" value="true" />
                    <Radio.RadioItem id="no" label="No" value="false" />
                  </Radio.RadioGroup>
                </Form.Group>
              </Col>
            </Row>

            <Row className="my-4">
              <Col xs lg="2">
                <Button type="submit" variant="primary">Submit</Button>
              </Col>
              <Col>
                <Button onClick={resetHandler} variant="info">Reset</Button>
              </Col>
            </Row>

          </Card.Body>
        </ValidationForm>
      </Card>
    </Container >
  );
};

export default UserForm;
