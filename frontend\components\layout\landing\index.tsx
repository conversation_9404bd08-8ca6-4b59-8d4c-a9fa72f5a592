//Import Library
import React, { useRef } from "react";
import { Container, Row, Col, Button } from "react-bootstrap";

//Import services/components
import Header from './Header';
import Slider from "./Slider";
import Welcome from "./Welcome";
import Networks from "./Networks";
import NewsFeed from "./NewsFeed";
import AboutUs from "./AboutUs";
import Footer from "./Footer";

const Layout: React.FunctionComponent<{}> = (): React.ReactElement => {
  const homeRef = useRef<HTMLDivElement | null>(null);
  const networkRef = useRef<HTMLDivElement | null>(null);
  const aboutUsRef = useRef<HTMLDivElement | null>(null);
  const footerRef = useRef<HTMLDivElement | null>(null);

  /**
   * Scrolls to the respective ref element
   * @param index: number
   */
  const onScrollToElement = (index: number): void => {
    const refs = [
      homeRef,
      networkRef,
      aboutUsRef,
      footerRef
    ];

    if (refs[index] && refs[index].current) {
      const headerHeight = 100;
      const topOfElement = (window.pageYOffset + refs[index].current.getBoundingClientRect().top - headerHeight);
      window.scrollTo({ behavior: "smooth", top: topOfElement });
    }
  }

  return (
    <div className="landing">
      <Header onScroll={onScrollToElement} />
      <div className='horizontal-line' />
      <div className="home" ref={homeRef}>
        <Slider />
      </div>
      <Welcome />
      <div ref={networkRef} >
        <Row className='feeds-section'>
          <Networks />
          <NewsFeed />
        </Row>
      </div>
      <AboutUs innerRef={aboutUsRef} />
      <Footer innerRef={footerRef} />
    </div>
  );
}

export default Layout;
