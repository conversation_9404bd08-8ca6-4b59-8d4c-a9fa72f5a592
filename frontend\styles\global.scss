@import "~bootstrap/scss/bootstrap";
@import "./fontawesome/fontawesome";
@import "./fontawesome/brands";
@import "./fontawesome/regular";
@import "./fontawesome/solid";
@import "~react-big-calendar/lib/sass/styles";
@import "~nprogress/nprogress.css";
@import "~react-big-calendar/lib/addons/dragAndDrop/styles";
@import url("https://fonts.googleapis.com/css?family=Fira+Sans:400,400i,500,500i,600,600i,700,700i,800,800i,900,900i&display=swap");
@import url("https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans:400,500,700");
@import "~react-alice-carousel/lib/scss/alice-carousel.scss";
@import "~react-datepicker/dist/react-datepicker.css";
@import "~react-bootstrap-range-slider/dist/react-bootstrap-range-slider.css";
//Components SCSS import
@import "components";
//Pages SCSS import
@import "pages";
@import "../components/common/widgets/twitterTimeline.scss";

body {
  margin: 0;
  overflow-x: hidden;
  font-family: "Fira Sans", sans-serif !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    text-decoration: none;
  }

  .fa {
    font-family: 'Font Awesome 5 Free', sans-serif;
  }

  .btn-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;

    &:hover {
      color: #fff;
      background-color: #138496;
      border-color: #117a8b;
    }

    &:focus {
      color: #fff;
      background-color: #138496;
      border-color: #117a8b;
      box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
    }

    &:disabled {
      color: #fff;
      background-color: #17a2b8;
      border-color: #17a2b8;
    }
  }

  .rki-table {
    .btn {
      margin: 2px;
    }
  }
}

.bar {
  height: 3px !important;
  background: #54799b !important;
}

.modal-dialog {
  .modal-header {
    padding: 10px 20px;
    align-items: center;

    .close {
      font-family: "Roboto", sans-serif !important;

      &:focus {
        outline: none;
      }
    }
  }

  .modal-content {
    overflow: hidden;
  }

  .modal-body {
    max-height: calc(100vh - 115px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .list-group-item {
    border: none;
    border-bottom: 1px solid #ededed;
    padding: 10px 3px;

    &:last-child {
      border: none;
    }
  }
}

.privacyCard {
  margin-top: 5px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.formCard {
  padding: 2px 5px 5px;

  .card {
    margin-top: 5px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  }
}

.header-block {
  padding: 20px 0px;

  h6 {
    width: 100%;
    border-bottom: 1px solid rgb(209, 209, 209);
    line-height: 22px;
    margin: 10px 0 20px;
    font-weight: bold;
  }

  h6 span {
    background: #fff;
    padding: 0px 10px 0px 0px;
  }
}

.alphabetBlock {
  .pagination {
    justify-content: flex-end;

    a {
      cursor: pointer;
    }
  }

  .alphabetLists {
    column-count: 3;
    padding: 10px 0;
    position: relative;
    min-height: 120px;

    .noresultFound {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      padding: 20px 0;
      border: 1px solid #f3f3f3;
      border-radius: 4px;
      margin: 25px 0 0;
      box-shadow: 1px 1px 9px rgba(150, 150, 150, 0.08) !important;
    }

    .alphaListItems {
      text-decoration: none;
      padding: 6px 0;
      display: block;
      width: 100%;
      float: none;
      clear: both;
      -o-column-break-inside: avoid;
      -ms-column-break-inside: avoid;
      -webkit-column-break-inside: avoid;
      page-break-inside: avoid;
      break-inside: avoid-column;

      a {
        color: #565656;

        &:hover {
          color: #2ca8ff;
        }
      }
    }
  }
}

.alphabetContainer {
  display: flex;
  flex-wrap: wrap;

  .alphabetItems {
    font-weight: 500;
    margin: 10px 12px 5px 0;
    padding: 6px 11px;
    border-style: solid;
    border-color: #e5e5e5;
    border-width: 1px;
    background-color: #f9f9f9;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    cursor: pointer;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover,
    &.active {
      background: #00a8ff;
      color: #fff;
    }
  }
}

// react date-pcicker
.react-datepicker-wrapper {
  .react-datepicker__input-container input {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
}

// custom error label
.error-label {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}

.error-field {
  border-color: #dc3545 !important;
  padding-right: calc(1.5em + 0.75rem);
  // background-image: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' hei…circle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e);
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.searchInput {
  border-radius: 20px;
  background: #fafafa;
}

select.form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 20px;
  background: #f9f9f9;
  padding-right: 23px;
  background-position: 99% center;
  background-repeat: no-repeat;
  color: grey;
  background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="grey" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"><path d="M6 9L12 15 18 9"></path></svg>');
  // background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAMCAYAAABSgIzaAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDZFNDEwNjlGNzFEMTFFMkJEQ0VDRTM1N0RCMzMyMkIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDZFNDEwNkFGNzFEMTFFMkJEQ0VDRTM1N0RCMzMyMkIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo0NkU0MTA2N0Y3MUQxMUUyQkRDRUNFMzU3REIzMzIyQiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo0NkU0MTA2OEY3MUQxMUUyQkRDRUNFMzU3REIzMzIyQiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PuGsgwQAAAA5SURBVHjaYvz//z8DOYCJgUxAf42MQIzTk0D/M+KzkRGPoQSdykiKJrBGpOhgJFYTWNEIiEeAAAMAzNENEOH+do8AAAAASUVORK5CYII=);
}

.multi-select {
  .dropdown {
    border-radius: 20px;
    background: #fafafa;

    .gray {
      color: #808080;
    }
  }

  li {
    margin: 0;
  }

  .select-item {
    padding: 5px 10px;
    margin: 0;

    span {
      display: flex;
      flex-direction: row;
      font-size: 15px;

      input {
        margin: 4px 3px 0 0;
      }
    }
  }
}

#main-content {
  .mb-3 {
    margin-bottom: 1rem;
  }

  .was-validated .form-control:valid,
  .was-validated .form-control:valid:focus,
  .form-control.is-valid:focus,
  .form-control.is-valid {
    border-color: #ced4da;
    box-shadow: none;
    background-size: 24px;
    background-position: 99% center;
  }

  select.form-control:valid:focus,
  select.form-control:valid,
  select.form-control.is-valid:focus,
  select.form-control.is-valid {
    background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="grey" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"><path d="M6 9L12 15 18 9"></path></svg>');
  }

  input.form-control:valid:focus,
  input.form-control.is-valid:focus,
  input.form-control.is-valid {
    background-image: none;
  }

  .was-validated .form-check-input:valid~.form-check-label,
  .form-check-input.is-valid~.form-check-label {
    color: rgba(0, 0, 0, 0.7);
  }
}

.react-toast-notifications__container {
  z-index: 9999 !important;
}

.info-section {
  margin-top: 0px;
  font-size: 13px;
}

.required-field::after {
  content: "*";
  color: red;
}

.disclaimer {
  font-size: 8px;
}

.content-block h4 button {
  background: transparent;
  border: none;
  color: #707ec5;
  font-size: 16px;
}

.projectTitleBlock h4 button,
.institution-image-inner-content h4 button {
  color: #e9e9e9;
}

.imprintDetails {
  .imprintBlock {
    border-bottom: 1px solid #e9e9e9;
    margin: 0 0 10px;

    &:last-child {
      border: none;
      margin: 0;
    }

    h6 {
      margin: 0;
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}

.spinner-border {
  display: block;
  position: absolute;
  z-index: 1031;
  top: 50%;
  left: 50%;
  color: #1e7bb2 !important;
  border-width: 3px;
}

// Responsive
@media screen and (max-width: 560px) {
  .main-container {
    .alphabetBlock .alphabetLists {
      column-count: 1;
    }
  }
}

@media print {
  div {
    page-break-inside: avoid;
    padding: initial;
  }

  .updatesList {
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      border-bottom: 1px solid #eee;
      clear: both;
      padding: 15px 0 10px;
      overflow: hidden;
    }

    .timeline-badge {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      border: 1px solid #eee;
      text-align: center;
      float: left;

      i {
        color: #787878;
        margin-top: -2px;
      }
    }

    .timeline-content {
      float: left;
      margin: -5px 0 0;
      width: calc(100% - 55px);
    }

    span.title {
      display: block;
      font-weight: 600;
      line-height: 18px;
      font-size: 15px;
    }

    span.date {
      font-size: 15px;
      display: block;
    }

    .description {
      font-size: 15px;
      margin: 0;
      padding: 0;
    }

    .updatesAction {
      display: none;
    }
  }

  .dashboardScreen {
    .aboutUs {
      text-align: center;
    }

    .infoCard {

      .card-header,
      .card-body {
        text-align: center;
      }

      .card-footer {
        display: none;
      }
    }

    .rbc-calendar {
      // display: none;
      height: 300px;
      margin: 20px 0 5px;
      width: 100%;
    }

    .ongoingBlock {
      text-align: center;
    }

    .announcements {
      p {
        font-size: 14px;
      }

      a {
        color: #505050;
        text-decoration: none;
        font-size: 15px;
        font-weight: 600;
        position: relative;
      }
    }
  }

  .content-block {
    width: 100% !important;
  }

  .sidebar-rightregion {
    display: none;
  }
}

.source_link {
  background-color: white;
  color: black;
  padding: 10px;
  border-radius: 15px;

  a {
    outline: none !important;
  }

}

.region__table {
  .rki-table {
    overflow: inherit !important;
  }
}

.btn--download {
  text-decoration: none !important;

  &:active:focus,
  &:focus {
    outline: none !important;
  }
}

.modal--icon {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #d6deec;
}

.modal--align {
  display: flex;
  justify-content: center;
  align-items: center;
}

//Media gallery preview
.preview {
  cursor: pointer;
  position: absolute;
  max-height: 100%;
  background-size: cover;
  width: 100%;
}


.docPreview {
  height: 70%;
  display: flex;
  margin-left: 20px;
}

.docImagePreview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 150px;
  height: 150px;
  background-size: cover;
  margin: 25px;
  padding-bottom: 25px;
}

.imgPreview {
  background-size: cover;
}

.dropdown-container {
  border-radius: 200px !important;
}

.dropdown-content li .item-renderer {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.dropdown-content li .item-renderer input {
  margin: 4px 0 0;
}

.p-0.outlineButton.btn.btn-link.btn-sm {
  box-shadow: none;
  font-size: 11.8px;
}

.pb-5.modal-body {
  height: 400px !important;
}

.rbc-calendar {
  min-height: 350px;
  height: 350px;
}

.active-projects-announcements {
  position: relative;
}

.sidebar-rightregion {
  width: auto;
}



// element.style {
//   height: 100%;
//   width: 100%;
//   position: absolute;
//   top: 0px;
//   left: 0px;
//   background-color: rgb(14 22 38) !important;
// }

.cke {
  margin-bottom: 15px !important;
}

.rki-table {
  overflow: auto !important;
}

.rmsc {
  .dropdown-container {
    .dropdown-content {
      input[type="checkbox"] {
        margin-right: 5px;
      }

    }

  }

}

.was-validated select.form-control:valid,
select.form-control.is-valid {
  padding-right: 20px !important;
}

.gm-style-cc {
  display: none;
}

.sc-bBHxTw {
  padding: 10px;
}

.Focalpoint {
  font-size: 11.8px;
  font-weight: 600;
}

.form-check {
  position: relative;
  display: block;
  padding-left: 0px;
}

.pb-4.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}
.modal-content{
  .pb-4.form-check {
  display: inline-flex;
  padding-left: 0.2rem;
}
}
.check {
  padding-left: 1.25rem !important;
}

.mapprint {
  width: 100%;
  margin-left: -30%;
}

.map-container {
  overflow: hidden;
}

@media print {
  .gmnoscreen {
    display: none;
  }

  .gm-control-active.gm-fullscreen-control {
    display: none;
  }

  .p-0.sidebar-region.false {
    display: none;
  }

  .map-container {
    width: 100% !important;
  }

  .gm-style {
    background: #0e1626;
    background: -moz-linear-gradient(top, #0e1626 36%, #023e58 65%);
    background: -webkit-linear-gradient(top, #0e1626 36%, #023e58 65%);
    background: linear-gradient(to bottom, #0e1626 36%, #023e58 65%);
  }
}
._institutionfocalpoint{
  padding: 10px 0px;
}._institutionfocalpoint .updateActions {
  right: 12px;
  top: 20px;
}
.operationDesc table{width: 100% !important;}
.needs-validation .form-check {
  padding-left: 20px;
}
.operationDetail{
  padding: 10px 0px;
}.operationDetail .updateActions {
  right: 12px;
  top: 20px;
}
.projectDetail {
  padding: 10px 0px;
}.projectDetail .updateActions {
  right: 12px;
  top: 20px;
}
.projectDetail .projectRow {
  overflow: hidden;
}

.eventDetail {
  padding: 10px 0px;
}.eventDetail .updateActions {
  right: 12px;
  top: 20px;
}

.vspaceDetails {
  padding: 10px 0px;
}.vspaceDetails .updateActions {
  right: 12px;
  top: 20px;
}
.contacts-us-modal{
  .modal-body{
    .row{
      margin-bottom: 15px;
      label{padding-right: 0px;}
  }
  }
}
.invalid-feedback {
    font-size: 80%;
}
.helps-modal{
  .modal-body{
    ul{list-style-type: none;
      padding: 0px;
    }
    
  }
}
.accordion-button:focus {
        box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
    }
@media (min-width: 768px) {
    .col-md-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }
}
.float-right {
    float: right !important;
}
.rmsc .dropdown-heading .dropdown-heading-value {
    text-align: left;
}
.mb-3 {
    margin-bottom: 1rem !important;
}
.rdt_Table {
    border-radius: 4px;
    border: 1px solid #eaeaea;
    margin: 10px 0 0;
    box-shadow: 1px 1px 15px rgba(150, 150, 150, .1) !important;
    height: auto;
    min-height: 50vh;
}
.sc-gfUXbm {
    padding: 10px;
}
.custom-file-label:after {
    bottom: 0;
    z-index: 3;
    display: block;
    height: calc(1.5em + .75rem);
    content: "Browse";
    background-color: #e9ecef;
    border-left: inherit;
    border-radius: 0 .25rem .25rem 0;
}.custom-file-label, .custom-file-label:after {
    position: absolute;
    top: 0;
    right: 0;
    padding: .375rem .75rem;
    line-height: 1.5;
    color: #495057;
}.custom-file-input~.custom-file-label[data-browse]:after {
    content: attr(data-browse);
}
.custom-file-input {
    z-index: 2;
    margin: 0;
    overflow: hidden;
    opacity: 0;
}
.custom-file, .custom-file-input {
    position: relative;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
}
.position-relative.px-3.py-2.false.col-auto {
  width: 100%;
}
.infoCard_admin_card{
  .infoCard .card-body p, .infoCard .card-body a {
    color: #6b6a6a;
  } 
}
.countryAccordionNew.accordion, .operationAccordion .accordion, .projectAccordion .accordion, .eventAccordion .accordion, .vspaceAccordion .accordion {
  margin-top: 1.3rem;
  .cardArrow{
    display: none;
  }
} 
.countryBtn{
  width: 100%;
  border-radius: 4px;
}
.dropdown-content, .css-hlgwow, .css-1nmdiq5-menu{text-align: left;}
.switch-right{
  position: relative;
  padding-left: 8px;
  .form-check-input {
    margin-left: 0em;
  }
}
.rdt_TableCell{
  ul{
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    li {
      padding: 0 0 4px;
      }
      li:after {
          content: ",";
          margin: 0 5px 0 1px;
          color: #3f51b5;
      }
    
  }
}
.rdt_TableHeadRow {
    background-color: #edf4f4 !important;
    border-bottom: 2px solid #2da0dc !important;
}
.rdt_TableCol {
    font-size: 15px;
    padding: 10px;
    border-right: 1px solid #d2d2d2;
    overflow: hidden;
}.rdt_TableCol_Sortable {
    justify-content: space-between !important;
        font-weight: 300;
}.sort-icon {
    transform: rotate(90deg) !important;
    opacity: 1 !important;
    color: #ababab !important;
}.rdt_TableCell {
    color: rgba(0, 0, 0, .7);
    border-right: 1px solid #dedede;
    padding: 10px 12px;
}
.rdt_TableRow:nth-child(odd) {
    background-color: #fff;
}
.rdt_TableRow{
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
.rdt_TableRow:not(:last-of-type) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
.form-check .form-check-input {
    margin-left: 0px;
    margin-right: 7px;
}.organisationInfo{
  .list-group-item{
    border: 0px;
  }
}
.alice-carousel__prev-btn-item {
    top: 40% !important;
    left: -16px !important;
}
.alice-carousel__next-btn-item {
    top: 40% !important;
    right: -29px !important;
}