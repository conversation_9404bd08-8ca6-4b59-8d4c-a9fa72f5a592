# Install the app dependencies in a full Node docker image
FROM node:16
  
WORKDIR "/app"

# Install OS updates 
RUN apt-get update \
 && apt-get dist-upgrade -y \
 && apt-get clean \
 && echo 'Finished installing dependencies'

# Copy package.json and package-lock.json
COPY package*.json ./

# Install app dependencies
RUN npm install && npm install -g rimraf

COPY . .

# Building app
RUN npm run build

# Copy the dependencies into a Slim Node docker image
FROM node:16-slim
  
WORKDIR "/app"

# Install OS updates 
RUN apt-get update \
 && apt-get dist-upgrade -y \
 && apt-get clean \
 && echo 'Finished installing dependencies'

# Install app dependencies
COPY --from=0 /app ./

ENV NODE_ENV production

# USER node

CMD ["npm", "run", "start:prod"]