//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { useTranslation } from 'next-i18next';
import { ProjectStatus } from "../../../types";
import apiService from "../../../services/apiService";

interface ProjectstatusFormProps {
    [key: string]: any;
}

const ProjectstatusForm = (props: ProjectstatusFormProps) => {
    const { t } = useTranslation('common');
    const _initialprojectstatus = {
        title: "",
    };

    const [initialVal, setInitialVal] = useState<ProjectStatus>(_initialprojectstatus);

    const editform = props.routes && props.routes[0] === "edit_projectstatus" && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialprojectstatus);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "toast.Projectstatusisupdatedsuccessfully";
            response = await apiService.patch(`/projectstatus/${props.routes[1]}`, obj);
        } else {
            toastMsg = "toast.Projectstatusisaddedsuccessfully";
            response = await apiService.post("/projectstatus", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/projectstatus");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const projectstatusParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getProjectstatusData = async () => {
                const response: ProjectStatus = await apiService.get(`/projectstatus/${props.routes[1]}`, projectstatusParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getProjectstatusData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.ProjectStatus.ProjectStatus")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.ProjectStatus.ProjectStatus")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => value.trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.ProjectStatus.PleaseAddtheProjectStatus"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.ProjectStatus.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.ProjectStatus.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/projectstatus`}
                                        >
                                        <Button variant="secondary">{t("adminsetting.ProjectStatus.Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default ProjectstatusForm;
