//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';


//Import services/components
import { DeploymentStatusController } from './deployment-status.controller';
import { DeploymentStatusService } from './deployment-status.service';
// SCHEMAS
import { DeploymentStatusSchema } from '../../schemas/deployment_status.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'DeploymentStatus', schema: DeploymentStatusSchema }
    ])
  ],
  controllers: [DeploymentStatusController],
  providers: [DeploymentStatusService],
})

export class DeploymentStatusModule { }