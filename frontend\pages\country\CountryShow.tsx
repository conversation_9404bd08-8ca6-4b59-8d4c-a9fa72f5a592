//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";

//Import services/components
import CountryCoverSection from "./components/CountryCoverSection";
import CountryButtonSection from "./components/CountryButtonSection";
import CountryAccordionSection from "./components/CountryAccordionSection";
import UpdatePopup from "../../components/updates/UpdatePopup";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

interface CountryShowProps {
  routes: string[];
}

const CountryShow = (props: CountryShowProps) => {
  const { t } = useTranslation('common');
  const initialVal = {
    title: "",
    health_profile: "",
    security_advice: "",
    _id: "",
  };

  const statsInitialVal = {
    operation_count: 0,
    project_count: 0,
    event_count: 0,
  };

  const [countryData, setCountryData] = useState<any>(initialVal);
  const [countryStatsData, setCountryStatsData] = useState<any>(statsInitialVal);
  const [latlng, setLatLng] = useState<any>({});
  const [operationStatusId, SetOperationStatusId] = useState<any>();
  const [projectStatusId, SetProjectStatusId] = useState<any>();
  const [loading] = useState<boolean>(false);
  const [eventStatusId, SetEventStatusId] = useState<any>();

  //Showing Image & document
  const [document, setDocuments] = useState<any[]>([]);
  const [updateDocument, setUpdateDocuments] = useState<any[]>([]);
  const [images, setImages] = useState<any[]>([]);
  const [docSrc] = useState<any[]>([]);
  const [imgSrc, setImgSrc] = useState<any[]>([]);
  //end

  let operationParams: any = {
    sort: { created_at: "asc" },
    query: { country: props.routes[1] },
    limit: "~",
    select:
      "-contact_details -end_date -start_date -description -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at",
  };

  let operationDocParams: any = {
    sort: { doc_created_at: "asc" },
    query: {},
    limit: "~",
    DocTable: true,
    select:
      "-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at",
  };

  let updateParams: any = {
    sort: { doc_created_at: "asc" },
    query: {},
    limit: "~",
    DocUpdateTable: true,
    select:
      "-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at",
  };

  const fetchLinkedDoc = async (sortParams) => {
    const updates = await apiService.get(
      `/country/${props.routes[1]}`,
      sortParams
    );
    let docs = [];
      updates?.data?.map((item) => {
        item?.document?.length > 0 &&
        item.document.map((ele, i) => {
          let description = item.document[i].docsrc;
          ele.description = description;
          docs.push(ele);
        });
      });
    setDocuments(docs.flat());
  };

  const fetchLinkedUpdateDoc = async () => {
    const updates = await apiService.get(
      `/country/${props.routes[1]}`,
      updateParams
    );
    let docs = [];
      updates?.data?.map((item) => {
        item?.document?.length > 0 &&
        item.document.map((ele, i) => {
          let description = item.document[i].docsrc;
          ele.description = description;
          docs.push(ele);
        });
      });
    setUpdateDocuments(docs.flat());
  };

  const getDocuments = async () => {
    let _imgSrc = [];
    let _docSrc = [];
    let _documents = [];
    let _images = [];
    let response = await apiService.get(`/operation`, operationParams);
    if (response) {
        response?.data.map((item, _i) => {
          if (item?.document?.length > 0) {
            _documents.push(item.document);
            _docSrc.push(item.doc_src);
          }
          if (item?.images?.length > 0) {
            _images.push(item.images);
            _imgSrc.push(item.images_src);
          }
        });

      setImages(_images.flat(Infinity));

      setImgSrc(_imgSrc.flat(Infinity));
    }
  };

  const updateSort = (data) => {
    let updatesortParams: any = {
      sort: {},
      query: { parent_country: props.routes[1] },
      limit: "~",
      DocTable: true,
      select:
        "-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at",
    };
    updatesortParams.sort = {
      [data.columnSelector]: data.sortDirection,
    };
    fetchLinkedDoc(updatesortParams);
  };
  const updateDocumentSort = (data) => {
    let updatesortParams: any = {
      sort: {},
      query: { parent_country: props.routes[1] },
      limit: "~",
      DocUpdateTable: true,
      select:
        "-contact_details -end_date -images -start_date -description -images -link -media -reply -parent_vspace -user -type -updated_at -update_type -created_at",
    };
    updatesortParams.sort = {
      [data.columnSelector]: data.sortDirection,
    };
    fetchLinkedUpdateDoc();
  };

  const fetchProjectStatus = async (_countryParams) => {
    const response = await apiService.get("/projectStatus");
    if (response?.data?.length > 0) {
      let statusId: any = [];
      _.forEach(response.data, function (item) {
        if (item.title == "Ongoing" || item.title == "Planning") {
          statusId.push(item._id);
        }
      });
      SetProjectStatusId(statusId);
    }
    return false;
  };

  const getCountryStatsData = async (countryParams) => {
    let response = await apiService.get(
      `/stats/country/${props.routes[1]}`,
      countryParams
    );
    setCountryStatsData(response);
  };

  const fetchOperationStatus = async (_countryParams) => {
    const response = await apiService.get("/operation_status");
    if (response?.data?.length > 0) {
      let statusId: any = [];
      _.forEach(response.data, function (item) {
        if (
          item.title == "Deployed" ||
          item.title == "Mobilizing" ||
          item.title == "Monitoring"
        ) {
          statusId.push(item._id);
        }
      });
      SetOperationStatusId(statusId);
    }
    return false;
  };
  useEffect(() => {
    if (props?.routes[1]) {
      const getCountryData = async (countryParams) => {
        let response = await apiService.get(
          `/country/${props.routes[1]}`,
          countryParams
        );
        setLatLng({
          lat: parseFloat(
            response &&
              Array.isArray(response.coordinates) &&
              response.coordinates[0] &&
              response.coordinates[0].latitude
              ? response.coordinates[0].latitude
              : 20.593684
          ),
          lng: parseFloat(
            response &&
              Array.isArray(response.coordinates) &&
              response.coordinates[0] &&
              response.coordinates[0].longitude
              ? response.coordinates[0].longitude
              : 78.96288
          ),
        });
        setCountryData(response);
      };

      const fetchEventtStatus = async (_countryParams) => {
        const response = await apiService.get("/eventStatus", {
          query: { title: "Current" },
        });
        if (response?.data?.length > 0) {
          SetEventStatusId(response.data[0]._id);
        }
        return false;
      };

      getCountryData({});
      getCountryStatsData({});
      fetchOperationStatus({});
      fetchProjectStatus({});
      fetchEventtStatus({});
      fetchLinkedDoc(operationDocParams);
      fetchLinkedUpdateDoc();
      getDocuments();
    }
  }, []);

  /**Lang based health profile & security Advice*/
  let health_profile =
    countryData?.health_profile?.replace("en", t("healthProfileLang"));
  let security_advice;
  switch (t("securityAdviceLang")) {
    case "en":
    case "fr":
      security_advice =
        countryData?.security_advice?.replace(
          "/de/aussenpolitik/laender/",
          "/en/aussenpolitik/laenderinformationen/"
        );
      break;
    case "de":
      security_advice =
        countryData?.security_advice?.replace(
          "/de",
          `/${t("securityAdviceLang")}`
        );
      break;
  }
  /***End ******/

  let coverSectionData = {
    latlng : latlng,
    countryData : countryData,
    operationStatusId : operationStatusId,
    countryStatsData : countryStatsData,
    eventStatusId : eventStatusId,
    projectStatusId : projectStatusId
  }

  let buttonSectionData = {
    health_profile : health_profile,
    security_advice :security_advice
  }

  let accordionData = {
    prop : props,
    images : images,
    imgSrc : imgSrc,
    loading : loading,
    updateSort : updateSort,
    document : document,
    docSrc : docSrc,
    updateDocumentSort : updateDocumentSort,
    updateDocument : updateDocument
  }
  return (
    <div>
      <UpdatePopup routes={props.routes} />
      <CountryCoverSection {...coverSectionData} />
      <br />
      < CountryButtonSection {...buttonSectionData} />
      <CountryAccordionSection {...accordionData} />
    </div>
  );
};

export default CountryShow;

