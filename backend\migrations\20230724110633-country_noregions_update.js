const { isValidObjectId } = require('mongoose');
const { resolve } = require('path');
module.exports = {
  async up(db, client) {
    const countriesNoRegion = await db.collection('countries').aggregate([
      {
        $lookup: {
          "from": "regions",
          localField: "_id",
          foreignField: "country",
          as: "COUNTRY"
        }
      },
      {
        $project: {
          "country": { $arrayElemAt: ["$COUNTRY", 0] },
          "title": 1,
          "code": 1
        }
      },
      { '$match': { "country": { $eq: null } } }
    ]).toArray();

    async function reccUpdate(_array, _count, _countryId, _res, _rej) {
      if (_count < _array.length) {
        const region = _array[_count]
        const res = await db.collection('regions').updateOne({ "country": _countryId, "title": region },
          { $set: { "country": _countryId, "title": region, created_at: new Date(), updated_at: new Date() } },
          { upsert: true });
        if (res.acknowledged) {
          reccUpdate(_array, _count + 1, _countryId, _res, _rej)
        } else {
          _rej("unable to update regions")
        }
      } else {
        _res(true);
      }
    }

    async function reccUpdatePromise(newRegions, _countryId) {
      return new Promise(function (res, rej) {
        reccUpdate(newRegions, 0, _countryId, res, rej);
      })
    }

    async function recAddRegion(_array, _count, _res, _rej) {
      if (_count < _array.length) {
        const country = _array[_count];
        const newRegions = ["South", "North", "East", "West", "Central"];
        const res = await reccUpdatePromise(newRegions, country._id);
        if (res) {
          recAddRegion(_array, _count + 1, _res, _rej)
        }
      } else {
        _res(true);
      }
    }

    return new Promise(function (res, rej) {
      recAddRegion(countriesNoRegion, 0, res, rej);
    })
  },

  async down(db, client) { }
};
