//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const RiskLevelSchema = new mongoose.Schema({
  title: { type: String, required: true, unique: true },
  level: { type: Number, required: true, unique: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
});

RiskLevelSchema.plugin(mongoosePaginate);
