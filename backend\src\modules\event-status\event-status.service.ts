//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { EventStatusInterface } from '../../interfaces/event-status.interface';
import { CreateEventStatusDto } from './dto/create-event-status.dto';
import { UpdateEventStatusDto } from './dto/update-event-status.dto';

const EventStatus = 'Could not find Event Status.';
@Injectable()
export class EventStatusService {
  constructor(
    @InjectModel('EventStatus')
    private eventStatusModel: Model<EventStatusInterface>,
  ) {}

  async create(
    createEventStatusDto: CreateEventStatusDto,
  ): Promise<EventStatusInterface> {
    const createdEventStatus = new this.eventStatusModel(createEventStatusDto);
    return createdEventStatus.save();
  }

  async findAll(query): Promise<EventStatusInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.eventStatusModel.paginate(_filter, options);
  }

  async get(eventStatusId): Promise<EventStatusInterface[]> {
    let _result;
    try {
      _result = await this.eventStatusModel.findById(eventStatusId).exec();
    } catch (error) {
      throw new NotFoundException(EventStatus);
    }
    if (!_result) {
      throw new NotFoundException(EventStatus);
    }
    return _result;
  }

  async update(eventStatusId: any, updateEventStatusDto: UpdateEventStatusDto) {
    const getById: any = await this.eventStatusModel
      .findById(eventStatusId)
      .exec();
    const updatedData = new this.eventStatusModel(updateEventStatusDto);
    Object.keys(updateEventStatusDto).forEach((d) => {
      getById[d] = updatedData[d];
    });
    getById.updated_at = new Date();
    return getById.save();
  }

  async delete(eventStatusId: string) {
    const result = await this.eventStatusModel
      .deleteOne({ _id: eventStatusId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(EventStatus);
    }
  }
}
