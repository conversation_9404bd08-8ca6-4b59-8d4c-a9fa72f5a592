//Import Library
import { useEffect, useState } from "react";
import Link from "next/link";

//Import services/components
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';


function OrganizationTable(props) {
  const [tabledata, setDataToTable] = useState([]);
  const [, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage] = useState(10);
  const _countryId = props && props.routes ? props.routes[1] : null;
  const[pageSort, setPageSort] = useState(null);
  const { t,i18n } = useTranslation('common');
  const currentLang = i18n.language;

  const instParams = {
    sort: {},
    limit: perPage,
    page: 1,
    instiTable: true,
    query: { },
    languageCode:currentLang
  };

  const columns = [
    {
      name: t("Organisation"),
      selector: "title",
      cell: d => (
        <Link href="/institution/[...routes]" as={`/institution/show/${d._id}`}>
          {d.title}
        </Link>
      ),
      sortable: true,
      maxWidth: "200px"
    },
    {
      name: t("ContactName"),
      selector: "contact_name",
      cell: d => (
        d.user ? d.user.username : ''
      ),
      maxWidth: "200px"
    },
    {
      name: t("Expertise"),
      selector: "expertise",
      maxWidth: "200px"
    },
    {
      name: t("Region"),
      selector: "address.region",
      maxWidth: "200px"
    }
  ];

  const handleSort = async (column, sortDirection) => {
    setLoading(true);
    instParams.sort = {[column.selector]: sortDirection};
    const instSortParams = {
      sort: { [column.selector]: sortDirection },
      limit: perPage,
      page: 1,
      instiTable:true,
      query: {}
    };
    setPageSort(instSortParams)
    getInstData(instSortParams);
  };

  const getInstData = async (instParamsinit) => {
    setLoading(true);
    
    if(Object.keys(instParamsinit['sort']).length == 0){
      instParamsinit.sort = {created_at : 'desc'}
    }
    
    const response = await apiService.get(`/country/${_countryId}/institution`, instParamsinit);
      setLoading(true);
      if (response && response.data && response.data.length > 0) {
      response.data.forEach((element, index) => {
        response.data[index].expertise = element.expertise.map((e) => e.title).join(', ');
        response.data[index].address.region = element.address.region.map((e) => e.title).join(', ');
      });
      setDataToTable(response.data);
      setTotalRows(response.totalCount);
    }
    setLoading(false);
  };
  const handlePageChange = page => {
    instParams.limit = perPage;
    instParams.page = page;
    pageSort && (instParams.sort = pageSort.sort);
    getInstData(instParams);
  };

  const handlePerRowsChange = async (newPerPage, page) => {
    instParams.limit = newPerPage;
    instParams.page = page;
    pageSort && (instParams.sort = pageSort.sort);
    getInstData(instParams);
  };

  useEffect(() => {
    getInstData(instParams);
  }, []);
  return (
    <RKITable
      columns={columns}
      data={tabledata}
      totalRows={totalRows}
      handlePerRowsChange={handlePerRowsChange}
      handlePageChange={handlePageChange}
      persistTableHead
      onSort={handleSort}
    />
  );
}

export default OrganizationTable;
