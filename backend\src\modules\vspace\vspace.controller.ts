//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
  Req,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ACGuard,
  InjectRolesBuilder,
  <PERSON>sBuilder,
  UseRoles,
} from 'nest-access-control';

//Import services/components
import { CreateVspaceDto } from './dto/create-vspace.dto';
import { UpdateVspaceDto } from './dto/update-vspace.dto';
import { ResponseError } from '../../common/dto/response.dto';
import { VspaceService } from './vspace.service';
import { UsersService } from '../../users/users.service';
import { ImageService } from './../image/image.service';
import { SessionGuard } from 'src/auth/session-guard';
import { EmailService } from './../../email.service';
import { VspaceRequestSubscribersService } from './../vspace-request-subscribers/vspace-request-subscribers.service';
import { FilesService } from '../files/files.service';
import { RegionService } from '../region/region.service';
import { InstitutionService } from './../institution/institution.service';
import { UserInviteService } from './../user-invite/user-invite.service';
import { FlagService } from '../flag/flag.service';
import { UpdateService } from '../updates/update.service';

@Controller('vspace')
@UseGuards(SessionGuard)
export class VspaceController {
  constructor(
    private readonly _vspaceService: VspaceService,
    private readonly _emailService: EmailService,
    private readonly _vspaceRequestSubscribersService: VspaceRequestSubscribersService,
    private readonly _imageService: ImageService,
    private readonly _usersService: UsersService,
    private readonly _filesService: FilesService,
    private readonly _regionService: RegionService,
    private readonly _institutionService: InstitutionService,
    private readonly _userInviteService: UserInviteService,
    private readonly _flagService: FlagService,
    private readonly _updateService: UpdateService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) {}

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'vspace',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(
    @Body() createVspaceDto: CreateVspaceDto,
    @Req() request: Request,
  ) {
    const usersId = [];
    createVspaceDto['members'].map((item) => usersId.push(item));
    const user: any = request.user;
    usersId.push(user._id);
    createVspaceDto['user'] = user._id;
    createVspaceDto['subscribers'] = usersId;
    const resp = await this._vspaceService.create(createVspaceDto);
    const imageIds = resp['images'] ? resp['images'].map((d) => d._id) : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
    const documentIds = resp?.document?.map((d) => d['_id']) || [];
    if (documentIds.length > 0) {
      await this._filesService.bulkUpdate(documentIds);
    }
    const languageCode = createVspaceDto['language']
      ? createVspaceDto['language']
      : 'en';
    if (!resp.visibility && resp['vspace_email_invite']) {
      const user = await this._usersService.findOne({
        email: resp['vspace_email_invite'],
      });
      const d = {};
      d['title'] = resp['title'];
      d['username'] = user['username'];
      d['emailActivateToken'] = user.emailActivateToken;
      d['email'] = user['email'];
      this._emailService.vspaceInvite(d, languageCode, resp['id']);
    }
    return resp;
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'vspace',
    action: 'read',
    possession: 'any',
  })
  @Get()
  findAll(@Query() query: any) {
    return this._vspaceService.findAll(query);
  }

  @Post('filterUser')
  async filterUSer(@Body() obj: any, @Req() request: Request) {
    const filterUser = [];
    if (request.body.query.country) {
      const countryList = request.body.query.country;
      const _regions = await this._regionService.getMultipleRegions(
        countryList,
      );
      filterUser.push({ regions: _regions });
    }
    if (request.body.query.institution_type) {
      const institutionTypeList = request.body.query.institution_type;
      const _organisation =
        await this._institutionService.getfilteredInstitution(
          institutionTypeList,
        );
      filterUser.push({ organisation: _organisation });
    }
    const _userList = await this._userInviteService.getUserForInvite(
      request.body,
    );
    filterUser.push({ usersList: _userList });
    return filterUser;
  }

  @Post('filterNonmember')
  async filterNonmember(@Body() obj: any, @Req() request: Request) {
    return this._userInviteService.checkNonMemberList(request.body.email);
  }

  @Post('updateSubscriber')
  async updateSubscriber(@Body() obj: any, @Req() request: Request) {
    const user: any = request.user;
    const foundRecord: any = await this._vspaceService.get(obj['_id']);
    if (foundRecord) {
      if (obj['isSubscribe']) {
        this.vspaceFoundrecord(foundRecord, user);
      } else {
        this.vspaceSubscribers(foundRecord, user);
      }
      foundRecord.save();
    }
    return { status: 200, record: foundRecord };
  }

  private vspaceFoundrecord(foundRecord: any, user: any) {
    if (foundRecord['subscribers'] && foundRecord['members']) {
      foundRecord['subscribers'] = foundRecord['subscribers'].map((d) =>
        d._id.toString(),
      );
      foundRecord['members'] = foundRecord['members'].map((d) =>
        d._id.toString(),
      );
      if (foundRecord['subscribers'].indexOf(user._id.toString()) === -1) {
        foundRecord['subscribers'].push(user._id.toString());
      }
      if (foundRecord['members'].indexOf(user._id.toString()) === -1) {
        foundRecord['members'].push(user._id.toString());
      }
    } else {
      foundRecord['subscribers'] = [user._id];
      foundRecord['members'] = [user._id];
    }
  }

  private vspaceSubscribers(foundRecord: any, user: any) {
    if (foundRecord['subscribers']) {
      const newSub = [];
      foundRecord['subscribers'].forEach((d) => {
        if (d._id.toString() !== user._id.toString()) {
          newSub.push(d._id);
        }
      });
      foundRecord['subscribers'] = newSub;
    }
  }

  @Post('subscribeRequest')
  async subscribeRequest(@Body() obj: any, @Req() request: Request) {
    const user: any = request.user;
    const foundRecord: any = await this._vspaceService.get(obj['_id']);
    if (foundRecord?.user) {
      const data = {
        user: foundRecord.user.username,
        vspaceTitle: foundRecord.title,
        vspaceId: `${process.env.BASE_URL}/vspace/acceptvspace/${obj['_id']}/requestuser/${user._id}`,
        requestUser: user.username,
        userInstitution: user.institution ? user.institution.title : '',
      };
      const languageCode = obj.language ? obj.language : 'en';
      const resp = await this._emailService.requestVspaceSubscribe(
        data,
        foundRecord.user.email,
        languageCode,
      );
      return resp;
    } else {
      return { status: 400, error: 'Vpsace not found !!!' };
    }
  }

  @Post('requestStatus')
  async requestStatus(@Body() obj: any, @Req() request: Request) {
    const foundRecord: any = await this._vspaceRequestSubscribersService.get(
      obj['_id'],
    );
    if (foundRecord) {
      const data = {
        requestedUser: foundRecord.requested_by.username,
        requestedTo: foundRecord.requested_to.username,
        vspace: foundRecord.vspace.title,
        action: '',
      };
      const vspaceDetails: any = await this._vspaceService.get(
        foundRecord.vspace._id,
      );
      if (obj['status'] === 'approved') {
        vspaceDetails['subscribers'] = vspaceDetails['subscribers'].map(
          (d) => d._id,
        );
        vspaceDetails['members'] = vspaceDetails['members'].map((d) => d._id);
        vspaceDetails['subscribers'].push(foundRecord.requested_by._id);
        vspaceDetails['members'].push(foundRecord.requested_by._id);
        vspaceDetails.save();
        data.action = 'approved';
      } else {
        data.action = 'declined';
      }
      const languageCode = obj.language ? obj.language : 'en';
      await this._emailService.requestAcknowledgement(
        data,
        foundRecord.requested_by.email,
        languageCode,
      );
      await this._vspaceRequestSubscribersService.delete(obj['_id']);
      return { status: 200, msg: 'success' };
    } else {
      return { status: 400, error: 'Vpsace not found !!!' };
    }
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'vspace',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') vspaceId: string, @Req() request: Request) {
    if (request?.query?.Doctable) {
      return this._vspaceService.getSortedDocList(vspaceId, request.query);
    } else if (request?.query?.updateDoctable) {
      return this._vspaceService.getSortedUpdateDocList(
        vspaceId,
        request.query,
      );
    } else {
      return this._vspaceService.get(vspaceId);
    }
  }

  @UseGuards(ACGuard)
  @Patch(':id')
  async update(
    @Param('id') vspaceId: string,
    @Body() updateVspaceDto: UpdateVspaceDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    const oldVspace: any = await this._vspaceService.get(vspaceId);
    const vspaceUserId = oldVspace.user ? oldVspace.user._id : null;
    const permission =
      user._id == vspaceUserId
        ? this.roleBuilder.can(user.roles).updateOwn('vspace').granted
        : this.roleBuilder.can(user.roles).updateAny('vspace').granted;
    if (permission) {
      const resp = await this._vspaceService.update(vspaceId, updateVspaceDto);
      const imageIds = resp['images'] ? resp['images'].map((d) => d._id) : [];
      if (imageIds.length > 0) {
        await this._imageService.bulkUpdate(imageIds);
      }
      const documentIds = resp['document']
        ? resp['document'].map((d) => d['_id'])
        : [];
      if (documentIds.length > 0) {
        await this._filesService.bulkUpdate(documentIds);
      }
      const languageCode = updateVspaceDto['language']
        ? updateVspaceDto['language']
        : 'en';
      await this.vspaceEmail(resp, languageCode, vspaceId);
      return resp;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  private async vspaceEmail(resp: any, languageCode: any, vspaceId: any) {
    if (!resp.visibility && resp['vspace_email_invite']) {
      const user = await this._usersService.findOne({
        email: resp['vspace_email_invite'],
      });
      const d = {};
      d['title'] = resp['title'];
      d['username'] = user['username'];
      d['emailActivateToken'] = user.emailActivateToken;
      d['email'] = user['email'];
      this._emailService.vspaceInvite(d, languageCode, vspaceId);
    }
  }

  @UseGuards(ACGuard)
  @Delete(':id')
  async remove(@Param('id') vspaceId: string, @Req() request: Request) {
    try {
      const user: any = request.user;
      const oldVspace: any = await this._vspaceService.get(vspaceId);
      const vspaceUserId = oldVspace.user ? oldVspace.user._id : null;
      const permission =
        user._id == vspaceUserId
          ? this.roleBuilder.can(user.roles).deleteOwn('vspace').granted
          : this.roleBuilder.can(user.roles).deleteAny('vspace').granted;
      if (permission) {
        this._flagService.deleteFlagAssociatedWithEntity(vspaceId);
        this._updateService.deleteUpdateAssociatedWithEntity(vspaceId, "parent_vspace");
        return this._vspaceService.delete(vspaceId);
      } else {
        return new ResponseError('VSPACE.ERROR.ACCESS_DENIED');
      }
    } catch (e) {
      return new ResponseError('VSPACE.ERROR.NOT_FOUND');
    }
  }

  @UseGuards(ACGuard)
  @Post('acceptSubscriptionRequest/:id')
  async acceptSubscriptionRequest(
    @Param('id') vspaceId: string,
    @Body() updateVspaceDto: UpdateVspaceDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    const oldVspace: any = await this._vspaceService.get(vspaceId);
    const vspaceUserId = oldVspace.user ? oldVspace.user._id : null;
    const permission =
      user._id == vspaceUserId
        ? this.roleBuilder.can(user.roles).updateOwn('vspace').granted
        : this.roleBuilder.can(user.roles).updateAny('vspace').granted;
    if (permission) {
      const resp = await this._vspaceService.acceptSubscriptionRequest(
        vspaceId,
        updateVspaceDto,
      );
      return resp;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }
}
