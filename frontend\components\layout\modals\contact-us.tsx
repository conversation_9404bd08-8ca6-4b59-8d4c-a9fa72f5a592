//Import Library
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import React, { useRef } from "react";
import { Formik, Form as FormikForm, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';

//Import services/components
import { useTranslation } from 'next-i18next';
import apiService from "../../../services/apiService";

// Define validation schema
const ContactSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  position: Yup.string().required('Position is required'),
  organisation: Yup.string().required('Organisation is required'),
  subject: Yup.string().required('Subject is required'),
  message: Yup.string().required('Message is required'),
});

// Initial form values
const initialValues = {
  name: "",
  position: "",
  organisation: "",
  subject: "",
  message: "",
};

interface ContactUsProps {
  show: boolean;
  onHide: () => void;
  [key: string]: any;
}

const ContactUs = (props: ContactUsProps) => {
  const { t } = useTranslation('common');
  const formRef = useRef(null);

  // Handle form submission
  const handleSubmit = async (values: any, { resetForm, setSubmitting }: { resetForm: () => void; setSubmitting: (isSubmitting: boolean) => void }) => {
    try {
      await apiService.post("/users/contactUs", values);
      toast.success(t("toast.Contactusformsubmittedsuccessfully"));
      resetForm();
      props.onHide();
    } catch (error) {
      toast.error((error as any)?.message || "An error occurred");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal {...props} size="lg" aria-labelledby="contact-us-modal" className="contacts-us-modal">
      <Modal.Header closeButton>
        <Modal.Title className="text-capitalize" id="contact-us-modal">
          {t("contactUs.tab").toLocaleLowerCase()}
        </Modal.Title>
      </Modal.Header>

      <Formik
        initialValues={initialValues}
        validationSchema={ContactSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, touched, errors }) => (
          <FormikForm ref={formRef}>
            <Modal.Body id="main-content" style={{ width: "100%" }}>
              <p>{t("contactUs.tab1")}</p>

              {/* Name Field */}
              <Form.Group as={Row} controlId="formHorizontalName">
                <Form.Label column md="4" xs="5" lg="2" className="required-field">
                  {t("contactUs.tab2")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <Field
                    name="name"
                    as={Form.Control}
                    isInvalid={touched.name && !!errors.name}
                  />
                  <ErrorMessage name="name" render={msg => <Form.Control.Feedback type="invalid">{t("thisfieldisrequired")}</Form.Control.Feedback>} />
                </Col>
              </Form.Group>

              {/* Position Field */}
              <Form.Group as={Row} controlId="formHorizontalPosition">
                <Form.Label column md="4" xs="5" lg="2" className="required-field">
                  {t("contactUs.tab3")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <Field
                    name="position"
                    as={Form.Control}
                    isInvalid={touched.position && !!errors.position}
                  />
                  <ErrorMessage name="position" render={msg => <Form.Control.Feedback type="invalid">{t("thisfieldisrequired")}</Form.Control.Feedback>} />
                </Col>
              </Form.Group>

              {/* Organisation Field */}
              <Form.Group as={Row} controlId="formHorizontalOrganization">
                <Form.Label column md="4" xs="5" lg="2" className="required-field">
                  {t("contactUs.tab4")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <Field
                    name="organisation"
                    as={Form.Control}
                    isInvalid={touched.organisation && !!errors.organisation}
                  />
                  <ErrorMessage name="organisation" render={msg => <Form.Control.Feedback type="invalid">{t("thisfieldisrequired")}</Form.Control.Feedback>} />
                </Col>
              </Form.Group>

              {/* Subject Field */}
              <Form.Group as={Row} controlId="formHorizontalReasonofContact">
                <Form.Label column md="4" xs="5" lg="2" className="required-field">
                  {t("contactUs.tab5")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <Field
                    name="subject"
                    as="select"
                    className={`form-control ${touched.subject && errors.subject ? 'is-invalid' : ''}`}
                  >
                    <option value="">{t("subject.tab")}</option>
                    <option value="Membership">{t("subject.tab1")}</option>
                    <option value="Questions about Public Health Events">{t("subject.tab2")}</option>
                    <option value="Technical Support">{t("subject.tab3")}</option>
                    <option value="Others">{t("subject.tab4")}</option>
                    <option value="Remove consent">{t("subject.tab5")}</option>
                  </Field>
                  <ErrorMessage name="subject" render={msg => <Form.Control.Feedback type="invalid">{t("thisfieldisrequired")}</Form.Control.Feedback>} />
                </Col>
              </Form.Group>

              {/* Message Field */}
              <Form.Group as={Row} controlId="formHorizontalMessage">
                <Col md="12" xs="12" lg="12">
                <Form.Label className="required-field">{t("contactUs.tab6")}</Form.Label>
                <Field
                  name="message"
                  as="textarea"
                  rows={3}
                  className={`form-control ${touched.message && errors.message ? 'is-invalid' : ''}`}
                  style={{ backgroundImage: "none", borderColor: "#ced4da" }}
                />
                <ErrorMessage name="message" render={msg => <Form.Control.Feedback type="invalid">{t("thisfieldisrequired")}</Form.Control.Feedback>} />
                </Col>
              </Form.Group>

              {/* Submit Button */}
              <Form.Group as={Row}>
                <Col className="text-end" sm={{ span: 3, offset: 9 }}>
                  <Button type="submit" disabled={isSubmitting}>
                    {t("submit")}
                  </Button>
                </Col>
              </Form.Group>
            </Modal.Body>
          </FormikForm>
        )}
      </Formik>
    </Modal>
  );
};

export default ContactUs;
