//Import Library
import React, { useEffect, useState } from "react";

//Import services/components
import PeopleTableFilter from "./peopleTableFilter";
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

function PeopleTable(_props: any) {
    const [tabledata, setDataToTable] = useState<any[]>([]);
    const { t } = useTranslation('common');
    const [loading, setLoading] = useState<boolean>(false);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [perPage, setPerPage] = useState<number>(10);
    const [role] = useState<any[]>([]);
    const [institution] = useState<any[]>([]);
    const [filterText, setFilterText] = useState<string>("");
    const [pageSort, setPageSort] = useState<any>(null);
    const [resetPaginationToggle, setResetPaginationToggle] = useState<boolean>(false);

    const userParams: any = {
        sort: { created_at: "desc" },
        limit: perPage,
        page: 1,
        query: { status: { $ne: "Request Pending" }, vspace_status: { $ne: "Request Pending" } },
        select: "-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position",
    };

    const columns = [
        {
            name: t("People.form.Username"),
            selector: "username",
            cell: (d: any) => d.username,
            sortable: true,
        },
        {
            name: t("People.form.Email"),
            selector: "email",
            cell: (d: any) => d.email,
            sortable: true,
        },
        {
            name: t("People.form.Role"),
            selector: "roles",
            cell: (d) => (d.roles ? d.roles[0] : ""),
            sortable: true,
        },
        {
            name: t("People.form.Organisation"),
            selector: "institution",
            cell: (d) => (d.institution && d.institution.title ? d.institution.title : ""),
            sortable: true,
        },
    ];

    const getUserData = async (userParamsinit) => {
        setLoading(true);
        const response = await apiService.get("/users", userParamsinit);
        if (response && Array.isArray(response.data)) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handleSort = async (column, sortDirection) => {
        setLoading(true);
        userParams.sort = {
            // ...userParams.sort,
            [column.selector]: sortDirection,
        };
        await getUserData(userParams);
        setPageSort(userParams);
        setLoading(false);
    };

    const handlePageChange = (page) => {
        userParams.limit = perPage;
        userParams.page = page;
        pageSort && (userParams.sort = pageSort.sort);
        getUserData(userParams);
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        userParams.limit = newPerPage;
        userParams.page = page;
        setLoading(true);
        const response = await apiService.get("/users", userParams);
        if (response && Array.isArray(response.data)) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    useEffect(() => {
        getUserData(userParams);
    }, []);

    const subHeaderComponentMemo = React.useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText("");
            }
        };

        const handleSearchTitle = (query) => {
            if (query) {
                const emailRegex = /^[^@]+@[^@]+\.[^@]+$/;
                if (emailRegex.test(query.toLowerCase())) {
                    userParams.query = { ...userParams.query, ...{ email: query } };
                } else {
                    userParams.query = { ...userParams.query, ...{ username: query } };
                }
            }

            getUserData(userParams);
            userParams.query = { status: { $ne: "Request Pending" }, vspace_status: { $ne: "Request Pending" } };
        };

        const handleChange = (e) => {
            if (e && e.label) {
                setFilterText(e.label);
                handleSearchTitle(e.label);
            } else {
                userParams.query = { status: { $ne: "Request Pending" }, vspace_status: { $ne: "Request Pending" } };
                setFilterText("");
                getUserData(userParams);
            }
        };

        const handleKeypress = () => {
            //it triggers by pressing the enter ke
            onSearch();
        };

        const onSearch = () => {
            handleSearchTitle(filterText);
        };

        const userdataKeypress = (event) => {
            if (event.key === "Enter") {
                handleKeypress();
            }
        };

        return (
            <PeopleTableFilter
                onFilter={handleChange}
                onClear={handleClear}
                filterText={filterText}
                roles={role}
                onHandleSearch={onSearch}
                institutions={institution}
                onKeyPress={userdataKeypress}
            />
        );
    }, [filterText]);

    return (
        <div>
            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                subheader
                persistTableHead
                loading={loading}
                onSort={handleSort}
                sortServer
                pagServer={true}
                resetPaginationToggle={resetPaginationToggle}
                subHeaderComponent={subHeaderComponentMemo}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
}

export default PeopleTable;
