//Import services/components
import RKITable from "../../components/common/RKITable";
import _ from "lodash";

const VspaceSubscribeRequestUsers = (propData: any) => {
    const sortSubscribeRequestTousers = (rows: any, field: any, direction: any) => {
        const handleField = (row: any) => {
          if (row[field]) {
            return row[field].toLowerCase();
          }
          return row[field];
        };
        return _.orderBy(rows, handleField, direction);
    };

    return(
        <>
            <RKITable
              noHeader={true}
              columns={propData.SubscribeRequestUsers.requestedColumns}
              data={propData.SubscribeRequestUsers.vspaceRequest}
              dense={true}
              paginationServer
              pagServer={true}
              paginationTotalRows={0}
              subHeader
              subHeaderAlign="left"
              pagination={true}
              persistTableHead
              sortFunction={sortSubscribeRequestTousers}
            />
        </>
    )
};

export default VspaceSubscribeRequestUsers;