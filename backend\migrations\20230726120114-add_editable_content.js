module.exports = {
  async up(db, client) {
    const landingpagesData = await db.collection('landingpages').aggregate().toArray();
    const addDefaultLandingPages = ["Header", "About Us"];
    let dataTitles = landingpagesData.map(x => x.title);
    addDefaultLandingPages.forEach(ele => {
      if (!dataTitles.includes(ele.title)) {
        db.collection('landingpages').updateOne({ "title": ele }, 
        { $set: {"title": ele ,description: "<p>"+ele+"</p>",isEnabled: true, language: "en", created_at: new Date(), updated_at: new Date() } }, 
        { upsert: true });
      }
    })
  },

  async down(db, client) {
    const landingpagesData = await db.collection('landingpages').aggregate().toArray();
    const addDefaultLandingPages = ["Header", "About Us"];

    addDefaultLandingPages.forEach(ele => {
      if (landingpagesData.filter(x => x.title == ele) && landingpagesData.filter(x => x.title == ele).length > 0) {
        const data = landingpagesData.filter(x => x.title == ele)[0];
        if (data) {
          updateLandingPage(data._id);
        }
      }
    });

    async function updateLandingPage(id) {
      await db.collection('landingpages').deleteOne({ "_id": id });
    }
  }
};
