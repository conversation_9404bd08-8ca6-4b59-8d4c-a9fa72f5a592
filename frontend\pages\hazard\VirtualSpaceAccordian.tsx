//Import Library
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Card, Accordion } from "react-bootstrap";

//Import services/components
import VspaceTable from "../../components/common/VspaceTable";
import { useTranslation } from 'next-i18next';
import { useState } from "react";

interface VirtualSpaceAccordianProps {
  loading: boolean;
  Document: any[];
  updateDocument: any[];
  hazardDocSort: (data: { columnSelector: string; sortDirection: string }) => void;
  hazardDocUpdateSort: (data: { columnSelector: string; sortDirection: string }) => void;
  docSrc: string[];
  routeData?: {
    routes?: string[];
  };
}

const VirtualSpaceAccordian = (props: VirtualSpaceAccordianProps) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return(
        <>
           <Accordion.Item eventKey="0">
              <Accordion.Header onClick={() => setSection(!section)}>
                <div className="cardTitle">{t("LinkedVirtualSpace")}</div>
                <div className="cardArrow">
                  {section ? (
                    <FontAwesomeIcon icon={faMinus} color="#fff" />
                  ) : (
                    <FontAwesomeIcon icon={faPlus} color="#fff" />
                  )}
                </div>
              </Accordion.Header>
              <Accordion.Body>
                <VspaceTable
                  id={props.routeData?.routes?.[1] || ''}
                  type="Project"
                  vspaceData={[]}
                  vspaceDataLoading={false}
                  vspaceDataTotalRows={0}
                  vspaceDataPerPage={10}
                  vspaceDataCurrentPage={1}
                />
              </Accordion.Body>
            </Accordion.Item>
        </>
    )
};

export default VirtualSpaceAccordian;