//Import Library
import React from 'react';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

//Import services/components
import R403 from "./r403";

const internalStaticPages = ['data-privacy-policy', 'declarationform', 'profile', 'reset-password', 'search', 'forgot-password']
export const canAccessRoutes = connectedAuthWrapper({
  authenticatedSelector: (state: any, props: any) => {
    const permissions = state.permissions;
    let page = props.router.route.split("/")[1];
    if (page === ""){page = "dashboard";}
    if (page === "people"){ page = "users";}
    if (page === "adminsettings"){ page = "admin";}
    if (page === "events-calendar"){ page = "event";}
    if (page === "updates"){ page = "update";}

    if (permissions && permissions[page] && permissions[page]['read:any']) {
      return true;
    } else {
      if (internalStaticPages.includes(page)) {
        return true;
      }
    }
    return  <R403/>;
  },
  wrapperDisplayName: 'CanAccessRoutes',
});
export default canAccessRoutes;