//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateProjectStatusDto } from './dto/create-project-status.dto';
import { UpdateProjectStatusDto } from './dto/update-project-status.dto';
import { ProjectStatusService } from './project-status.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('projectstatus')
@UseGuards(SessionGuard)
export class ProjectStatusController {
  constructor(private readonly _projectstatusService: ProjectStatusService) {}

  @Post()
  async create(@Body() createProjectStatusDto: CreateProjectStatusDto) {
    try {
      const resp = await this._projectstatusService.create(
        createProjectStatusDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._projectstatusService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') projectstatusId: string) {
    return this._projectstatusService.get(projectstatusId);
  }

  @Patch(':id')
  async update(
    @Param('id') projectstatusId: string,
    @Body() updateProjectStatusDto: UpdateProjectStatusDto,
  ) {
    try {
      const resp = await this._projectstatusService.update(
        projectstatusId,
        updateProjectStatusDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') projectstatusId: string) {
    const deletedData = this._projectstatusService.delete(projectstatusId);
    return deletedData;
  }
}
