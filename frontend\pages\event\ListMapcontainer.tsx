//Import Library
import _ from "lodash";
import Link from "next/link";
import React, { useState, useEffect } from "react";

//Import services/components
import { useTranslation } from 'next-i18next';
import RKIMAP1 from "../../components/common/RKIMap1";
import RKIMapMarker from "../../components/common/RKIMapMarker";

const ListMapContainer = (props: any) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language;
  const { events } = props;
  const [groupedEvents, setGroupedEvents] = useState<{[key: string]: any[]}>({});
  const [points, setPoints] = useState<any[]>([]);
  const [activeMarker, setactiveMarker] = useState<any>({});
  const [markerInfo, setMarkerInfo] = useState<any>({});

  const MarkerInfo = (Markerprops: any) => {
    const { info } = Markerprops;
    if (info && info.countryId && groupedEvents[info.countryId]) {
      return (
        <ul>
          {groupedEvents[info.countryId].map((item, index) => {
            return (
              <li key={index}>
                <Link href="/event/[...routes]" as={`/${currentLang}/event/show/${item._id}`}>
                  {item.title}
                </Link>
              </li>
            );
          })}
        </ul>
      );
    }
    return null;
  };

  const resetMarker = () => {
    setactiveMarker(null);
    setMarkerInfo(null);
  };

  const onMarkerClick = (propsinit: any, marker: any, _e: any) => {
    resetMarker();
    setactiveMarker(marker);
    setMarkerInfo({
      name: propsinit.name,
      id: propsinit.id,
      countryId: propsinit.countryId,
    });
  };

  const setPointsFromEvents = () => {
    const eventFilterpoints = [];
    _.forEach(events, (event) => {
      eventFilterpoints.push({
        title: event.title,
        id: event._id,
        lat:
          event.country &&
          event.country.coordinates &&
          event.country.coordinates[0].latitude,
        lng:
          event.country &&
          event.country.coordinates &&
          event.country.coordinates[0].longitude,
        countryId: event.country && event.country._id,
      });
    });
    setPoints([...eventFilterpoints]);
  };

  useEffect(() => {
    setPointsFromEvents();
    setGroupedEvents(_.groupBy(events, "country._id"));
  }, [events]);

  return (
    <RKIMAP1
      onClose={resetMarker}
      language={currentLang}
      activeMarker={activeMarker}
      markerInfo={<MarkerInfo info={markerInfo} />}
    >
      {points.length >= 1
        ? points.map((item, index) => {
            if (item.lat) {
              return (
                <RKIMapMarker
                  key={index}
                  name={item.title}
                  id={item.id}
                  countryId={item.countryId}
                  icon={{
                    url: "/images/map-marker-white.svg",
                  }}
                  onClick={onMarkerClick}
                  position={item}
                />
              );
            }
          })
        : null}
    </RKIMAP1>
  );
};

export default ListMapContainer;
