import React, { useRef } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import TinyMC<PERSON> with SSR disabled
const Editor = dynamic(
  () => import('@tinymce/tinymce-react').then(mod => mod.Editor),
  { ssr: false }
);

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Write something...',
  height = 300,
  disabled = false,
}) => {
  const editorRef = useRef(null);

  return (
    <div className="rich-text-editor">
      {typeof window !== 'undefined' && (
        <Editor
          // Remove the apiKey property entirely
          onInit={(_evt, editor) => {
            (editorRef.current as any) = editor;
          }}
          initialValue={value}
          value={value}
          onEditorChange={(newValue) => {
            onChange(newValue);
          }}
          init={{
            height,
            menubar: false,
            plugins: [
              'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview',
              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
              'insertdatetime', 'table', 'code', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
              'bold italic forecolor | alignleft aligncenter ' +
              'alignright alignjustify | bullist numlist outdent indent | ' +
              'removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            placeholder,
            readonly: disabled as any,
            branding: false,
            promotion: false,
            skin: 'oxide',
            // Use local TinyMCE instead of cloud version
            setup: (editor: any) => {
              editor.on('init', () => {
                (editorRef.current as any) = editor;
              });
            }
          }}
        />
      )}
    </div>
  );
};

export default RichTextEditor;
