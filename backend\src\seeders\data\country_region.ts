export const countryRegion  = [
  {
    "ISO2": "AR",
    "Country_Region": "Buenos Aires"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Catamarca"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Chaco"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Chubut"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Cordoba"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Corrientes"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Entre Rios"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Formosa"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Jujuy"
  },
  {
    "ISO2": "AR",
    "Country_Region": "La Pampa"
  },
  {
    "ISO2": "AR",
    "Country_Region": "La Rioja"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Mendoza"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Misiones"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Neuquen"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Rio Negro"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Salta"
  },
  {
    "ISO2": "AR",
    "Country_Region": "San Juan"
  },
  {
    "ISO2": "AR",
    "Country_Region": "San Luis"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Santa Cruz"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Santa Fe"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Santiago Del Estero"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Tierra Del Fuego"
  },
  {
    "ISO2": "AR",
    "Country_Region": "Tucuman"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Burgenland"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Kärnten"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Niederösterreich"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Oberösterreich"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Salzburg"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Steiermark"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Tirol"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Vorarlberg"
  },
  {
    "ISO2": "AT",
    "Country_Region": "Wien"
  },
  {
    "ISO2": "AU",
    "Country_Region": "Australian Capital Territory"
  },
  {
    "ISO2": "AU",
    "Country_Region": "New South Wales"
  },
  {
    "ISO2": "AU",
    "Country_Region": "Northern Territory"
  },
  {
    "ISO2": "AU",
    "Country_Region": "Queensland"
  },
  {
    "ISO2": "AU",
    "Country_Region": "South Australia"
  },
  {
    "ISO2": "AU",
    "Country_Region": "Tasmania"
  },
  {
    "ISO2": "AU",
    "Country_Region": "Victoria"
  },
  {
    "ISO2": "AU",
    "Country_Region": "Western Australia"
  },
  {
    "ISO2": "AX",
    "Country_Region": "Ålands landsbygd"
  },
  {
    "ISO2": "AX",
    "Country_Region": "Ålands skärgård"
  },
  {
    "ISO2": "AX",
    "Country_Region": "Mariehamns stad"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Ağcabədi"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Ağdaş"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Ağdam"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Ağstafa"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Ağsu"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Abşeron"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Astara"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Şabran"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Şamaxi"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Şəki"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Şəmkir"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Şirvan"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Baki"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Balakən"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Bərdə"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Beyləqan"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Biləsuvar"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Cəlilabad"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Daşkəsən"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Füzuli"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Göyçay"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Göy-Göl"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Gədəbəy"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Gəncə"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Goranboy"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Haciqabul"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Imişli"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Ismayilli"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Kürdəmir"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Kəlbəcər"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Lerik"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Lənkəran"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Masalli"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Mingəçevir"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Naftalan"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Neftçala"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Oğuz"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Qax"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Qazax"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Qəbələ"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Qobustan"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Quba"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Qusar"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Saatli"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Sabirabad"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Salyan"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Samux"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Siyəzən"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Sumqayit"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Tər-Tər"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Tovuz"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Ucar"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Xaçmaz"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Xizi"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Yardimli"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Yevlax"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Zaqatala"
  },
  {
    "ISO2": "AZ",
    "Country_Region": "Zərdab"
  },
  {
    "ISO2": "BD",
    "Country_Region": "Barisal Division"
  },
  {
    "ISO2": "BD",
    "Country_Region": "Chittagong"
  },
  {
    "ISO2": "BD",
    "Country_Region": "Dhaka Division"
  },
  {
    "ISO2": "BD",
    "Country_Region": "Khulna Division"
  },
  {
    "ISO2": "BD",
    "Country_Region": "Rājshāhi Division"
  },
  {
    "ISO2": "BD",
    "Country_Region": "Rangpur Division"
  },
  {
    "ISO2": "BD",
    "Country_Region": "Sylhet Division"
  },
  {
    "ISO2": "BE",
    "Country_Region": "Bruxelles-Capitale"
  },
  {
    "ISO2": "BE",
    "Country_Region": "Vlaanderen"
  },
  {
    "ISO2": "BE",
    "Country_Region": "Wallonie"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Разград / Razgrad"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Русе / Ruse"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Силистра / Silistra"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Сливен / Sliven"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Смолян / Smoljan"
  },
  {
    "ISO2": "BG",
    "Country_Region": "София (столица) / Sofija (stolica)"
  },
  {
    "ISO2": "BG",
    "Country_Region": "София / Sofija"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Стара Загора / Stara Zagora"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Шумен / Shumen"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Ямбол / Jambol"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Благоевград / Blagoevgrad"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Бургас / Burgas"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Видин / Vidin"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Варна / Varna"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Велико Търново / Veliko Turnovo"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Враца / Vraca"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Габрово / Gabrovo"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Добрич / Dobrich"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Ловеч / Lovech"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Търговище / Turgovishhe"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Хасково / Khaskovo"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Монтана / Montana"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Кърджали / Kurdzhali"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Кюстендил / Kjustendil"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Плевен / Pleven"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Пловдив / Plovdiv"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Пазарджик / Pazardzhik"
  },
  {
    "ISO2": "BG",
    "Country_Region": "Перник / Pernik"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Devonshire Parish"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Hamilton"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Paget Parish"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Pembroke Parish"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Saint George"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Saint George’s Parish"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Sandys Parish"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Smith’s Parish"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Southampton Parish"
  },
  {
    "ISO2": "BM",
    "Country_Region": "Warwick Parish"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Acre"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Alagoas"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Amapa"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Amazonas"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Bahia"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Ceara"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Distrito Federal"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Espirito Santo"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Goias"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Maranhao"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Mato Grosso"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Mato Grosso do Sul"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Minas Gerais"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Para"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Paraiba"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Parana"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Pernambuco"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Piaui"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Rio de Janeiro"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Rio Grande do Norte"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Rio Grande do Sul"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Rondonia"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Roraima"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Santa Catarina"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Sao Paulo"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Sergipe"
  },
  {
    "ISO2": "BR",
    "Country_Region": "Tocantins"
  },
  {
    "ISO2": "BY",
    "Country_Region": "Brest"
  },
  {
    "ISO2": "BY",
    "Country_Region": "Gomel"
  },
  {
    "ISO2": "BY",
    "Country_Region": "Grodno"
  },
  {
    "ISO2": "BY",
    "Country_Region": "Minsk"
  },
  {
    "ISO2": "BY",
    "Country_Region": "Moghilev"
  },
  {
    "ISO2": "BY",
    "Country_Region": "Vitebsk"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Alberta"
  },
  {
    "ISO2": "CA",
    "Country_Region": "British Columbia"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Manitoba"
  },
  {
    "ISO2": "CA",
    "Country_Region": "New Brunswick"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Newfoundland and Labrador"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Northwest Territory"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Nova Scotia"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Nunavut Territory"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Ontario"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Prince Edward Island"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Quebec"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Saskatchewan"
  },
  {
    "ISO2": "CA",
    "Country_Region": "Yukon"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Canton de Berne"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Canton de Fribourg"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Canton de Vaud"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Canton du Valais"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Genève"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Jura"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Aargau"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Appenzell Ausserrhoden"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Appenzell Innerrhoden"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Basel-Landschaft"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Basel-Stadt"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Glarus"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Graubünden"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Luzern"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Nidwalden"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Obwalden"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Schaffhausen"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Schwyz"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Solothurn"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton St. Gallen"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Thurgau"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Uri"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Zürich"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Kanton Zug"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Neuchâtel"
  },
  {
    "ISO2": "CH",
    "Country_Region": "Ticino"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región Aysén"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de Antofagasta"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de Arica y Parinacota"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de Atacama"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de Coquimbo"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de la Araucanía"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de los Lagos"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de los Ríos"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de Magallanes y Antártica Chilena"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de Tarapacá"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región de Valparaíso"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región del Biobío"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región del Libertador General Bernardo O’Higgins"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región del Maule"
  },
  {
    "ISO2": "CL",
    "Country_Region": "Región Metropolitana"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Amazonas"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Antioquia"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Arauca"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Archipielago De San Andres"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Atlantico"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Bogota, D.C."
  },
  {
    "ISO2": "CO",
    "Country_Region": "Bolivar"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Boyaca"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Caldas"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Caqueta"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Casanare"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Cauca"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Cesar"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Choco"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Cordoba"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Cundinamarca"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Guainia"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Guaviare"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Huila"
  },
  {
    "ISO2": "CO",
    "Country_Region": "La Guajira"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Magdalena"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Meta"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Nariño"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Norte De Santander"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Putumayo"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Quindio"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Risaralda"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Santander"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Sucre"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Tolima"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Valle Del Cauca"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Vaupes"
  },
  {
    "ISO2": "CO",
    "Country_Region": "Vichada"
  },
  {
    "ISO2": "CR",
    "Country_Region": "Provincia de Alajuela"
  },
  {
    "ISO2": "CR",
    "Country_Region": "Provincia de Cartago"
  },
  {
    "ISO2": "CR",
    "Country_Region": "Provincia de Guanacaste"
  },
  {
    "ISO2": "CR",
    "Country_Region": "Provincia de Heredia"
  },
  {
    "ISO2": "CR",
    "Country_Region": "Provincia de Limón"
  },
  {
    "ISO2": "CR",
    "Country_Region": "Provincia de Puntarenas"
  },
  {
    "ISO2": "CR",
    "Country_Region": "Provincia de San José"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Ústecký kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Hlavní město Praha"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Jihočeský kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Jihomoravský kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Karlovarský kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Královéhradecký kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Kraj Vysočina"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Liberecký kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Moravskoslezský kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Olomoucký kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Pardubický kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Plzeňský kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Středočeský kraj"
  },
  {
    "ISO2": "CZ",
    "Country_Region": "Zlínský kraj"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Baden-Württemberg"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Bayern"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Berlin"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Brandenburg"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Bremen"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Hamburg"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Hessen"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Mecklenburg-Vorpommern"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Niedersachsen"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Nordrhein-Westfalen"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Rheinland-Pfalz"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Saarland"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Sachsen"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Sachsen-Anhalt"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Schleswig-Holstein"
  },
  {
    "ISO2": "DE",
    "Country_Region": "Thüringen"
  },
  {
    "ISO2": "DK",
    "Country_Region": "Region Hovedstaden"
  },
  {
    "ISO2": "DK",
    "Country_Region": "Region Midtjylland"
  },
  {
    "ISO2": "DK",
    "Country_Region": "Region Nordjylland"
  },
  {
    "ISO2": "DK",
    "Country_Region": "Region Sjælland"
  },
  {
    "ISO2": "DK",
    "Country_Region": "Region Syddanmark"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Adrar"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Ain-Defla"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Ain-Temouchent"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Alger"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Annaba"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Batna"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Bechar"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Bejaia"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Biskra"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Blida"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Bordj-Bou-Arreridj"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Bouira"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Boumerdes"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Chlef"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Constantine"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Djelfa"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "El-Bayadh"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "El-Oued"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "El-Taref"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Ghardaia"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Guelma"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Illizi"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Jijel"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Khenchela"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "L.Aghouat"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Mascara"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Medea"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Mila"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Mostaganem"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "M'Sila"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Naama"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Oran"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Ouargla"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Oum-El-Bouaghi"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Relizane"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Saida"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Setif"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Sidi-Bel-Abbes"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Skikda"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Souk-Ahras"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tamanrasset"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tebessa"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tiaret"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tindouf"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tipaza"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tissemsilt"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tizi-Ouzou"
  },
  {
    "ISO2": "DZ",
    "Country_Region": "Tlemcen"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Harju maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Hiiu maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Ida-Viru maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Järva maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Jõgeva maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Lääne maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Lääne-Viru maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Pärnu maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Põlva maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Rapla maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Saare maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Tartu maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Võru maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Valga maakond"
  },
  {
    "ISO2": "EE",
    "Country_Region": "Viljandi maakond"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Andalucia"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Aragon"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Asturias"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Baleares"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Canarias"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Cantabria"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Castilla - La Mancha"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Castilla - Leon"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Cataluna"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Ceuta"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Comunidad Valenciana"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Extremadura"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Galicia"
  },
  {
    "ISO2": "ES",
    "Country_Region": "La Rioja"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Madrid"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Melilla"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Murcia"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Navarra"
  },
  {
    "ISO2": "ES",
    "Country_Region": "Pais Vasco"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Central Finland Region"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Central Ostrobothnia Region"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Kainuu"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Kanta-Häme"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Kymenlaakso"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Lapland"
  },
  {
    "ISO2": "FI",
    "Country_Region": "North Karelia"
  },
  {
    "ISO2": "FI",
    "Country_Region": "North Ostrobothnia Region"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Northern Savo"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Ostrobothnia Region"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Päijänne Tavastia"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Pirkanmaa"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Satakunta"
  },
  {
    "ISO2": "FI",
    "Country_Region": "South Karelia"
  },
  {
    "ISO2": "FI",
    "Country_Region": "South Ostrobothnia Region"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Southern Savonia"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Southwest Finland"
  },
  {
    "ISO2": "FI",
    "Country_Region": "Uusimaa"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Auvergne-Rhône-Alpes"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Île-de-France"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Bourgogne-Franche-Comté"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Bretagne"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Centre-Val de Loire"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Corse"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Grand Est"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Hauts-de-France"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Normandie"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Nouvelle-Aquitaine"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Occitanie"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Pays de la Loire"
  },
  {
    "ISO2": "FR",
    "Country_Region": "Provence-Alpes-Côte d'Azur"
  },
  {
    "ISO2": "GB",
    "Country_Region": "England"
  },
  {
    "ISO2": "GB",
    "Country_Region": "Northern Ireland"
  },
  {
    "ISO2": "GB",
    "Country_Region": "Scotland"
  },
  {
    "ISO2": "GB",
    "Country_Region": "Wales"
  },
  {
    "ISO2": "GF",
    "Country_Region": "Guyane"
  },
  {
    "ISO2": "GP",
    "Country_Region": "Guadeloupe"
  },
  {
    "ISO2": "GT",
    "Country_Region": "Ciudad de Guatemala"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE ALTA VERAPAZ"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE BAJA VERAPAZ"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE CHIMALTENANGO"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE CHIQUIMULA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE EL PROGRESO"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE ESCUINTLA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE GUATEMALA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE HUEHUETENANGO"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE IZABAL"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE JALAPA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE JUTIAPA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE PETEN"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE QUETZALTENANGO"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE RETALHULEU"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE SACATEPEQUEZ"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE SAN MARCOS"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE SANTA ROSA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE SOLOLA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE SUCHITEPEQUEZ"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE TOTONICAPAN"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DE ZACAPA"
  },
  {
    "ISO2": "GT",
    "Country_Region": "DEPTO DEL QUICHE"
  },
  {
    "ISO2": "GU",
    "Country_Region": "Gu"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Šibensko-Kninska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Bjelovarsko-Bilogorska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Brodsko-Posavska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Dubrovačko-Neretvanska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Grad Zagreb"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Istarska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Karlovačka"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Koprivničko-Križevačka"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Krapinsko-Zagorska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Ličko-Senjska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Međimurska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Osječko-Baranjska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Požeško-Slavonska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Primorsko-Goranska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Sisačko-Moslavačka"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Splitsko-Dalmatinska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Varaždinska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Virovitičko-Podravska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Vukovarsko-Srijemska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Zadarska"
  },
  {
    "ISO2": "HR",
    "Country_Region": "Zagrebačka"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Bács-Kiskun"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Békés"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Baranya"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Borsod-Abaúj-Zemplén"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Budapest"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Csongrád"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Fejér"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Győr-Moson-Sopron"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Hajdú-Bihar"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Heves"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Jász-Nagykun-Szolnok"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Komárom-Esztergom"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Nógrád"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Pest"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Somogy"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Szabolcs-Szatmár-Bereg"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Tolna"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Vas"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Veszprém"
  },
  {
    "ISO2": "HU",
    "Country_Region": "Zala"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Andaman & Nicobar Islands"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Andhra Pradesh"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Arunachal Pradesh"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Assam"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Bihar"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Chandigarh"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Chattisgarh"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Dadra & Nagar Haveli"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Daman & Diu"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Delhi"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Goa"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Gujarat"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Haryana"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Himachal Pradesh"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Jammu & Kashmir"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Jharkhand"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Karnataka"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Kerala"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Lakshadweep"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Madhya Pradesh"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Maharashtra"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Manipur"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Meghalaya"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Mizoram"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Nagaland"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Odisha"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Pondicherry"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Punjab"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Rajasthan"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Sikkim"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Tamil Nadu"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Telangana"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Tripura"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Uttar Pradesh"
  },
  {
    "ISO2": "IN",
    "Country_Region": "Uttarakhand"
  },
  {
    "ISO2": "IN",
    "Country_Region": "West Bengal"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Abruzzi"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Basilicata"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Calabria"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Campania"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Emilia-Romagna"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Friuli-Venezia Giulia"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Lazio"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Liguria"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Lombardia"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Marche"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Molise"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Piemonte"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Puglia"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Sardegna"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Sicilia"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Toscana"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Trentino-Alto Adige"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Umbria"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Valle D'Aosta"
  },
  {
    "ISO2": "IT",
    "Country_Region": "Veneto"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Aichi Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Akita Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Aomori Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Chiba Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Ehime Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Fukui Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Fukuoka Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Fukushima Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Gifu Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Gumma Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Hiroshima Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Hokkaido"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Hyogo Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Ibaraki Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Ishikawa Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Iwate Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Kagawa Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Kagoshima Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Kanagawa Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Kochi Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Kumamoto Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Kyoto Fu"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Mie Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Miyagi Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Miyazaki Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Nagano Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Nagasaki Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Nara Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Niigata Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Oita Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Okayama Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Okinawa Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Osaka Fu"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Saga Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Saitama Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Shiga Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Shimane Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Shizuoka Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Tochigi Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Tokushima Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Tokyo To"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Tottori Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Toyama Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Wakayama Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Yamagata Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Yamaguchi Ken"
  },
  {
    "ISO2": "JP",
    "Country_Region": "Yamanashi Ken"
  },
  {
    "ISO2": "KR",
    "Country_Region": "광주광역시"
  },
  {
    "ISO2": "KR",
    "Country_Region": "강원도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "부산광역시"
  },
  {
    "ISO2": "KR",
    "Country_Region": "경기도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "경상남도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "경상북도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "대구광역시"
  },
  {
    "ISO2": "KR",
    "Country_Region": "대전광역시"
  },
  {
    "ISO2": "KR",
    "Country_Region": "전라남도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "전라북도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "제주특별자치도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "인천광역시"
  },
  {
    "ISO2": "KR",
    "Country_Region": "세종특별자치시"
  },
  {
    "ISO2": "KR",
    "Country_Region": "서울특별시"
  },
  {
    "ISO2": "KR",
    "Country_Region": "충청남도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "충청북도"
  },
  {
    "ISO2": "KR",
    "Country_Region": "울산광역시"
  },
  {
    "ISO2": "LI",
    "Country_Region": "Eschen"
  },
  {
    "ISO2": "LI",
    "Country_Region": "Mauren"
  },
  {
    "ISO2": "LK",
    "Country_Region": "Central Province"
  },
  {
    "ISO2": "LK",
    "Country_Region": "Eastern Province"
  },
  {
    "ISO2": "LK",
    "Country_Region": "North Central Province"
  },
  {
    "ISO2": "LK",
    "Country_Region": "North Western Province"
  },
  {
    "ISO2": "LK",
    "Country_Region": "Northern Province"
  },
  {
    "ISO2": "LK",
    "Country_Region": "Province of Uva"
  },
  {
    "ISO2": "LK",
    "Country_Region": "Sabaragamuwa Province"
  },
  {
    "ISO2": "LK",
    "Country_Region": "Southern Province"
  },
  {
    "ISO2": "LK",
    "Country_Region": "Western Province"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Šiauliai County"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Alytus County"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Kaunas County"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Klaipėda County"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Marijampolė County"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Panevėžys"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Tauragė County"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Telšių apskritis"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Utena County"
  },
  {
    "ISO2": "LT",
    "Country_Region": "Vilniaus apskritis"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Capellen"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Clervaux"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Diekirch"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Echternach"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Esch-sur-Alzette"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Grevenmacher"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Luxembourg"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Mersch"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Redange"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Remich"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Vianden"
  },
  {
    "ISO2": "LU",
    "Country_Region": "Wiltz"
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ērgļu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ķeguma nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ķekavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ādažu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Aglonas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Aizkraukles nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Aizputes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Aknīstes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Alūksnes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Alojas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Alsungas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Amatas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Apes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Auces nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Babītes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Baldones nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Baltinavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Balvu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Bauskas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Beverīnas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Brocēnu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Burtnieku nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Cēsu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Carnikavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Cesvaines nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ciblas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Dagdas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Daugavpils nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Dobeles nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Dundagas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Durbes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Engures nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Garkalnes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Grobiņas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Gulbenes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Iecavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ikšķiles nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ilūkstes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Inčukalna nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Jēkabpils nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Jaunjelgavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Jaunpiebalgas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Jaunpils nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Jelgavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Kandavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Kārsavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Kocēnu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Kokneses nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Krāslavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Krimuldas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Krustpils nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Kuldīgas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Līgatnes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Līvānu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Lielvārdes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Limbažu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Lubānas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ludzas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Mērsraga nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Madonas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Mālpils nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Mārupes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Mazsalacas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Nīcas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Naukšēnu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Neretas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ogres nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Olaines nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ozolnieku nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Pļaviņu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Pārgaujas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Pāvilostas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Preiļu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Priekuļu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Priekules nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Rēzeknes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Rūjienas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Raunas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Riebiņu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Rojas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ropažu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Rucavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Rugāju nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Rundāles nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Sējas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Salas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Salaspils nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Saldus nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Saulkrastu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Siguldas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Skrīveru nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Skrundas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Smiltenes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Stopiņu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Strenču nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Tērvetes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Talsu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Tukuma nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Vaiņodes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Valkas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Varakļānu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Vārkavas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Vecpiebalgas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Vecumnieku nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Ventspils nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Viļakas nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Viļānu nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Viesītes nov."
  },
  {
    "ISO2": "LV",
    "Country_Region": "Zilupes nov."
  },
  {
    "ISO2": "MC",
    "Country_Region": "Monaco"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Anenii Noi"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Bender Tr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Briceni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Cahul"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Calarasi"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Camenca Tr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Cantemir"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Causeni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Cimislia"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Comrat"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Criuleni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Criuleni- Dub."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Criuleni-Dub."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Donduseni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Drochia"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Dubasari Cr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Dubasari Tr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Edinet"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Falesti"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Floresti"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Glodeni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Grigoriopol Tr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Hincesti"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Ialoveni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Leova"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Mun.Balti"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Mun.Chisinau"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Nisporeni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Ocnita"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Orhei"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Rezina"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Ribnita Tr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Riscani"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Singerei"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Slobozia Tr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Soldanesti"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Soroca"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Stefan-Voda"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Straseni"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Taraclia"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Telenesti"
  },
  {
    "ISO2": "MD",
    "Country_Region": "Tiraspol Tr."
  },
  {
    "ISO2": "MD",
    "Country_Region": "Ungheni"
  },
  {
    "ISO2": "MH",
    "Country_Region": "Mh"
  },
  {
    "ISO2": "MQ",
    "Country_Region": "Martinique"
  },
  {
    "ISO2": "MT",
    "Country_Region": "Għajnsielem"
  },
  {
    "ISO2": "MT",
    "Country_Region": "Iż-Żebbuġ"
  },
  {
    "ISO2": "MT",
    "Country_Region": "Il-Kalkara"
  },
  {
    "ISO2": "MT",
    "Country_Region": "Il-Marsa"
  },
  {
    "ISO2": "MT",
    "Country_Region": "Il-Munxar"
  },
  {
    "ISO2": "MW",
    "Country_Region": "Central Region"
  },
  {
    "ISO2": "MW",
    "Country_Region": "Northern Region"
  },
  {
    "ISO2": "MW",
    "Country_Region": "Southern Region"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Aguascalientes"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Baja California"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Baja California Sur"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Campeche"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Chiapas"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Chihuahua"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Coahuila de Zaragoza"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Colima"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Distrito Federal"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Durango"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Guanajuato"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Guerrero"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Hidalgo"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Jalisco"
  },
  {
    "ISO2": "MX",
    "Country_Region": "México"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Michoacán de Ocampo"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Morelos"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Nayarit"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Nuevo León"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Oaxaca"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Puebla"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Querétaro"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Quintana Roo"
  },
  {
    "ISO2": "MX",
    "Country_Region": "San Luis Potosí"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Sinaloa"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Sonora"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Tabasco"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Tamaulipas"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Tlaxcala"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Veracruz de Ignacio de la Llave"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Yucatán"
  },
  {
    "ISO2": "MX",
    "Country_Region": "Zacatecas"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Johor"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Kedah"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Kelantan"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Kuala Lumpur"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Labuan"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Melaka"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Negeri Sembilan"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Pahang"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Perak"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Perlis"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Pulau Pinang"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Putrajaya"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Sabah"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Sarawak"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Selangor"
  },
  {
    "ISO2": "MY",
    "Country_Region": "Terengganu"
  },
  {
    "ISO2": "NC",
    "Country_Region": "Îles Loyauté"
  },
  {
    "ISO2": "NC",
    "Country_Region": "Province Nord"
  },
  {
    "ISO2": "NC",
    "Country_Region": "Province Sud"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Drenthe"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Flevoland"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Friesland"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Gelderland"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Groningen"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Limburg"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Noord-Brabant"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Noord-Holland"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Overijssel"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Utrecht"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Zeeland"
  },
  {
    "ISO2": "NL",
    "Country_Region": "Zuid-Holland"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Agder"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Innlandet"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Møre og Romsdal"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Nordland"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Oslo County"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Rogaland"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Trøndelag"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Troms og Finnmark"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Vestfold og Telemark"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Vestland"
  },
  {
    "ISO2": "NO",
    "Country_Region": "Viken"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Auckland"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Bay of Plenty"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Canterbury"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Gisborne"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Hawke's Bay"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Manawatu-Wanganui"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Marlborough"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Nelson"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Northland"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Otago"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Southland"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Taranaki"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Tasman"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Waikato"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "Wellington"
  },
  {
    "ISO2": "NZ",
    "Country_Region": "West Coast"
  },
  {
    "ISO2": "PK",
    "Country_Region": "Balochisan"
  },
  {
    "ISO2": "PK",
    "Country_Region": "Federal Capial &AJK"
  },
  {
    "ISO2": "PK",
    "Country_Region": "Hyderabad"
  },
  {
    "ISO2": "PK",
    "Country_Region": "Lahore"
  },
  {
    "ISO2": "PK",
    "Country_Region": "Norhern Punajb Rawalpindi"
  },
  {
    "ISO2": "PK",
    "Country_Region": "NWFP Peshawar"
  },
  {
    "ISO2": "PK",
    "Country_Region": "Souhern Punajb Mulan"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Łódź Voivodeship"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Świętokrzyskie"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Greater Poland"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Kujawsko-Pomorskie"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Lesser Poland"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Lower Silesia"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Lublin"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Lubusz"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Mazovia"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Opole Voivodeship"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Podlasie"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Pomerania"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Silesia"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Subcarpathia"
  },
  {
    "ISO2": "PL",
    "Country_Region": "Warmia-Masuria"
  },
  {
    "ISO2": "PL",
    "Country_Region": "West Pomerania"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Aguadilla"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Aibonito"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Arecibo"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Bayamon"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Cabo Rojo"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Caguas"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Carolina"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Catano"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Cayey"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Ceiba"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Fajardo"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Guanica"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Guayama"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Guayanilla"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Guaynabo"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Humacao"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Lares"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Mayaguez"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Naguabo"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Ponce"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Rio Grande"
  },
  {
    "ISO2": "PR",
    "Country_Region": "San German"
  },
  {
    "ISO2": "PR",
    "Country_Region": "San Juan"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Toa Alta"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Toa Baja"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Trujillo Alto"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Utuado"
  },
  {
    "ISO2": "PR",
    "Country_Region": "Vega Baja"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Évora"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Aveiro"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Azores"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Beja"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Braga"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Bragança"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Castelo Branco"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Coimbra"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Faro"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Guarda"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Leiria"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Lisboa"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Madeira"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Portalegre"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Porto"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Santarém"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Setúbal"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Viana do Castelo"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Vila Real"
  },
  {
    "ISO2": "PT",
    "Country_Region": "Viseu"
  },
  {
    "ISO2": "RE",
    "Country_Region": "Réunion"
  },
  {
    "ISO2": "RE",
    "Country_Region": "Reunion (general)"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Alba"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Arad"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Argeş"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Bacău"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Bihor"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Bistriţa-Năsăud"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Botoşani"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Braşov"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Brăila"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Bucureşti"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Buzău"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Călăraşi"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Caraş-Severin"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Cluj"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Constanţa"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Covasna"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Dâmboviţa"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Dolj"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Galaţi"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Giurgiu"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Gorj"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Harghita"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Hunedoara"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Iaşi"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Ialomiţa"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Ilfov"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Maramureş"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Mehedinţi"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Mureş"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Neamţ"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Olt"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Prahova"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Sălaj"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Satu Mare"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Sibiu"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Suceava"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Teleorman"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Timiş"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Tulcea"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Vâlcea"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Vaslui"
  },
  {
    "ISO2": "RO",
    "Country_Region": "Vrancea"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Ростовская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Рязанская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Самарская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Санкт-Петербург"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Саха (Якутия) Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Сахалинская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Саратовская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Северная Осетия-Алания Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Смоленская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Свердловская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Ставропольский Край"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Ярославская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Ингушетия Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Ивановская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Иркутская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Байконур"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Башкортостан Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Белгородская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Брянская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Бурятия Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Владимирская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Вологодская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Волгоградская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Воронежская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Дагестан Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Липецкая Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Ленинградская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Тамбовская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Татарстан Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Томская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Тверская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Тыва Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Тульская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Тюменская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Удмуртская Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Ульяновская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Хабаровский Край"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Хакасия Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Читинская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Челябинская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Чеченская Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Чувашская Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Адыгея Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Нижегородская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Алтай Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Алтайский Край"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Амурская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Новосибирская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Новгородская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Архангельская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Астраханская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Магаданская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Марий Эл Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Мордовия Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Московская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Москва"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Мурманская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Кировская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Кабардино-Балкарская Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Калининградская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Калмыкия Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Калужская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Камчатская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Карачаево-Черкесская Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Карелия Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Кемеровская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Коми Республика"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Костромская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Краснодарский Край"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Красноярский Край"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Курганская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Курская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Пензенская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Пермский Край"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Приморский Край"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Псковская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Омская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Орловская Область"
  },
  {
    "ISO2": "RU",
    "Country_Region": "Оренбургская Область"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Örebro"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Östergötland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Blekinge"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Dalarna"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Gävleborg"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Gotland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Halland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Jämtland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Jönköping"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Kalmar"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Kronoberg"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Norrbotten"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Södermanland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Skåne"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Stockholm"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Uppsala"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Värmland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Västerbotten"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Västernorrland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Västmanland"
  },
  {
    "ISO2": "SE",
    "Country_Region": "Västra Götaland"
  },
  {
    "ISO2": "SJ",
    "Country_Region": "Svalbard"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Žilinský kraj"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Banskobystrický kraj"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Bratislavský kraj"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Košický kraj"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Nitriansky kraj"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Prešovský kraj"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Trenčiansky kraj"
  },
  {
    "ISO2": "SK",
    "Country_Region": "Trnavský kraj"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Amnat Charoen"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Ang Thong"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Bangkok"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Buri Ram"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chachoengsao"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chai Nat"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chaiyaphum"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chanthaburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chiang Mai"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chiang Rai"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chon Buri"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Chumphon"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Kalasin"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Kamphaeng Phet"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Kanchanaburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Khon Kaen"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Krabi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Lampang"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Lamphun"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Loei"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Lopburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Mae Hong Son"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Maha Sarakham"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Mukdahan"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nakhon Nayok"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nakhon Pathom"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nakhon Phanom"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nakhon Ratchasima"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nakhon Sawan"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nakhon Si Thammarat"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nan"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Narathiwat"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nong Bua Lam Phu"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nong Khai"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Nonthaburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Pathum Thani"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Pattani"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phang Nga"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phatthalung"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phayao"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phetchaburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phichit"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phitsanulok"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phra Nakhon Si Ayutthaya"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phrae"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Phuket"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Prachin Buri"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Prachuap Khiri Khan"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Ranong"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Ratchaburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Rayong"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Roi Et"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Sa Kaeo"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Sakon Nakhon"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Samut Prakan"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Samut Sakhon"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Samut Songkhram"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Saraburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Satun"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Si Sa Ket"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Sing Buri"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Songkhla"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Sukhothai"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Suphanburi"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Surat Thani"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Tak"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Trang"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Trat"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Ubon Ratchathani"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Udon Thani"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Uthai Thani"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Uttaradit"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Yala"
  },
  {
    "ISO2": "TH",
    "Country_Region": "Yasothon"
  },
  {
    "ISO2": "TR",
    "Country_Region": "İstanbul"
  },
  {
    "ISO2": "TR",
    "Country_Region": "İzmir"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Çanakkale"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Çankiri"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Çorum"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Ağri"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Adana"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Adiyaman"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Afyonkarahisar"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Aksaray"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Amasya"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Ankara"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Antalya"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Ardahan"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Artvin"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Aydin"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Şanliurfa"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Şirnak"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Balikesir"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Bartin"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Batman"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Bayburt"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Bilecik"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Bingöl"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Bitlis"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Bolu"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Burdur"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Bursa"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Düzce"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Denizli"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Diyarbakir"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Edirne"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Elaziğ"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Erzincan"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Erzurum"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Eskişehir"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Gümüşhane"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Gaziantep"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Giresun"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Hakkari"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Hatay"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Iğdir"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Isparta"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kütahya"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kahramanmaraş"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Karabük"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Karaman"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kars"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kastamonu"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kayseri"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kilis"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kirşehir"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kirikkale"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kirklareli"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kktc"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Kocaeli"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Konya"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Malatya"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Manisa"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Mardin"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Mersin(İçel)"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Muş"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Muğla"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Nevşehir"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Niğde"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Ordu"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Osmaniye"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Rize"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Sakarya"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Samsun"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Siirt"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Sinop"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Sivas"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Tekirdağ"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Tokat"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Trabzon"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Tunceli"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Uşak"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Van"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Yalova"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Yozgat"
  },
  {
    "ISO2": "TR",
    "Country_Region": "Zonguldak"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Cherkaska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Chernihivska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Chernivetska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Dnipropetrovska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Donetska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Ivano-Frankivska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Kharkivska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Khersonska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Khmelnytska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Kirovohradska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Kyiv"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Kyivska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Luhanska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Lvivska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Mykolaivska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Odeska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Poltavska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Rivnenska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Sumska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Ternopilska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Vinnytska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Volynska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Zakarpatska"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Zaporizka"
  },
  {
    "ISO2": "UA",
    "Country_Region": "Zhytomyrska"
  },
  {
    "ISO2": "US",
    "Country_Region": "Alabama"
  },
  {
    "ISO2": "US",
    "Country_Region": "Alaska"
  },
  {
    "ISO2": "US",
    "Country_Region": "Arizona"
  },
  {
    "ISO2": "US",
    "Country_Region": "Arkansas"
  },
  {
    "ISO2": "US",
    "Country_Region": "California"
  },
  {
    "ISO2": "US",
    "Country_Region": "Colorado"
  },
  {
    "ISO2": "US",
    "Country_Region": "Connecticut"
  },
  {
    "ISO2": "US",
    "Country_Region": "Delaware"
  },
  {
    "ISO2": "US",
    "Country_Region": "District of Columbia"
  },
  {
    "ISO2": "US",
    "Country_Region": "Florida"
  },
  {
    "ISO2": "US",
    "Country_Region": "Georgia"
  },
  {
    "ISO2": "US",
    "Country_Region": "Hawaii"
  },
  {
    "ISO2": "US",
    "Country_Region": "Idaho"
  },
  {
    "ISO2": "US",
    "Country_Region": "Illinois"
  },
  {
    "ISO2": "US",
    "Country_Region": "Indiana"
  },
  {
    "ISO2": "US",
    "Country_Region": "Iowa"
  },
  {
    "ISO2": "US",
    "Country_Region": "Kansas"
  },
  {
    "ISO2": "US",
    "Country_Region": "Kentucky"
  },
  {
    "ISO2": "US",
    "Country_Region": "Louisiana"
  },
  {
    "ISO2": "US",
    "Country_Region": "Maine"
  },
  {
    "ISO2": "US",
    "Country_Region": "Maryland"
  },
  {
    "ISO2": "US",
    "Country_Region": "Massachusetts"
  },
  {
    "ISO2": "US",
    "Country_Region": "Michigan"
  },
  {
    "ISO2": "US",
    "Country_Region": "Minnesota"
  },
  {
    "ISO2": "US",
    "Country_Region": "Mississippi"
  },
  {
    "ISO2": "US",
    "Country_Region": "Missouri"
  },
  {
    "ISO2": "US",
    "Country_Region": "Montana"
  },
  {
    "ISO2": "US",
    "Country_Region": "Nebraska"
  },
  {
    "ISO2": "US",
    "Country_Region": "Nevada"
  },
  {
    "ISO2": "US",
    "Country_Region": "New Hampshire"
  },
  {
    "ISO2": "US",
    "Country_Region": "New Jersey"
  },
  {
    "ISO2": "US",
    "Country_Region": "New Mexico"
  },
  {
    "ISO2": "US",
    "Country_Region": "New York"
  },
  {
    "ISO2": "US",
    "Country_Region": "North Carolina"
  },
  {
    "ISO2": "US",
    "Country_Region": "North Dakota"
  },
  {
    "ISO2": "US",
    "Country_Region": "Ohio"
  },
  {
    "ISO2": "US",
    "Country_Region": "Oklahoma"
  },
  {
    "ISO2": "US",
    "Country_Region": "Oregon"
  },
  {
    "ISO2": "US",
    "Country_Region": "Pennsylvania"
  },
  {
    "ISO2": "US",
    "Country_Region": "Rhode Island"
  },
  {
    "ISO2": "US",
    "Country_Region": "South Carolina"
  },
  {
    "ISO2": "US",
    "Country_Region": "South Dakota"
  },
  {
    "ISO2": "US",
    "Country_Region": "Tennessee"
  },
  {
    "ISO2": "US",
    "Country_Region": "Texas"
  },
  {
    "ISO2": "US",
    "Country_Region": "Utah"
  },
  {
    "ISO2": "US",
    "Country_Region": "Vermont"
  },
  {
    "ISO2": "US",
    "Country_Region": "Virginia"
  },
  {
    "ISO2": "US",
    "Country_Region": "Washington"
  },
  {
    "ISO2": "US",
    "Country_Region": "West Virginia"
  },
  {
    "ISO2": "US",
    "Country_Region": "Wisconsin"
  },
  {
    "ISO2": "US",
    "Country_Region": "Wyoming"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Artigas"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Canelones"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Cerro Largo"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Colonia"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Durazno"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Flores"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Florida"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Lavalleja"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Maldonado"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Montevideo"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Paysandu"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Rio Negro"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Rivera"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Rocha"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Salto"
  },
  {
    "ISO2": "UY",
    "Country_Region": "San Jose"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Soriano"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Tacuarembo"
  },
  {
    "ISO2": "UY",
    "Country_Region": "Treinta y Tres"
  },
  {
    "ISO2": "VI",
    "Country_Region": "Vi"
  },
  {
    "ISO2": "YT",
    "Country_Region": "Mamoudzou"
  }
 ]