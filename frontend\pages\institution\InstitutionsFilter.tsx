//Import Library
import React, { useEffect, useState } from "react";
import _ from "lodash";
import {
  Col,
  Container,
  FormControl,
  Row,
} from "react-bootstrap";
import {MultiSelect} from "react-multi-select-component";

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';


const InstitutionFilter = ({
  filterText,
  onFilter,
  onFilterTypeChange,
  onClear,
  filterType,
  onFilterNetworkChange,
  filterNetwork,
}) => {
  const [type, setType] = useState([]);
  const [networks, setNetworks] = useState([]);
  const { t } = useTranslation('common');

  const getType = async (params) => {
    const response = await apiService.get("/institutionType", params);
    if (response && Array.isArray(response.data)) {
      const types = _.map(response.data, (item) => {
        return {
          label: item.title,
          value: item._id,
        };
      });
      setType(types);
    }
  };

  const getNetworks = async (params) => {
    const response = await apiService.get("/institutionNetwork", params);
    if (response && Array.isArray(response.data)) {
      setNetworks(response.data);
    }
  };

  useEffect(() => {
    getType({ query: {}, sort: { title: "asc" } });
    getNetworks({ query: {}, sort: { title: "asc" } });
  }, []);

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={4} className="p-0">
          <FormControl
            type="text"
            className="searchInput"
            placeholder={t("search")}
            aria-label="Search"
            value={filterText}
            onChange={onFilter}
          />
        </Col>
        <Col xs={4}>
          <MultiSelect
            overrideStrings={{
              selectSomeItems: t("SelectType"),
              allItemsAreSelected: "All Types are Selected",
            }}
            onChange={onFilterTypeChange}
            value={filterType}
            options={type}
            className={"select-type"}
            labelledBy={t("SelectType")}
          />
        </Col>
        <Col xs={4} className="p-0">
          <FormControl
            as="select"
            aria-label="Network"
            aria-placeholder="Network"
            onChange={onFilterNetworkChange}
            value={filterNetwork}
          >
            <option value={""}>{t("SelectNetwork")}</option>
            {networks.map((item, index) => {
              return (
                <option key={index} value={item._id}>
                  {item.title}
                </option>
              );
            })}
          </FormControl>
        </Col>
      </Row>
    </Container>
  );
};

export default InstitutionFilter;
