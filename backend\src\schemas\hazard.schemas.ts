//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const HazardSchema = new mongoose.Schema({
  title_en: { type: String },
  title: {
    en: { type: String, unique: true },
    de: { type: String, unique: true },
    fr: { type: String, unique: true },
  },
  first_letter: { type: String },
  hazard_type: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'HazardType',
    autopopulate: true,
  },
  description: {
    en: { type: String },
    de: { type: String },
    fr: { type: String },
  },
  picture: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Image',
    autopopulate: true,
  },
  picture_source: { type: String },
  enabled: { type: Boolean, default: true },
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users' },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
  images: [
    { type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true },
  ],
  images_src: [{ type: String }],
});

HazardSchema.plugin(mongoosePaginate);
