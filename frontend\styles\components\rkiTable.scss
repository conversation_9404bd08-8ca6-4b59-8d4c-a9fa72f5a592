.rki-table {
  .jEDPQU {
    padding: 0 7px;
    .rdt_Pagination {
      border: none;
    }
  }

  .rdt_Table {
    border-radius: 4px;
    border: 1px solid #eaeaea;
    margin: 10px 0 0;
    box-shadow: 1px 1px 15px rgba(150, 150, 150, 0.1) !important;
    height: auto;
    min-height: 50vh;

    
    .jeqOAT {
      min-height: 50px;
      .spinner-border {
        display: flex;
        position: relative;
        top: 0;
        left: 0;
        margin: 0 auto;
      }
    }
  }

  .rdt_TableHead {
    .rdt_TableHeadRow {
      background-color: #edf4f4;
      border-bottom: 2px solid #2da0dc;
      .rdt_TableCol {
        font-size: 15px;
        padding: 10px;
        border-end: 1px solid #d2d2d2;
        overflow: hidden;
        &:last-child {
          border-end: none;
        }
        .rdt_TableCol_Sortable {
          width: 100%;
          .__rdt_custom_sort_icon__ {
            margin-left: auto;
            opacity: 1 !important;
            .sort-icon {
              transform: rotate(90deg) !important;
              opacity: 1 !important;
              color: #c1c1c1;
            }
          }
        }
      }
    }
  }

  .rdt_TableBody {
    .rdt_TableRow {
      font-size: 15px;
      &:hover {
        .rdt_TableCell {
          a {
            color: #3ca3d9 !important;
          }
        }
      }
      a {
        color: #151515;
        line-height: 20px;
        &:hover {
          color: #3ca3d9;
        }
      }
      &:nth-child(even) {
        background-color: #fafafa;
      }
      &:nth-child(odd) {
        background-color: #FFFFFF;
      }
    }
    .rdt_TableCell {
      color: rgba(0, 0, 0, .7);
      border-end: 1px solid #dedede;
      padding: 10px 12px;
      &:last-child {
        border-end: none;
      }
      ul {
        padding: 0;
        margin: 0;
        list-style: none;
        display: flex;
        flex-wrap: wrap;
        li {
          padding: 0 0 4px;
          &::after {
            content: ",";
            margin: 0 5px 0 1px;
            color: #3f51b5;
          }
          &:last-child::after {
            display: none;
          }
        }
      }
    }
  }
}