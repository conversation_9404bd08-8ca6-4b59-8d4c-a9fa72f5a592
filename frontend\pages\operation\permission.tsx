//Import Library
import React from 'react';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

//Import services/components
import R403 from "../r403";

export const canAddOperation = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddOperation',
});

export const canAddOperationForm = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.operation && state.permissions.operation['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddOperationForm',
  FailureComponent: () => <R403/>
});

export const canEditOperation = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.operation) {
      if (state.permissions.operation['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.operation['update:own']) {
          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditOperation',
});

export const canEditOperationForm = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.operation) {
      if (state.permissions.operation['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.operation['update:own']) {
          if (props.operation && props.operation.user && props.operation.user._id === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditOperationForm',
  FailureComponent: () => <R403/>
});

export const canViewDiscussionUpdate = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewDiscussionUpdate',
});

export default canAddOperation;