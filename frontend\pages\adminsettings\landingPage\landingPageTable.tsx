//Import Library
import Link from "next/link";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const LandingPageTable = (_props: any) => {
    const { t, i18n } = useTranslation('common');
    const currentLang = i18n.language ? i18n.language : "en";

    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectLandingPage, setSelectLandingPage] = useState({});
    const modalHide = () => setModal(false);
    
    const columns = [
        {
            name: t("adminsetting.landing.table.Title"),
            selector: "title",
            cell: (d) => d.title,
        },
        {
            name: t("adminsetting.landing.table.Enabled"),
            selector: "isEnabled",
            cell: (d) => (d.isEnabled ? "Yes" : "No"),
        },
        {
            name: t("adminsetting.landing.table.Action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_landing/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    {" "}
                </div>
            ),
        },
    ];
    const landingPageParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
    };

    useEffect(() => {
        getlandingPageData();
    }, []);

    const getlandingPageData = async () => {
        setLoading(true);
        const response = await apiService.get("/landingPage", landingPageParams);
        if (response) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        landingPageParams.limit = perPage;
        landingPageParams.page = page;
        getlandingPageData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        landingPageParams.limit = newPerPage;
        landingPageParams.page = page;
        setLoading(true);
        const response = await apiService.get("/landingPage", landingPageParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/landingPage/${selectLandingPage}`);
            getlandingPageData();
            setModal(false);
            toast.success(t("adminsetting.landing.table.landingDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.landing.table.errorDeletingLanding"));
        }
    };

    const userAction = async (row) => {
        setSelectLandingPage(row._id);
        setModal(true);
    };

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.landing.table.DeleteEditableContent")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.landing.table.AreyousurewanttodeletethisEditableContent?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.landing.table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.landing.table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default LandingPageTable;
