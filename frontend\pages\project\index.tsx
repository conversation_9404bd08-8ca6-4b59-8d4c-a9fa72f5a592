//Import Library
import React, {useState} from "react";
import Link from 'next/link';
import Button from 'react-bootstrap/Button';
import {Container, Col, Row} from "react-bootstrap";

//Import services/components
import PageHeading from "../../components/common/PageHeading";
import ProjectsTable from "./ProjectsTable";
import ListMapContainer from "./ListMapContainer";
import { useTranslation } from 'next-i18next';
import { canAddProject } from "./permission";
import RegionsMultiCheckboxes from "../../components/common/RegionsMultiCheckboxes";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Project = (_props: any) => {
  const { t } = useTranslation('common');
  const [projects, setProjects] = useState([]);
  const [selectedRegions, setSelectedRegions] = useState(null);

  const AddProjectComponent = () => {
    return (
      <Link href='/project/[...routes]' as='/project/create' >
        <Button variant="secondary" size="sm">
          {t('addProject')}
        </Button>
      </Link>
    );
  };

  const CanAddProject = canAddProject(() =>  <AddProjectComponent />);

  const regionHandler = (val: any) => {
     setSelectedRegions(val);
    }

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={12}>
          <PageHeading title={t("projects")}/>
        </Col>
      </Row>
      <Row>
        <Col xs={12}>
          <ListMapContainer selectedRegions={selectedRegions} projects={projects} />
        </Col>
      </Row>
      <Row>
        <Col xs={12}>
          <RegionsMultiCheckboxes
            filtreg={(val)=> regionHandler(val)}
            selectedRegions={[]}
            regionHandler={regionHandler}
          />
        </Col>
      </Row>
      <Row>
        <Col xs={12} className="ps-4">
          <CanAddProject />
        </Col>
      </Row>
      <Row className="mt-3">
        <Col xs={12}>
          <ProjectsTable selectedRegions={selectedRegions} setProjects={setProjects} />
        </Col>
      </Row>
    </Container>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Project;