//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
// import { ValidationForm, TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput, SelectGroup } from "../../../components/common/FormValidation";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { AreaOfWorkInterface } from "../../../components/interfaces/areaOfWork.interface";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

interface AreaOfWorkFormProps {
    routes: string[];
}

const AreaOfWorkForm = (props: AreaOfWorkFormProps) => {
    const { t } = useTranslation('common');

    const _initialareaOfWork = {
        title: "",
    };

    const [initialVal, setInitialVal] = useState<AreaOfWorkInterface>(_initialareaOfWork);

    const editform: boolean = !!(props.routes && props.routes[0] === "edit_area_of_work" && props.routes[1]);

    const formRef = useRef<any>(null);

    const resetHandler = () => {
        setInitialVal(_initialareaOfWork);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event: any) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "Areaofworkisupdatedsuccessfully";
            response = await apiService.patch(`/areaofwork/${props.routes[1]}`, obj);
        } else {
            toastMsg = "Areaofworkisaddedsuccessfully";
            response = await apiService.post("/areaofwork", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/area_of_work");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const areaOfWorkParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getAreaOfWorkData = async () => {
                const response = await apiService.get(`/areaofwork/${props.routes[1]}`, areaOfWorkParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getAreaOfWorkData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.areaofwork.Forms.AreaOfWork")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.areaofwork.Forms.AreaOfWork")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value: any) => String(value || '').trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.areaofwork.Forms.PleaseAddtheAreaofWork"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.areaofwork.Forms.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.areaofwork.Forms.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/area_of_work`}
                                        >
                                        <Button variant="secondary">{t("adminsetting.areaofwork.Forms.Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default AreaOfWorkForm;
