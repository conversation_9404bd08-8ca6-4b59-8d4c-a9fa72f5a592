declare module 'nprogress' {
  interface NProgress {
    start(): NProgress;
    done(force?: boolean): NProgress;
    set(n: number): NProgress;
    inc(amount?: number): NProgress;
    configure(options: Partial<NProgressOptions>): NProgress;
    status: number | null;
    isStarted(): boolean;
    remove(): void;
  }

  interface NProgressOptions {
    minimum: number;
    template: string;
    easing: string;
    speed: number;
    trickle: boolean;
    trickleSpeed: number;
    showSpinner: boolean;
    barSelector: string;
    spinnerSelector: string;
    parent: string;
  }

  const nprogress: NProgress;
  export = nprogress;
}
