//Import Library
import React from "react";
import { Modal } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import { useTranslation } from 'next-i18next';

const InfoPopup = (props) => {
  const { t } = useTranslation('common');
  const { isShow, isClose, data, name } = props;
  const route = name === "Projects" ? "project" : arrowfun();
  const titles =
    data.length > 0 ? (
      data.map((item, i) => (
        <span key={i}>
          <Link href={`/${route}/show/${item._id}`}>
            {item.title}
          </Link>
          <hr />
        </span>
      ))
    ) : (
      <p>
        {t("No")} {name} {t("Found")}.
      </p>
    );

  return (
    <Modal
      centered
      size="sm"
      show={isShow}
      onHide={() => isClose(!isShow)}
      aria-labelledby="modal_popup"
    >
      <Modal.Header closeButton>
        <Modal.Title>{name}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div>{titles}</div>
      </Modal.Body>
    </Modal>
  );

  function arrowfun() {
    return name === "Partners" ? "institution" : "operation";
  }
};

export default InfoPopup;
