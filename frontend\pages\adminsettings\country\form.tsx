//Import Library
import { useRef, useState, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput, SelectGroup } from "../../../components/common/FormValidation";
import toast from 'react-hot-toast';
import Router from "next/router";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMapMarkerAlt } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import { useTranslation } from 'next-i18next';
import apiService from "../../../services/apiService";
import { CountryInterface } from "../../../components/interfaces/country.interface";

const CountryForm = (props: any) => {
    const { t } = useTranslation('common');
    const _initialCountry = {
        title: "",
        code: "",
        code3: "",
        dial_code: "",
        coordinates: [
            {
                latitude: "",
                longitude: "",
            },
        ],
        world_region: "",
        health_profile: "",
        security_advice: "",
    };

    const [initialVal, setInitialVal] = useState<CountryInterface>(_initialCountry);
    const [worldregion, setworldRegion] = useState([]);
    const [coordinates, showCoordinates] = useState(false);

    const editform =
        props.routes && (props.routes[0] === t("adminsetting.Countries.Forms.edit_country") ||
        "edit_land" === t("adminsetting.Countries.Forms.edit_country")) && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialCountry);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleSubmit = async (event, values) => {
        event.preventDefault();
        // Use Formik values if available, otherwise fall back to initialVal
        const formValues = values || initialVal;
        const newTitle = formValues.title.charAt(0).toUpperCase() + formValues.title.slice(1);
        const obj = {
            title: newTitle?.trim(),
            title_de: newTitle?.trim(),
            code: formValues.code?.trim(),
            code3: formValues.code3,
            dial_code: formValues.dial_code,
            first_letter: {
                en: formValues.title.charAt(0).toUpperCase(),
                fr: formValues.title.charAt(0).toUpperCase(),
                de: formValues.title.charAt(0).toUpperCase(),
            },
            coordinates: formValues.coordinates,
            world_region: formValues.world_region,
            health_profile: formValues.health_profile,
            security_advice: formValues.security_advice,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.Countries.Forms.Countryisupdatedsuccessfully";
            response = await apiService.patch(`/country/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.Countries.Forms.Countryisaddedsuccessfully";
            response = await apiService.post("/country", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/country");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    const handleChange = (e) => {
        if (e.target) {
            const { name, value } = e.target;
            if (name === "longitude" || name === "latitude") {
                setInitialVal((prevState) => ({
                    ...prevState,
                    coordinates: [{ ...initialVal.coordinates[0], [name]: value }],
                }));
            } else {
                setInitialVal((prevState) => ({
                    ...prevState,
                    [name]: value,
                }));
            }
        }
    };

    const getworldregion = async (countriesParams) => {
        const response = await apiService.get("/worldregion", countriesParams);
        if (response) {
            setworldRegion(response.data);
        }
    };

    useEffect(() => {
        const countriesParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };

        if (editform) {
            const getCountryData = async (countriesParams_initial) => {
                const response = await apiService.get(`/country/${props.routes[1]}`, countriesParams_initial);
                if (response) {
                    const { world_region } = response;
                    response.world_region = world_region && world_region._id ? world_region._id : "";
                    setInitialVal((prevState) => ({ ...prevState, ...response }));
                }
            };
            getCountryData(countriesParams);
        }

        getworldregion(countriesParams);
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{editform ? t("adminsetting.Countries.Forms.EditCountry") : t("adminsetting.Countries.Forms.AddCountry")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row className="mb-3">
                                <Col md lg={12} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.Countries.Forms.CountryName")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => String(value || '').trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.Countries.Forms.PleaseAddtheCountryName"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                                </Row>
                                <Row className="mb-3">
                                <Col md lg={4} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.Countries.Forms.CountryCode")}
                                        </Form.Label>
                                        <TextInput
                                            name="code"
                                            id="code"
                                            required
                                            value={initialVal.code}
                                            errorMessage={{validator: t("adminsetting.Countries.Forms.PleaseAddtheCountryCode")}}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md lg={4} sm={12}>
                                    <Form.Group>
                                        <Form.Label>{t("adminsetting.Countries.Forms.CountryCode3")}</Form.Label>
                                        <TextInput
                                            name="code3"
                                            id="code3"
                                            value={initialVal.code3}
                                            errorMessage={t("adminsetting.Countries.Forms.PleaseAddtheCountryCode3")}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>

                                <Col md lg={4} sm={12}>
                                    <Form.Group>
                                        <Form.Label>{t("adminsetting.Countries.Forms.DialCode")} </Form.Label>
                                        <TextInput
                                            name="dial_code"
                                            id="dial code"
                                            value={initialVal.dial_code}
                                            errorMessage={t("adminsetting.Countries.Forms.PleaseAddtheCountryDialCode")}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="mb-3">
                                <Col>
                                    <Card.Title>{t("adminsetting.Countries.Forms.Co-ordinates")}</Card.Title>
                                </Col>
                            </Row>

                            {coordinates ? (
                                <Row className="mb-3">
                                    <Col md lg={4} sm={12}>
                                        <Form.Group>
                                            <Form.Label>{t("adminsetting.Countries.Forms.Latitude")}</Form.Label>
                                            <TextInput
                                                name="latitude"
                                                id="latitude"
                                                value={initialVal.coordinates[0].latitude}
                                                errorMessage={t("adminsetting.Countries.Forms.PleaseAddtheLatitude")}
                                                onChange={handleChange}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md lg={4} sm={12}>
                                        <Form.Group>
                                            <Form.Label>{t("adminsetting.Countries.Forms.Longitude")}</Form.Label>
                                            <TextInput
                                                name="longitude"
                                                id="longitude"
                                                value={initialVal.coordinates[0].longitude}
                                                errorMessage={t("adminsetting.Countries.Forms.PleaseAddtheLongitude")}
                                                onChange={handleChange}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md lg={4} sm={12}>
                                        <div style={{ marginTop: "30px" }}>
                                            <Button variant="secondary" onClick={() => showCoordinates(!coordinates)}>
                                                {" "}
                                                <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" />
                                                {t("adminsetting.Countries.Forms.HideCoordinates")}
                                            </Button>
                                        </div>
                                    </Col>
                                </Row>
                            ) : (
                                <div className="mb-3">
                                    <Button variant="secondary" onClick={() => showCoordinates(!coordinates)}>
                                        {" "}
                                        <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" />
                                        {t("adminsetting.Countries.Forms.coordinatesBtnText")}
                                    </Button>
                                </div>
                            )}
                            <Row className="mb-3">
                                <Col md lg={4} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.Countries.Forms.WorldRegion")}
                                        </Form.Label>
                                        <SelectGroup
                                            name="world_region"
                                            id="world_region"
                                            value={initialVal.world_region}
                                            errorMessage={{ validator: t("adminsetting.Countries.Forms.PleaseAddtheWorldRegion")}}
                                            required
                                            onChange={handleChange}
                                        >
                                            <option value="">
                                                {t("adminsetting.Countries.Forms.SelectWorldRegion")}
                                            </option>
                                            {worldregion.length >= 1
                                                ? worldregion.map((item, _i) => {
                                                      return (
                                                          <option key={item._id} value={item._id}>
                                                              {item.title}
                                                          </option>
                                                      );
                                                  })
                                                : null}
                                        </SelectGroup>
                                    </Form.Group>
                                </Col>
                                <Col md lg={4} sm={12}>
                                    <Form.Group>
                                        <Form.Label>{t("adminsetting.Countries.Forms.Healthprofile")}</Form.Label>
                                        <TextInput
                                            name="health_profile"
                                            id="health_profile"
                                            value={initialVal.health_profile}
                                            errorMessage={t("adminsetting.Countries.Forms.PleaseAddtheHealthProfile")}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md lg={4} sm={12}>
                                    <Form.Group>
                                        <Form.Label>{t("adminsetting.Countries.Forms.SecurityAdvice")}</Form.Label>
                                        <TextInput
                                            name="security_advice"
                                            id="security_advice"
                                            value={initialVal.security_advice}
                                            errorMessage={t("adminsetting.Countries.Forms.PleaseAddtheSecurityAdvice")}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.Countries.Forms.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.Countries.Forms.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/country`}
                                        >
                                        <Button variant="secondary">{t("adminsetting.Countries.Forms.Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};

export default CountryForm;
