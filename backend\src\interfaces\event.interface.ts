export interface EventInterface {
  title: string,
  description: string,
  operation: object,
  country: object,
  country_regions: object,
  hazard_type: object,
  hazard: object,
  syndrome: object,
  status: object,
  laboratory_confirmed: boolean,
  officially_validated: boolean,
  rki_monitored: boolean,
  risk_assessment: {
    country: object,
    region: object,
    international: object,
    description: string
  },
  document: Buffer;
  doc_src: Array<any>;
  images: Array<any>;
  images_src: Array<any>;
  date: Date,
  more_info: string
}
