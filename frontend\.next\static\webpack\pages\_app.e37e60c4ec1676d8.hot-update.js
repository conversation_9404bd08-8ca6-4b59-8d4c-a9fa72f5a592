"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/common/widgets/TwitterTimeline.tsx":
/*!*******************************************************!*\
  !*** ./components/common/widgets/TwitterTimeline.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_tweet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-tweet */ \"(pages-dir-browser)/./node_modules/react-tweet/dist/index.client.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(pages-dir-browser)/./node_modules/axios/index.js\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../services/authService */ \"(pages-dir-browser)/./services/authService.tsx\");\n//Import Library\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TwitterTimeline = ()=>{\n    _s();\n    const [tweets, setTweets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TwitterTimeline.useEffect\": ()=>{\n            const fetchTweets = {\n                \"TwitterTimeline.useEffect.fetchTweets\": async ()=>{\n                    try {\n                        var _response_data;\n                        setLoading(true);\n                        const headers = await _services_authService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getAuthHeader();\n                        // Request 2 tweets but handle if we get fewer\n                        const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"\".concat(\"http://localhost:3001/api/v1\", \"/tweets?count=2\"), {\n                            headers\n                        });\n                        if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) {\n                            setTweets(response.data.data);\n                        } else {\n                            setTweets([]);\n                        }\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error fetching tweets:', err);\n                        setError('Failed to load tweets');\n                        setTweets([]);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"TwitterTimeline.useEffect.fetchTweets\"];\n            fetchTweets();\n        }\n    }[\"TwitterTimeline.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"twitter__timeline\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"timeline-Header timeline-InformationCircle-widgetParent\",\n                \"data-scribe\": \"section:header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"timeline-Header-title u-inlineBlock\",\n                    \"data-scribe\": \"element:title\",\n                    children: [\n                        tweets.length > 1 ? 'Latest Tweets' : 'Latest Tweet',\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"timeline-Header-byline\",\n                            \"data-scribe\": \"element:byline\",\n                            children: [\n                                \"\\xa0by\\xa0\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"customisable-highlight\",\n                                    href: \"https://twitter.com/rki_de\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    title: \"‎@rki_de on Twitter\",\n                                    children: \"‎@rki_de\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '10px'\n                },\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        padding: '20px'\n                    },\n                    children: \"Loading tweets...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        padding: '20px',\n                        color: '#666'\n                    },\n                    children: [\n                        error,\n                        \". \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://x.com/rki_de\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            children: \"Visit @rki_de on X\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 22\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: tweets.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: tweets.map((tweet, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: index < tweets.length - 1 ? '20px' : '0'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tweet__WEBPACK_IMPORTED_MODULE_4__.Tweet, {\n                                    id: tweet.tweetId\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, tweet.tweetId, false, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 19\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            textAlign: 'center',\n                            color: '#666'\n                        },\n                        children: [\n                            \"No tweets to display. \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://x.com/rki_de\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                children: \"Visit @rki_de on X\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 39\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false)\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\common\\\\widgets\\\\TwitterTimeline.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TwitterTimeline, \"TsviQxYgQYw3EKAENgdCofvw9w8=\");\n_c = TwitterTimeline;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TwitterTimeline);\nvar _c;\n$RefreshReg$(_c, \"TwitterTimeline\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/common/widgets/TwitterTimeline.tsx\n"));

/***/ })

});