// React Imports
import React from "react";
import { Col, Row } from "react-bootstrap";
import { useTranslation } from 'next-i18next';

// Components Imports
import RKICard from "../../../components/common/RKICard";
import ReadMoreContainer from "../../../components/common/readMore/readMore";

// Interfaces Imports
import { IProject } from "../../../shared/interfaces/project.interface";

const filterPartnerInstitutes = (partner_institutions: any[]) => {
  let partnerInstitutes = [];
  partner_institutions?.forEach((institution) => {
      institution.partner_institution?.forEach((item) => {
          partnerInstitutes.push({
              _id: item._id,
              title: item.title,
          });
      });
  });
  partnerInstitutes = partnerInstitutes.filter(
      (value, index, self) => self.findIndex((m) => m._id === value._id) === index
  );
  return partnerInstitutes;
};

const ProjectInfoSection = (props: { project: IProject }) => {
  const { t } = useTranslation('common');
  let { description, partner_institutions } = props.project;
  const partnerInstitutes = filterPartnerInstitutes(partner_institutions);

  return (
    <>
      <Row className="projectInfoBlock">
        <Col className="projectDescBlock" md={8}>
          <ReadMoreContainer description={description} />
        </Col>
        <Col md={4} className="projectInfo">
          <RKICard
            header={t("ProjectInformation")}
            body={Project_info_func(t, props.project)}
          />
          <RKICard
            header={t("PartnerOrganisation")}
            body={paratner_func(partnerInstitutes)}
          />

          <RKICard
            header={t("CountriesCoveredbyProject")}
            body={project_func(partner_institutions)}
          />
        </Col>
      </Row>
    </>
  );
};

export default ProjectInfoSection;
function Project_info_func(t: (key: string) => string, projectData: IProject) {
  const { area_of_work, funded_by } = projectData;
  return (
    <div>
      <div className="projetInfoItems">
        <h6>{t("AreaofWork")}: </h6>
        <p>{area_of_work?.map((item) => item.title).join(", ")}</p>
      </div>
      <div className="projetInfoItems">
        <h6>{t("FundedBy")}: </h6>
        <p>{funded_by}</p>
      </div>
    </div>
  );
}

function project_func(partner_institutions: any[]) {
  return (
    <ul className="projectPartner">
      {partner_institutions?.map((institution, index) => {
        return <li key={index}>{institution?.partner_country?.title || ""}</li>;
      })}
    </ul>
  );
}

function paratner_func(partner_institutions: any[]) {
  return (
    <ul className="projectPartner">
      {partner_institutions?.map((institution, index) => {
        return <li key={index}>{institution?.title || ""}</li>;
      })}
    </ul>
  );
}
