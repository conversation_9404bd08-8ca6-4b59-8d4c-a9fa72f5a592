//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateInstitutionNetworkDto } from './dto/create-institution-network.dto';
import { UpdateInstitutionNetworkDto } from './dto/update-institution-network.dto';
import { InstitutionNetworkService } from './institution-network.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('institutionnetwork')
@UseGuards(SessionGuard)
export class InstitutionNetworkController {
  constructor(
    private readonly _institutionnetworkService: InstitutionNetworkService,
  ) {}

  @Post()
  async create(
    @Body() createInstitutionNetworkDto: CreateInstitutionNetworkDto,
  ) {
    try {
      const resp = await this._institutionnetworkService.create(
        createInstitutionNetworkDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._institutionnetworkService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') institutionNetworkId: string) {
    return this._institutionnetworkService.get(institutionNetworkId);
  }

  @Patch(':id')
  async update(
    @Param('id') institutionNetworkId: string,
    @Body() updateInstitutionNetworkDto: UpdateInstitutionNetworkDto,
  ) {
    try {
      const resp = await this._institutionnetworkService.update(
        institutionNetworkId,
        updateInstitutionNetworkDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') institutionNetworkId: string) {
    const deletedData =
      this._institutionnetworkService.delete(institutionNetworkId);
    return deletedData;
  }
}
