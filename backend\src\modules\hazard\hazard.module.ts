//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { HazardController } from './hazard.controller';
import { HazardService } from './hazard.service';
import { ImageService } from './../image/image.service';
// SCHEMAS
import { HazardSchema } from '../../schemas/hazard.schemas';
import { EventSchema } from 'src/schemas/event.schemas';
import { EventStatusSchema } from 'src/schemas/event_status.schemas';
import { OperationSchema } from 'src/schemas/operation.schemas';
import { InstitutionSchema } from 'src/schemas/institution.schemas';
import { OperationStatusSchema } from 'src/schemas/operation_status.schemas';
import { ImageSchema } from 'src/schemas/image.schemas';
import { UpdateSchema } from 'src/schemas/update.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Hazard', schema: HazardSchema },
      { name: 'Event', schema: EventSchema },
      { name: 'EventStatus', schema: EventStatusSchema },
      { name: 'Operation', schema: OperationSchema },
      { name: 'OperationStatus', schema: OperationStatusSchema },
      { name: 'Update', schema: UpdateSchema},
      { name: 'Institution', schema: InstitutionSchema },
      { name: 'Image', schema: ImageSchema }
    ])
  ],
  controllers: [HazardController],
  providers: [HazardService, ImageService],
})

export class HazardModule { }