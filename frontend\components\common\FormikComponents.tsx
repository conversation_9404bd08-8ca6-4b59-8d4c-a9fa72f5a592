import React from 'react';
import { Form } from 'react-bootstrap';
import { Field, ErrorMessage, useField } from 'formik';

// Text Input Component
export const TextInput = ({ name, id, required, validator, errorMessage, as, multiline, rows, ...props }: any) => {
  const [field, meta, helpers] = useField(name);

  return (
    <Form.Control
      {...field}
      {...props}
      id={id}
      as={as || 'input'}
      rows={rows}
      isInvalid={meta.touched && !!meta.error}
    />
  );
};

// Select Group Component
export const SelectGroup = ({ name, id, required, errorMessage, children, ...props }: any) => {
  const [field, meta] = useField(name);

  return (
    <Form.Control
      as="select"
      {...field}
      {...props}
      id={id}
      isInvalid={meta.touched && !!meta.error}
    >
      {children}
    </Form.Control>
  );
};

// Validation Form Component (for backward compatibility)
export const ValidationForm = ({ children, onSubmit, ...props }: any) => {
  return (
    <Form onSubmit={onSubmit} {...props}>
      {children}
    </Form>
  );
};
