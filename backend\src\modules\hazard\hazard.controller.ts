//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ACGuard,
  InjectRolesBuilder,
  RolesBuilder,
  UseRoles,
} from 'nest-access-control';

//Import services/components
import { CreateHazardDto } from './dto/create-hazard.dto';
import { UpdateHazardDto } from './dto/update-hazard.dto';
import { ResponseError } from '../../common/dto/response.dto';
import { HazardService } from './hazard.service';
import { ImageService } from './../image/image.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('hazard')
@UseGuards(SessionGuard)
export class HazardController {
  constructor(
    private readonly _hazardService: HazardService,
    private readonly _imageService: ImageService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) {}

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'hazard',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(
    @Body() createHazardDto: CreateHazardDto,
    @Req() request: Request,
  ) {
    try {
      const user: any = request.user;
      createHazardDto['user'] = user._id;
      const _hazard: any = await this._hazardService.create(createHazardDto);
      const imageIds = _hazard['picture'] ? [_hazard['picture']._id] : [];
      if (imageIds.length > 0) {
        await this._imageService.bulkUpdate(imageIds);
      }
      const mediaimageIds = _hazard['images']
        ? _hazard['images'].map((d) => d._id)
        : [];
      if (mediaimageIds.length > 0) {
        await this._imageService.bulkUpdate(mediaimageIds);
      }

      return _hazard;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'hazard',
    action: 'read',
    possession: 'any',
  })
  @Get()
  async findAll(@Query() query: any) {
    return this._hazardService.findAll(query);
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'hazard',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') hazardId: string, @Req() request: Request) {
    if (request?.query?.Doctable) {
      return this._hazardService.getSortedDocList(hazardId, request.query);
    } else if (request.query?.DocUpdatetable) {
      return this._hazardService.getSortedUpdateDocList(
        hazardId,
        request.query,
      );
    } else {
      return this._hazardService.get(hazardId);
    }
  }

  @UseGuards(ACGuard)
  @Patch(':id')
  async update(
    @Param('id') hazardId: string,
    @Body() updateHazardDto: UpdateHazardDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    const oldHazard: any = await this._hazardService.get(hazardId);
    const hazardUserid = oldHazard.user ? oldHazard.user._id : null;
    const permission =
      user._id === hazardUserid
        ? this.roleBuilder.can(user.roles).updateOwn('operation').granted
        : this.roleBuilder.can(user.roles).updateAny('operation').granted;
    if (permission) {
      try {
        const _hazard: any = await this._hazardService.update(
          hazardId,
          updateHazardDto,
        );
        const imageIds = _hazard['picture'] ? [_hazard['picture']._id] : [];
        if (imageIds.length > 0) {
          await this._imageService.bulkUpdate(imageIds);
        }
        const mediaimageIds = _hazard['images']
          ? _hazard['images'].map((d) => d._id)
          : [];
        if (mediaimageIds.length > 0) {
          await this._imageService.bulkUpdate(mediaimageIds);
        }

        return _hazard;
      } catch (error) {
        return {
          message: error.message,
          errorCode: error.code,
        };
      }
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  @UseGuards(ACGuard)
  @Delete(':id')
  async remove(@Param('id') hazardId: string, @Req() request: Request) {
    try {
      const user: any = request.user;
      const oldHazard: any = await this._hazardService.get(hazardId);
      const hazardUserId = oldHazard.user ? oldHazard.user._id : null;
      const permission =
        user._id === hazardUserId
          ? this.roleBuilder.can(user.roles).updateOwn('operation').granted
          : this.roleBuilder.can(user.roles).updateAny('operation').granted;
      if (permission) {
        const _hazard: any = await this._hazardService.delete(hazardId);
        const imageIds = _hazard['picture'] ? [_hazard['picture']._id] : [];
        if (imageIds.length > 0) {
          await this._imageService.bulkDelete(imageIds);
        }
        const mediaimageIds = _hazard['images']
          ? _hazard['images'].map((d) => d._id)
          : [];
        if (mediaimageIds.length > 0) {
          await this._imageService.bulkDelete(mediaimageIds);
        }
        return _hazard;
      } else {
        return new ResponseError('HAZARD.ERROR.ACCESS_DENIED');
      }
    } catch (e) {
      return new ResponseError('HAZARD.ERROR.NOT_FOUND');
    }
  }

  @UseRoles({
    resource: 'event',
    action: 'read',
    possession: 'any',
  })
  @Get(':id/events/:status')
  getEvents(
    @Param('id') hazardId: string,
    @Param('status') status: string,
    @Query() query: any,
  ) {
    const _country = this._hazardService.getEvents(hazardId, status, query);
    return _country;
  }

  @UseRoles({
    resource: 'institution',
    action: 'read',
    possession: 'any',
  })
  @Get(':id/institutions')
  getInstitutions(@Param('id') hazardId: string, @Query() query: any) {
    const _country = this._hazardService.getInstitutions(hazardId, query);
    return _country;
  }

  @UseRoles({
    resource: 'operation',
    action: 'read',
    possession: 'any',
  })
  @Get(':id/operations')
  getOperations(
    @Param('id') hazardId: string,
    @Param('status') status: string,
    @Query() query: any,
  ) {
    const _country = this._hazardService.getOperations(hazardId, status, query);
    return _country;
  }
}
