//Import Library
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ServeStaticModule } from '@nestjs/serve-static';
import { AccessControlModule } from "nest-access-control";
import { join } from 'path';

//Import services/components
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AnonymousController } from './modules/anonymous/anonymous.controller';
import { EmailService } from './email.service';
import { roles } from './app.roles';
import { OperationModule } from './modules/operation/operation.module';
import { SyndromeModule } from './modules/syndrome/syndrome.module';
import { CountryModule } from './modules/country/country.module';
import { WorldRegionModule } from './modules/world-region/world-region.module';
import { AreaOfWorkModule } from './modules/area-of-work/area-of-work.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { LanguageModule } from './modules/language/language.module';
import { EventModule } from './modules/event/event.module';
import { ProjectModule } from './modules/project/project.module';
import { CategoryModule } from './modules/category/category.module';
import { ClassificationModule } from './modules/classification/classification.module';
import { HazardModule } from './modules/hazard/hazard.module';
import { HazardTypeModule } from './modules/hazard-type/hazard_type.module';
import { OperationStatusModule } from './modules/operation-status/operation-status.module';
import { UpdateModule } from './modules/updates/update.module';
import { RiskLevelModule } from './modules/risk-level/risk-level.module';
import { DeploymentStatusModule } from './modules/deployment-status/deployment-status.module';
import { ExpertiseModule } from './modules/expertise/expertise.module';
import { VspaceTopicModule } from './modules/vspace-topic/vspace-topic.module';
import { VspaceModule } from './modules/vspace/vspace.module';
import { RegionModule } from './modules/region/region.module';
import { UpdateTypeModule } from './modules/update-type/update-type.module';
import { ProjectStatusModule } from './modules/project-status/project-status.module';
import { InstitutionTypeModule } from './modules/institution-type/institution-type.module';
import { InsitutionNetworkModule } from './modules/institution-network/institution-network.module';
import { InstitutionModule } from './modules/institution/institution.module';
import { TaxonomyIconModule } from './modules/taxonomy-icon/taxonomy-icon.module';
import { RolesModule } from './modules/roles/roles.module';
import { ImageModule } from './modules/image/image.module';
import { FilesModule } from './modules/files/files.module';
import { EventStatusModule } from './modules/event-status/event-status.module';
import { CountryRegionModule } from './modules/country-region/country-region.module';
import { HazardHazardTypeModule } from './modules/hazard-hazardtype/hazard-hazardtype.module';
import { StatsModule } from './modules/stats/stats.module';
import { VspaceRequestSubscribersModule } from './modules/vspace-request-subscribers/vspace-request-subscribers.module';
import { UserInviteModule } from './modules/user-invite/user-invite.module';
import { FileCategoryModule } from './modules/file-category/file-category.module';
import { LandingPageModule } from './modules/landing-page/landingPage.module';
import { PageCategoryModule } from './modules/page-category/page-category.module';
import { FlagModule } from './modules/flag/flag.module';
import { TweetsModule } from './modules/tweets/tweets.module';

@Module({
  imports: [
    // Static Content
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'build'),
    }),

    //Setting access control for roles
    AccessControlModule.forRoles(roles),

    // Custom Modules

    // Authentication Modules
    AuthModule,
    UsersModule,

    // Project dependencies Modules
    OperationModule,
    SyndromeModule,
    CountryModule,
    WorldRegionModule,
    AreaOfWorkModule,
    LanguageModule,
    EventModule,
    CategoryModule,
    ClassificationModule,
    ProjectModule,
    HazardModule,
    HazardTypeModule,
    OperationStatusModule,
    UpdateModule,
    RiskLevelModule,
    DeploymentStatusModule,
    ExpertiseModule,
    VspaceTopicModule,
    VspaceModule,
    RegionModule,
    UpdateTypeModule,
    ProjectStatusModule,
    InstitutionTypeModule,
    InsitutionNetworkModule,
    InstitutionModule,
    TaxonomyIconModule,
    RolesModule,
    ImageModule,
    FilesModule,
    EventStatusModule,
    CountryRegionModule,
    HazardHazardTypeModule,
    StatsModule,
    VspaceRequestSubscribersModule,
    UserInviteModule,
    FileCategoryModule,
    LandingPageModule,
    PageCategoryModule,
    FlagModule,
    TweetsModule,
    // External Modules
    ConfigModule.forRoot(),
    MongooseModule.forRoot(process.env.MONGODB, {
      connectionFactory: (connection) => {
        connection.plugin(require('mongoose-autopopulate'));
        return connection;
      }
    })
  ],
  controllers: [AppController, AnonymousController],
  providers: [AppService, EmailService],
})

export class AppModule { }
