//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';


export const InstitutionSchema = new mongoose.Schema({
  title: { type: String },
  contact_name: { type: String },
  description: { type: String },
  type: { type: mongoose.Schema.Types.ObjectId, ref: 'InstitutionType', autopopulate: true },
  networks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'InstitutionNetwork', autopopulate: true }],
  expertise: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Expertise', autopopulate: true }],
  hazard_types: [{ type: mongoose.Schema.Types.ObjectId, ref: 'HazardType', autopopulate: true }],
  hazards: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Hazard', autopopulate: true }],
  address: {
    country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
    region: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true }],
    world_region: {type: mongoose.Schema.Types.ObjectId, ref: 'WorldRegion'},
    city: { type: String },
    line_1: { type: String },
    line_2: { type: String },
    coordinates: {
      latitude: { type: String, default: 40.709011 },
      longitude: { type: String, default: -73.98 }
    }
  },
  focal_points: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Users'}],
  website: { type: String },
  telephone: { type: String },
  dial_code: { type: String },
  twitter: { type: String },
  header: { type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true },
  use_default_header: { type: Boolean },
  document: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Files', autopopulate: true }],
  doc_src: [{ type: String}],
  images: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true }],
  images_src: [{ type: String}],
  status: String,
  email: String,
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true }, // institution created user
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
  primary_focal_point: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true },
  partners: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Institution'}],
  department: { type: String },
  unit:  { type: String }
});

InstitutionSchema.plugin(mongoosePaginate);