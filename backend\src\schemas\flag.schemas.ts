//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const FlagSchema = new mongoose.Schema({
  entity_type: { type: String, required: true },
  entity_id: {type: mongoose.Schema.Types.ObjectId, refPath: 'onModel', required: true },
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users'},
  timestamp: { type: Date, default: Date.now},
  onModel: {
    type: String,
    required: true,
    enum: ['Institution', 'Operation', 'Project', 'Event', 'Vspace']
  }
});
FlagSchema.plugin(mongoosePaginate);