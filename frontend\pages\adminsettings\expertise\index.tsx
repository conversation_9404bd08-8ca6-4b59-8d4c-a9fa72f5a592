//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import ExpertiseTable from "./expertiseTable";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import PageHeading from "../../../components/common/PageHeading";
import { canAddExpertise } from "../permissions";
import NoAccessMessage from "../../rNoAccess";
import { useSelector } from "react-redux";

const ExpertiseIndex = (props) => {
  const { t } = useTranslation('common');
  const ShowExpertiseIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title={t("adminsetting.Expertise.Forms.Expertise")} />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_expertise"
                >
                <Button variant="secondary" size="sm">
                {t('adminsetting.Expertise.Forms.AddExpertise')}
              </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              <ExpertiseTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  const ShowAddExpertise = canAddExpertise(() => <ShowExpertiseIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.expertise?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddExpertise />
  )
}

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default ExpertiseIndex;