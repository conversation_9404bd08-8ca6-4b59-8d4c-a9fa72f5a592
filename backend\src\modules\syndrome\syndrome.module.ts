//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { SyndromeController } from './syndrome.controller';
import { SyndromeService } from './syndrome.service';
// SCHEMAS
import { SyndromeSchema } from '../../schemas/syndrome.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Syndrome', schema: SyndromeSchema }
    ])
  ],
  controllers: [SyndromeController],
  providers: [SyndromeService],
})

export class SyndromeModule { }