//Import Library
import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import {
  Con<PERSON>er,
  <PERSON>,
  Button,
  Form,
  Row,
  Col,
  Spinner,
} from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faEdit,
  faCheckSquare,
} from "@fortawesome/free-solid-svg-icons";

//Import services/components
import ProfileEdit from "./profileEdit";
import MyConsent from "./myConsent";
import BookmarkTable from "./bookmarkTable";
import { loadLoggedinUserData } from "../../stores/userActions";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import ImageEditor from "../../components/common/ImageEditor";
import PageHeading from "../../components/common/PageHeading";


const Profile = (props) => {
  const { t } = useTranslation('common');
  const [show, setShow]: any = useState(false);
  const [consentShow, setConsentShow] = useState(false);
  const [modal, setModal] = useState(false);

  const handleShow = () => setShow(true);

  const closeHandler = (val) => {
    setShow(val);
    setConsentShow(false);
  };

  const intialState = {
    username: "",
    institution: "",
    email: "",
    image: null,
    firstname: "",
    lastname: "",
    position: ""
  };

  const [userData, setUserData]: any = useState(intialState);

  useEffect(() => {
    const getUser = async (userParams) => {
      const response = await apiService.post("/users/getLoggedUser", userParams);
      if (response) {
        response.image =
          response.image && response.image._id
            ? `${process.env.API_SERVER}/image/show/${response.image._id}`
            : "/images/rkiProfile.jpg";
        setUserData((prevState) => ({ ...prevState, ...response }));
      }
    };
    getUser({});
  }, [show, modal]);

  /***Handle Image Edit****/
  const imageEditorHandler = () => {
    setModal(true);
  };

  const modalClose = (val) => {
    props.dispatch(loadLoggedinUserData());
    setModal(val);
  };
  /********End *********/

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={12}>
          <PageHeading title={t("setInfo.myprofile")} />
        </Col>
      </Row>
      <Row>
        <Col xs lg={12}>
          <Container fluid>
            <Row>
              <Col>
                <Card className=" mt-3 form--outline profile--card">
                  <div className="row mx-3 mt-4">
                    <div className="col-lg-3 col-md-4 text-center">
                      {userData.image ? (
                        <>
                          <img
                            className="imgStyle"
                            src={
                              userdata_func()
                            }
                          />
                        </>
                      ) : (
                        <Spinner
                          className="text-center m-5"
                          animation="grow"
                          variant="dark"
                      />
                        )}
                    </div>
                    <div className="col-md-9 w-100">
                      <Form>
                        <div>
                          <Form.Group
                            as={Row}
                            controlId="username"
                            className="align-items-center mb-3"
                          >
                            <Form.Label column md="3" xs="5" lg="2" className="px-1">
                              {t("setInfo.username")}
                            </Form.Label>
                            <Col xs="1">
                              <span>:</span>
                            </Col>
                            <Col md="8" xs="5" lg="9">
                              <Form.Control
                                plaintext
                                readOnly
                                defaultValue={userData.username}
                              />
                            </Col>
                          </Form.Group>

                          <Form.Group
                            as={Row}
                            controlId="name"
                            className="align-items-center mb-3"
                          >
                            <Form.Label column md="3" xs="5" lg="2" className="px-1">
                              {t("setInfo.name")}
                            </Form.Label>
                            <Col xs="1">
                              <span>:</span>
                            </Col>
                            <Col md="8" xs="5" lg="9">
                              <Form.Control
                                plaintext
                                readOnly
                                defaultValue={userData && userData.firstname ? `${userData.firstname} ${userData.lastname}` : ""}
                              />
                            </Col>
                          </Form.Group>
                          <Form.Group
                            as={Row}
                            controlId="position"
                            className="align-items-center mb-3"
                          >
                            <Form.Label column md="3" xs="5" lg="2" className="px-1">
                              {t("setInfo.position")}
                            </Form.Label>
                            <Col xs="1">
                              <span>:</span>
                            </Col>
                            <Col md="8" xs="5" lg="9">
                              <Form.Control
                                plaintext
                                readOnly
                                defaultValue={userData && userData.position ? userData.position : ""}
                              />
                            </Col>
                          </Form.Group>

                          <Form.Group
                            as={Row}
                            controlId="Organization"
                            className="align-items-center mb-3"
                          >
                            <Form.Label column md="3" xs="5" lg="2" className="px-1">
                              {t("setInfo.organisation")}
                            </Form.Label>
                            <Col xs="1">
                              <span>:</span>
                            </Col>
                            <Col md="8" xs="5" lg="9">
                              <Form.Control
                                readOnly
                                plaintext
                                defaultValue={
                                  userData.institution &&
                                  userData.institution.title
                                }
                              />
                            </Col>
                          </Form.Group>
                          <ImageEditor
                            image={userData.image}
                            isOpen={modal}
                            onModalClose={(val) => modalClose(val)}
                          />
                          <Form.Group
                            as={Row}
                            controlId="Email"
                            className="align-items-center mb-3"
                          >
                            <Form.Label column md="3" xs="5" lg="2" className="px-1">
                              {t("setInfo.email")}
                            </Form.Label>
                            <Col xs="1">
                              <span>:</span>
                            </Col>
                            <Col md="8" xs="5" lg="9">
                              <Form.Control
                                plaintext
                                readOnly
                                defaultValue={userData.email}
                              />
                            </Col>
                          </Form.Group>
                          <Form.Group
                            as={Row}
                            controlId="consent"
                            className="align-items-center mb-3"
                          >
                            <Form.Label column md="3" xs="5" lg="2" className="px-1">
                            {t("setInfo.Consent")}
                            </Form.Label>
                            <Col xs="1">
                              <span>:</span>
                            </Col>
                            <Col md="8" xs="5" lg="9">
                              <FontAwesomeIcon
                                className="clickable"
                                icon={faCheckSquare}
                                color="#293F92"
                                onClick={() => setConsentShow(true)}
                              />
                            </Col>
                          </Form.Group>
                        </div>
                      </Form>
                    </div>
                  </div>
                  <div className="mb-3 mx-3">
                    <Button onClick={imageEditorHandler} variant="secondary">
                      <FontAwesomeIcon icon={faEdit} />
                      &nbsp;{t("setInfo.editmyimage")}
                    </Button>
                    <Button className="float-right" onClick={handleShow} variant="secondary">
                      <FontAwesomeIcon icon={faEdit} />
                      &nbsp;{t("setInfo.editmyprofile")}
                    </Button>
                  </div>
                </Card>
              </Col>
            </Row>
          </Container>
        </Col>
      </Row>
      {show && (
        <ProfileEdit
          data={userData}
          isOpen={show}
          manageClose={(val) => closeHandler(val)}
        />
      )}
      <Col xs={12} className="mt-3">
        <PageHeading title={t("MyBookmarks")} />
        <BookmarkTable />
      </Col>
      {consentShow && (
        <MyConsent
          isOpen={consentShow}
          manageClose={(val) => closeHandler(val)}
          id={userData._id}
        />
      )}
    </Container>
  );

  function userdata_func(): string {
    return userData && userData.image ? userData.image : "";
  }
};

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default connect()(Profile);
