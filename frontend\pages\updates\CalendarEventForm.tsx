//Import Library
import React, { useEffect, useState } from "react";
import { Form, Container, Row, Col } from "react-bootstrap";

//Import services/components
import DocumentForm from "./DocumentForm";
import RKIDatePicker from "../../components/common/RKIDatePicker";
import { useTranslation } from 'next-i18next';

//TOTO refactor
interface CalendarEventFormProps {
  validation: any;
  onChangeDate: (date: Date, key: string) => void;
  startDate: Date | null;
  endDate: Date | null;
  data: any[];
  getId: (id: any[]) => void;
  imgSrc: any[];
  getSourceCollection: (docSrcArr: any[]) => void;
}

const CalendarEventForm = (props: CalendarEventFormProps): React.ReactElement => {
  const [, setEndDate] = useState<boolean>(false);
  const { validation, onChangeDate, startDate, endDate } = props;
  const { t } = useTranslation('common');

  useEffect(() => {
    endDate == null ? setEndDate(false) : setEndDate(true);
  }, [endDate]);

  const uploadHandler = (id) => {
    props.getId(id);
  };

  const getSourceText = (imgSrcArr) => {
    props.getSourceCollection(imgSrcArr);
  }

  return (
    <Container className="formCard" fluid>
      <Col className="header-block" lg={12}>
        <h6>
          <span>{t("update.Date")}</span>
        </h6>
      </Col>
      <Row>
        <Col>
          <Form.Group>
            <Form.Label className="d-block required-field">{t("update.StartDate")}</Form.Label>
            <RKIDatePicker
              selected={startDate}
              minDate={new Date()}
              showTimeSelect
              timeIntervals={15}
              onChange={(date) => onChangeDate(date, "startDate")}
              placeholderText= {t("update.Selectadate")}
              dateFormat="MMMM d, yyyy h:mm aa"
            />
          </Form.Group>
          { (  validation.startDate === true && (!startDate)) &&  <p style={{  color: "red"}}>{t("update.Pleaseenterthestartdate")}</p>}

        </Col>
        <Col>

            <Form.Group>
              <Form.Label className="d-block required-field">{t("update.EndDate")}</Form.Label>
              <RKIDatePicker
                selected={endDate}
                showTimeSelect
                timeIntervals={15}
                onChange={(date) => onChangeDate(date, "endDate")}
                placeholderText={t("update.Selectadate")}
                minDate={startDate}
                dateFormat="MMMM d, yyyy h:mm aa"
              />
            </Form.Group>
            { (  validation.startDate === true && (!endDate)) &&  <p style={{  color: "red"}}>{t("update.Pleaseentertheenddate")} </p>}

        </Col>
      </Row>
      <DocumentForm data={props.data} srcText={props.imgSrc} getId={(id) => uploadHandler(id)} getSourceCollection={(imgSrcArr) => getSourceText(imgSrcArr)} />
    </Container>
  );
};
export default CalendarEventForm;
