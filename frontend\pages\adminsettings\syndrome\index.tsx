//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import SyndromeTable from "./syndromeTable";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddSyndromes } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const SyndromeIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowSyndromeIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title={t("adminsetting.syndrome.Syndromes")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_syndrome"
              >
              <Button variant="secondary" size="sm">
              {t("adminsetting.syndrome.AddSyndrome")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <SyndromeTable />
          </Col>
        </Row>
      </Container>
    );
  }

  const ShowAddSyndromes = canAddSyndromes(() => <ShowSyndromeIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.syndrome?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddSyndromes />
  )
};

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default SyndromeIndex;
