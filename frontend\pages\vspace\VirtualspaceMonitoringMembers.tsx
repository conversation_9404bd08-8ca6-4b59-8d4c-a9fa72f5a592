//Import Library
import _ from "lodash";

//Import services/components
import RKITable from "../../components/common/RKITable";

const VspaceMonitoringMembers = (propData: any) => {
 
  const sortMonitoringAndEvaluationMembers = (rows: any, field: any, direction: any) => {
    const handleField = (row: any) => {
      if (row[field]) {
        return row[field].toLowerCase();
      }
      return row[field];
    };
    return _.orderBy(rows, handleField, direction);
  };

  return (
    <>
      <RKITable
        noHeader={true}
        columns={propData.Monitoringmembers.columns}
        data={propData.Monitoringmembers.subscribers}
        dense={true}
        paginationServer
        pagServer={true}
        paginationTotalRows={0}
        subHeader
        subHeaderAlign="left"
        pagination={true}
        persistTableHead
        sortFunction={sortMonitoringAndEvaluationMembers}
        />
    </>
  )
}

export default VspaceMonitoringMembers;