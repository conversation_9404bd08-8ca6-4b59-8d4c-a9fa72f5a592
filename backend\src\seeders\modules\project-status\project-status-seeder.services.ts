//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { projectStatuses } from "../../data/project-status";
import { ProjectStatusInterface } from "src/interfaces/project-status.interface";

/**
 * Service dealing with Project Status.
 *
 * @class
 */
@Injectable()
export class ProjectStatusSeederService {

  constructor(
    @InjectModel('ProjectStatus') private projectStatusModel: Model<ProjectStatusInterface>
  ) {}

  /**
   * Seed all Project Status.
   *
   * @function
   */
  create(): Array<Promise<ProjectStatusInterface>> {
    return projectStatuses.map(async (classif: any) => {
      return await this.projectStatusModel
        .findOne({ title: classif.title })
        .exec()
        .then(async dbProjectStatus => {
          // We check if a Project Status already exists.
          // If it does don't create a new one.
          if (dbProjectStatus) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.projectStatusModel.create(classif),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}