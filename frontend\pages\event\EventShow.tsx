//Import Library
import React, { useEffect, useState } from "react";
import { Container } from "react-bootstrap";

//Import services/components
import UpdatePopup from "../../components/updates/UpdatePopup";
import apiService from "../../services/apiService";
import EventHeaderSection from "./components/EventHeaderSection";
import EventCoverSection from "./components/EventCoverSection";
import EventAccordionSection from "./components/EventAccordionSection";

interface EventShowProps {
  routes: string[];
}

const EventShow = (props: EventShowProps) => {
    const [editAccess, setEditAccess] = useState(false);
    const [eventData, setEventData] = useState<any>({
        title: "",
        laboratory_confirmed: "",
        officially_validated: "",
        status: { title: "" },
        syndrome: { title: "" },
        country: { title: "" },
        hazard_type: { title: "" },
        rki_monitored: "",
        risk_assessment: {
            country: { title: "" },
            international: { title: "" },
            region: { title: "" },
        },
        hazard: [],
        country_regions: [],
        operation: { title: "" },
        description: "",
        more_info: "",
        images: [],
        images_src: [],
    });

    const getEventData = async (eventParams, loginUserData) => {
        const response = await apiService.get(`/event/${props.routes[1]}`, eventParams);
        if (response) {
            setEventData(response);
            getEventEditAccess(response, loginUserData);
        }
    };
    useEffect(() => {
        if (props.routes && props.routes[1]) {
            getLoggedInUser();
        }
    }, []);

    const getLoggedInUser = async () => {
        const data = await apiService.post("/users/getLoggedUser", {});
        getEventData({}, data);
    };

    function getEventEditAccess(eventData, loginUser) {
        setEditAccess(false);
        if (loginUser && loginUser['roles']) {
            if (loginUser['roles'].includes("SUPER_ADMIN")) {
                //SUPER_ADMIN can Edit all organisations
                setEditAccess(true);
            } else if (loginUser['roles'].includes("GENERAL_USER") && eventData.user._id == loginUser['_id']) {
                //"GENERAL_USER" can Edit organisations which is added by them only
                setEditAccess(true);
            }
            else if (loginUser['roles'].includes("PLATFORM_ADMIN") && eventData.user._id == loginUser['_id']) {
                //"PLATFORM_ADMIN" can Edit organisations which is added by them only
                setEditAccess(true);
            }
        }
    }

    const propsData = {
        eventData : eventData,
        routeData : props,
        editAccess: editAccess
    }

    return (
        <Container className="eventDetail" fluid>
            <UpdatePopup routes={props.routes} />
            <EventHeaderSection { ...propsData } />
            <EventCoverSection eventData = { eventData } />
            <EventAccordionSection { ...propsData } />
        </Container>
    );

};

export default EventShow;
