import React, { forwardRef } from 'react';
import { Formik, Form, FormikProps, FormikHelpers } from 'formik';
import * as Yup from 'yup';

// This is a wrapper component that replaces react-bootstrap4-form-validation with Formik
interface MockEvent {
  preventDefault: () => void;
  stopPropagation: () => void;
  currentTarget: null;
  target: null;
}

interface ValidationFormWrapperProps {
  children: React.ReactNode | ((formikProps: FormikProps<any>) => React.ReactNode);
  onSubmit: (event: MockEvent, values?: Record<string, any>, actions?: FormikHelpers<Record<string, any>>) => void;
  onErrorSubmit?: (errors: any) => void;
  initialValues?: Record<string, any>;
  enableReinitialize?: boolean;
  autoComplete?: string;
  className?: string;
  onKeyPress?: (e: any) => void;
}

const ValidationFormWrapper = forwardRef<HTMLFormElement, ValidationFormWrapperProps>((props, ref) => {
  const { children, onSubmit, autoComplete, className, onKeyPress, initialValues, ...rest } = props;

  // Create an empty validation schema by default
  const validationSchema = Yup.object().shape({});

  return (
    <Formik
      initialValues={initialValues || {}}
      validationSchema={validationSchema}
      onSubmit={(values: Record<string, any>, actions: FormikHelpers<Record<string, any>>) => {
        // Create a mock event object with preventDefault method and currentTarget
        const mockEvent = {
          preventDefault: () => {},
          stopPropagation: () => {},
          currentTarget: null, // Set to null to avoid checkValidity errors
          target: null
        };

        if (onSubmit) {
          // Pass the mock event object to maintain compatibility with the original code
          onSubmit(mockEvent, values, actions);
        }
      }}
      {...rest}
    >
      {(formikProps: FormikProps<any>) => (
        <Form
          ref={ref}
          onSubmit={formikProps.handleSubmit}
          autoComplete={autoComplete}
          className={className}
          onKeyPress={onKeyPress}
        >
          {typeof children === 'function' ? children(formikProps) : children}
        </Form>
      )}
    </Formik>
  );
});

ValidationFormWrapper.displayName = 'ValidationFormWrapper';

export default ValidationFormWrapper;
