//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { ProjectStatusInterface } from '../../interfaces/project-status.interface';
import { CreateProjectStatusDto } from './dto/create-project-status.dto';
import { UpdateProjectStatusDto } from './dto/update-project-status.dto';
const FindProjectStatus = 'Could not find Project Status.';
@Injectable()
export class ProjectStatusService {
  constructor(
    @InjectModel('ProjectStatus')
    private projectStatusModel: Model<ProjectStatusInterface>,
  ) {}

  async create(
    createProjectStatusDto: CreateProjectStatusDto,
  ): Promise<ProjectStatusInterface> {
    const createdProjectStatus = new this.projectStatusModel(
      createProjectStatusDto,
    );
    return createdProjectStatus.save();
  }

  async findAll(query): Promise<ProjectStatusInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.projectStatusModel.paginate(_filter, options);
  }

  async get(projectStatusId): Promise<ProjectStatusInterface[]> {
    let _result;
    try {
      _result = await this.projectStatusModel.findById(projectStatusId).exec();
    } catch (error) {
      throw new NotFoundException(FindProjectStatus);
    }
    if (!_result) {
      throw new NotFoundException(FindProjectStatus);
    }
    return _result;
  }

  async update(
    projectStatusId: any,
    updateProjectStatusDto: UpdateProjectStatusDto,
  ) {
    const getById: any = await this.projectStatusModel
      .findById(projectStatusId)
      .exec();
    const updatedData = new this.projectStatusModel(updateProjectStatusDto);
    Object.keys(updateProjectStatusDto).forEach((d) => {
      getById[d] = updatedData[d];
    });
    getById.updated_at = new Date();
    return getById.save();
  }

  async delete(projectStatusId: string) {
    const result = await this.projectStatusModel
      .deleteOne({ _id: projectStatusId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(FindProjectStatus);
    }
  }
}
