//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const CountryRegionSchema = new mongoose.Schema({
  ISO2: { type: String, required: true },
  Country_Region: { type: String, required: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

CountryRegionSchema.plugin(mongoosePaginate);
