//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import InstitutionNetworkTable from "./institutionNetworkTable";
import { useTranslation } from 'next-i18next';
import { canAddOrganisationNetworks } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";



const InstitutionNetworkIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowInstitutionNetworkIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title= {t("adminsetting.Organisationnetworks.OrganisationNetworks")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_institution_network"
              >
              <Button variant="secondary" size="sm">
              {t("adminsetting.Organisationnetworks.AddOrganisationNetwork")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <InstitutionNetworkTable />
          </Col>
        </Row>
      </Container>
    );
  }
  
  const ShowAddOrganisationNetworks = canAddOrganisationNetworks(() => <ShowInstitutionNetworkIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.institution_network?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddOrganisationNetworks />
  )
}
export default InstitutionNetworkIndex;