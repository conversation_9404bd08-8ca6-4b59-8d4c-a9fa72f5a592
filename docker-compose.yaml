version: '3.4'

services:
  rki-frontend:
    container_name: rki-frontend
    build: 
      context: ./frontend
    image: ${DOCKER_REGISTRY-}rki-frontend
    restart: always
    ports:
      - 127.0.0.1:3000:3000
    network_mode: host
  
  rki-backend:
    container_name: rki-backend
    build:
      context: ./backend
    image: ${DOCKER_REGISTRY-}rki-backend
    restart: always
    ports:
      - 127.0.0.1:3001:3001
    network_mode: host
