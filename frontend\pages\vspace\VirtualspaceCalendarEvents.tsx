//Import Library
import {useEffect, useState} from "react";
import _ from "lodash";

//Import services/components
import RKICalendar from "../../components/common/RKICalendar";
import apiService from "../../services/apiService";

function VspaceCalendarEvents(props: any) {
  const [events, setEvents] = useState<any[]>([]);
  
  const fetchUpdateType = async () => {
    const response = await apiService.get('/updateType', {"query": {"title": "Calendar Event"}});
    if (response && response.data) {
      fetchCalendarEvents(response.data[0]._id);
    }
  };
  
  const fetchCalendarEvents = async (updateTypeId: any) => {
    const calendarEventsParams = {
      "query": {"type": "vspace", "update_type": updateTypeId},
      "sort": { "created_at": "desc" },
      "limit": 20,
    }
    const response = await apiService.get('/updates', calendarEventsParams);
    if (response && response.data) {
        const key = "parent_vspace";
       if(props.id){
        const _data = _.filter(response.data, { [key]: props.id });
        _.forEach(_data, function(val, i) {
          _data[i].allDay = false;
        });
        setEvents(_data);  
       }
    }
  };
  
  useEffect(() => {
      if(props.id){
        fetchUpdateType();
      }
  },[props.id])
  
  return (
    <RKICalendar eventsList={events} minicalendar showEventCounts />
  )
}

export default VspaceCalendarEvents;