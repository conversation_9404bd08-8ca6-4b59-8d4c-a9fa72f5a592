//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { WorldRegionSeederService } from './world-region-seeder.services';
// SCHEMAS
import { WorldRegionSchema } from 'src/schemas/world_region.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'WorldRegion', schema: WorldRegionSchema }
      ]
    )
  ],
  providers: [WorldRegionSeederService],
  exports: [WorldRegionSeederService],
})
export class WorldRegionSeederModule { }