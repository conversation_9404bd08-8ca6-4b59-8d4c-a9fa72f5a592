//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { VspaceRequestSubscribersInterface } from './interfaces/vspace-request-subscribers.interface';
import { CreateVspaceRequestSubscribersDto } from './dto/create-vspace-request-subscribers.dto';
import { UpdateVspaceRequestSubscribersDto } from './dto/update-vspace-request-subscribers.dto';
const VspaceRequestSubscribers = 'Could not find VspaceRequestSubscribers.'
@Injectable()
export class VspaceRequestSubscribersService {
  constructor(
    @InjectModel('VspaceRequestSubscribers') private vspaceRequestSubscribersModel: Model<VspaceRequestSubscribersInterface>
  ) { }

  async create(createVspaceRequestSubscribersDto: CreateVspaceRequestSubscribersDto): Promise<VspaceRequestSubscribersInterface> {
    const createdVspaceRequestSubscribers = new this.vspaceRequestSubscribersModel(createVspaceRequestSubscribersDto);
    return createdVspaceRequestSubscribers.save();
  }

  async getTotalCount(filter: any) {
    let _filter = {};
    try {
      _filter = filter.query ? filter.query : {};
    } catch (e) {
    }
    return this.vspaceRequestSubscribersModel.count(_filter).exec();
  }

  async findAll(query): Promise<VspaceRequestSubscribersInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.vspaceRequestSubscribersModel.paginate(_filter, options);
  }

  async get(id): Promise<VspaceRequestSubscribersInterface[]> {
    let _result;
    try {
      _result = await this.vspaceRequestSubscribersModel.findById(id).exec();
    } catch (error) {
      throw new NotFoundException(VspaceRequestSubscribers);
    }
    if (!_result) {
      throw new NotFoundException(VspaceRequestSubscribers);
    }
    return _result;
  }

  async update(id: any, updateVspaceRequestSubscribersDto: UpdateVspaceRequestSubscribersDto) {
    const getById: any = await this.vspaceRequestSubscribersModel.findById(id).exec();
    const updatedData = new this.vspaceRequestSubscribersModel(updateVspaceRequestSubscribersDto);
    try {
      Object.keys(updateVspaceRequestSubscribersDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update VspaceRequestSubscribers.');
    }
    return getById;
  }

  async delete(id: string) {
    const result = await this.vspaceRequestSubscribersModel.deleteOne({ _id: id }).exec();
    if (result.n === 0) {
      throw new NotFoundException(VspaceRequestSubscribers);
    }
  }
}
