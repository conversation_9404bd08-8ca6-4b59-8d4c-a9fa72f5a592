//Import Library
import React, { useState, useEffect } from "react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import { Form, Row, Modal } from "react-bootstrap";
import _ from "lodash";

//Import services/components
import apiService from "../../../services/apiService";
import Search from './Search';
import Sort from './Sort';
import { useTranslation } from 'next-i18next';

interface HazardItem {
  _id: string;
  title: {
    [key: string]: string;
  };
  enabled: boolean;
}

interface RKIInfiniteScrollProps {
  api: string;
  params: {
    query: Record<string, any>;
    [key: string]: any;
  };
  callback: (selectedItems: string[]) => void;
  datas: string[];
}

const RKIInfiniteScroll = (props: RKIInfiniteScrollProps) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language === 'fr' ? 'en' : i18n.language;
  const titleSearch = currentLang ? `title.${currentLang}` : "title.en";
  const { api, params, callback, datas } = props;
    /************STATE INIT*******/
    const [loading, setLoading]: any = useState(false);
    const [items, setItems]: any = useState([]);
    const [filterText, setFilterText] = useState('');
    const [hazardList, sethazardList]: any = useState([]);
    const [sortHazard, setSortHazard] = useState(1);
    const [editChecked, setEditChecked] = useState(datas)
  const [currLang,] = useState(titleSearch);
  const [nextPage,] = useState(true);
    /********END******************/

    const RESPONSE_TIME: any = process.env.INFINITE_SCROLL_TIME    //SCROLL RESPONSE TIME

    const search = false;

    const handleSortChange = (e: React.ChangeEvent<any>) => {
        setSortHazard(Number(e.target.value));

      };

      const sortHazardList = () => {
        if (sortHazard === 1) {
          const call = (a: any, b: any) => {
            if (a.title[currentLang] > b.title[currentLang]) {
              return 1;
            } else if (a.title[currentLang] < b.title[currentLang]) {
              return -1;
            } else {
              return 0;
            }
          };
          hazardList.sort(call);
        } else {
          const call = (a: any, b: any) => {
            if (a.title[currentLang] > b.title[currentLang]) {
              return -1;
            } else if (a.title[currentLang] < b.title[currentLang]) {
              return 1;
            } else {
              return 0;
            }
          };
          hazardList.sort(call);
        }
        sethazardList([...hazardList]);
      };

      useEffect(() => {
        sortHazardList();
      }, [sortHazard]);

    const sendQuery = (q: string) =>{
      if(q){
        params.query = {[currLang]: q,"enabled":true};
      }
      else{
        delete params.query[currLang];
        setFilterText('');
      }
        setItems([]);
        handleLoadMore();
      }


      const handleSearchTitle = _.debounce((q: string) => {sendQuery(q)}, Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);
      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFilterText(e.target.value);
        handleSearchTitle(e.target.value);
      }

    const loadItems: any = async (prevArray: any[], _startCursor: number) => {
       if(params.query[currLang]){
        prevArray = [];
        _startCursor = 0;
      }

        const response = await apiService.get(api, params);

        return new Promise((resolve: any) => {
            setTimeout(() => {
                let newArray = prevArray;
                const fetchedData = response && Array.isArray(response.data) ? response.data : null;
                newArray = [...newArray, ...fetchedData];
                resolve(newArray);
            }, RESPONSE_TIME);
        });
    }

    const handleLoadMore = () => {
        setLoading(true);
        loadItems(items, items.length).then((newArray: any) => {
            setLoading(false);
            setItems(newArray)
            sethazardList(newArray);
        });
    }

    const handleHazard = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        const _index = editChecked.indexOf(value);
        if (_index > -1) {
            editChecked.splice(_index, 1)
        } else {
            editChecked.push(value)
        }
        setEditChecked([...editChecked])
        callback(editChecked)
    }

    const infiniteRef = useInfiniteScroll({
        loading,
        hasNextPage: nextPage,
        onLoadMore: handleLoadMore,
    });

    return (
        <>
            <Modal.Header style={{ display: "inline-block" }}>
                <Row>
                    <Search onChange={handleChange} filter={filterText} />
                    <Sort sortHazard={sortHazard} handleSortChange={handleSortChange} />
                </Row>
            </Modal.Header>
            <Modal.Body className="pb-5">
                <div ref={infiniteRef as any} style={{
                    display: "grid", gridTemplateColumns: "1fr 1fr 1fr", gridGap: "1em"
                }}>
                    {hazardList.map((item: HazardItem, i: number) => (<div key={i}>
                        <Form.Check
                            type="checkbox"
                            checked={editChecked.indexOf(item._id) > -1 ? true : false}
                            value={item._id}
                            name={item._id}
                            label={item.title[currentLang]}
                            onChange={handleHazard}
                        />
                    </div>))}
                </div>
            </Modal.Body>
        </>
    )
}


export default RKIInfiniteScroll;



