//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { SyndromeSeederService } from './syndrome-seeder.services';
// SCHEMAS
import { SyndromeSchema } from 'src/schemas/syndrome.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Syndrome', schema: SyndromeSchema }
      ]
    )
  ],
  providers: [SyndromeSeederService],
  exports: [SyndromeSeederService],
})

export class SyndromeSeederModule { }
