//Import Library
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Model } from 'mongoose-paginate-v2';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { FlagInterface } from '../../interfaces/flag.interface';
import { CreateFlagDto } from './dto/create-flag.dto';
import { EmailService } from '../../email.service';
const FlagFound = 'FLAG.NO_FLAG_FOUND';
@Injectable()
export class FlagService {
  constructor(
    @InjectModel('Flag') private flagModel: Model<FlagInterface>,
    private readonly _emailService: EmailService,
  ) {}

  async create(createFlagDto: CreateFlagDto): Promise<FlagInterface> {
    const createdFlag = new this.flagModel(createFlagDto);
    return createdFlag.save();
  }

  async findAll(query): Promise<FlagInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
      populate: query.populate ? query.populate : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};

    //Return data based on roles and permission
    if (query?.user) {
      if (_filter && !_filter.user) {
        _filter.user = query.user;
      }
    }

    return this.flagModel.paginate(_filter, options);
  }

  async get(flagId): Promise<FlagInterface[]> {
    try {
      const response = await this.flagModel.findById(flagId).exec();
      if (response === null) {
        throw new HttpException(FlagFound, HttpStatus.NOT_FOUND);
      }
      return response;
    } catch (error) {
      throw new HttpException(FlagFound, HttpStatus.NOT_FOUND);
    }
  }

  async delete(flagId: string) {
    const result = await this.flagModel.deleteOne({ _id: flagId }).exec();
    if (result.deletedCount === 1) {
      return result;
    } else {
      throw new HttpException(FlagFound, HttpStatus.NOT_FOUND);
    }
  }

  async deleteFlagAssociatedWithEntity(entityId: string) {
    console.log(entityId);
    await this.flagModel
      .deleteOne({ entity_id: entityId })
      .exec();
  }

  async alertSubscribers(title, id, entityType, languageCode) {
    const result = await this.flagModel
      .find({ entity_id: id, entity_type: entityType })
      .populate({
        path: 'user',
        select: '-institution -role -country -region -password -image',
      })
      .exec();
    await result.forEach((item, _i) => {
      this._emailService.alertSubscribers(
        item.user.username,
        item.user.email,
        title,
        id,
        entityType,
        languageCode,
      );
    });
  }
}
