//Import Library
import { <PERSON><PERSON>, Accordion, But<PERSON> } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

interface HelpModalProps {
  show: boolean;
  onHide: () => void;
  [key: string]: any;
}

export default function HelpModal(props: HelpModalProps) {
  const { t } = useTranslation('common');
  return (
    <Modal
      {...props}
      size="lg"
      aria-labelledby="help-modal" className="helps-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title id="help-modal">
          {t('help.tab')}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <ul>
          <Accordion >
            <Accordion.Item eventKey="0">
              <li className="help-content-li-title">
                <Accordion.Header>
                  {t('help.tab1')}
                </Accordion.Header>
              </li>
              <Accordion.Body className="help-content-li-content">
                <p> {t('helpDesc.tab1')}</p>
              </Accordion.Body>
            </Accordion.Item>
            <Accordion.Item eventKey="1">
              <li className="help-content-li-title">
                <Accordion.Header>
                  {t('help.tab2')}
                </Accordion.Header>
              </li>
              <Accordion.Body className="help-content-li-content">
                <p>{t('helpDesc.tab2')}</p>
              </Accordion.Body>
            </Accordion.Item>
            <Accordion.Item eventKey="2">
              <li className="help-content-li-title">
                <Accordion.Header>
                  {t('help.tab3')}
                </Accordion.Header>
              </li>
              <Accordion.Body className="help-content-li-content">
                <p>{t('helpDesc.tab3')}</p>
              </Accordion.Body>
            </Accordion.Item>
            <Accordion.Item eventKey="3">
              <li className="help-content-li-title">
                <Accordion.Header>
                  {t('help.tab4')}
                </Accordion.Header>
              </li>
              <Accordion.Body className="help-content-li-content">
                <p>{t('helpDesc.tab4')}</p>
              </Accordion.Body>
            </Accordion.Item>
          </Accordion>
        </ul>
      </Modal.Body>
    </Modal>
  )
}