//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { HazardTypeController } from './hazard_type.controller';
import { HazardTypeService } from './hazard_type.service';
// SCHEMAS
import { HazardTypeSchema } from '../../schemas/hazard_type.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'HazardType', schema: HazardTypeSchema }
    ])
  ],
  controllers: [HazardTypeController],
  providers: [HazardTypeService],
})

export class HazardTypeModule { }