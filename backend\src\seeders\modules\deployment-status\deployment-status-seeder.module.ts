//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { DeploymentStatusSeederService } from './deployment-status-seeder.services';
// SCHEMAS
import { DeploymentStatusSchema } from 'src/schemas/deployment_status.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'DeploymentStatus', schema: DeploymentStatusSchema }
      ]
    )
  ],
  providers: [DeploymentStatusSeederService],
  exports: [DeploymentStatusSeederService],
})
export class DeploymentStatusSeederModule { }