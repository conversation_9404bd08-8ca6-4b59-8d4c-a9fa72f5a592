"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/hoc/AuthSync.tsx":
/*!*************************************!*\
  !*** ./components/hoc/AuthSync.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/CustomLoader */ \"(pages-dir-browser)/./components/common/CustomLoader.tsx\");\n//Import Library\n\n\n\n\n\n// Public routes used to handle layouts\nconst publicRoutes = [\n    \"/home\",\n    \"/login\",\n    // \"/admin/login\",\n    \"/forgot-password\",\n    \"/reset-password/[passwordToken]\",\n    \"/declarationform/[...routes]\"\n];\n// Gets the display name of a JSX component for dev tools\nconst getDisplayName = (Component1)=>Component1.displayName || Component1.name || \"Component\";\nfunction withAuthSync(WrappedComponent) {\n    class MainComponent extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n        static async getInitialProps(ctx) {\n            const componentProps = WrappedComponent.getInitialProps && await WrappedComponent.getInitialProps(ctx);\n            if (ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies) {\n                const objCookies = ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies ? ctx.ctx.req.cookies : {};\n                componentProps.objCookies = objCookies;\n                return {\n                    ...componentProps\n                };\n            } else {\n                return {\n                    ...componentProps\n                };\n            }\n        }\n        componentDidMount() {\n            const { route } = this.props.router;\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.on(\"routeChangeComplete\", (url)=>{\n                if (url === \"/home\") {\n                    this.setState({\n                        isLoading: false\n                    });\n                }\n            });\n            setTimeout(()=>{\n                if (!this.state.cookie && publicRoutes.indexOf(route) === -1) {\n                    this.props.router.push(\"/home\");\n                    return;\n                }\n                this.setState({\n                    isLoading: false\n                });\n            }, 0);\n        }\n        componentWillUnmount() {\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.off(\"routeChangeComplete\", ()=>null);\n        }\n        componentDidUpdate(prevProps) {\n            if (!prevProps.objCookies && this.props.objCookies) {\n                this.setState({\n                    cookie: this.props && this.props.objCookies && this.props.objCookies[\"connect.sid\"] ? this.props.objCookies[\"connect.sid\"] : null,\n                    isLoading: true\n                });\n            }\n        }\n        render() {\n            const { router } = this.props;\n            const isPublicRoute = publicRoutes.indexOf(router.route) > -1;\n            return this.state.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 97,\n                columnNumber: 37\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n                isLoading: this.state.isLoading,\n                isPublicRoute: isPublicRoute,\n                ...this.props\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this);\n        }\n        constructor(props){\n            super(props);\n            this.state = {\n                isLoading: true,\n                cookie: this.props && this.props.objCookies && this.props.objCookies[\"connect.sid\"] ? this.props.objCookies[\"connect.sid\"] : null\n            };\n        }\n    }\n    MainComponent.displayName = \"withAuthSync(\".concat(getDisplayName(WrappedComponent), \")\");\n    return (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.connect)((state)=>state)(MainComponent);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withAuthSync);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL2NvbXBvbmVudHMvaG9jL0F1dGhTeW5jLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSxnQkFBZ0I7O0FBQ3lCO0FBQ0g7QUFDTDtBQUNpQjtBQVlsRCx1Q0FBdUM7QUFDdkMsTUFBTUssZUFBeUI7SUFDN0I7SUFDQTtJQUNBLGtCQUFrQjtJQUNsQjtJQUNBO0lBQ0E7Q0FDRDtBQUVELHlEQUF5RDtBQUN6RCxNQUFNQyxpQkFBaUIsQ0FBQ0MsYUFDdEJBLFdBQVdDLFdBQVcsSUFBSUQsV0FBV0UsSUFBSSxJQUFJO0FBRS9DLFNBQVNDLGFBQWFDLGdCQUFxQjtJQUN6QyxNQUFNQyxzQkFBc0JYLDRDQUFTQTtRQUVuQyxhQUFhWSxnQkFBZ0JDLEdBQVEsRUFBRTtZQUNyQyxNQUFNQyxpQkFDSkosaUJBQWlCRSxlQUFlLElBQy9CLE1BQU1GLGlCQUFpQkUsZUFBZSxDQUFDQztZQUMxQyxJQUFJQSxJQUFJQSxHQUFHLElBQUlBLElBQUlBLEdBQUcsQ0FBQ0UsR0FBRyxJQUFJRixJQUFJQSxHQUFHLENBQUNFLEdBQUcsQ0FBQ0MsT0FBTyxFQUFFO2dCQUNqRCxNQUFNQyxhQUNKSixJQUFJQSxHQUFHLElBQUlBLElBQUlBLEdBQUcsQ0FBQ0UsR0FBRyxJQUFJRixJQUFJQSxHQUFHLENBQUNFLEdBQUcsQ0FBQ0MsT0FBTyxHQUN6Q0gsSUFBSUEsR0FBRyxDQUFDRSxHQUFHLENBQUNDLE9BQU8sR0FDbkIsQ0FBQztnQkFDUEYsZUFBZUcsVUFBVSxHQUFHQTtnQkFDNUIsT0FBTztvQkFBRSxHQUFHSCxjQUFjO2dCQUFDO1lBQzdCLE9BQU87Z0JBQ0wsT0FBTztvQkFBRSxHQUFHQSxjQUFjO2dCQUFDO1lBQzdCO1FBQ0Y7UUFlQUksb0JBQW9CO1lBQ2xCLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUcsSUFBSSxDQUFDQyxLQUFLLENBQUNDLE1BQU07WUFFbkNuQix5REFBYSxDQUFDcUIsRUFBRSxDQUFDLHVCQUF1QixDQUFDQztnQkFDdkMsSUFBSUEsUUFBUSxTQUFTO29CQUNuQixJQUFJLENBQUNDLFFBQVEsQ0FBQzt3QkFBRUMsV0FBVztvQkFBTTtnQkFDbkM7WUFDRjtZQUNBQyxXQUFXO2dCQUNULElBQUksQ0FBQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsTUFBTSxJQUFJekIsYUFBYTBCLE9BQU8sQ0FBQ1gsV0FBVyxDQUFDLEdBQUc7b0JBQzlELElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxNQUFNLENBQUNVLElBQUksQ0FBQztvQkFDdkI7Z0JBQ0Y7Z0JBQ0EsSUFBSSxDQUFDTixRQUFRLENBQUM7b0JBQUVDLFdBQVc7Z0JBQU07WUFDakMsR0FBRztRQUNMO1FBRUFNLHVCQUF1QjtZQUNyQjlCLHlEQUFhLENBQUMrQixHQUFHLENBQUMsdUJBQXVCLElBQU07UUFDakQ7UUFFQUMsbUJBQW1CQyxTQUFTLEVBQVE7WUFDbEMsSUFBRyxDQUFDQSxVQUFVbEIsVUFBVSxJQUFJLElBQUksQ0FBQ0csS0FBSyxDQUFDSCxVQUFVLEVBQUM7Z0JBQ2hELElBQUksQ0FBQ1EsUUFBUSxDQUFDO29CQUFDSSxRQUFRLElBQUksQ0FBQ1QsS0FBSyxJQUMvQixJQUFJLENBQUNBLEtBQUssQ0FBQ0gsVUFBVSxJQUNyQixJQUFJLENBQUNHLEtBQUssQ0FBQ0gsVUFBVSxDQUFDLGNBQWMsR0FDaEMsSUFBSSxDQUFDRyxLQUFLLENBQUNILFVBQVUsQ0FBQyxjQUFjLEdBQ3BDO29CQUFNUyxXQUFXO2dCQUFJO1lBQzdCO1FBQ0Y7UUFFQVUsU0FBUztZQUNQLE1BQU0sRUFBRWYsTUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDRCxLQUFLO1lBQzdCLE1BQU1pQixnQkFBZ0JqQyxhQUFhMEIsT0FBTyxDQUFDVCxPQUFPRixLQUFLLElBQUksQ0FBQztZQUM1RCxPQUFPLElBQUksQ0FBQ1MsS0FBSyxDQUFDRixTQUFTLGlCQUFHLDhEQUFDdkIsNERBQVlBOzs7O3FDQUN6Qyw4REFBQ087Z0JBQ0NnQixXQUFXLElBQUksQ0FBQ0UsS0FBSyxDQUFDRixTQUFTO2dCQUMvQlcsZUFBZUE7Z0JBQ2QsR0FBRyxJQUFJLENBQUNqQixLQUFLOzs7Ozs7UUFHcEI7UUF0REFrQixZQUFZbEIsS0FBVSxDQUFFO1lBQ3RCLEtBQUssQ0FBQ0E7WUFDTixJQUFJLENBQUNRLEtBQUssR0FBRztnQkFDWEYsV0FBVztnQkFDWEcsUUFDRSxJQUFJLENBQUNULEtBQUssSUFDVixJQUFJLENBQUNBLEtBQUssQ0FBQ0gsVUFBVSxJQUNyQixJQUFJLENBQUNHLEtBQUssQ0FBQ0gsVUFBVSxDQUFDLGNBQWMsR0FDaEMsSUFBSSxDQUFDRyxLQUFLLENBQUNILFVBQVUsQ0FBQyxjQUFjLEdBQ3BDO1lBQ1I7UUFDRjtJQTRDRjtJQXpFTU4sY0FDR0osY0FBYyxnQkFBaUQsT0FBakNGLGVBQWVLLG1CQUFrQjtJQTBFeEUsT0FBT1Qsb0RBQU9BLENBQUMsQ0FBQzJCLFFBQVVBLE9BQU9qQjtBQUNuQztBQUVBLGlFQUFlRixZQUFZQSxFQUFDIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcY29tcG9uZW50c1xcaG9jXFxBdXRoU3luYy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy9JbXBvcnQgTGlicmFyeVxyXG5pbXBvcnQgUmVhY3QsIHsgQ29tcG9uZW50IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IGNvbm5lY3QgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcclxuaW1wb3J0IFJvdXRlciBmcm9tIFwibmV4dC9yb3V0ZXJcIjtcclxuaW1wb3J0IEN1c3RvbUxvYWRlciBmcm9tIFwiLi4vY29tbW9uL0N1c3RvbUxvYWRlclwiO1xyXG5cclxudHlwZSBJSG9jU3RhdGUgPSB7XHJcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xyXG4gIGNvb2tpZToge307XHJcbn07XHJcblxyXG5pbnRlcmZhY2UgQXV0aFN5bmNQcm9wcyB7XHJcbiAgb2JqQ29va2llcz86IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH07XHJcbiAgW2tleTogc3RyaW5nXTogYW55O1xyXG59XHJcblxyXG4vLyBQdWJsaWMgcm91dGVzIHVzZWQgdG8gaGFuZGxlIGxheW91dHNcclxuY29uc3QgcHVibGljUm91dGVzOiBzdHJpbmdbXSA9IFtcclxuICBcIi9ob21lXCIsXHJcbiAgXCIvbG9naW5cIixcclxuICAvLyBcIi9hZG1pbi9sb2dpblwiLFxyXG4gIFwiL2ZvcmdvdC1wYXNzd29yZFwiLFxyXG4gIFwiL3Jlc2V0LXBhc3N3b3JkL1twYXNzd29yZFRva2VuXVwiLFxyXG4gIFwiL2RlY2xhcmF0aW9uZm9ybS9bLi4ucm91dGVzXVwiLFxyXG5dO1xyXG5cclxuLy8gR2V0cyB0aGUgZGlzcGxheSBuYW1lIG9mIGEgSlNYIGNvbXBvbmVudCBmb3IgZGV2IHRvb2xzXHJcbmNvbnN0IGdldERpc3BsYXlOYW1lID0gKENvbXBvbmVudDE6IGFueSkgPT5cclxuICBDb21wb25lbnQxLmRpc3BsYXlOYW1lIHx8IENvbXBvbmVudDEubmFtZSB8fCBcIkNvbXBvbmVudFwiO1xyXG5cclxuZnVuY3Rpb24gd2l0aEF1dGhTeW5jKFdyYXBwZWRDb21wb25lbnQ6IGFueSkge1xyXG4gIGNsYXNzIE1haW5Db21wb25lbnQgZXh0ZW5kcyBDb21wb25lbnQ8YW55LCBJSG9jU3RhdGU+IHtcclxuICAgIHN0YXRpYyBkaXNwbGF5TmFtZSA9IGB3aXRoQXV0aFN5bmMoJHtnZXREaXNwbGF5TmFtZShXcmFwcGVkQ29tcG9uZW50KX0pYDtcclxuICAgIHN0YXRpYyBhc3luYyBnZXRJbml0aWFsUHJvcHMoY3R4OiBhbnkpIHtcclxuICAgICAgY29uc3QgY29tcG9uZW50UHJvcHMgPVxyXG4gICAgICAgIFdyYXBwZWRDb21wb25lbnQuZ2V0SW5pdGlhbFByb3BzICYmXHJcbiAgICAgICAgKGF3YWl0IFdyYXBwZWRDb21wb25lbnQuZ2V0SW5pdGlhbFByb3BzKGN0eCkpO1xyXG4gICAgICBpZiAoY3R4LmN0eCAmJiBjdHguY3R4LnJlcSAmJiBjdHguY3R4LnJlcS5jb29raWVzKSB7XHJcbiAgICAgICAgY29uc3Qgb2JqQ29va2llcyA9XHJcbiAgICAgICAgICBjdHguY3R4ICYmIGN0eC5jdHgucmVxICYmIGN0eC5jdHgucmVxLmNvb2tpZXNcclxuICAgICAgICAgICAgPyBjdHguY3R4LnJlcS5jb29raWVzXHJcbiAgICAgICAgICAgIDoge307XHJcbiAgICAgICAgY29tcG9uZW50UHJvcHMub2JqQ29va2llcyA9IG9iakNvb2tpZXM7XHJcbiAgICAgICAgcmV0dXJuIHsgLi4uY29tcG9uZW50UHJvcHMgfTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICByZXR1cm4geyAuLi5jb21wb25lbnRQcm9wcyB9O1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3RydWN0b3IocHJvcHM6IGFueSkge1xyXG4gICAgICBzdXBlcihwcm9wcyk7XHJcbiAgICAgIHRoaXMuc3RhdGUgPSB7XHJcbiAgICAgICAgaXNMb2FkaW5nOiB0cnVlLFxyXG4gICAgICAgIGNvb2tpZTpcclxuICAgICAgICAgIHRoaXMucHJvcHMgJiZcclxuICAgICAgICAgIHRoaXMucHJvcHMub2JqQ29va2llcyAmJlxyXG4gICAgICAgICAgdGhpcy5wcm9wcy5vYmpDb29raWVzW1wiY29ubmVjdC5zaWRcIl1cclxuICAgICAgICAgICAgPyB0aGlzLnByb3BzLm9iakNvb2tpZXNbXCJjb25uZWN0LnNpZFwiXVxyXG4gICAgICAgICAgICA6IG51bGwsXHJcbiAgICAgIH07XHJcbiAgICB9XHJcblxyXG4gICAgY29tcG9uZW50RGlkTW91bnQoKSB7XHJcbiAgICAgIGNvbnN0IHsgcm91dGUgfSA9IHRoaXMucHJvcHMucm91dGVyO1xyXG4gICAgICBcclxuICAgICAgUm91dGVyLmV2ZW50cy5vbihcInJvdXRlQ2hhbmdlQ29tcGxldGVcIiwgKHVybCkgPT4ge1xyXG4gICAgICAgIGlmICh1cmwgPT09IFwiL2hvbWVcIikge1xyXG4gICAgICAgICAgdGhpcy5zZXRTdGF0ZSh7IGlzTG9hZGluZzogZmFsc2UgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKCF0aGlzLnN0YXRlLmNvb2tpZSAmJiBwdWJsaWNSb3V0ZXMuaW5kZXhPZihyb3V0ZSkgPT09IC0xKSB7XHJcbiAgICAgICAgdGhpcy5wcm9wcy5yb3V0ZXIucHVzaChcIi9ob21lXCIpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG4gICAgICB0aGlzLnNldFN0YXRlKHsgaXNMb2FkaW5nOiBmYWxzZSB9KTtcclxuICAgICAgfSwgMCkgXHJcbiAgICB9XHJcblxyXG4gICAgY29tcG9uZW50V2lsbFVubW91bnQoKSB7XHJcbiAgICAgIFJvdXRlci5ldmVudHMub2ZmKFwicm91dGVDaGFuZ2VDb21wbGV0ZVwiLCAoKSA9PiBudWxsKTtcclxuICAgIH1cclxuXHJcbiAgICBjb21wb25lbnREaWRVcGRhdGUocHJldlByb3BzKTogdm9pZCB7XHJcbiAgICAgIGlmKCFwcmV2UHJvcHMub2JqQ29va2llcyAmJiB0aGlzLnByb3BzLm9iakNvb2tpZXMpe1xyXG4gICAgICAgIHRoaXMuc2V0U3RhdGUoe2Nvb2tpZTogdGhpcy5wcm9wcyAmJlxyXG4gICAgICAgICAgdGhpcy5wcm9wcy5vYmpDb29raWVzICYmXHJcbiAgICAgICAgICB0aGlzLnByb3BzLm9iakNvb2tpZXNbXCJjb25uZWN0LnNpZFwiXVxyXG4gICAgICAgICAgICA/IHRoaXMucHJvcHMub2JqQ29va2llc1tcImNvbm5lY3Quc2lkXCJdXHJcbiAgICAgICAgICAgIDogbnVsbCwgaXNMb2FkaW5nOiB0cnVlfSlcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHJlbmRlcigpIHtcclxuICAgICAgY29uc3QgeyByb3V0ZXIgfSA9IHRoaXMucHJvcHM7XHJcbiAgICAgIGNvbnN0IGlzUHVibGljUm91dGUgPSBwdWJsaWNSb3V0ZXMuaW5kZXhPZihyb3V0ZXIucm91dGUpID4gLTE7XHJcbiAgICAgIHJldHVybiB0aGlzLnN0YXRlLmlzTG9hZGluZyA/IDxDdXN0b21Mb2FkZXIvPiA6IChcclxuICAgICAgICA8V3JhcHBlZENvbXBvbmVudFxyXG4gICAgICAgICAgaXNMb2FkaW5nPXt0aGlzLnN0YXRlLmlzTG9hZGluZ31cclxuICAgICAgICAgIGlzUHVibGljUm91dGU9e2lzUHVibGljUm91dGV9XHJcbiAgICAgICAgICB7Li4udGhpcy5wcm9wc31cclxuICAgICAgICAvPlxyXG4gICAgICApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIGNvbm5lY3QoKHN0YXRlKSA9PiBzdGF0ZSkoTWFpbkNvbXBvbmVudCk7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IHdpdGhBdXRoU3luYztcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ29tcG9uZW50IiwiY29ubmVjdCIsIlJvdXRlciIsIkN1c3RvbUxvYWRlciIsInB1YmxpY1JvdXRlcyIsImdldERpc3BsYXlOYW1lIiwiQ29tcG9uZW50MSIsImRpc3BsYXlOYW1lIiwibmFtZSIsIndpdGhBdXRoU3luYyIsIldyYXBwZWRDb21wb25lbnQiLCJNYWluQ29tcG9uZW50IiwiZ2V0SW5pdGlhbFByb3BzIiwiY3R4IiwiY29tcG9uZW50UHJvcHMiLCJyZXEiLCJjb29raWVzIiwib2JqQ29va2llcyIsImNvbXBvbmVudERpZE1vdW50Iiwicm91dGUiLCJwcm9wcyIsInJvdXRlciIsImV2ZW50cyIsIm9uIiwidXJsIiwic2V0U3RhdGUiLCJpc0xvYWRpbmciLCJzZXRUaW1lb3V0Iiwic3RhdGUiLCJjb29raWUiLCJpbmRleE9mIiwicHVzaCIsImNvbXBvbmVudFdpbGxVbm1vdW50Iiwib2ZmIiwiY29tcG9uZW50RGlkVXBkYXRlIiwicHJldlByb3BzIiwicmVuZGVyIiwiaXNQdWJsaWNSb3V0ZSIsImNvbnN0cnVjdG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/hoc/AuthSync.tsx\n"));

/***/ })

});