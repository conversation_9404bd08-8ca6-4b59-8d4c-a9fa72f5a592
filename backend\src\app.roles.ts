//Import Library
import { RolesBuilder } from "nest-access-control";

export enum AppRoles {
  AUTHENTICATED = 'AUTHENTICATED',
  SUPER_ADMIN = 'SUPER_ADMIN',
  PLATFORM_ADMIN = 'PLATFORM_ADMIN',
  NGOS = 'NGOS',
  EMT = 'EMT',
  INIG_STAKEHOLDER = 'INIG_STAKEHOLDER',
  HEALTH_PROFESSIONAL = 'HEALTH_PROFESSIONAL',
  GENERAL_USER = 'GENERAL_USER',
  EMT_NATIONAL_FOCALPOINT = 'EMT_NATIONAL_FOCALPOINT'
}

export const roles: RolesBuilder = new RolesBuilder();

const resourceList = [
  'dashboard',
  'area_of_work',
  'category',
  'classification',
  'country',
  'country_region',
  'deployment_status',
  'event',
  'event_status',
  'expertise',
  'file_category',
  'files',
  'flag',
  'hazard',
  'hazard_type',
  'image',
  'institution',
  'institution_network',
  'institution_type',
  'institution_focal_point',
  'landing_page',
  'language',
  'operation',
  'operation_status',
  'page-category',
  'project',
  'project_status',
  'region',
  'risk_level',
  'roles',
  'syndrome',
  'taxonomy_icon',
  'update',
  'update_type',
  'users',
  'vspace',
  'vspace-request-subscribers',
  'vspace_topic',
  'worl_region',
  'admin'
];

export const commonResources = ['dashboard', 'country', 'institution', 'institution_focal_point', 'operation', 'project', 'event', 'hazard', 'vspace', 'update'];

roles
  .grant(AppRoles.AUTHENTICATED)
  .readAny(commonResources)
  .readOwn('flag')
  .createAny('flag')
  .updateOwn('flag')
  .deleteOwn('flag')

  .grant(AppRoles.SUPER_ADMIN)
  .readAny(resourceList)
  .createAny(resourceList)
  .updateAny(resourceList)
  .deleteAny(resourceList)

  .grant(AppRoles.PLATFORM_ADMIN)
  .readAny(resourceList)
  .createAny(resourceList.filter(x => x != 'country' && x != 'landing_page' && x != 'hazard' && x != 'hazard_type'))
  .updateAny(resourceList.filter(x => x != 'country' && x != 'landing_page' && x != 'hazard' && x != 'hazard_type'))
  .deleteAny(resourceList.filter(x => x != 'country' && x != 'landing_page' && x != 'hazard' && x != 'hazard_type'))

  .grant(AppRoles.NGOS)
  .readAny(commonResources)
  .readOwn(['flag'])
  .createAny(['project', 'flag'])
  .updateOwn(['project', 'flag'])
  .deleteOwn(['project', 'flag'])

  .grant(AppRoles.EMT)
  .readAny(commonResources)
  .readOwn(['flag'])
  .createAny(['operation', 'project', 'flag', 'vspace', 'update'])
  .updateAny(['operation', 'project', 'institution_focal_point'])
  .updateOwn(['operation', 'project', 'flag', 'vspace', 'update', 'institution_focal_point'])
  .deleteOwn(['operation', 'project', 'flag', 'vspace', 'update'])

  .grant(AppRoles.INIG_STAKEHOLDER)
  .readAny(commonResources)
  .readOwn(['flag'])
  .createAny(['operation', 'project', 'flag', 'vspace', 'update'])
  .updateOwn(['operation', 'project', 'flag', 'vspace', 'update'])
  .deleteOwn(['operation', 'project', 'flag', 'vspace', 'update'])

  .grant(AppRoles.HEALTH_PROFESSIONAL)
  .readAny(commonResources)
  .readOwn(['flag'])
  .createAny(['flag', 'vspace'])
  .updateOwn(['flag', 'vspace'])
  .deleteOwn(['flag', 'vspace'])

  .grant(AppRoles.GENERAL_USER)
  .readAny([...commonResources,'admin'])
  .readOwn(['flag'])
  .createAny(['event', 'institution', 'operation', 'project', 'flag', 'institution_focal_point', 'vspace', 'update'])
  .updateOwn(['event', 'institution', 'operation', 'project', 'flag', 'institution_focal_point', 'vspace', 'update'])
  .deleteOwn(['event', 'institution', 'operation', 'project', 'flag', 'institution_focal_point', 'vspace', 'update'])

  .grant(AppRoles.EMT_NATIONAL_FOCALPOINT)
  .readAny(commonResources)
  .readOwn(['flag'])
  .createAny(['operation', 'project', 'flag', 'update'])
  .updateAny(['operation', 'project','update'])
  .updateOwn(['operation', 'project', 'flag', 'update'])
  .deleteOwn(['operation', 'project', 'flag', 'update'])