//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from "@nestjs/mongoose";

//Import services/components
import { FlagController } from './flag.controller';
import { FlagService } from './flag.service';
import { EmailService } from "../../email.service";
import { UsersService } from "../../users/users.service";
// SCHEMAS
import { FlagSchema } from "../../schemas/flag.schemas";
import { UsersSchema } from "../../schemas/users.schemas";
import { VspaceSchema } from "../../schemas/vspace.schemas";

@Module({
  imports: [
    MongooseModule.forFeature([
      {name: 'Flag', schema: FlagSchema },
      {name: 'Users', schema: UsersSchema},
      {name: 'Vspace', schema: VspaceSchema}

    ])
  ],
  controllers: [FlagController],
  providers: [FlagService, EmailService, UsersService]
})
export class FlagModule {}
