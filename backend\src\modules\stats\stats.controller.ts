//Import Library
import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';

//Import services/components
import { StatsService } from './stats.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('stats')
@UseGuards(SessionGuard)
export class StatsController {
  constructor(private readonly _statsService: StatsService) {}

  @Get('country/:id')
  getStatsForCountry(@Param('id') countryId: string) {
    return this._statsService.getStatsForCountry(countryId);
  }

  @Get('institution/:id')
  getStatsForInstitution(@Param('id') institutionId: string) {
    return this._statsService.getStatsForInstitution(institutionId);
  }

  @Get('getOperationWithVspace/:id')
  getOperationVspace(@Param('id') operationId: string) {
    return this._statsService.getOperationLinkedVspace(operationId);
  }

  @Get('getProjectWithVspace/:id')
  getProjectVspace(@Param('id') ProjectId: string) {
    return this._statsService.getProjectLinkedVspace(ProjectId);
  }

  @Get('institutions')
  getStatsForInstitutions(@Query() query: any) {
    return this._statsService.getStatsForInstitutions(query);
  }
}
