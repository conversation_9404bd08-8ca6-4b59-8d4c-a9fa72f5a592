//Import Library
import React, { useEffect, useState } from "react";
import Link from 'next/link';
import _ from "lodash";
import { Container, Row, Col, Button } from 'react-bootstrap';
import toast from 'react-hot-toast';


//Import services/components
import RKICarousel from "../../components/common/RKICarousel";
import apiService from "../../services/apiService";
import RK<PERSON><PERSON> from "../../components/common/RKICard";
import { useTranslation } from 'next-i18next';
import PageHeading from "../../components/common/PageHeading";
import { canAddVspace } from "./permission";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
const truncateLength = 180;

const VSpace = (): React.ReactElement => {
  const { t } = useTranslation('common');
  const [subscribedContent, setSubscribedContent] = useState([]);
  const [filterText, setFilterText] = useState('');
  const [publicVirtualspaces, setPublicVirtualspace] = useState([]);
  const [restrictedVirtualSpaces, setRestrictedVirtualSpace] = useState([]);
  const [vspaceRequest, setVspaceRequest] = useState([]);

  const vspaceParams = {
    sort: { created_at: "desc" },
    lean: true,
    query: {},
    populate: [
      { path: "topic", select: "title" },
      { path: "subscribers", select: "_id" }
    ],
    select: "-start_date -end_date -members -images -user -created_at -updated_at -file_category -vspace_email_invite"
  };

  const sendQuery = (q) => {
    if (q) {vspaceParams.query = { "title": q }}
    fetchVspace()
  }

  const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value);
    handleSearchTitle(e.target.value)
  }

  const fetchVspace = async () => {
    const responseUser = await apiService.post("/users/getLoggedUser", {});
    if(responseUser && responseUser['_id']) {
      try {
        const { data } = await apiService.get('/vspace', vspaceParams);
        await response_func(data, responseUser, setSubscribedContent, setPublicVirtualspace, setVspaceRequest, setRestrictedVirtualSpace);
      } catch (err) {
        setSubscribedContent([]);
      }
    }

  };

  useEffect(() => {
    fetchVspace();
  }, []);

  const getTrimmedString = (html) => {
    const div = document.createElement("div");
    div.innerHTML = html;
    const string = div.textContent || div.innerText || "";
    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);
  }

  const onRenderSubscribedItems = (item: any, idx: number) => {
    const trimmedString = getTrimmedString(item.description);

    return (
      <Link
        key={item._id}
        href={`vspace/[...routes]`}
        as={`/vspace/show/${item._id}`}
        >
        <div className='rki-carousel-card'>
          <RKICard header={item.title} body={trimmedString} />
        </div>
      </Link>
    );
  }
  const updateSubscribe = async (item: any) => {
    if (item && item._id) {
      await apiService.post('/vspace/updateSubscriber', {
        "isSubscribe": true,
        "_id": item._id
      });
      const notSubscribed = publicVirtualspaces.filter((d) => d._id !== item._id);
      setPublicVirtualspace(notSubscribed);
      setSubscribedContent(oldArr => [ item ,...oldArr]);
      toast.success(t('vspace.subscribedForToast'));

    }
  }

  const onRenderPublicItems = (item: any, idx: number) => {
    const trimmedString = getTrimmedString(item.description);

    return (
      <div className='rki-carousel-card' key={item._id}>
        <RKICard header={item.title} body={trimmedString} />
        <div className={`hover-btn text-center`} onClick={() => updateSubscribe(item)}>{t("vspace.Subscribe")}</div>
      </div>
    );
  }

  const onRenderRestrictedItems = (item: any, idx: number) => {
    const trimmedString = getTrimmedString(item.description);

    return (
      <div className='rki-carousel-card' key={item._id}>
        <RKICard header={item.title} body={trimmedString} />
        {
          vspaceRequest.indexOf(item._id) === -1 ?
            <div className={`hover-btn text-center`} onClick={() => requestConfirm(item)}>{t("vspace.Request")}</div>
            :
            <div className={`hover-btn text-center`}>{t("vspace.Requested")}</div>
        }
      </div>
    );
  };

  const requestConfirm = async (item: any) => {
    if (item && item._id) {
      const resp = await apiService.post('/vspace/subscribeRequest', {
        "_id": item._id
      });
      if(resp){
      setVspaceRequest(prev => [...prev, item._id]);
      setRestrictedVirtualSpace(prev => [...prev]);
      toast.success(t("vspace.vspacehasrequested"));
      }
    }
  }

  const AddNewVspaceComponent = () => {
    return (
      <Link href="/vspace/[...routes]" as="/vspace/create" >
        <Button>{t('createvirtualSpaces')}</Button>
      </Link>
    );
  };

  const CanAddVspace = canAddVspace(() => <AddNewVspaceComponent />)

  return (
    <Container className='vspace'>
      <Row className='page-header'>
        <Col lg={12}>
          <PageHeading title={t('vspace.virtualSpaces')} />
        </Col>
      </Row>
      <Row>
        <Col className="ps-2" md={6}>
          <div className="searchbar">
            <input className="searchInput form-control" onChange={handleChange} value={filterText} type="text" name="" placeholder= {t("vspace.Search")} />
            <a href="#" className="search_icon"><i className="fas fa-search"></i></a>
          </div>
        </Col>
        <Col md={3}>
        </Col>
        <Col md={3} className="createVSpace">
          <CanAddVspace />
        </Col>
      </Row>
      <Row className='subscriptionBlock'>
        <Col className='header-block' lg={12}>
          <h6><span>{t('vspace.subscribedVspace')}</span></h6>
        </Col>
        <Col lg={12}>
          <RKICarousel selector="title" items={subscribedContent} renderItems={onRenderSubscribedItems} />
        </Col>
      </Row>
      <Row className='subscriptionBlock publicVSpace popButton'>
        <Col className='header-block' lg={12}>
          <h6><span>{t('vspace.publicVspaceUnSubscribed')}</span></h6>
        </Col>
        <Col lg={12}>
          <RKICarousel items={publicVirtualspaces} renderItems={onRenderPublicItems} />
        </Col>
      </Row>
      <Row className='subscriptionBlock popButton'>
        <Col className='header-block' lg={12}>
          <h6><span>{t('vspace.restrictedVSpaces')}</span></h6>
        </Col>
        <Col lg={12}>
          <RKICarousel items={restrictedVirtualSpaces} renderItems={onRenderRestrictedItems} />
        </Col>
      </Row>
    </Container>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default VSpace;
async function response_func(data: any, responseUser: any, setSubscribedContent: React.Dispatch<React.SetStateAction<any[]>>, setPublicVirtualspace: React.Dispatch<React.SetStateAction<any[]>>, setVspaceRequest: React.Dispatch<React.SetStateAction<any[]>>, setRestrictedVirtualSpace: React.Dispatch<React.SetStateAction<any[]>>) {
  if (Array.isArray(data) && data.length > 0) {
    const subscribedVspaces = _.filter(data, (i) => {
      let isSubscribed = false;
      if (Array.isArray(i.subscribers) && i.subscribers.length > 0) {
        isSubscribed = _.filter(i.subscribers, (d) => d._id === responseUser['_id']).length > 0;
      }
      return isSubscribed;
    });
    const vSpaceIds = subscribedVspaces.map((d) => d._id);
    const publicVspaces = _.filter(data, (i) => (i.visibility === true && vSpaceIds.indexOf(i._id) === -1));
    const privateVspace = _.filter(data, (i) => (i.visibility === false && vSpaceIds.indexOf(i._id) === -1));
    setSubscribedContent(subscribedVspaces);
    setPublicVirtualspace(publicVspaces);

    const requestData = await apiService.get('/vspace-request-subscribers/getRequestedByMe', { query: { requested_by: responseUser['_id'] } });
    if (requestData && requestData.data) {
      const updateData = requestData.data.map((d) => d.vspace._id);
      setVspaceRequest(updateData);
    }
    setRestrictedVirtualSpace(privateVspace);
  }
}

