//Import Library
import React, { useEffect, useState, useRef } from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col } from "react-bootstrap";
import Router, { useRouter } from "next/router";
import moment from "moment";
import _ from "lodash";
import { TextInput } from "../../components/common/FormValidation";
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";

//Import services/components
import LinkForm from "./LinkForm";
import ConversationForm from "./ConversationForm";
import DocumentForm from "./DocumentForm";
import ContactForm from "./ContactForm";
import CalendarEventForm from "./CalendarEventForm";
import ImageForm from "./ImageForm";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { EditorComponent } from "../../shared/quill-editor/quill-editor.component"


const initialFormValue = {
    title: "",
    description: null,
    startDate: null,
    endDate: null,
    classification: null,
    parentType: null,
    parentId: null,
    showAsAnnouncement: false,
    images: [],
    document: [],
    images_src: [],
    doc_src: [],
};

const resetState = {
    title: "",
    description: null,
    startDate: null,
    endDate: null,
    classification: null,
    showAsAnnouncement: false,
    images: [],
    document: [],
    images_src: [],
    doc_src: [],
};

const contactState = { telephoneNo: "", mobileNo: "" };

const linkState = [{ title: "", link: "" }];

const Classifications = [
    {
        name: "Confidential",
        value: 1,
    },
    {
        name: "Restricted",
        value: 2,
    },
    {
        name: "Public",
        value: 3,
    },
];
const validationIntialState = {
    startDate: false,
    endDate: false,
};

interface UpdateFormProps {
  router: any;
}

const UpdateForm = (props: UpdateFormProps): React.ReactElement => {
    const formRef = useRef(null);

    const { t } = useTranslation('common');
    const { query } = useRouter();
    const [formState, setFormState] = useState(initialFormValue);
    const [conversation, setConversation] = useState({ title: "" });
    const [link, setLink] = useState([{ title: "", link: "" }]);
    const [contact_details, setContact_details] = useState(contactState);
    const [validation, setValidation] = useState(validationIntialState);
    const [, setValidated] = useState(false);
    const [updateType, setUpdateType] = useState("");
    const [documentDropZone, setDocumentDropCollection] = useState([]);
    const [docSrcCollection, setDocSrcCollection] = useState([]);
    const [dropZoneCollection, setDropZoneCollection] = useState([]);
    const [srcCollection, setSrcCollection] = useState([]);
    const isEditForm =
        props.router &&
        props.router.query &&
        props.router.query.routes &&
        props.router.query.routes.length > 1 &&
        props.router.query.routes[1];

    const onHandleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormState((prevState) => ({
            ...prevState,
            [name]: value,
        }));
    };

    const conversationHandleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setConversation((prevState) => ({
            ...prevState,
            [name]: value,
        }));
    };

    const ContactHandleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setContact_details((prevState) => ({
            ...prevState,
            [name]: value,
        }));
    };

    const handleChangeforTimeline = (e: React.ChangeEvent<HTMLInputElement>, i: number) => {
        const { name, value } = e.target;
        const _tempCosts = [...link];
        _tempCosts[i][name] = value;
        setLink(_tempCosts);
    };

    const removeForm = (_e, i: any) => {
        link.splice(i, 1);
        setLink([...link]);
        if (link.length === 0) {
            addform();
        }
    };

    const addform = () => {
        const a = { title: "", link: "" };
        setLink((oldArray) => [...oldArray, a]);
    };

    const onCheckAnnouncement = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = e.target;

        setFormState((prevState) => ({
            ...prevState,
            showAsAnnouncement: checked,
        }));
    };

    const onChangeDescription = (value: string) => {
        setFormState((prevState) => ({
            ...prevState,
            description: value,
        }));
    };

    const onChangeDate = (date: Date, key: string) => {
        setFormState((prevState) => ({
            ...prevState,
            [key]: date,
        }));

        setValidation((prevState) => ({
            ...prevState,
            startDate: key === "startDate" ? true : false,
            endDate: key === "endDate" ? true : false,
        }));
    };

    const checkCustomValidation = () => {
        if (!formState.startDate) {
            setValidation((prev) => ({ ...prev, startDate: true }));
            return false;
        }
        return true;
    };

    const getItem = () => {
        if (query && query.routes[0] === "add") {
            Router.push(`/${query.parent_type}/show/${query.parent_id}`);
        } else {
            if (props && props.router && props.router.query) {
                Router.push(
                    `/${formState.parentType}/[...routes]`,
                    `/${formState.parentType}/show/${formState.parentId}`
                );
            }
        }
    };

    const onResetHandler = () => {
        setFormState((prevState) => ({ ...prevState, ...resetState }));
        setDocumentDropCollection([]);
        setDocSrcCollection([]);
        setDropZoneCollection([]);
        setSrcCollection([]);
        setContact_details(contactState);
        setLink(linkState);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleErrorSubmit = (errors: any) => {
        window.scrollTo(0, 0);
    };

    const getresponse = () => {
        const { startDate, endDate } = formState;
        if (updateType === "Calendar Event") {
            setValidation((prevState) => ({
                ...prevState,
                startDate: true,
            }));
            if (!startDate || !endDate) {
                event.preventDefault();
                return true;
            }
        }

        if (updateType === "Link") {
            let isTitle = _.findIndex(link, { title: "" });
            let isLink = _.findIndex(link, { link: "" });
            if (isTitle !== -1 || isLink !== -1) {
                event.preventDefault();
                return true;
            }
        }
    };

    const getprops = async (data: any) => {
        data.type = formState.parentType;
        const respData = await apiService.patch(`/updates/${props.router.query.routes[1]}`, data);
        if (respData && respData._id) {
            Router.push(`/${formState.parentType}/[...routes]`, `/${formState.parentType}/show/${formState.parentId}`);
        }
    };

    const getelseprops = async (data: any) => {
        switch (props.router.query.parent_type) {
            case "operation":
                data["parent_operation"] = props.router.query.parent_id;
                break;
            case "event":
                data["parent_event"] = props.router.query.parent_id;
                break;
            case "project":
                data["parent_project"] = props.router.query.parent_id;
                break;
            case "vspace":
                data["parent_vspace"] = props.router.query.parent_id;
                break;
            case "country":
                data["parent_country"] = props.router.query.parent_id;
                break;
            case "hazard":
                data["parent_hazard"] = props.router.query.parent_id;
                break;
            case "institution":
                data["parent_institution"] = props.router.query.parent_id;
                break;
            default:
                break;
        }
        const respData = await apiService.post("/updates", data);

        if (respData && respData._id) {
            Router.push(
                `/${props.router.query.parent_type}/[...routes]`,
                `/${props.router.query.parent_type}/show/${props.router.query.parent_id}`
            );
        }
    };

    const onSubmitForm = async (event: any, values?: any) => {
        const { title, description, startDate, endDate, showAsAnnouncement, document, doc_src, images, images_src } =
            values || formState;
        if (getresponse()) {
            return;
        }
        if (event && event.preventDefault) {
            event.preventDefault();
        }

        // Skip form validation for ValidationFormWrapper since Formik handles it
        if (event && event.currentTarget && checkCustomValidation() === false) {
            const form = event.currentTarget as HTMLFormElement;
            if (form.checkValidity() === false) {
                event.stopPropagation();
                return;
            }
        }
        const isValidLink = link.find((x) => x.title == "" || x.link == "");
        if (updateType == "Link" && isValidLink) {
            return;
        }
        setValidated(true);
        const data = {
            title: updateType === "Conversation" ? conversation.title.trim() : title.trim(),
            type: props.router.query.parent_type,
            description: description,
            show_as_announcement: showAsAnnouncement,
            start_date: startDate,
            end_date: endDate,
            update_type: props.router.query.update_type,
            link: link,
            document: document ? document : [],
            contact_details: contact_details ? contact_details : {},
            images: images ? images : [],
            images_src: images_src ? images_src : [],
            doc_src: doc_src ? doc_src : [],
        };

        if (props.router.query && props.router.query.routes.length > 1 && props.router.query.routes[1]) {
            getprops(data);
        } else {
            getelseprops(data);
        }
    };

    const fetchUpdateType = async () => {
        const data = await apiService.get(`/updateType/${props.router.query.update_type}`);
        if (data) {
            setUpdateType(data.title);
        }
    };

    const getSwitch = (data: any, respData: any) => {
        switch (respData.type) {
            case "operation":
                data["parentId"] = respData.parent_operation;
                break;
            case "event":
                data["parentId"] = respData.parent_event;
                break;
            case "project":
                data["parentId"] = respData.parent_project;
                break;
            case "vspace":
                data["parentId"] = respData.parent_vspace;
                break;
            case "country":
                data["parentId"] = respData.parent_country;
                break;
            case "hazard":
                data["parentId"] = respData.parent_hazard;
                break;
            case "institution":
                data["parentId"] = respData.parent_institution;
                break;
            default:
                break;
        }
    };

    const getdocument = (respData: any, data: any) => {
        setDocumentDropCollection(respData.document ? respData.document : []);
        setDocSrcCollection(respData.doc_src ? respData.doc_src : []);
        setDropZoneCollection(respData.images ? respData.images : []);
        setSrcCollection(respData.images_src ? respData.images_src : []);
        setLink(respData.link.length ? respData.link : linkState);
        setContact_details(respData.contact_details ? respData.contact_details : contact_details);
        getSwitch(data, respData);
    };
    useEffect(() => {
        const fetchData = async () => {
            const respData = await apiService.get(`/updates/${props.router.query.routes[1]}`);
            const data: any = {
                title: respData.title,
                description: respData.description,
                startDate: respData.start_date ? moment(respData.start_date).toDate() : null,
                endDate: respData.end_date ? moment(respData.end_date).toDate() : null,
                classification: null,
                parentType: respData.type,
                showAsAnnouncement: respData.show_as_announcement,
                document: respData.document ? respData.document : [],
                doc_src: respData.doc_src ? respData.doc_src : [],
                images: respData.images ? respData.images : [],
                images_src: respData.images_src ? respData.images_src : [],
            };
            getdocument(respData, data);
            setFormState(data);
        };
        fetchUpdateType();
        if (isEditForm) {
            fetchData();
        }
    }, []);

    const uploadHandler = (id: any[]) => {
        const ids = id.map((item: any) => item.serverID);
        setFormState((prevState) => ({ ...prevState, document: ids }));
    };

    /**Seperately Hanlde images**/
    const uploadImgHandler = (id: any[]) => {
        const ids = id.map((item: any) => item.serverID);
        setFormState((prevState) => ({ ...prevState, images: ids }));
    };

    const getSource = (imgSrcArr: any[]) => {
        setFormState((prevState) => ({ ...prevState, images_src: imgSrcArr }));
    };

    const getDocSource = (docSrcArr: any[]) => {
        setFormState((prevState) => ({ ...prevState, doc_src: docSrcArr }));
    };

    const getComponent = () => {
        switch (updateType) {
            case "Conversation":
                return <ConversationForm onHandleChange={conversationHandleChange} {...conversation} />;

            case "Link":
                return (
                    <LinkForm
                        link={link}
                        handleChangeforTimeline={handleChangeforTimeline}
                        removeForm={removeForm}
                        addform={addform}
                    />
                );

            case "Document":
                return (
                    <DocumentForm
                        srcText={docSrcCollection}
                        getSourceCollection={(docSrcArr) => getDocSource(docSrcArr)}
                        data={documentDropZone}
                        getId={(id) => uploadHandler(id)}
                    />
                );

            case "Contact":
                return <ContactForm onHandleChange={ContactHandleChange} {...contact_details} />;

            case "Calendar Event":
                return (
                    <CalendarEventForm
                        imgSrc={docSrcCollection}
                        getSourceCollection={(docSrcArr) => getDocSource(docSrcArr)}
                        validation={validation}
                        onChangeDate={onChangeDate}
                        {...formState}
                        data={documentDropZone}
                        getId={(id) => uploadHandler(id)}
                    />
                );

            case "Image":
                return (
                    <ImageForm
                        data={dropZoneCollection}
                        imgSrc={srcCollection}
                        getId={(id) => uploadImgHandler(id)}
                        getSourceCollection={(imgSrcArr) => getSource(imgSrcArr)}
                    />
                );

            case "General / Notice":
                return (
                    <>
                        <DocumentForm
                            srcText={docSrcCollection}
                            getSourceCollection={(docSrcArr) => getDocSource(docSrcArr)}
                            data={documentDropZone}
                            getId={(id) => uploadHandler(id)}
                        />
                        <ImageForm
                            imgSrc={srcCollection}
                            getSourceCollection={(imgSrcArr) => getSource(imgSrcArr)}
                            data={dropZoneCollection}
                            getId={(id) => uploadImgHandler(id)}
                        />
                    </>
                );

            default:
                return null;
        }
    };

    return (
        <Container className="formCard" fluid>
            <Card>
                <ValidationFormWrapper onSubmit={onSubmitForm} ref={formRef} initialValues={formState} enableReinitialize={true} onErrorSubmit={handleErrorSubmit}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>
                                    {`${isEditForm ? t("update.Edit") : t("update.Create")}`} {t("update.Update")}{" "}
                                </Card.Title>
                            </Col>
                        </Row>
                        <hr />
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("update.Title")}</Form.Label>
                                    <TextInput
                                        required
                                        validator={(value) => String(value || '').trim() !== ""}
                                        errorMessage={{
                                            validator: t("update.provide"),
                                        }}
                                        type="text"
                                        name="title"
                                        value={formState.title}
                                        onChange={onHandleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("update.Description")}</Form.Label>
                                    <EditorComponent initContent={formState.description} onChange={(evt) => onChangeDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        {getComponent()}
                        <Row className="mt-3">
                            <Col>
                                <Form.Group controlId="showAsAnnouncement">
                                    <Form.Check
                                        className="check"
                                        id="showAsAnnouncement"
                                        name="showAsAnnouncement"
                                        type="checkbox"
                                        label={t("update.Showasanannouncement")}
                                        checked={formState.showAsAnnouncement}
                                        onChange={onCheckAnnouncement}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="my-4">
                            <Col>
                                <Button
                                    className="me-2"
                                    type="submit"
                                    variant="primary"
                                    onClick={() => {
                                        setValidation((prevState) => ({
                                            ...prevState,
                                            startDate: true,
                                        }));
                                    }}
                                >
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" onClick={onResetHandler} variant="info">
                                    {t("reset")}
                                </Button>
                                <Button onClick={getItem} variant="secondary">
                                    {t("Cancel")}
                                </Button>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
        </Container>
    );
};

export async function getStaticProps({ locale }: { locale: string }) {
    return {
        props: {
            ...(await serverSideTranslations(locale, ['common'])),
        },
    }
}

export default UpdateForm;
