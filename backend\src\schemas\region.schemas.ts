//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const RegionSchema = new mongoose.Schema({
  title: { type: String, required: true },
  country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
  coordinates: {
    latitude: { type: String },
    longitude: { type: String },
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

RegionSchema.plugin(mongoosePaginate);
