interface UpdateIconProps {
  type: string;
  isActive?: boolean;
  getupdateData?: () => void;
}

export default function UpdateIcon(props: UpdateIconProps) {

  const updateTypeByIcon: { [key: string]: string } = {
    'Announcement': 'fa-bullhorn',
    'Calendar Event': 'fa-calendar',
    'Link': 'fa-link',
    'Contact': 'fa-phone-square',
    'Document': 'fa-file',
    "General / Notice": 'fa-bullhorn',
    "Conversation": "fa-comment",
    "Image": "fa-image"
  }
  const icon = updateTypeByIcon[props.type];

  return (
    <i className={`fas ${icon} ${props.isActive && "isIconActive"}`} />
  )
}