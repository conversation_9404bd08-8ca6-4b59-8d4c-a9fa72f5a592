//Import Library
import React, { useState, useRef, Fragment, useEffect } from "react";
import { Col, Row } from 'react-bootstrap';
import AliceCarousel from "react-alice-carousel";

//Import services/components
import { useTranslation } from 'next-i18next';

interface RKICarouselProps {
  items: any[];
  renderItems: (item: any, index: number) => React.ReactNode;
  selector?: string;
}

const responsive = {
  576: { items: 1 },
  1024: { items: 4 }
};
const stagePadding = {
  paddingLeft: 0,
  paddingRight: 0,
  width: 250
};

const RKICarousel = ({ items, renderItems, ...props }: RKICarouselProps) => {
  const CarouselRef = useRef<any>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [i, setItems] = useState<any[]>([]);
  const { t } = useTranslation('common');


  const onSlideChange = (event: { item: number }) => {
    const { item } = event;
    setCurrentIndex(item);
  };
  const onChangePrev = () => {
    if (CarouselRef.current) {
      CarouselRef.current.slidePrev();
    }
  };
  const onChangeNext = () => {
    if (CarouselRef.current) {
      CarouselRef.current.slideNext();
    }
  };
  useEffect(() => {
    const data = items.map((item, idx) => renderItems(item, idx));
    setItems(data);
  }, [items])

  return (
    <div style={{position: 'relative'}}>
      <Col lg={12} style={{paddingLeft: "13px"}}>
        <AliceCarousel
          items={i}
          activeIndex={currentIndex}
          responsive={responsive}
          infinite={true}
          disableButtonsControls={true}
          paddingLeft={stagePadding.paddingLeft}
          paddingRight={stagePadding.paddingRight}
          disableDotsControls={true}
          onInitialized={onSlideChange}
          onSlideChanged={onSlideChange}
          onResized={onSlideChange}
          ref={CarouselRef}
        />
      </Col>
      {items && (items.length > 0) ? (
        <Fragment>
          <div className="alice-carousel__prev-btn-item" onClick={onChangePrev} aria-label="Previous Slide"><i className="fa fa-chevron-circle-left" /></div>
          <div className="alice-carousel__next-btn-item" onClick={onChangeNext} aria-label="Next Slide"><i className="fa fa-chevron-circle-right" /></div>
        </Fragment>
      ) : (
          <Row>
            <Col xs={12} className="p-0">
              <div className="text-center">{t("Content")}</div>
            </Col>
          </Row>
        )}
    </div>
  );
};

export default RKICarousel;
