//Import Library
import React from 'react';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

export const canViewDiscussionUpdate = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewDiscussionUpdate',
});

export default canViewDiscussionUpdate;