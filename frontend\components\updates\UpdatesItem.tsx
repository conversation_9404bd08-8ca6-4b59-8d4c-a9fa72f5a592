//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";
import Link from "next/link";
import { Row, Col, Button } from "react-bootstrap";
// @ts-ignore
import Truncate from "react-truncate";
import Router from "next/router";
import { usePathname, useSearchParams } from "next/navigation";
import Modal from "react-bootstrap/Modal";
import moment from "moment";

//Import services/components
import UpdateIcon from "./UpdateIcon";
import { canEditUpdate, canDeleteUpdate } from "./permissions";
import { useTranslation } from 'next-i18next';

const truncateLength = 120;
const formatDate = "MM-D-YYYY";

interface UpdatesItemProps {
  item: {
    _id: string;
    title: string;
    description: string;
    update_type: string;
    updated_at: string;
    type: string;
    [key: string]: any;
  };
  updateTypes: any[];
  onRemoveUpdate: (id: string) => void;
  [key: string]: any;
}

export default function UpdatesItem(props: UpdatesItemProps) {
  const { item, updateTypes } = props;
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Extract routes from pathname
  const pathParts = pathname.split('/');
  const routes = pathParts.slice(1); // Remove the first empty element

  const timelineActive =
    routes.length >= 4 &&
    routes[2] === "update" &&
    routes[3] === item._id;

  const pathArr = pathname.substr(1).split("/");
  const [smShow, setSmShow] = useState(false);
  const [updatetype, setupdatetype] = useState("");
  const smhandleClose = () => setSmShow(false);
  const { t } = useTranslation('common');

  const confirm = () => {
    props.onRemoveUpdate(item._id);
    setSmShow(false);
    const parentType = `parent_${item.type}`;
    Router.push(`/${item.type}/show/${item[parentType]}`);
  };

  useEffect(() => {
    if (updateTypes && updateTypes.length > 0) {
      const updateTypess = _.find(updateTypes, { _id: item.update_type });
      if (updateTypess && updateTypess.title) {
        setupdatetype(updateTypess.title);
      }
    }
  }, [item]);

  const getTrimmedString = (html: string) => {
    const div = document.createElement("div");
    div.innerHTML = html;
    const string = div.textContent || div.innerText || "";
    return string.length > truncateLength
      ? `${string.substring(0, truncateLength - 3)}...`
      : string;
  };

  const getItem = () => {
    if (item && item.type && item._id) {
      const key = `parent_${item.type}`; // Get Parent Id  ex: operation/show/{5e8e143579223a3be5e4063a}

      Router.push(
        `/${item.type}/[...routes]`,
        `/${item.type}/show/${item[key]}/update/${item._id}`
      ); // Dynamic url
    }
  };

  const EditLink = () => {
    return (
      <Link
        href={{
          pathname: `/updates/[...routes]`,
          query: {
            parent_type: pathArr[0],
            update_type: item.update_type,
          },
        }}
        as={`/updates/edit/${item._id}?parent_type=${pathArr[0]}&update_type=${item.update_type}`}
        >
        <i className="icon fas fa-edit" />
      </Link>
    );
  };

  const DeleteLink = () => {
    return (
      <>
        <i
          className="icon fas fa-trash-alt"
          onClick={() => setSmShow(true)}
        />
        <Modal
          size="sm"
          show={smShow}
          onHide={() => setSmShow(false)}
          aria-labelledby="example-modal-sizes-title-sm"
        >
          <Modal.Header closeButton>
            <Modal.Title id="example-modal-sizes-title-sm">
            {t("updates")}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
           {t("Areyousureyouwanttodeletetheupdate")}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={smhandleClose}>
           { t("Cancel")}
            </Button>
            <Button variant="primary" onClick={confirm}>
           { t("Ok")}
            </Button>
          </Modal.Footer>
        </Modal>
      </>
    )
  }

  const CanEditUpdate = canEditUpdate(() => <EditLink />)

  const CanDeleteUpdate = canDeleteUpdate(() => <DeleteLink />)

 const timelineActiveNew = timelineActive ? timelineActive : ""


  return (
    <li>
      <div
        className={`timeline-badge ${timelineActive && "timeline-isActive"} `}
        onClick={getItem}
      >
        <UpdateIcon
          type={updatetype}
          getupdateData={getItem}
          isActive={!!timelineActiveNew}
        />
      </div>
      <div className="timeline-content">
        <span className="title" onClick={getItem}>
          {item.title}
        </span>
        <span className="date">
          {moment(item.updated_at).format(formatDate)}{" "}
        </span>
        <p className="description">
          <Truncate lines={1} width={1400}>
            {item.description ? getTrimmedString(item.description) : null}
          </Truncate>
        </p>
        <div className="updatesAction">
          <div>
            <CanEditUpdate update={item} />
          </div>
          <div>
            <CanDeleteUpdate update={item} />
          </div>
        </div>
      </div>
    </li>
  );
}
