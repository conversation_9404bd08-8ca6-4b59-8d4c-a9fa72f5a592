//Import services/components
import { useTranslation } from 'next-i18next';

interface FooterImprintContentProps {
  [key: string]: any;
}

const FooterImprintContent = (props: FooterImprintContentProps) => {
  const { t } = useTranslation('common');

  return (
    <div className="imprintDetails">
      <div className="imprintBlock">
        <h5>{t("imprintHeader.tab1")}</h5>
        <p>
          <span style={{ fontWeight: "bold" }}>{t("imprintHeader.tab2")}</span>
          <br />
          {t("imprintContent.tab")}
          <br />
          {t("imprintContent.tab1")}
          <br />
          {t("imprintContent.tab2")}
        </p>
        <h6>
          {t("imprintContent.tab3")}
          <br />
          {t("imprintContent.tab4")}
        </h6>
        <p>
          <span style={{ fontWeight: "bold" }}>
            {" "}
            {t("imprintContent.tab5")}
          </span>
          <br />
          {t("imprintContent.tab6")}
        </p>
        <h6>{t("imprintHeader.tab3")}</h6>
        <p> {t("imprintContent.tab7")}</p>
        <h6>{t("imprintHeader.tab4")}</h6>
        <p>
          {t("imprintContent.tab8")}
          <br />
          {t("imprintContent.tab9")}
        </p>
        <h6>{t("imprintHeader.tab5")}</h6>
        <p> {t("imprintContent.tab10")}</p>
        <h6>{t("imprintHeader.tab6")}</h6>
        <p> {t("imprintContent.tab11")}</p>
        <h6>{t("imprintHeader.tab7")}</h6>
        <p>
          {" "}
          {t("imprintContent.tab12")}
          <br />
          <a href="https://adappt.co.uk/">{t("imprintContent.tab12a")}</a>
        </p>
      </div>

      <div className="imprintBlock">
        <h5>{t("imprintHeader.tab8")}</h5>
        <p>{t("imprintContent.tab13")}</p>
        <p>{t("imprintContent.tab14")}</p>
        <p>{t("imprintContent.tab15")}</p>
        <p>{t("imprintContent.tab16")}</p>
        <p>{t("imprintContent.tab17")}</p>
        <p>{t("imprintContent.tab18")}</p>
        <p>{t("imprintContent.tab19")}</p>
      </div>

      <div className="imprintBlock">
        <h5>{t("imprintHeader.tab9")}</h5>
        <p>{t("imprintContent.tab20")}</p>
      </div>
    </div>
  );
};

export default FooterImprintContent;
