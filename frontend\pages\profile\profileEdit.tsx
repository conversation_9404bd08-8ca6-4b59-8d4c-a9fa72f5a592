//Import Library
import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { Form, Modal, Row, Col, Button } from "react-bootstrap";

import toast from 'react-hot-toast';
import { TextInput, SelectGroup } from "../../components/common/FormValidation";
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";

//Import services/components
import { loadLoggedinUserData } from "../../stores/userActions";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const ProfileEdit = (props) => {
  const { t } = useTranslation('common');
    const { isOpen, manageClose, data } = props;

  const handleClose = () => {
    manageClose(false);
  };

  const intialState = {
    _id: "",
    username: "",
    firstname: "",
    lastname: "",
    position: "",
    institution: "",
    role: "",
    image: null,
    email: "",
    password: "",
    dataConsentPolicy: "",
    restrictedUsePolicy: "",
    acceptCookiesPolicy: "",
    withdrawConsentPolicy: "",
    medicalConsentPolicy: "",
    fullDataProtectionConsentPolicy: ""
  };

  const [profile, setProfile]: any = useState(intialState);
  const [organisation, setOrganisation] = useState([]);

  useEffect(() => {
    const updateProfile = async () => {
      const id =
        data && data.institution && data.institution._id
          ? data.institution._id
          : "";
      setProfile((prevState) => ({
        ...prevState,
        ...data,
        institution: id,
        image: null,
      }));
    };
    updateProfile();
  }, [data]);

  useEffect(() => {
    const organisationParams = {
      query: {},
      sort: { title: "asc" },
      limit: "~",
      select:
        "-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point",
    };
    const fetchOrganisation = async () => {
      const organisations = await apiService.get(
        "/institution",
        organisationParams
      );
      if (organisations && Array.isArray(organisations.data)){
        setOrganisation(organisations.data);
      }
    };
    fetchOrganisation();
  }, []);

  const profileHandler = (e) => {
    if (e.target) {
      const { name, value } = e.target;
      setProfile((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    }
  };

  const submitHandler = async (e) => {
    e.preventDefault();
    const response = await apiService.post("/users/updateProfile", profile);
    if (response) {
      props.dispatch(loadLoggedinUserData());
      toast.success(t("toast.ProfileUpdatedSuccessfully"));
      manageClose(false);
    }
  };

  const organisationHandler = (e) => {
    const { value } = e.target;
    setProfile((prevState) => ({ ...prevState, institution: value }));
  };

  return (
    <div>
      <Modal
        show={isOpen}
        onHide={handleClose}
        size="xl"
        className="w-100"
        centered
      >
        <ValidationFormWrapper className="m-4" onSubmit={submitHandler} initialValues={profile} enableReinitialize={true}>
          <Modal.Header closeButton>
            <Modal.Title>{t("setInfo.editprofile")}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <h5>{t("setInfo.MyInformation")}</h5>
            <div className="p-2 w-100">
              <Form.Group as={Row} controlId="username" className="mb-3">
                <Form.Label column md="4" xs="5" lg="2" className="required-field">
                  {t("setInfo.username")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <TextInput
                    name="username"
                    required
                    type="text"
                    value={profile && profile.username}
                    placeholder={t("setInfo.EnterYourName")}
                    onChange={profileHandler}
                  />
                </Col>
              </Form.Group>
              <Form.Group as={Row} controlId="username" className="mb-3">
                <Form.Label column md="4" xs="5" lg="2" className="required-field">
                  {t("setInfo.name")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <Row>
                    <Col xs="12" sm="6">
                      <TextInput
                        name="firstname"
                        required
                        type="text"
                        value={profile && profile.firstname}
                        placeholder={t("setInfo.Enteryourfirstname")}
                        errorMessage={t("setInfo.Pleaseenteryourfirstname")}
                        onChange={profileHandler}
                      />
                    </Col>
                    <Col xs="12" sm="6" className="pt-2 pt-sm-0">
                      <TextInput
                        name="lastname"
                        type="text"
                        value={profile && profile.lastname}
                        placeholder={t("setInfo.EnterYourlastname")}
                        onChange={profileHandler}
                      />
                    </Col>
                  </Row>
                </Col>
              </Form.Group>
              <Form.Group as={Row} controlId="position" className="mb-3">
                <Form.Label column md="4" xs="5" lg="2" >
                  {t("setInfo.position")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <TextInput
                    name="position"
                    type="text"
                    value={profile && profile.position}
                    placeholder={t("setInfo.EnterYourposition")}
                    onChange={profileHandler}
                  />
                </Col>
              </Form.Group>

              <Form.Group as={Row} controlId="institution" className="mb-3">
                <Form.Label column md="4" xs="5" lg="2">
                  {t("setInfo.organisation")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <SelectGroup
                    name="partner_institution"
                    id="partner_institution"
                    value={profile.institution}
                    onChange={organisationHandler}
                    style={{
                      backgroundColor: "inherit",
                      borderRadius: "5px",
                      color: "#495057",
                    }}
                  >
                    <option value="">{t("setInfo.SelectOrganisation")}</option>
                    {organisation.map((item, _i) => {
                      return <option value={item._id}>{item.title}</option>;
                    })}
                  </SelectGroup>
                </Col>
              </Form.Group>
              <Form.Group as={Row} controlId="Email" className="mb-3">
                <Form.Label column md="4" xs="5" lg="2" className="required-field">
                  {t("setInfo.email")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <TextInput
                    required
                    name="email"
                    type="text"
                    value={profile && profile.email}
                    placeholder={t("setInfo.EnterYourEmail")}
                    onChange={profileHandler}
                  />
                </Col>
              </Form.Group>

              <Form.Group as={Row} controlId="password" className="mb-3">
                <Form.Label column md="4" xs="5" lg="2">
                  {t("setInfo.password")}
                </Form.Label>
                <Col md="8" xs="7" lg="10">
                  <TextInput
                    name="password"
                    type="password"
                    value={profile.password}
                    placeholder={t("setInfo.EnterYourNewPassword")}
                    onChange={profileHandler}
                    pattern="^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$"
                    errorMessage={{
                      pattern:
                        t("setInfo.Passwordshouldcontainatleastcharacter"),
                    }}
                  />
                </Col>
              </Form.Group>
            </div>
          </Modal.Body>
          <Modal.Footer className="pb-0">
          <Button variant="primary" type="submit">
              {t("setInfo.savechanges")}
            </Button>
            <Button variant="danger"  onClick={handleClose}>
              {t("setInfo.Cancel")}
            </Button>
          </Modal.Footer>
        </ValidationFormWrapper>
      </Modal>
    </div>
  );
};

export default connect()(ProfileEdit);
