//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { SyndromeInterface } from "src/interfaces/syndrome.interface";
import { syndromes } from "../../data/syndrome";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class SyndromeSeederService {

  constructor(
    @InjectModel('Syndrome') private syndromeModel: Model<SyndromeInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<SyndromeInterface>> {
    return syndromes.map(async (syndrome: any) => {
      return await this.syndromeModel
        .findOne({ title: syndrome.title })
        .exec()
        .then(async dbSyndrome => {
          // We check if a hazard already exists.
          // If it does don't create a new one.
          if (dbSyndrome) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.syndromeModel.create(syndrome),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}