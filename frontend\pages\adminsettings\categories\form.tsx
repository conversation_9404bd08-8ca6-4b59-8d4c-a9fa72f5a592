//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
// import { ValidationForm } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput } from "../../../components/common/FormValidation";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from 'next/link';

//Import services/components
import { CategoryInterface } from "../../../components/interfaces/category.interface";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';


interface CategoryFormProps {
    routes: string[];
}

const CategoryForm = (props: CategoryFormProps) => {

  const _initialcategory = {
    title: '',
  }

  const [initialVal, setInitialVal] = useState<CategoryInterface>(_initialcategory);

  const editform: boolean = !!(props.routes && props.routes[0] === "edit_category" && props.routes[1]);
  const { t } = useTranslation('common');


  const formRef = useRef(null);

  const resetHandler = () => {
    setInitialVal(_initialcategory);
    // Reset validation state (Formik handles this automatically)
    window.scrollTo(0, 0);
  };

  const handleChange = (e: any) => {
    if (e.target) {
      const { name, value } = e.target;
      setInitialVal(prevState => ({
        ...prevState,
        [name]: value
      }));
    }
  }

  const handleSubmit = async (event: any) => {
    event.preventDefault();
    const obj = {
      title: initialVal.title.trim(),
    };


    let response;
    if (editform) {
      response = await apiService.patch(`/category/${props.routes[1]}`, obj);
    } else {
      response = await apiService.post("/category", obj);
    }
    if (response && response._id) {
      toast.success(t("Categoryisaddedsuccessfully"));
      Router.push("/adminsettings/category");
    } else {
      toast.error(response);
    }
  }

  useEffect(() => {
    const categoryParams = {
      query: {},
      sort: { title: "asc" },
      limit: "~",
    };
    if (editform) {
      const getCategoryData = async () => {
        const response = await apiService.get(`/category/${props.routes[1]}`, categoryParams);
        setInitialVal((prevState) => ({ ...prevState, ...response }));
      }
      getCategoryData();
    }
  }, []);

  return (
    <div>
      <Container className="formCard" fluid>
        <Card style={{ marginTop: "5px", boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)" }}>
          <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
            <Card.Body>
              <Row>
                <Col>
                  <Card.Title>{t("Category")}</Card.Title>
                </Col>
              </Row>
              <hr />
              <Row>
                <Col md lg={6} sm={12}>
                  <Form.Group>
                    <Form.Label className="required-field">{t("Category")}</Form.Label>
                    <TextInput
                      name="title"
                      id="title"
                      required value={initialVal.title}
                      validator={((value: any) => String(value || '').trim() !== "")}
                      errorMessage={{
                        validator: t("PleaseAddtheCategory")}}
                      onChange={handleChange}
                    />
                  </Form.Group>
                </Col>
              </Row>
              <Row className="my-4">
                <Col>
                  <Button className="me-2" type="submit" variant="primary">{t("submit")}</Button>
                  <Button className="me-2" onClick={resetHandler} variant="info">{t("reset")}</Button>
                  <Link
                    href="/adminsettings/[...routes]"
                    as={`/adminsettings/category`}
                    ><Button variant="secondary">{t("Cancel")}</Button></Link>
                </Col>
              </Row>
            </Card.Body>
          </ValidationFormWrapper>
        </Card>
      </Container>
    </div>
  );
}
export default CategoryForm;
