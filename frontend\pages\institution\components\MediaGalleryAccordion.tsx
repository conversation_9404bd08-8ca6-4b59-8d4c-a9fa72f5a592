//Import Library
import React from "react";
import { Accordion } from "react-bootstrap";

//Import services/components
import ReactImages from "../../../components/common/ReactImages";
import { useTranslation } from 'next-i18next';

const MediaGalleryAccordion = (props) => {
    const { t } = useTranslation('common');
    return (
        <Accordion defaultActiveKey="1">
            <Accordion.Item eventKey="1">
                <Accordion.Header>
                    <div className="cardTitle">{t("MediaGallery")}</div>
                </Accordion.Header>
                <Accordion.Body>
                    <ReactImages
                        gallery={props.images}
                        imageSource={props.images_src}
                    />
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    )
}

export default MediaGalleryAccordion;