//Import Library
import React, { useState } from "react";
import { Accordion, Card } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import { useTranslation } from 'next-i18next';
import Discussion from "../../../components/common/disussion";

interface DiscussionAccordionProps {
  routeData: {
    routes: string[];
  };
}

const DiscussionAccordion = (props: DiscussionAccordionProps) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(true);
    return (
        <Accordion.Item eventKey="2">
            <Accordion.Header onClick={() => setSection(!section)}>
                <div className="cardTitle">{t("Discussions")}</div>
                <div className="cardArrow">
                    {section ? <FontAwesomeIcon icon={faPlus} color="#fff" /> :
                        <FontAwesomeIcon icon={faMinus} color="#fff" />}
                </div>
            </Accordion.Header>
            <Accordion.Body>
                <Discussion type='project' id={ props?.routeData?.routes ? props.routeData.routes[1] : ''} />
            </Accordion.Body>
        </Accordion.Item>
    )
};

export default DiscussionAccordion;