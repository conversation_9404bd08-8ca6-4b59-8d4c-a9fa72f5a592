//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { LanguageInterface } from '../../../interfaces/language.interface';
import { languages } from "../../data/language";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class LanguageSeederService {

  constructor(
    @InjectModel('language') private languageModel: Model<LanguageInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<LanguageInterface>> {
    return languages.map(async (language: LanguageInterface) => {
      return await this.languageModel
        .findOne({ title: language.title })
        .exec()
        .then(async dbLangauge => {
          // We check if a language already exists.
          // If it does don't create a new one.
          if (dbLangauge) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.languageModel.create(language),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}