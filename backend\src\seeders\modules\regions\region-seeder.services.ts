//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { RegionInterface } from '../../../interfaces/region.interface';
import { CountryInterface } from "src/interfaces/country.interface";
import { Regions } from "../../data/region";

/**
 * Service dealing with regions based operations.
 *
 * @class
 */
@Injectable()
export class RegionSeederService {
  constructor(
    @InjectModel('Region') private regionModel: Model<RegionInterface>,
    @InjectModel('Country') private countryModel: Model<CountryInterface>

  ) {}
  /**
   * Seed all regions.
   *
   * @function
   */
  create(): Array<Promise<RegionInterface>> {
    return Regions.map(async (region: RegionInterface) => {
      const country = await this.countryModel.findOne({ code: region.country_code }).exec();
      region.country = country ? country._id : null;

      return await this.regionModel
        .findOne({ title: region.title })
        .exec()
        .then(async dbRegion => {
          // We check if a regions already exists.
          // If it does don't create a new one.
          if (dbRegion) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.regionModel.create(region),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}