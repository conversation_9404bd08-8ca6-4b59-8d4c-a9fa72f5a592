//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Patch,
} from '@nestjs/common';

//Import services/components
import { CreateAreaOfWorkDto } from './dto/create-area-of-work.dto';
import { UpdateAreaOfWorkDto } from './dto/update-area-of-work.dto';
import { AreaOfWorkService } from './area-of-work.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('areaofwork')
@UseGuards(SessionGuard)
export class AreaOfWorkController {
  constructor(private readonly _areaOfWorkService: AreaOfWorkService) {}

  @Post()
  async create(@Body() createAreaOfWorkDto: CreateAreaOfWorkDto) {
    try {
      const _area_of_work = await this._areaOfWorkService.create(
        createAreaOfWorkDto,
      );
      return _area_of_work;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._areaOfWorkService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') areaOfWorkId: string) {
    return this._areaOfWorkService.get(areaOfWorkId);
  }

  @Patch(':id')
  async update(
    @Param('id') areaOfWorkId: string,
    @Body() updateAreaOfWorkDto: UpdateAreaOfWorkDto,
  ) {
    try {
      const _area_of_work = await this._areaOfWorkService.update(
        areaOfWorkId,
        updateAreaOfWorkDto,
      );
      return _area_of_work;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') areaOfWorkId: string) {
    const _area_of_work = this._areaOfWorkService.delete(areaOfWorkId);
    return _area_of_work;
  }
}
