//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { HazardSeederService } from './hazard-seeder.services';
// SCHEMAS
import { HazardSchema } from 'src/schemas/hazard.schemas';
import { HazardTypeSchema } from 'src/schemas/hazard_type.schemas';
import { ImageSchema } from 'src/schemas/image.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Hazard', schema: HazardSchema },
        { name: 'HazardType', schema: HazardTypeSchema },
        { name: 'Image', schema: ImageSchema }
      ]
    )
  ],
  providers: [HazardSeederService],
  exports: [HazardSeederService],
})
export class HazardSeederModule { }