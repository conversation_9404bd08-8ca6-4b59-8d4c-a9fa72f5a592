//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateClassificationDto } from './dto/create-classification.dto';
import { UpdateClassificationDto } from './dto/update-classification.dto';
import { ClassificationService } from "./classification.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('classification')
@UseGuards(SessionGuard)
export class ClassificationController {

  constructor(
    private readonly _classificationService: ClassificationService
  ) { }

  @Post()
  create(@Body() createClassificationDto: CreateClassificationDto) {
    const resp = this._classificationService.create(createClassificationDto);
    return resp;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._classificationService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') classificationId: string) {
    return this._classificationService.get(classificationId);
  }

  @Patch(':id')
  update(@Param('id') classificationId: string, @Body() updateClassificationDto: UpdateClassificationDto) {
    const resp = this._classificationService.update(classificationId, updateClassificationDto);
    return resp;
  }

  @Delete(':id')
  remove(@Param('id') classificationId: string) {
    const deletedData = this._classificationService.delete(classificationId);
    return deletedData;
  }
}
