//Import services/components
import React, { useState } from "react";
import { Accordion, Card } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import { useTranslation } from 'next-i18next';
import ReactImages from "../../../components/common/ReactImages";

const MediaGalleryAccordian = (props) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("MediaGallery")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <ReactImages
                        gallery={props.operation.images}
                        imageSource={props.operation.images_src}
                    />
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
}

export default MediaGalleryAccordian;