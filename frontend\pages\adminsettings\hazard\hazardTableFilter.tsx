//Import Library
import {Col, Container, FormControl, Row} from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

const HazardTableFilter = ({ filterText, onFilter ,onClear}: any) => {
  const { t } = useTranslation('common');


  return (
    <Container fluid className="p-0">
      <Row>
        <Col md={4} className="p-0">
          <FormControl
            type="text"
            className="searchInput"
            placeholder={t("adminsetting.hazard.Search")}
            aria-label="Search"
            value={filterText}
            onChange={onFilter}
          />
        </Col>
      </Row>
    </Container>
  )
};

export default HazardTableFilter;
