//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { RolesController } from './roles.controller';
import { RolesService } from './roles.service';
// SCHEMAS
import { RolesSchema } from '../../schemas/roles.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Roles', schema: RolesSchema }
    ])
  ],
  controllers: [RolesController],
  providers: [RolesService],
})

export class RolesModule { }