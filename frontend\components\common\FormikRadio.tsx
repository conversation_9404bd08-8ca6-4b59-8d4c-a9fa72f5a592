import React from 'react';
import { useFormikContext, Field } from 'formik';
import { Form } from 'react-bootstrap';

interface RadioItemProps {
  id: string;
  label: string;
  value: string;
  name?: string;
  disabled?: boolean;
}

interface RadioGroupProps {
  name: string;
  valueSelected: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  errorMessage?: string;
  children: React.ReactNode;
}

// Type Guard to ensure child.props is an object
function isObject(props: any): props is { [key: string]: any } {
  return typeof props === 'object' && props !== null;
}

const RadioItem: React.FC<RadioItemProps> = ({ id, label, value, name, disabled }) => {
  const { values, setFieldValue } = useFormikContext<any>();
  const fieldName = name || id;

  return (
    <Form.Check
      type="radio"
      id={id}
      label={label}
      value={value}
      name={fieldName}
      checked={values[fieldName] === value}
      onChange={(e) => {
        setFieldValue(fieldName, e.target.value);
      }}
      disabled={disabled}
      inline
    />
  );
};

const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  valueSelected,
  onChange,
  errorMessage,
  children,
}) => {
  const { errors, touched } = useFormikContext<any>();
  const hasError = touched[name] && errors[name];

  // Create a context to pass the name to RadioItems
  const radioContext = React.useMemo(() => ({ name }), [name]);

  // Clone children to pass the name, ensuring props are spread safely
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      // Ensure child.props is an object before spreading
      if (isObject(child.props)) {
        return React.cloneElement(child as React.ReactElement<RadioItemProps>, {
          name,
          ...child.props, // Safely spread child.props
        });
      }
    }
    return child;
  });

  return (
    <div>
      <div className="radio-group">
        {childrenWithProps}
      </div>
      {hasError && (
        <div className="invalid-feedback d-block">
          {errorMessage || (typeof errors[name] === 'string' ? errors[name] : String(errors[name]))}
        </div>
      )}
    </div>
  );
};

export const Radio = {
  RadioGroup,
  RadioItem,
};

export default Radio;
