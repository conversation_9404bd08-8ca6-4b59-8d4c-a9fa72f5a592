//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { deploymentStatuses } from "../../data/deployment-status";
import { DeploymentStatusInterface } from "src/interfaces/deployment-status.interface";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class DeploymentStatusSeederService {

  constructor(
    @InjectModel('DeploymentStatus') private deploymentStatusModel: Model<DeploymentStatusInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<DeploymentStatusInterface>> {
    return deploymentStatuses.map(async (deploymentStatus: any) => {
      return await this.deploymentStatusModel
        .findOne({ title: deploymentStatus.title })
        .exec()
        .then(async dbDeploymentStatus => {
          // We check if a Deployment Status already exists.
          // If it does don't create a new one.
          if (dbDeploymentStatus) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.deploymentStatusModel.create(deploymentStatus),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}