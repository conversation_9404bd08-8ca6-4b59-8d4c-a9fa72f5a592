.rkimapmarker {
  font-size: 26px;

  &.skyBlue {
    color: #04a5fc;
  }
  
  &.white {
    color: #ffffff;
  }
}

.gm-style {
  .gm-style .gm-style-iw-t::after {
    height: 10px;
    width: 10px;
  }
  .gm-style-iw-c {
    background-color: white;
    border-radius: 4px;
    padding: 10px 13px 0 10px;
    box-shadow: 1px 1px 10px rgba(150, 150, 150, 0.5);
    font-size: 14px;
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }
    li {
      padding: 3px 0;
      border-bottom: 1px solid #d5d5d5;
      a {
        color: #3f51b5;
      }
    }
    button {
      top: 3px !important;
      right: 3px !important;
      width: 15px !important;
      height: 15px !important;
      background: #d5d5d5 !important;
      border-radius: 20px !important;
      opacity: 1;
      // display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      span {
        position: relative;
        background-color: red;
        margin: 0px !important;
        height: 15px !important;
        width: 15px !important;
      }
      img {
        width: 12px !important;
        height: 12px !important;
        margin: 0 !important;
        opacity: .6;
      }
    }
  }
}

.map-legends {
  position: absolute;
  bottom: 0;
  margin-bottom: 3px;
  margin-left: 3px;
  ul {
    margin: 0;
    list-style: none;
    display: flex;
    padding: 5px 7px;
    background: #fff;
    font-size: 12px;
    border-radius: 18px;
    li {
      margin-right: 10px;
    }
  }
  .marker-blue-legend i {
    color: #04aaff;
  }
  
  .marker-green-legend i {
    color: #26c724;
  }

  .marker-yellow-legend i {
    color: #fb9504;
  }

  .marker-red-legend i {
    color: #c52c52;
  }
}

