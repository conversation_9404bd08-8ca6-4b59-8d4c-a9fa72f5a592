//Import Library
import React, { useEffect, useState } from "react";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Accordion, Card } from "react-bootstrap";

//Import services/components
import ReactImages from "../../../components/common/ReactImages";
import { useTranslation } from 'next-i18next';

const MediaGalleryAccordion = (props) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);

    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("Events.show.MediaGallery")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <ReactImages gallery={props.images} imageSource={props.images_src} />
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
};

export default MediaGalleryAccordion;