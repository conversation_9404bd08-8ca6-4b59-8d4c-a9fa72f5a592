//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const FilesSchema = new mongoose.Schema({
  name: { type: String, required: true },
  original_name: String,
  extension: String,
  is_temp: { type: Boolean, default: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

FilesSchema.plugin(mongoosePaginate);
