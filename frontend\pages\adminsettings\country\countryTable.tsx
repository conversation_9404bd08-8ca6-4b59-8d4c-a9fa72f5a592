//Import Library
import { useState, useEffect, useMemo } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import _ from "lodash";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import CountryTableFilter from "./countryTableFilter";
import { useTranslation } from 'next-i18next';

const CountryTable = (_props: any) => {
    const { t, i18n } = useTranslation('common');
    const titleSearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };
    const currentLang = i18n.language;
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectCountryDetails, setSelectCountry] = useState({});
    const [filterText, setFilterText] = useState("");
    const [resetPaginationToggle, setResetPaginationToggle] = useState(false);


    const countryParams = {
        sort: titleSearch,
        limit: perPage,
        page: 1,
        query: {},
        languageCode: currentLang ? currentLang : "en",
    };

    const columns = [
        {
            name: t("adminsetting.Countries.Table.Country"),
            selector: row => row.title,
            sortable: true,
        },
        {
            name: t("adminsetting.Countries.Table.Code"),
            selector: row => row.code,
            sortable: true,
        },
        {
            name: t("adminsetting.Countries.Table.DialCode"),
            selector: row => row.dial_code,
            sortable: true,
        },
        {
            name: t("adminsetting.Countries.Table.WorldRegion"),
            selector: row => row.world_region?.title || '',
            sortable: true,
        },
        {
            name: t("adminsetting.Countries.Table.Action"),
            selector: row => row._id,
            sortable: false,
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_country/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>
                </div>
            ),
        },
    ];

    const getCountriesData = async (countryParams_initial) => {
        setLoading(true);
        const response = await apiService.get("/country", countryParams_initial);
        if (response) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        countryParams.limit = perPage;
        countryParams.page = page;
        getCountriesData(countryParams);
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        countryParams.limit = newPerPage;
        countryParams.page = page;
        setLoading(true);
        const response = await apiService.get("/country", countryParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectCountry(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/country/${selectCountryDetails}`);
            getCountriesData(countryParams);
            setModal(false);
            toast.success(t("adminsetting.Countries.Table.countryDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.Countries.Table.errorDeletingcountry"));
        }
    };

    const modalHide = () => setModal(false);

    const subHeaderComponentMemo = useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText("");
            }
        };

        const sendQuery = (q) => {
            if (q) {
                countryParams.query = { title: q };
            }
            getCountriesData(countryParams);
        };

        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            setFilterText(e.target.value);
            handleSearchTitle(e.target.value);
        };

        return <CountryTableFilter onFilter={handleChange} onClear={handleClear} filterText={filterText} />;
    }, [filterText]);

    useEffect(() => {
        getCountriesData(countryParams);
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.Countries.Table.DeleteCountry")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.Countries.Table.Areyousurewanttodeletethiscountry?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.Countries.Table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.Countries.Table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                subheader
                pagServer={true}
                resetPaginationToggle={resetPaginationToggle}
                subHeaderComponent={subHeaderComponentMemo}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default CountryTable;
