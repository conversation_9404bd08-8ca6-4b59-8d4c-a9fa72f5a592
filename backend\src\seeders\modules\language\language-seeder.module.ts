//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { LanguageSeederService } from './language-seeder.services';
// SCHEMAS
import { LanguageSchema } from 'src/schemas/language.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'language', schema: LanguageSchema }
      ]
    )
  ],
  providers: [LanguageSeederService],
  exports: [LanguageSeederService],
})
export class LanguageSeederModule { }