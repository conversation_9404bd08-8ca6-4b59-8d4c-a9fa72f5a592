//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { ClassificationInterface } from "src/interfaces/classification.interface";
import { classifications } from "../../data/classification";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class ClassificationSeederService {

  constructor(
    @InjectModel('Classification') private classificaitonModel: Model<ClassificationInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<ClassificationInterface>> {
    return classifications.map(async (classif: any) => {
      return await this.classificaitonModel
        .findOne({ title: classif.title })
        .exec()
        .then(async dbClassification => {
          // We check if a Classification already exists.
          // If it does don't create a new one.
          if (dbClassification) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.classificaitonModel.create(classif),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}