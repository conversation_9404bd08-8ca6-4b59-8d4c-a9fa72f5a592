import React from 'react';
import { Marker } from '@react-google-maps/api';

interface RKIMapMarkerProps {
  name?: string;
  id?: string;
  countryId?: string;
  type?: string;
  icon?: {
    url: string;
    scaledSize?: google.maps.Size;
  };
  position: {
    lat: number;
    lng: number;
  };
  onClick?: (props: any, marker: any, e: any) => void;
  title?: string;
  draggable?: boolean;
}

const RKIMapMarker: React.FC<RKIMapMarkerProps> = ({
  name = 'Marker',
  id = '',
  countryId = '',
  type,
  icon,
  position,
  onClick,
  title,
  draggable = false,
}) => {
  const handleClick = (e: google.maps.MapMouseEvent) => {
    if (onClick) {
      const markerProps = {
        name,
        id,
        countryId,
        type,
        position,
      };

      // Create a marker-like object for compatibility with old onClick signature
      const marker = {
        position,
        getPosition: () => position,
      };

      onClick(markerProps, marker, e);
    }
  };

  // Ensure position is valid
  if (!position || typeof position.lat !== 'number' || typeof position.lng !== 'number') {
    return null;
  }

  return (
    <Marker
      position={position}
      icon={icon}
      title={title || name}
      draggable={draggable}
      onClick={handleClick}
    />
  );
};

export default RKIMapMarker;
