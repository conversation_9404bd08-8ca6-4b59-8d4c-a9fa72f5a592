//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { FileCategoryController } from './file-category.controller';
import { FileCategoryService } from './file-category.service';
// SCHEMAS
import { FileCategorySchema } from '../../schemas/file_category.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'FileCategory', schema: FileCategorySchema }
    ])
  ],
  controllers: [FileCategoryController],
  providers: [FileCategoryService],
})

export class FileCategoryModule { }