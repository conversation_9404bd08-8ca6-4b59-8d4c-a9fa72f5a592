//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Button } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const OperationstatusTable = (_props: any) => {
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectOperationstatus, setSelectOperationstatus] = useState({});
    const { t } = useTranslation('common');
    
    const operationstatusParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("Title"),
            selector: "title",
        },
        {
            name: t("action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_operationstatus/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    const getoperationstatusData = async () => {
        setLoading(true);
        const response = await apiService.get("/operation_status", operationstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        operationstatusParams.limit = perPage;
        operationstatusParams.page = page;
        getoperationstatusData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        operationstatusParams.limit = newPerPage;
        operationstatusParams.page = page;
        setLoading(true);
        const response = await apiService.get("/operation_status", operationstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectOperationstatus(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/operation_status/${selectOperationstatus}`);
            getoperationstatusData();
            setModal(false);
            toast.success(t("adminsetting.OperationStatus.Table.opStatusDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.OperationStatus.Table.errorDeletingOpStatus"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        getoperationstatusData();
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.OperationStatus.Delete")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.OperationStatus.sure")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default OperationstatusTable;
