//Import Library
import React from "react";
import { Row, Col } from "react-bootstrap";
import Link from 'next/link';

//Import services/components
import { sliderData } from "../../../data/landing";

const Slider: React.FunctionComponent<{}> = (): React.ReactElement => (
  <div className='slider'>
    <Row>
      <Col xs={12}>
        {sliderData.map((item, idx) => (
          <div className="myslider" key={idx}>
            <img src="images/home/<USER>" width="100%" />
            <div className="sliderContent">
              <div className="sliderTitle">{item.title}</div>
              <div className="sliderDesc">{item.content}
              <Link href={item.href} target="_blank">
          More Information
        </Link></div>
            </div>
          </div>
        ))}
      </Col>
    </Row>
  </div>
);

export default Slider;
