//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { OperationStatusInterface } from '../../interfaces/operation-status.interface';
import { CreateOperationStatusDto } from './dto/create-operation-status.dto';
import { UpdateOperationStatusDto } from './dto/update-operation-status.dto';
const FindOperationStatus = 'Could not find Operation Status.';
@Injectable()
export class OperationStatusService {
  constructor(
    @InjectModel('OperationStatus')
    private operationStatusModel: Model<OperationStatusInterface>,
  ) {}

  async create(
    createOperationStatusDto: CreateOperationStatusDto,
  ): Promise<OperationStatusInterface> {
    const createdOperationStatus = new this.operationStatusModel(
      createOperationStatusDto,
    );
    return createdOperationStatus.save();
  }

  async findAll(query): Promise<OperationStatusInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.operationStatusModel.paginate(_filter, options);
  }

  async get(operationStatusId): Promise<OperationStatusInterface[]> {
    let _operationStatus;
    try {
      _operationStatus = await this.operationStatusModel
        .findById(operationStatusId)
        .exec();
    } catch (error) {
      throw new NotFoundException(FindOperationStatus);
    }
    if (!_operationStatus) {
      throw new NotFoundException(FindOperationStatus);
    }
    return _operationStatus;
  }

  async update(
    operationStatusId: any,
    updateOperationStatusDto: UpdateOperationStatusDto,
  ) {
    const getOperationStatusById: any = await this.operationStatusModel
      .findById(operationStatusId)
      .exec();
    const updatedOperationStatus = new this.operationStatusModel(
      updateOperationStatusDto,
    );
    Object.keys(updateOperationStatusDto).forEach((d) => {
      getOperationStatusById[d] = updatedOperationStatus[d];
    });
    getOperationStatusById.updated_at = new Date();
    return getOperationStatusById.save();
  }

  async delete(operationStatusId: string) {
    const result = await this.operationStatusModel
      .deleteOne({ _id: operationStatusId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(FindOperationStatus);
    }
  }
}
