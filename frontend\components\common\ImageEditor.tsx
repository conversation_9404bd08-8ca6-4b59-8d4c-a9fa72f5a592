//Import Library
import React, { useState, useRef } from "react";
import AvatarEditor from "react-avatar-editor";
import RangeSlider from "react-bootstrap-range-slider";
import { Modal, Button, Row, Col } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const ImageEditor = ({ isOpen, onModalClose, image}: any) => {
  const { t } = useTranslation('common');
  const file = t("setInfo.Choosefile");
  const error = "Something wrong in server || your data!";
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [name, setName] = useState("");
  const [img, setImg] = useState<string | null>(null);
  const editorRef = useRef<any>(null);

  const cropHandler = async () => {
    /*****Helper Function to convert to blob******/
    const dataURLtoBlob = (dataurl: string) => {
    const arr = dataurl.split(",");
     const mimeMatch = arr[0].match(/:(.*?);/);
     const mime = mimeMatch ? mimeMatch[1] : '';
     const bstr = atob(arr[1]);
     let n = bstr.length;
     const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    };
    /*****End ********/

    const canvas = editorRef.current
      .getImageScaledToCanvas()
      .toDataURL("image/jpeg", 0.8);
    const blob = dataURLtoBlob(canvas);
    const fd = new FormData();
    fd.append("file", blob, name);

    try {
      const res = await apiService.post("/image", fd, {
        "Content-Type": "multipart/form-data",
      });

      if (res && res._id) {
        const response = await apiService.post("/users/updateProfile", {
          image: res._id,
        });
        if (response) {
          toast.success(t("setInfo.ProfileUpdatedSuccessfully"));

        }
      }
    } catch (error: unknown) {
      throw error instanceof Error ? error : new Error('Unknown error occurred');
    }
    onModalClose(false);
    setImg(null);
    setName(file);
    setScale(1);
  };

  /**File Handler**/
  const fileHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setName(e.target.files[0].name);
      setImg(URL.createObjectURL(e.target.files[0]));
    }
  };
  /**End**/

  return (
    <>
      <div>
        <Modal
          show={isOpen}
          size="lg"
          aria-labelledby="ProfileEdit"
          onHide={() => onModalClose(false)}
          centered
        >
          <Modal.Header>
            <Modal.Title id="contained-modal-title-vcenter">
            {t("setInfo.EditYourImage")}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="d-flex flex-column justify-content-center align-items-center">
              <AvatarEditor
                ref={editorRef}
                borderRadius={100}
                scale={scale}
                rotate={rotate}
                color={[0, 0, 0, 0.6]}
                image={img ? img : "/images/rkiProfile.jpg"}
              />
            </div>

            <div className="my-3 mx-2">
              <Row className="align-items-center mb-4">
                <Col sm={2} md={2} lg={2} className="pe-0">
                <b>{t("setInfo.Uploadaimage")}</b>
                </Col>
                <Col sm={10} md={10} lg={10}>
                  <div className="form-control custom-file position-relative">
                    <input
                      type="file"
                      name="files"
                      className="form-control-input form-control custom-file-input"
                      accept="image/*"
                      id="customFile"
                      onChange={fileHandler}
                    />
                    <label className="custom-file-label form-control-label w-100"  data-browse = {t("Browse")}>{t("setInfo.Choosefile")}</label>
                  </div>
                </Col>
              </Row>
            </div>

            <div className="my-3 mx-2">
              <Row>
                <Col sm={2} md={2} lg={2} className="pe-0">
                <b>{t("setInfo.Zoom")}</b>
                </Col>
                <Col sm={4} md={4} lg={4}>
                  <RangeSlider
                    value={scale}
                    tooltip="auto"
                    min={1}
                    max={10}
                    step={0.1}
                    variant="primary"
                    onChange={(changeEvent: React.ChangeEvent<HTMLInputElement>) =>
                      setScale(Number(changeEvent.target.value))
                    }
                  />
                </Col>
                <Col sm={2} md={2} lg={2} className="pe-0">
                <b>{t("setInfo.Rotate")}</b>
                </Col>
                <Col sm={4} md={4} lg={4}>
                  <RangeSlider
                    value={rotate}
                    tooltip="auto"
                    tooltipLabel={(currentValue) => `${currentValue}°`}
                    min={0}
                    max={360}
                    variant="primary"
                    onChange={(changeEvent: React.ChangeEvent<HTMLInputElement>) =>
                      setRotate(Number(changeEvent.target.value))
                    }
                  />
                </Col>
              </Row>
            </div>
          </Modal.Body>
          <Modal.Footer>
          <Button onClick={cropHandler}>{t("setInfo.SaveChanges")}</Button>
            <Button variant="danger" onClick={() => {
              onModalClose(false)
              setImg(null)
              }
            }>
            {t("Cancel")}
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    </>
  );
};

export default ImageEditor;
