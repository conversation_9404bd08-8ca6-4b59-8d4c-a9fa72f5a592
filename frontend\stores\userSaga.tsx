//Import Library
import { all, call, delay, put, take, takeEvery } from 'redux-saga/effects';

//Import services/components
import { loadDataSuccess, actionTypes } from './userActions';
import apiService from "../services/apiService";

function* loadDataSaga(): Generator<any, void, any> {
  try {
    const data = yield apiService.post('/users/getLoggedUser',{});
    yield put(loadDataSuccess(data))
  } catch (err) {
    console.log(err);
  }
}

const loadata = takeEvery(actionTypes.LOAD_DATA, loadDataSaga);

export default loadata;
