import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { TweetsService } from './tweets.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('tweets')
@UseGuards(SessionGuard)
export class TweetsController {
  constructor(private readonly _tweetsService: TweetsService) {}

  @Get()
  async findAll(@Query() query: any) {
    return this._tweetsService.findLatestTweets(query);
  }
}