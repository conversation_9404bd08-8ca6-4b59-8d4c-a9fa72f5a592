//Import Library
import React, { useEffect, useState } from "react";

//Import services/components
import RKIMap1 from "../RKIMap1";
import RKIMapMarker from "../RKIMapMarker";
import { useTranslation } from 'next-i18next';

interface MapData {
  _id: string;
  title: string;
  country?: {
    coordinates?: Array<{
      latitude: string;
      longitude: string;
    }>;
  };
}

interface ShowMapContainerProps {
  mapdata: MapData;
}

const ShowMapContainer = (props: ShowMapContainerProps) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language;
  const { mapdata } = props;
  const [points, setPoints]: any = useState({});
  const [activeMarker, setactiveMarker]: any = useState({});
  const [markerInfo, setMarkerInfo]: any = useState({});

  const MarkerInfo = (Markerprops: { info: { name?: string } }) => {
    const { info } = Markerprops;
    return <a>{info?.name}</a>;
  };

  const resetMarker = () => {
    setactiveMarker(null);
    setMarkerInfo(null);
  };

  const onMarkerClick = (onMarkerprops: { name: string }, marker: any, e: any) => {
    resetMarker();
    setactiveMarker(marker);
    setMarkerInfo({
      name: onMarkerprops.name,
    });
  };

  const setPointsFromMapData = () => {
    setPoints({
      title: mapdata.title,
      id: mapdata._id,
      lat:
        mapdata.country && mapdata.country.coordinates
          ? parseFloat(mapdata.country.coordinates[0].latitude)
          : null,
      lng:
        mapdata.country && mapdata.country.coordinates
          ? parseFloat(mapdata.country.coordinates[0].longitude)
          : null,
    });
  };

  useEffect(() => {
    setPointsFromMapData();
  }, [mapdata]);
  return (
    <>
      {" "}
      {points && points.id ? (
        <RKIMap1
          onClose={resetMarker}
          language={currentLang}
          initialCenter={{ lat: points.lat, lng: points.lng }}
          activeMarker={activeMarker}
          markerInfo={<MarkerInfo info={markerInfo} />}
        >
          <RKIMapMarker
            name={points.title}
            icon={{
              url: "/images/map-marker-white.svg",
            }}
            onClick={onMarkerClick}
            position={points}
          />
        </RKIMap1>
      ) : null}
    </>
  );
};

export default ShowMapContainer;
