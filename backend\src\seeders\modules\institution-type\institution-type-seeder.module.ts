//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { InstitutionTypeSeederService } from './institution-type-seeder.services';
// SCHEMAS
import { InstitutionTypeSchema } from 'src/schemas/institution_type.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'InstitutionType', schema: InstitutionTypeSchema }
      ]
    )
  ],
  providers: [InstitutionTypeSeederService],
  exports: [InstitutionTypeSeederService],
})

export class InstitutionTypeSeederModule { }
