//Import Library
import React, { useEffect, useState } from "react";

//Import services/components
import apiService from "../../services/apiService";
import CardPlaceholder from "./../../components/common/placeholders/CardPlaceholder";
import { useTranslation } from 'next-i18next';

const AboutUs = () => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language ? i18n.language : "en";
  const _initialVal = {
    title: "",
    description: "",
    pageCategory: "",
    images: "",
    isEnabled: true,
  };

  const [aboutUs, setAboutUs] = useState<any>(_initialVal);
  const [loading, setLoading] = useState(false);
  const createMarkup = (htmlContent: string) => {
    return { __html: htmlContent };
  };

  const landingPageParams = {
    query: { pageCategory: "" },
    sort: { title: "asc" },
    limit: "~",
  };

  const fetchAboutUs = async () => {
    const defautDesc =
      "The Robert Koch Institut is taking over the coordination of the “WHO AMR Surveillance and Quality Assessment Collaborating Centres Network” this autumn 2019. The network supports the World Health Organization (WHO) to reduce drug-resistant infections globally. It focuses on further developing the global antimicrobial resistance (AMR) surveillance system (GLASS), and promoting exchange and peer support between countries.";

    const pageCategoryId: any = await fetchPageCategory();
    setLoading(true);
    if (pageCategoryId.length > 0) {
      landingPageParams.query.pageCategory = pageCategoryId;
      const response = await apiService.get("/landingPage", landingPageParams);
      if (Array.isArray(response.data) && response.data.length > 0) {
        const landingPageData = response.data[response.data.length - 1];
        landingPageData.images =
          response &&
          landingPageData.images.length > 0 &&
          landingPageData.isEnabled === true
            ? landingPageData.images.map((item, _i) =>
                String(`${process.env.API_SERVER}/image/show/${item._id}`)
              )
            : "/images/logo.jpg";
            landingPageData.description =
          response &&
          landingPageData.description.length > 0 &&
          landingPageData.isEnabled === true
            ? landingPageData.description
            : defautDesc;
        setAboutUs(landingPageData);
        fetchPageCategory();
        setLoading(false);
      }
    }
  };

  const fetchPageCategory = async () => {
    const response = await apiService.get("/pagecategory", {
      query: { title: "AboutUs" },
    });
    if (response && response.data && response.data.length > 0) {
      const pageCategoryId = response.data[0]._id;
      return pageCategoryId;
    }
    return false;
  };

  useEffect(() => {
    fetchAboutUs();
  }, []);

  const desc = aboutUs.description.replace(/\&nbsp;/g, " ");

  return (
    <div className="aboutUs">
      {loading === true ? (
        <CardPlaceholder />
      ) : (
        <div>
          <img className="logoImg" src={aboutUs.images} alt="" />
          <div dangerouslySetInnerHTML={createMarkup(desc)}></div>{" "}
        </div>
      )}
    </div>
  );
};

export default AboutUs;
