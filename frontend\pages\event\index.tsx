//Import Library
import React, { useState } from "react";
import Link from 'next/link';
import Button from 'react-bootstrap/Button';
import { Container, Col, Row } from "react-bootstrap";

//Import services/components
import PageHeading from "../../components/common/PageHeading";
import EventsTable from "./EventsTable";
import ListMapContainer from "./ListMapcontainer";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import ContactUsModal from "../../components/layout/modals/contact-us";
import { canAddEvent } from "./permission";
import RegionsMultiCheckboxes from "../../components/common/RegionsMultiCheckboxes";

const Project = () => {
  const { t } = useTranslation('common');
  const [isContactModalEnbled, setContactModal] = useState(false);
  const [selectedRegions, setSelectedRegions] = useState(null);
  const [events, setEvents] = useState([]);
  const onSetModal = () => setContactModal(!isContactModalEnbled);

  const AddEventComponent = () => {
    return (
      <Link href='/event/[...routes]' as='/event/create' ><Button variant="secondary" size="sm">
          {t('addEvent')}
        </Button></Link>
    );
  }

  const CanAddEvent = canAddEvent(() => <AddEventComponent />);

  const regionHandler = (val: any) => {
    setSelectedRegions(val);
  }

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={12}>
          <PageHeading title={t('menu.events')} />
        </Col>
      </Row>
      <Row>
        <Col xs={12}>
          <ListMapContainer events={events} />
        </Col>
      </Row>
      <Row>
        <Col xs={12}>
          <RegionsMultiCheckboxes
            filtreg={(val) => regionHandler(val)}
            selectedRegions={[]}
            regionHandler={regionHandler}
          />
        </Col>
      </Row>
      <Row>
        <Col xs={12} className="ps-4">
          <Row>
            <Col>
              <CanAddEvent />
            </Col>
            <Col>
              <p className="m-0"><small>{t("Events.table.Doyouknowofaneventthatneedstobeadded")} <Button variant="link" size="sm" className="p-0 outlineButton" onClick={onSetModal}>{t("Events.table.Clickhere")}</Button><span>&nbsp;</span>{t("Events.table.toinformthePublicHealthIntelligenceTeam")}</small></p>
            </Col>
          </Row>
        </Col>
      </Row>
      <Row className="mt-1">
        <Col xs={12}>
          <EventsTable selectedRegions={selectedRegions} setEvents={setEvents} />
        </Col>
      </Row>
      <ContactUsModal
        show={isContactModalEnbled}
        onHide={onSetModal}
      />
    </Container>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Project;






