//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateFileCategoryDto } from './dto/create-file-category.dto';
import { UpdateFileCategoryDto } from './dto/update-file-category.dto';
import { FileCategoryService } from "./file-category.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('filecategory')
@UseGuards(SessionGuard)
export class FileCategoryController {

  constructor(
    private readonly _fileCategoryService: FileCategoryService
  ) { }

  @Post()
  create(@Body() createFileCategoryDto: CreateFileCategoryDto) {
    const resp = this._fileCategoryService.create(createFileCategoryDto);
    return resp;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._fileCategoryService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') fileCategoryId: string) {
    return this._fileCategoryService.get(fileCategoryId);
  }

  @Patch(':id')
  update(@Param('id') fileCategoryId: string, @Body() updateFileCategoryDto: UpdateFileCategoryDto) {
    const resp = this._fileCategoryService.update(fileCategoryId, updateFileCategoryDto);
    return resp;
  }

  @Delete(':id')
  remove(@Param('id') fileCategoryId: string) {
    const deletedData = this._fileCategoryService.delete(fileCategoryId);
    return deletedData;
  }
}
