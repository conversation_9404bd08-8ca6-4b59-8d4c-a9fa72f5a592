//Import Library
import { faPlus, faMinus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Card, Accordion } from "react-bootstrap";
import { useState } from "react";

//Import services/components
import Announcement from "./vspace_announcement/Announcement";
import { useTranslation } from 'next-i18next';


const AnnouncementsAccordian = (props: any) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return(
        <>
            <Accordion.Item eventKey="1">
              <Accordion.Header onClick={() => setSection(!section)}>
                <div className="cardTitle">{t("vspace.Announcements")}</div>
                <div className="cardArrow">
                  {section ? <FontAwesomeIcon icon={faMinus} color="#fff" /> :
                    <FontAwesomeIcon icon={faPlus} color="#fff" />}
                </div>
              </Accordion.Header>
              <Accordion.Body>
                <Announcement />
              </Accordion.Body>
            </Accordion.Item>
        </>
    )
};

export default AnnouncementsAccordian;