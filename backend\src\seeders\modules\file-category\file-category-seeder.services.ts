//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { FileCategory } from "../../data/file-category";
import { FileCategoryInterface } from "src/interfaces/file-category.interface";

/**
 * Service dealing with File category.
 *
 * @class
 */
@Injectable()
export class FileCategorySeederService {

  constructor(
    @InjectModel('FileCategory') private fileCategoryModel: Model<FileCategoryInterface>
  ) {}

  /**
   * Seed all File category.
   *
   * @function
   */
  create(): Array<Promise<FileCategoryInterface>> {
    return FileCategory.map(async (classif: any) => {
      return await this.fileCategoryModel
        .findOne({ title: classif.title })
        .exec()
        .then(async dbEventStatus => {
          // We check if a File category already exists.
          // If it does don't create a new one.
          if (dbEventStatus) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.fileCategoryModel.create(classif),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}