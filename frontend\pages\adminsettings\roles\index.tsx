//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import RoleTable from "./roleTable";
import { useTranslation } from 'next-i18next';


const RoleIndex = (_props) => {
  const { t } = useTranslation('common');
  return (
    <div>
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title="Roles" />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_role"
              >
              <Button variant="secondary" size="sm">
              {t("AddRole")}
            </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <RoleTable />
          </Col>
        </Row>
      </Container>
    </div>
  );
}
export default RoleIndex;