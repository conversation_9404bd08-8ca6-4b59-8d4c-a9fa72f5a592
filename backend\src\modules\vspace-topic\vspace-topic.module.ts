//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { VspaceTopicController } from './vspace-topic.controller';
import { VspaceTopicService } from './vspace-topic.service';
// SCHEMAS
import { VspaceTopicSchema } from '../../schemas/vspace_topic.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'VspaceTopic', schema: VspaceTopicSchema }
    ])
  ],
  controllers: [VspaceTopicController],
  providers: [VspaceTopicService],
})

export class VspaceTopicModule { }