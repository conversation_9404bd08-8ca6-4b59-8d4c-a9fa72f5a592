//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

//Import services/components
import { EventInterface } from '../../interfaces/event.interface';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { FlagService } from '../flag/flag.service';

const FindEvent = 'Could not find Event.';
@Injectable()
export class EventService {
  constructor(
    @InjectModel('Event') private eventModel: Model<EventInterface>,
    private readonly _flagService: FlagService,
  ) {}

  async create(createEventDto: CreateEventDto): Promise<EventInterface> {
    const createdEvent = new this.eventModel(createEventDto);
    return createdEvent.save();
  }

  async getTotalCount(filter: any) {
    let _filter = {};
    try {
      _filter = filter.query ? filter.query : {};
    } catch (e) {
    }
    return this.eventModel.count(_filter).exec();
  }

  async findAll(query): Promise<EventInterface[]> {
    const options = this.customusLab(query);

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};

    if (_filter.title) {
      _filter.title = _filter.title
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(`^${_filter.title}`, 'gi');
      _filter['title'] = regex;
    }
    if (options.sort && (options.sort.country || options.sort.hazard_type)) {
      const sortOrder = options.sort.country
        ? options.sort.country
        : options.sort.hazard_type;
      const sortNumber = sortOrder === 'asc' ? 1 : -1;
      const skipValue = (options.page - 1) * options.limit;
      const sortArray = options.sort.country
        ? { 'countryArray.title': sortNumber }
        : { 'hazardtypeArray.title': sortNumber };
      const list = await this.getSortedList(
        _filter,
        sortArray,
        skipValue,
        options.limit,
        options.page,
      );
      return list;
    } else {
      return this.eventModel.paginate(_filter, options);
    }
  }

  private customusLab(query: any) {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      populate: query.populate ? query.populate : '',
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
      collation: { locale: 'en' },
    };
    return options;
  }

  async getSortedList(filter, arr, skip, limit, page) {
    let filterType;
    const worldregionArray = [];
    filterType = this.worldRegion(filter, worldregionArray, filterType);
    const list = await this.eventModel.aggregate([
      {
        $match: filterType,
      },
      {
        $lookup: {
          from: 'countries',
          localField: 'country',
          foreignField: '_id',
          as: 'countryArray',
        },
      },
      { $unwind: '$countryArray' },
      { $set: { country: '$countryArray' } },
      {
        $lookup: {
          from: 'hazards',
          localField: 'hazard',
          foreignField: '_id',
          as: 'hazardArray',
        },
      },
      { $set: { hazard: '$hazardArray' } },
      {
        $lookup: {
          from: 'hazardtypes',
          localField: 'hazard_type',
          foreignField: '_id',
          as: 'hazardtypeArray',
        },
      },
      {
        $unwind: { path: '$hazardtypeArray', preserveNullAndEmptyArrays: true },
      },
      { $set: { hazard_type: '$hazardtypeArray' } },
      { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
      { $sort: arr },
      {
        $project: {
          _id: 1,
          country: {
            _id: 1,
            title: 1,
          },
          hazard: {
            $map: {
              input: '$hazard',
              as: 'hazard',
              in: {
                _id: '$$hazard._id',
                title: '$$hazard.title',
              },
            },
          },
          hazard_type: {
            title: 1,
            _id: 1,
          },
          id: 1,
          title: 1,
        },
      },
      {
        $facet: {
          totalData: [
            { $match: {} },
            { $limit: limit + skip },
            { $skip: skip },
          ],
          totalCount: [
            {
              $group: {
                _id: null,
                count: { $sum: 1 },
              },
            },
          ],
        },
      },
    ]);
    const totalCount = list[0].totalCount[0].count;
    const eventList: any = {};
    eventList.data = list[0].totalData;
    eventList.totalCount = totalCount;
    eventList.limit = limit;
    eventList.totalPages = Math.ceil(totalCount / limit);
    eventList.page = page;
    eventList.pagingCounter = page;
    eventList.hasNextPage = page !== eventList.totalPages;
    eventList.hasPrevPage = !(page === 1 && page === eventList.totalPages);
    eventList.prevPage =
      page === 1 && page === eventList.totalPages ? null : page - 1;
    eventList.nextPage = page === eventList.totalPages ? null : page + 1;
    return eventList;
  }
  worldRegion(filter: any, worldregionArray: any[], filterType: any) {
    if (filter['world_region']) {
      filter['world_region'].forEach((element) => {
        const val = element.value ? element.value : element;
        worldregionArray.push(new mongoose.Types.ObjectId(val));
      });
    }

    if (filter.hazard_type && filter.title) {
      filterType = {
        $and: [
          { hazard_type: new mongoose.Types.ObjectId(filter.hazard_type) },
          { world_region: { $in: worldregionArray } },
          { title: filter['title'] },
        ],
      };
    } else if (filter.hazard_type && !filter.title) {
      filterType = {
        $and: [
          { hazard_type: new mongoose.Types.ObjectId(filter.hazard_type) },
          { world_region: { $in: worldregionArray } },
        ],
      };
    } else if (filter.title) {
      filterType = filter.title
        ? {
            $and: [
              { title: filter['title'] },
              { world_region: { $in: worldregionArray } },
            ],
          }
        : {};
    } else {
      filterType = { world_region: { $in: worldregionArray } };
    }
    return filterType;
  }
  async get(eventId): Promise<EventInterface[]> {
    let _event;
    try {
      _event = await this.eventModel.findById(eventId).exec();
    } catch (error) {
      throw new NotFoundException(FindEvent);
    }
    if (!_event) {
      throw new NotFoundException(FindEvent);
    }
    return _event;
  }

  async update(eventId: any, updateEventDto: UpdateEventDto) {
    const getEventById: any = await this.eventModel.findById(eventId).exec();
    const updatedEvent = new this.eventModel(updateEventDto);
    try {
      Object.keys(updateEventDto).forEach((d) => {
        getEventById[d] = updatedEvent[d];
      });
      getEventById.updated_at = new Date();
      getEventById.save();
    } catch (e) {
      throw new NotFoundException('Could not update event.');
    }

    const languageCode = updateEventDto['language']
      ? updateEventDto['language']
      : 'en';

    await this._flagService.alertSubscribers(
      getEventById.title,
      getEventById._id,
      'event',
      languageCode,
    );
    return getEventById;
  }

  async delete(eventId: string) {
    const result = await this.eventModel.deleteOne({ _id: eventId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindEvent);
    }
  }
}
