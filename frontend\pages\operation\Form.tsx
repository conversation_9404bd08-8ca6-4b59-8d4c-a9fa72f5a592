//Import Library
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col, Tab, Tabs } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus } from "@fortawesome/free-solid-svg-icons";
import { MultiSelect } from "react-multi-select-component";
import _ from "lodash";
import moment from "moment";
import Link from "next/link";
import { TextInput, SelectGroup } from "../../components/common/FormValidation";
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";

import toast from 'react-hot-toast';
import Router from "next/router";

//Import services/components
import ReactDropZone from "../../components/common/ReactDropZone";
import apiService from "../../services/apiService";
import RKIDatePicker from "../../components/common/RKIDatePicker";
import VspaceModal from "../../components/common/VspaceModal";
import { useTranslation } from 'next-i18next';
import { EditorComponent } from "../../shared/quill-editor/quill-editor.component";

const OperationForm = (props: any) => {
    const { t, i18n } = useTranslation('common');
    const currentLang = i18n.language === "fr" ? "en" : i18n.language;
    const countrySearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };
    const titleSearch = currentLang ? `title.${currentLang}` : "title.en";
    const formRef = useRef(null);
    const buttonRef = useRef(null);
    const [groupVisibility, setGroupVisibility] = useState({
        invitesCountry: "",
        invitesRegion: [],
        invitesOrganisationType: "",
        invitesOrganisation: "",
        invitesExpertise: "",
        invitesNetWork: "",
        visibility: true,
    });

    const initialTabState = {
        institution: "",
        expertise: [],
        partner_expertise: [],
        organisation_status: "",
        work_description: "",
        status: "",
    };

    const [currLang] = useState<string>(titleSearch);
    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);
    const [srcCollection, setSrcCollection] = useState<any[]>([]);
    const [docSrcCollection, setDocSrcCollection] = useState<any[]>([]);
    const [docCollection, setDocCollection] = useState<any[]>([]);
    const [statusOperation, setOperationStatus] = useState<any[]>([]);
    const [countryList, setcountryList] = useState<any[]>([]);
    const [, setUsersList] = useState<any[]>([]);
    const [defaultActiveKey, setdefaultActiveKey] = useState<number>(1);
    const [partners, setPartners] = useState<any[]>([initialTabState]);
    const [hazardTypes, sethazardTypes] = useState<any[]>([]);
    const [regions, setRegion] = useState<any[]>([]);
    const [hazards, setHazard] = useState<any[]>([]);
    const [deploymentStatus, setDeploymentStatus] = useState<any[]>([]);
    const [syndrome, setSyndrome] = useState<any[]>([]);
    const [expertises, setExpertises] = useState<any[]>([]);
    const [organizationList, setOrganizationList] = useState<any[]>([]);
    const [virtualSpace, setVirtualSpace] = useState<boolean>(false);
    const [localeLang] = useState<string>(currentLang);
    const [modal, setModal] = useState<boolean>(false);
    const [resId, setResId] = useState<any>(null);
    const [form, setForm] = useState<any[]>([{ timetitle: "", iconclass: "", date: null }]);
    const [virtualSpaceAccessPermission, setVirtualSpaceAccessPermission] = useState<boolean>(true);
    const editform = props.routes && props.routes[0] === "edit" && props.routes[1];
    const _initialState = {
        title: "",
        country: "",
        world_region: "",
        region: [],
        status: " ",
        hazard_type: "",
        hazard: [],
        syndrome: null,
        active_tab: 1,
        start_date: null,
        TimeLineStartDate: new Date(),
        end_date: null,
        description: "",
        images: [],
        document: [],
        checked: false,
        images_src: [],
        doc_src: [],
    };

    const operationParams = {
        query: {},
        sort: countrySearch,
        limit: "~",
        languageCode: currentLang,
    };
    const [initialVal, setinitialVal] = useState<any>(_initialState);

    const tabAdd = () => {
        const _temp = [...partners];
        _temp.push({
            status: "",
            institution: "",
            expertise: [],
            organisation_status: "",
            work_description: "",
            partner_expertise: [],
        });
        setPartners(_temp);
        setdefaultActiveKey(_temp.length);
    };

    const removeTab = (_e, i: any) => {
        partners.splice(i, 1);
        const _temp = [...partners];
        setPartners(_temp);
        setdefaultActiveKey(_temp.length);
        if (partners.length === 0) {
            tabAdd();
        }
    };

    const handleTab = (e: any, i: any) => {
        if (e.target) {
            const { name, value } = e.target;
            partners[i][name] = value;
            if (name === "organisation_status") {
                partners[i]["status"] = value;
            }
        } else {
            partners[i].partner_expertise = e;
            partners[i].expertise = e.map((item: any) => {
                return item.value;
            });
        }
        setPartners([...partners]);
    };

    function handleDesTab(value: any, i: any) {
        const _tempCosts = [...partners];
        _tempCosts[i].work_description = value;
        setPartners(_tempCosts);
    }

    const handleEndDateCheckBox = () => {
        setinitialVal((prevState: any) => ({
            ...prevState,
            checked: !prevState.checked,
            end_date: null,
        }));
    };

    const getOperationInitialData = async (operationParamsinit: any) => {
        const operationStatus = await apiService.get("/operation_status", operationParamsinit);
        if (operationStatus && Array.isArray(operationStatus.data)) {
            setOperationStatus(operationStatus.data);
        } //operation_status

        const country = await apiService.get("/country", operationParamsinit);
        if (country && Array.isArray(country.data)) {
            setcountryList(country.data);
        } //country list

        const hazardtype = await apiService.get("/hazardtype", operationParamsinit);
        if (hazardtype && Array.isArray(hazardtype.data)) {
            sethazardTypes(hazardtype.data);
        } // hazardtype

        const syndromes = await apiService.get("/syndrome", operationParamsinit);
        if (syndromes && Array.isArray(syndromes.data)) {
            setSyndrome(syndromes.data);
        } //syndrome

        const institutionParams = operationParamsinit;
        institutionParams.select =
            "-contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website -telephone -twitter -header -use_default_header -images -status -email -user -created_at -updated_at -primary_focal_point";
        institutionParams.query.status = { $ne: "Request Pending" };
        const institutions = await apiService.get("/institution", institutionParams);
        if (institutions && Array.isArray(institutions.data)) {
            setOrganizationList(institutions.data);
        } // institution

        const expertise = await apiService.get("/expertise", operationParamsinit);
        if (expertise && Array.isArray(expertise.data)) {
            const _expertise = expertise.data.map((item: any, _i: any) => {
                return { label: item.title, value: item._id };
            });
            setExpertises(_expertise);
        }
        const deployment = await apiService.get("/deploymentstatus", operationParamsinit);
        if (deployment && Array.isArray(deployment.data)) {
            setDeploymentStatus(deployment.data);
        }
    };

    const getdoccollection = (response: any, normalizePartner: any, end_date: any) => {
        setDropZoneCollection(response.images ? response.images : []);
        setSrcCollection(response.images_src ? response.images_src : []);
        setDocCollection(response.document ? response.document : []);
        setDocSrcCollection(response.doc_src ? response.doc_src : []);
        setPartners(normalizePartner);
        getRegion(response.country); // update region based on the country
        getHazard(response.hazard_type); // update hazard based on the hazard type
        setinitialVal((prevState: any) => ({ ...prevState, ...response }));
        return end_date ? setinitialVal((prevState: any) => ({ ...prevState, checked: true })) : null;
    };

    const getnormalization = (item5: any, initRegions: any, _regionsId: any, normalizePartner: any) => {
        normalizePartner.push({
            institution: item5.institution && item5.institution._id,
            partner_expertise: initRegions,
            expertise: _regionsId,
            organisation_status: item5 && item5.status ? item5.status._id : "",
            status: item5 && item5.status ? item5.status._id : "",
            work_description: item5.work_description,
        });
    };

    const getresonse = (response: any) => {
        const { status, country, syndromes, region, hazard_type, hazard, timeline, start_date, end_date } = response;
        response.status = status && status._id ? status._id : " "; // status value
        response.start_date = start_date ? moment(start_date).toDate() : null;
        response.end_date = end_date ? moment(end_date).toDate() : null;
        response.country = country && country._id ? country._id : " ";
        response.syndrome = syndromes && syndromes._id ? syndromes._id : null;
        response.hazard_type = hazard_type && hazard_type._id ? hazard_type._id : "";
        response.hazard = hazard
            ? hazard.map((item1: any) => {
                  return { label: item1.title[localeLang], value: item1._id };
              })
            : [];
        response.region = region
            ? region.map((item2: any) => {
                  return { label: item2.title, value: item2._id };
              })
            : [];
        timeline && setForm(timeline);
    };

    const getOperationsData = async () => {
        const response = await apiService.get(`/operation/${props.routes[1]}`, operationParams);
        if (response) {
            const normalizePartner = [];
            const { end_date, partners } = response;
            getresonse(response);
            partners &&
                partners.forEach((item5: any) => {
                    const initRegions =
                        item5.expertise &&
                        item5.expertise.map((item3: any) => {
                            return { label: item3.title, value: item3._id };
                        });
                    const _regionsId =
                        item5.expertise &&
                        item5.expertise.map((item4: any) => {
                            return item4._id;
                        });
                    getnormalization(item5, initRegions, _regionsId, normalizePartner);
                });
            getdoccollection(response, normalizePartner, end_date);
        }
    };


    const getUserInformation = async () => {
        const currentUser = await apiService.post("/users/getLoggedUser", {});
        if (currentUser && currentUser['roles']) {
            const filteredRoles = currentUser['roles']?.filter((role: string) => (role == "EMT_NATIONAL_FOCALPOINT"));
            if (filteredRoles.length > 0) {
                setVirtualSpaceAccessPermission(false);
            } else {
                setVirtualSpaceAccessPermission(true);
            }
        }
    }

    useEffect(() => {
        /* Start Update data for form */
        if (editform) {
            getOperationsData();
        } /* End */

        // get the data from operation APi

        getOperationInitialData(operationParams);
        getUserInformation();
    }, []);

    const clearValue = (obj: any) => {
        setinitialVal((prevState: any) => ({
            ...prevState,
            ...obj,
        }));
        setGroupVisibility((prevState: any) => ({
            ...prevState,
            ...obj,
        }));
    };

    const countryRegion = async (id: any) => {
        let _regions: any[] = [];

        if (id) {
            const response = await apiService.get(`/country_region/${id}`, operationParams);
            if (response && response.data) {
                _regions = response.data.map((item: any, _i: any) => {
                    return { label: item.title, value: item._id };
                });
                _regions.sort((a: any, b: any) => a.label.localeCompare(b.label));
            }
        }
        return _regions;
    };

    const getRegion = async (id: any) => {
        setRegion(await countryRegion(id));
    };

    const getHazard = async (id: any) => {
        const regionsParams = {
            query: { enabled: true },
            sort: { [currLang]: "asc" },
            limit: "~",
            select: "-description -first_letter -hazard_type -picture -picture_source  -created_at -updated_at",
        };
        let _regions: any[] = [];
        if (id) {
            const response = await apiService.get(`/hazard_hazard_type/${id}`, regionsParams);
            if (response && response.data) {
                _regions = response.data.map((item: any) => ({
                    label: item.title[localeLang],
                    value: item._id,
                }));
            }
        }
        setHazard(_regions);
    };

    const addform = () => {
        const a = { timetitle: "", iconclass: "", date: null };
        setForm((oldArray: any) => [...oldArray, a]);
    };

    const removeForm = (_e: any, i: any) => {
        form.splice(i, 1);
        setForm([...form]);
        if (form.length === 0) {
            addform();
        }
    };

    const handleChange = (e: any) => {
        if (e.target) {
            const { name, value } = e.target;
            if (name === "country") {
                const selectedIndex = e.target.selectedIndex;
                if (e.target && selectedIndex && selectedIndex != null) {
                    const worldRegion = e.target[selectedIndex].getAttribute("data-worldregion");
                    setinitialVal((prevState: any) => ({
                        ...prevState,
                        world_region: worldRegion,
                    }));
                }
            }
            setinitialVal((prevState: any) => ({ ...prevState, [name]: value }));
            if (name === "country") {
                getRegion(value);
                clearValue({ region: [] });
            }
            if (name === "hazard_type") {
                getHazard(value);
                clearValue({ hazard: [] });
            }
        } else {
            setinitialVal((prevState: any) => ({ ...prevState, region: e }));
        }
    };

    React.useEffect(() => {
        if (groupVisibility) {
            const normalizeGroup: any = {};
            Object.keys(groupVisibility).forEach((item: any, _i: any) => {
                const _data: any[] =
                    (groupVisibility as any)[item].length > 0 &&
                    (groupVisibility as any)[item].map((d: any) => {
                        return d.value;
                    });
                normalizeGroup[item] = _data ? _data : [];
            });
            getUsers(normalizeGroup);
        }
    }, [groupVisibility]);

    const getUsers = async (normalizeGroup: any) => {
        const { invitesCountry, invitesOrganisation } = normalizeGroup;
        const groupParams = {
            query: {
                country: invitesCountry,
                institution: invitesOrganisation,
            },
        };
        const userInvites = await apiService.post("/user-invite", groupParams);
        if (userInvites && Array.isArray(userInvites)) {
            const _users = userInvites.map((item: any, _i: any) => {
                return { label: item.username, value: item._id };
            });
            setUsersList(_users);
        }
    };

    //******To Handle Group Visibility******//
    const bindHazard = (e: any) => {
        setinitialVal((prevState: any) => ({
            ...prevState,
            hazard: e,
        }));
    };

    const handleChangeforTimeline = (e: any, i: any) => {
        const { name, value } = e.target;
        const _tempCosts = [...form];
        _tempCosts[i][name] = value;
        setForm(_tempCosts);
    };

    const onChangeDate = (date: any, key: any) => {
        if ((key === "start_date" && date == null) || key === "start_date") {
            setinitialVal((prevState: any) => ({
                ...prevState,
                end_date: null,
                start_date: date,
            }));
        } else {
            setinitialVal((prevState: any) => ({
                ...prevState,
                [key]: date,
            }));
        }
    };

    const onChangeDateTimeline = (dateval: any, i: any) => {
        form[i].date = dateval;
        setForm([...form]);
    };

    const handleDescription = (value: any) => {
        setinitialVal((prevState: any) => ({
            ...prevState,
            description: value,
        }));
    };



    const handleErrorSubmit = (errors: any) => {
        let searchIndex = -1;
        let mapped: any[] = _.map(
            partners,
            _.partialRight(_.pick, ["institution", "expertise", "status", "work_description"])
        );
        let currentIndex = 0;
        for (const i in mapped) {
            if ((mapped[i] as any).institution === "") {
                searchIndex = currentIndex;
                break;
            }
            if ((mapped[i] as any).status === "") {
                searchIndex = currentIndex;
                break;
            }
            currentIndex++;
        }
        if (searchIndex > -1) {
            setdefaultActiveKey(searchIndex + 1);
            let elmnt = document.getElementById("btnAddForm");
            elmnt?.scrollIntoView();
        }
    };

    const handleSubmit = async (event?: any, values?: any, actions?: any) => {
        if (event && event.preventDefault) {
            event.preventDefault();
        }

        if (buttonRef.current) {
            buttonRef.current.setAttribute("disabled", "disabled");
        }

        const mapped = _.map(
            partners,
            _.partialRight(_.pick, ["institution", "expertise", "status", "work_description"])
        );
        const obj = {
            title: initialVal.title.trim(),
            description: initialVal.description,
            status: initialVal.status,
            start_date: initialVal.start_date,
            end_date: initialVal.end_date,
            country: initialVal.country,
            world_region: initialVal.world_region,
            syndrome: initialVal.syndrome,
            hazard_type: initialVal.hazard_type,
            hazard: initialVal.hazard
                ? initialVal.hazard.map((item, _i) => {
                      return item.value;
                  })
                : [],
            region: initialVal.region
                ? initialVal.region.map((item, _i) => {
                      return item.value;
                  })
                : [],
            timeline: form.length > 0 && form[0].timetitle !== "" ? form : [],
            partners: mapped,
            images: initialVal.images,
            images_src: initialVal.images_src,
            document: initialVal.document,
            doc_src: initialVal.doc_src,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "toast.Operationupdatedsuccessfully";
            response = await apiService.patch(`/operation/${props.routes[1]}`, obj);
        } else {
            toastMsg = "toast.Operationaddedsuccessfully";
            response = await apiService.post("/operation", obj);
        }
        if (response && response._id) {
            if (virtualSpace) {
                setResId(response?._id && response._id);
                setModal(true);
            } else {
                toast.success(t(toastMsg));
                Router.push("/operation/[...routes]", `/operation/show/${response._id}`);
            }
        } else {
            if (editform) { toastMsg = "toast.OperationNotupdatedsuccessfully";}
            else { toastMsg = "toast.OperationNotaddedsuccessfully"; }

            toast.error(t(toastMsg));
        }

        // Re-enable the submit button
        if (buttonRef.current) {
            buttonRef.current.removeAttribute("disabled");
        }
    };

    const resetHandler = () => {
        setinitialVal(_initialState);
        setDropZoneCollection([]);
        setSrcCollection([]);
        setDocCollection([]);
        setDocSrcCollection([]);
        setOrganizationList([]);
        setForm([]);
        setPartners([]);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const getID = (id: any) => {
        const imageIds: any[] = [];
        const docIds: any[] = [];
        if (id.length > 0) {
            id.map((item: any) => {
                if (
                    item.type &&
                    (item.type.includes("pdf") ||
                        item.type.includes("docx") ||
                        item.type.includes("xlsx") ||
                        item.type.includes("xls"))
                ) {
                    docIds.push(item.serverID);
                } else {
                    imageIds.push(item.serverID);
                }
            });
        }
        setinitialVal((prevState) => ({ ...prevState, images: imageIds }));
        setinitialVal((prevState) => ({ ...prevState, document: docIds }));
    };

    const getSource = (imgSrcArr: any) => {
        setinitialVal((prevState: any) => ({ ...prevState, images_src: imgSrcArr }));
    };

    const getDocSource = (docSrcArr: any) => {
        setinitialVal((prevState: any) => ({ ...prevState, doc_src: docSrcArr }));
    };

    return (
        <Container className="formCard" fluid>
            <Card>
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} onErrorSubmit={handleErrorSubmit} initialValues={initialVal} enableReinitialize={true}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>{editform ? t("editOperation") : t("addOperation")}</Card.Title>
                            </Col>
                        </Row>
                        <hr />
                        <Row className="mb-3">
                            <Col md lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("CountryOrTerritory")}</Form.Label>
                                    <SelectGroup
                                        name="country"
                                        id="country"
                                        value={initialVal.country}
                                        onChange={handleChange}
                                        required
                                        errorMessage={t("thisfieldisrequired")}
                                    >
                                        <option value="">{t("SelectCountry")}</option>
                                        {countryList.map((item, i) => {
                                            return (
                                                <option
                                                    key={i}
                                                    data-worldregion={item.world_region._id}
                                                    value={item._id}
                                                >
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col md lg={6} sm={12}>
                                <Form.Group style={{ maxWidth: "600px" }}>
                                    <Form.Label>{t("CountryRegions")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectRegions"),
                                            allItemsAreSelected: "All Regions are Selected",
                                        }}
                                        options={regions}
                                        value={initialVal.region}
                                        onChange={handleChange}
                                        className={"region"}
                                        labelledBy={"Select Country Regions"}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md lg={4} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("HazardType")}</Form.Label>
                                    <SelectGroup
                                        name="hazard_type"
                                        id="hazard_type"
                                        value={initialVal.hazard_type}
                                        onChange={handleChange}
                                        required
                                        errorMessage={t("thisfieldisrequired")}
                                    >
                                        <option value="">{t("SelectHazardType")}</option>
                                        {hazardTypes.map((item, i) => {
                                            return (
                                                <option key={i} value={item._id}>
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col md lg={4} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("Hazard")}</Form.Label>
                                    <Form.Group>
                                        <MultiSelect
                                            overrideStrings={{
                                                selectSomeItems: t("SelectHazard"),
                                                allItemsAreSelected: "All Hazards are Selected",
                                            }}
                                            options={hazards}
                                            value={initialVal.hazard}
                                            onChange={bindHazard}
                                            className={"hazard"}
                                            labelledBy={"Select Hazards"}
                                        />
                                    </Form.Group>
                                </Form.Group>
                            </Col>
                            <Col md lg={4} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("Syndrome")}</Form.Label>
                                    <SelectGroup
                                        name="syndrome"
                                        id="syndrome"
                                        value={initialVal.syndrome}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("SelectSyndrome")}</option>
                                        {syndrome.map((item, i) => (
                                            <option key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("Title")}</Form.Label>
                                    <TextInput
                                        name="title"
                                        id="title"
                                        required
                                        value={initialVal.title}
                                        validator={(value: any) => value.trim() !== ""}
                                        errorMessage={{
                                            validator: t("PleaseAddtheTitle"),
                                        }}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("Description")}</Form.Label>
                                    <EditorComponent initContent={initialVal.description} onChange={(evt) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            {statusOperation.length ? (
                            <Col md lg={3} sm={12}>
                                
                                    <Form.Group>
                                        <Form.Label className="required-field">{t("OperationStatus")}</Form.Label>

                                        <SelectGroup
                                            name="status"
                                            id="status"
                                            value={initialVal.status}
                                            onChange={handleChange}
                                            required
                                            errorMessage={t("thisfieldisrequired")}
                                        >
                                            <option value="">{t("SelectOperationStatus")}</option>
                                            {statusOperation.map((item, i) => {
                                                return (
                                                    <option key={i} value={item._id}>
                                                        {item.title}
                                                    </option>
                                                );
                                            })}
                                        </SelectGroup>
                                    </Form.Group>
                            </Col>) : null}
                            <Col md lg={3} sm={12}>
                                <Form.Group>
                                    <Row>
                                        <Col>
                                            <Form.Label>{t("OperationStartDate")}</Form.Label>
                                        </Col>
                                    </Row>
                                    <RKIDatePicker
                                        selected={initialVal.start_date}
                                        onChange={(date: any) => onChangeDate(date, "start_date")}
                                        dateFormat="MMMM d, yyyy"
                                        placeholderText={t("SelectStartDate")}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md lg={2} sm={12}>
                                <Form.Check
                                    type="checkbox"
                                    checked={initialVal.checked}
                                    onChange={handleEndDateCheckBox}
                                    label={t("ShowEndDate")}
                                />
                            </Col>
                            {initialVal.checked && (
                                <Col md lg={3} sm={12}>
                                    <Form.Group>
                                        <Row>
                                            <Col>
                                                <Form.Label>{t("OperationEndDate")}</Form.Label>
                                            </Col>
                                        </Row>
                                        <RKIDatePicker
                                            selected={initialVal.end_date}
                                            disabled={!initialVal.start_date}
                                            onChange={(date: any) => onChangeDate(date, "end_date")}
                                            dateFormat="MMMM d, yyyy"
                                            minDate={initialVal.start_date}
                                            placeholderText={t("SelectEndDate")}
                                        />
                                    </Form.Group>
                                </Col>
                            )}
                        </Row>
                        <Card.Text>
                            <b>{t("Timeline")}</b>
                        </Card.Text>
                        <hr />
                        {form.map((item, i) => {
                            return (
                                <div key={i}>
                                    <Row>
                                        <Col md={4}>
                                            <Form.Group>
                                                <Form.Label>{t("Title")}</Form.Label>
                                                <TextInput
                                                    name="timetitle"
                                                    id="timetitle"
                                                    value={item.timetitle}
                                                    onChange={(e: any) => handleChangeforTimeline(e, i)}
                                                />
                                            </Form.Group>
                                        </Col>
                                        <Col md={3}>
                                            <Form.Group>
                                                <Form.Label>{t("IconClass")}</Form.Label>
                                                <SelectGroup
                                                    name="iconclass"
                                                    id="iconclass"
                                                    value={item.iconclass}
                                                    onChange={(e: any) => handleChangeforTimeline(e, i)}
                                                >
                                                    <option value="-1">-Select-</option>
                                                    <option value="1">RFA</option>
                                                    <option value="2">Alert</option>
                                                    <option value="3">Mission to Country</option>
                                                    <option value="4">Calendar Event</option>
                                                    <option value="5">Documents</option>
                                                    <option value="6">Meeting</option>
                                                    <option value="7">Others</option>
                                                </SelectGroup>
                                            </Form.Group>
                                        </Col>
                                        <Col md={3}>
                                            <Form.Group>
                                                <Row>
                                                    <Col>
                                                        <Form.Label>{t("StartDate")}</Form.Label>
                                                    </Col>
                                                </Row>
                                                <RKIDatePicker
                                                    selected={item.date ? moment(item.date).toDate() : null}
                                                    onChange={(date: any) => onChangeDateTimeline(date, i)}
                                                    dateFormat="MMMM d, yyyy"
                                                    placeholderText={t("SelectStartDate")}
                                                />
                                            </Form.Group>
                                        </Col>
                                        <Col md={2} className="text-md-center">
                                            <Form.Group>
                                                {i === 0 ? (
                                                    <div></div>
                                                ) : (
                                                    <Button
                                                        variant="secondary"
                                                        style={{ marginTop: "30px" }}
                                                        onClick={(e: any) => removeForm(e, i)}
                                                    >
                                                        {t("Remove")}
                                                    </Button>
                                                )}
                                            </Form.Group>
                                        </Col>
                                    </Row>
                                </div>
                            );
                        })}
                        <Row>
                            <Col md lg="4">
                                <Button
                                    id="btnAddForm"
                                    variant="secondary"
                                    style={{ marginTop: "27px", marginBottom: "20px" }}
                                    onClick={addform}
                                >
                                    {t("ADDANOTHERITEM")}
                                </Button>
                            </Col>
                        </Row>
                        <Row className="mt-1">
                            <Col>
                                <Card.Text>
                                    <b>{t("AddOrganisation(s)")}</b>
                                </Card.Text>
                                <hr />
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Tabs
                                        activeKey={defaultActiveKey}
                                        onSelect={(k: any) => setdefaultActiveKey(k)}
                                        id="uncontrolled-tab-example"
                                    >
                                        {" "}
                                        {partners.map((item: any, i: number) => {
                                            return (
                                                <Tab key={i} eventKey={`${i + 1}`} title={`Organisation ${i + 1}`}>
                                                    <Row className="mb-3">
                                                        <Col md={4}>
                                                            <Form.Group style={{ paddingTop: "20px" }}>
                                                                <Form.Label className="required-field">
                                                                    {t("NameofAssociatedOrganisation")}
                                                                </Form.Label>
                                                                <SelectGroup
                                                                    name="institution"
                                                                    id="institution"
                                                                    value={item.institution}
                                                                    onChange={(e: any) => handleTab(e, i)}
                                                                    required
                                                                    errorMessage={t("thisfieldisrequired")}
                                                                >
                                                                    <option value="">{t("SelectOrganisation")}</option>
                                                                    {organizationList.map((item_o, _i) => {
                                                                        return (
                                                                            <option key={_i} value={item_o._id}>
                                                                                {item_o.title}
                                                                            </option>
                                                                        );
                                                                    })}
                                                                </SelectGroup>
                                                            </Form.Group>
                                                        </Col>
                                                        <Col md={4}>
                                                            <Form.Group
                                                                style={{
                                                                    paddingTop: "20px",
                                                                    maxWidth: "400px",
                                                                }}
                                                            >
                                                                <Form.Label>{t("WorkingExpertise")}</Form.Label>
                                                                <MultiSelect
                                                                    overrideStrings={{
                                                                        selectSomeItems: t("ChooseExpertise"),
                                                                        allItemsAreSelected:
                                                                            "All Expertise are Selected",
                                                                    }}
                                                                    onChange={(e: any) => handleTab(e, i)}
                                                                    options={expertises}
                                                                    value={item.partner_expertise}
                                                                    className={"work-expert"}
                                                                    labelledBy={"Select Expertise"}
                                                                />
                                                            </Form.Group>
                                                        </Col>
                                                        <Col md={4}>
                                                            <Form.Group style={{ paddingTop: "20px" }}>
                                                                <Form.Label className="required-field">
                                                                    {t("Status")}
                                                                </Form.Label>
                                                                <SelectGroup
                                                                    name="organisation_status"
                                                                    id="organisation_status"
                                                                    value={item.status}
                                                                    onChange={(e: any) => handleTab(e, i)}
                                                                    required
                                                                    errorMessage={t("thisfieldisrequired")}
                                                                >
                                                                    <option value="">{t("SelectStatus")}</option>
                                                                    {deploymentStatus.map((item_d, _i) => (
                                                                        <option key={_i} value={item_d._id}>
                                                                            {item_d.title}
                                                                        </option>
                                                                    ))}
                                                                </SelectGroup>
                                                            </Form.Group>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Col>
                                                            <Form.Group>
                                                                <Form.Label>{t("Description")}</Form.Label>
                                                                <EditorComponent initContent={item.work_description} onChange={(evt) => handleDesTab(evt, i)} />
                                                            </Form.Group>
                                                        </Col>
                                                    </Row>
                                                    <div>
                                                        {i === 0 ? (
                                                            <span></span>
                                                        ) : (
                                                            <Col xs lg="4">
                                                                <Button
                                                                    onSelect={(k: any) => setdefaultActiveKey(k)}
                                                                    variant="secondary"
                                                                    onClick={(e: any) => removeTab(e, i)}
                                                                >
                                                                    {t("Remove")}
                                                                </Button>
                                                            </Col>
                                                        )}
                                                    </div>
                                                </Tab>
                                            );
                                        })}
                                        <Tab
                                            title={
                                                <div>
                                                    <span onClick={tabAdd}>
                                                        <FontAwesomeIcon icon={faPlus} color="#808080" />
                                                    </span>
                                                </div>
                                            }
                                        ></Tab>
                                    </Tabs>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Col className="px-0">
                            <Card.Text>
                                <b>{t("MediaGallery")}</b>
                            </Card.Text>
                            <hr />
                        </Col>
                        <Row>
                            <Col>
                                <ReactDropZone
                                    datas={dropZoneCollection}
                                    srcText={srcCollection}
                                    getImgID={(id: any) => getID(id)}
                                    getImageSource={(imgSrcArr: any) => getSource(imgSrcArr)}
                                />
                            </Col>
                        </Row>
                        <Col className="px-0 mt-4">
                            <Card.Text>
                                <b>{t("Documents")}</b>
                            </Card.Text>
                            <hr />
                        </Col>
                        <Row>
                            <Col>
                                <ReactDropZone
                                    type="application"
                                    datas={docCollection}
                                    srcText={docSrcCollection}
                                    getImgID={(id: any) => getID(id)}
                                    getImageSource={(docSrcArr: any) => getDocSource(docSrcArr)}
                                />
                            </Col>
                        </Row>
                        <Row className="mt-4">
                            <Col>
                                <Card.Text>
                                    <b>{t("VirtualSpace")}</b>
                                </Card.Text>
                                <hr />
                                <Form.Check
                                    className="p-0"
                                    type="checkbox"
                                    disabled={!virtualSpaceAccessPermission}
                                    onChange={() => setVirtualSpace(!virtualSpace)}
                                    name="virtula"
                                    checked={virtualSpace}
                                    label={t("WouldliketocreateaVirtualSpace")}
                                />
                            </Col>
                        </Row>
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary" ref={buttonRef} onClick={handleSubmit}>
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("reset")}
                                </Button>
                                <Link href="/operation" as="/operation" >
                                    <Button variant="secondary">{t("Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
            {modal && <VspaceModal type="Operation" id={resId} />}
        </Container>
    );
};

export default OperationForm;
