//Import Library
import {Controller, Get, Request, Post, Body, Req, NotFoundException} from '@nestjs/common';

//Import services/components
import {UsersService} from "../../users/users.service";
const SECRETKEY = process.env.CRYPTO_SECRETKEY;
const Cryptr = require('cryptr'),
  cryptr = new Cryptr(SECRETKEY);

@Controller()
export class AnonymousController {
  constructor(
    private readonly _usersService: UsersService,
  ) { }
  
  @Post("userLinkValidate")
  async userLinkValidate(@Body() obj: any, @Req() request: Request) {
    const code = obj?.code ? obj.code : '';
    const query = {
      emailActivateToken: code
    }
    const _userDetails = await this._usersService.findOne(query);
    if (_userDetails) {
      const resp = await this._usersService.linkValidate(_userDetails['_id']);
      return resp;
    }
    return false;
  }

  @Post("saveInviteUserDetails")
  async saveInviteUserDetails(@Body() obj: any, @Req() request: Request) {
    const code = obj?.code ? obj.code : '';
    const resp = await this._usersService.saveUser(code, obj);
    return resp;
  }

  @Post("deleteUser")
  async deleteUser(@Body() obj: any, @Req() request: Request) {
    const code = obj?.code ? obj.code : '';
    try {
      const query = {
        emailActivateToken: code
      }
      const _userDetails = await this._usersService.findOne(query);
      if (_userDetails) {
        const resp = await this._usersService.delete(_userDetails['_id']);
        return resp;
      }
    } catch (e) {
      throw new NotFoundException('could not delete user.');
    }
  }

  @Get("encryptUsers")

  async encryptUsers(@Req() request: Request) {
    const query = {};
    const resp = await this._usersService.findAll(query);
    const data = await this._usersService.userEncrypt(resp);
    return data;
  }
}