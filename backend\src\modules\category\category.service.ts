//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { CategoryInterface } from '../../interfaces/category.interface';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
const  FindCategory = 'Could not find Category.'
@Injectable()
export class CategoryService {
  constructor(
    @InjectModel('Category') private categoryModel: Model<CategoryInterface>
  ) { }

  async create(createCategoryDto: CreateCategoryDto): Promise<CategoryInterface> {
    const createdCategory = new this.categoryModel(createCategoryDto);
    return createdCategory.save();
  }

  async getTotalCount(filter: any) {
    let _filter = {};
    try {
      _filter = filter.query ? filter.query : {};
    } catch (e) {
    }
    return this.categoryModel.count(_filter).exec();
  }

  async findAll(query): Promise<CategoryInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.categoryModel.paginate(_filter, options);
  }

  async get(categoryId): Promise<CategoryInterface[]> {
    let _result;
    try {
      _result = await this.categoryModel.findById(categoryId).exec();
    } catch (error) {
      throw new NotFoundException(FindCategory);
    }
    if (!_result) {
      throw new NotFoundException(FindCategory);
    }
    return _result;
  }

  async update(categoryId: any, updateCategoryDto: UpdateCategoryDto) {
    const getById: any = await this.categoryModel.findById(categoryId).exec();
    const updatedData = new this.categoryModel(updateCategoryDto);
    try {
      Object.keys(updateCategoryDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException(FindCategory);
    }
    return getById;
  }

  async delete(categoryId: string) {
    const result = await this.categoryModel.deleteOne({ _id: categoryId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindCategory);
    }
  }
}
