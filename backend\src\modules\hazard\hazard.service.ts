//Import Library
import { PaginateModel, Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

//Import services/components
import { CreateHazardDto } from './dto/create-hazard.dto';
import { UpdateHazardDto } from './dto/update-hazard.dto';
import { HazardInterface } from 'src/interfaces/hazard.interface';
import { EventInterface } from 'src/interfaces/event.interface';
import { OperationInterface } from 'src/interfaces/operation.interface';
import { OperationStatusInterface } from 'src/interfaces/operation-status.interface';
import { InstitutionInterface } from 'src/interfaces/institution.interface';
import { UpdateInterface } from 'src/interfaces/update.interface';

const ArrayIndex = '$arrayIndex';
const FindDocument = 'Could not find Document.';
const FindHazard = 'Could not find hazard.';
@Injectable()
export class HazardService {
  constructor(
    @InjectModel('Hazard') private hazardModel: PaginateModel<HazardInterface>,
    @InjectModel('Event') private eventModel: Model<EventInterface>,
    @InjectModel('EventStatus') private eventStatusModel: Model<EventInterface>,
    @InjectModel('Operation') private operationModel: Model<OperationInterface>,
    @InjectModel('OperationStatus')
    private operationStatusModel: Model<OperationStatusInterface>,
    @InjectModel('Update') private updateModel: Model<UpdateInterface>,
    @InjectModel('Institution')
    private institutionModal: Model<InstitutionInterface>,
  ) {}

  async create(createHazardDto: CreateHazardDto): Promise<HazardInterface> {
    const createdHazard = new this.hazardModel(createHazardDto);
    return createdHazard.save();
  }

  async findAll(query): Promise<HazardInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options: any = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.skip) {
      options.offset = query.skip ? Number(query.skip) : 0;
    }

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    this.filterTitle(_filter);

    return this.hazardModel.paginate(_filter, options);
  }

  private filterTitle(_filter: any) {
    if (
      Object.entries(_filter).length > 0 &&
      (_filter['title.en'] || _filter['title.de'] || _filter['title.fr'])
    ) {
      let filterTitle =
        _filter['title.en'] || _filter['title.de'] || _filter['title.fr'];
      filterTitle = filterTitle
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(`^${filterTitle}`, 'gi');
      if (_filter['title.fr']) {
        _filter['title.fr'] = regex;
      } else if (_filter['title.de']) {
        _filter['title.de'] = regex;
      } else {
        _filter['title.en'] = regex;
      }
    }
  }

  async getSortedDocList(hazardId, query): Promise<OperationInterface[]> {
    let _result = [];
    try {
      const options = {
        sort: query.sort ? query.sort : {},
        collation: query.collation ? query.collation : 'en',

        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
      };
      this.optionNumber(query, options);
      if (options.sort.document_title || options.sort.doc_created_at) {
        const { searchID, sortArray, limit, skip, collation_key } =
          this.collationHazard(options, hazardId);
        const list = await this.operationModel
          .aggregate([
            { $match: { hazard: { $in: [searchID] } } },
            {
              $unwind: {
                path: '$document',
                preserveNullAndEmptyArrays: true,
                includeArrayIndex: 'arrayIndex',
              },
            },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            {
              $lookup: {
                from: 'images',
                localField: 'images',
                foreignField: '_id',
                as: 'imageArray',
              },
            },
            { $set: { 'docArray.index': ArrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', ArrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $set: { images: '$imageArray' } },
            { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
                images: 1,
                images_src: 1,
              },
            },
            {
              $facet: {
                totalData: [
                  { $match: {} },
                  { $limit: limit + skip },
                  { $skip: skip },
                ],
                totalCount: [
                  {
                    $group: {
                      _id: null,
                      count: { $sum: 1 },
                    },
                  },
                ],
              },
            },
          ])
          .collation({ locale: collation_key, strength: 1 })
          .exec();
        const DocList: any = this.docListhazard(list, limit, options);
        _result = DocList;
      }
    } catch (error) {
      throw new NotFoundException(FindDocument);
    }
    if (!_result) {
      throw new NotFoundException(FindDocument);
    }
    return _result;
  }

  private collationHazard(
    options: { sort: any; collation: any; page: number; limit: number },
    hazardId: any,
  ) {
    const collation_key = options.collation ?? 'en';
    const sortOrder =
      options.sort.document_title ?? options.sort.doc_created_at;
    const sortNumber = sortOrder === 'asc' ? 1 : -1;
    const sortArray = options.sort.document_title
      ? { 'docArray.original_name': sortNumber }
      : { 'docArray.created_at': sortNumber };
    const searchID = new mongoose.Types.ObjectId(hazardId);
    const skip = (options.page - 1) * options.limit;
    const limit = options.limit;
    return { searchID, sortArray, limit, skip, collation_key };
  }

  private optionNumber(
    query: any,
    options: { sort: any; collation: any; page: number; limit: number },
  ) {
    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }
  }

  private docListhazard(
    list: any,
    limit: number,
    options: { sort: any; collation: any; page: number; limit: number },
  ) {
    const totalCount = list[0].totalCount[0].count;
    const DocList: any = {};
    DocList.data = list[0].totalData;
    DocList.totalCount = totalCount;
    DocList.limit = limit;
    DocList.totalPages = Math.ceil(totalCount / limit);
    DocList.page = options.page;
    DocList.pagingCounter = options.page;
    DocList.hasNextPage = options.page !== DocList.totalPages;
    DocList.hasPrevPage = !(
      options.page === 1 && options.page === DocList.totalPages
    );
    DocList.prevPage =
      options.page === 1 && options.page === DocList.totalPages
        ? null
        : options.page - 1;
    DocList.nextPage =
      options.page === DocList.totalPages ? null : options.page + 1;
    return DocList;
  }

  async getSortedUpdateDocList(hazardId, query): Promise<OperationInterface[]> {
    let _result = [];
    try {
      const options = {
        sort: query.sort ? query.sort : {},
        collation: query.collation ? query.collation : 'en',
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
      };
      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }
      if (options.sort.document_title || options.sort.doc_created_at) {
        const { searchID, sortArray, limitnum, skip, collation_key } =
          this.optionHazard(
            options.sort,
            options.collation,
            options.page,
            options.limit,
            hazardId,
          );
        const list = await this.updateModel
          .aggregate([
            { $match: { parent_hazard: searchID } },
            {
              $unwind: {
                path: '$document',
                preserveNullAndEmptyArrays: true,
                includeArrayIndex: 'arrayIndex',
              },
            },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            { $set: { 'docArray.index': ArrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', ArrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
              },
            },
            {
              $facet: {
                totalData: [
                  { $match: {} },
                  { $limit: limitnum + skip },
                  { $skip: skip },
                ],
                totalCount: [
                  {
                    $group: {
                      _id: null,
                      count: { $sum: 1 },
                    },
                  },
                ],
              },
            },
          ])
          .collation({ locale: collation_key, strength: 1 })
          .exec();
        const DocList: any = this.docListtotal(
          list,
          limitnum,
          options.page,
          options.limit,
        );
        _result = DocList;
      }
    } catch (error) {
      throw new NotFoundException(FindDocument);
    }
    if (!_result) {
      throw new NotFoundException(FindDocument);
    }
    return _result;
  }

  private optionHazard(
    sort: any,
    collation: any,
    page: number,
    limit: number,
    hazardId: any,
  ) {
    const collation_key = collation ?? 'en';
    const sortOrder = sort.document_title ?? sort.doc_created_at;
    const sortNumber = sortOrder === 'asc' ? 1 : -1;
    const sortArray = sort.document_title
      ? { 'docArray.original_name': sortNumber }
      : { 'docArray.created_at': sortNumber };
    const searchID = new mongoose.Types.ObjectId(hazardId);
    const skip = (page - 1) * limit;
    const limitnum = limit;
    return { searchID, sortArray, limitnum, skip, collation_key };
  }

  private docListtotal(
    list: any,
    limitnum: number,
    page: number,
    limit: number,
  ) {
    const totalCount = list[0].totalCount[0].count;
    const DocList: any = {};
    DocList.data = list[0].totalData;
    DocList.totalCount = totalCount;
    DocList.limitnum = limit;
    DocList.totalPages = Math.ceil(totalCount / limitnum);
    DocList.page = page;
    DocList.pagingCounter = page;
    DocList.hasNextPage = page !== DocList.totalPages;
    DocList.hasPrevPage = !(page === 1 && page === DocList.totalPages);
    DocList.prevPage =
      page === 1 && page === DocList.totalPages ? null : page - 1;
    DocList.nextPage = page === DocList.totalPages ? null : page + 1;
    return DocList;
  }

  async get(hazardId): Promise<HazardInterface[]> {
    let _hazard;
    try {
      _hazard = await this.hazardModel.findById(hazardId).exec();
    } catch (error) {
      throw new NotFoundException(FindHazard);
    }
    if (!_hazard) {
      throw new NotFoundException(FindHazard);
    }
    return _hazard;
  }

  async update(hazardId: any, updateHazardDto: UpdateHazardDto) {
    const getHazardById: any = await this.hazardModel.findById(hazardId).exec();
    Object.keys(updateHazardDto).forEach((d) => {
      getHazardById[d] = updateHazardDto[d];
    });
    getHazardById.updated_at = new Date();
    return getHazardById.save();
  }

  async delete(hazardId: string) {
    const result = await this.hazardModel
      .findOneAndDelete({ _id: hazardId })
      .exec();
    if (!result) {
      throw new NotFoundException(FindHazard);
    }
    return result;
  }

  async getInstitutions(hazardId: string, query: any) {
    let institutions;
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? Number(query.select) : 0,
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    try {
      const _filter = { $or: [{ hazards: { $in: [hazardId] } }] };

      institutions = await this.institutionModal.paginate(_filter, options);
    } catch (error) {
      throw new NotFoundException('Could not find Insitutions.', error);
    }
    if (!institutions) {
      throw new NotFoundException('Could not find Insitutions.');
    }
    return institutions;
  }

  async getEvents(hazardId: string, status: string, query: any) {
    let institutions;
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    try {
      const _eventStatusId = await this.eventStatusModel
        .findOne({ title: status })
        .exec();
      const _filter = {
        $and: [{ hazard: { $in: [hazardId] } }, { status: _eventStatusId._id }],
      };

      institutions = await this.eventModel.paginate(_filter, options);
    } catch (error) {
      throw new NotFoundException('Could not find Events.', error);
    }
    if (!institutions) {
      throw new NotFoundException('Could not find Events.');
    }
    return institutions;
  }

  async getOperations(hazardId: string, status: string, query: any) {
    let operations;
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    try {
      const _operationStatusIds = [];
      const _operationStatus = await this.operationStatusModel.find({}).exec();
      await _operationStatus.map((e) => {
        if (e.title === 'Ongoing') {
          _operationStatusIds.push(e._id);
        }
      });
      const _filter = {
        hazard: { $in: [hazardId] },
        status: { $in: _operationStatusIds },
      };
      operations = await this.operationModel.paginate(_filter, options);
    } catch (error) {
      throw new NotFoundException('Could not find Operations.', error);
    }
    if (!operations) {
      throw new NotFoundException('Could not find Operations.');
    }
    return operations;
  }
}
