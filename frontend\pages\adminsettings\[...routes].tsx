//Import Library
import { useRouter } from 'next/router';

//Import services/components
import FocalPointShow from './approval/focal_point_appoval';
import VirtualSpaceShow from './approval/vspace_appoval'
import InstitutionApproval from './approval/institution_approval';
import CountryIndex from './country';
import CountryForm from './country/form';
import RegionForm from './region/form';
import RegionIndex from './region';
import HazardIndex from './hazard';
import HazardForm from './hazard/forms';
import HazardTypeIndex from './hazardtypes';
import HazardTypeForm from './hazardtypes/forms';
import UpdateTypeIndex from './updateType';
import UpdateTypeForm from './updateType/forms';
import AreaOfWorkIndex from './areaOfWork';
import AreaOfWorkForm from './areaOfWork/forms';
import SyndromeIndex from './syndrome';
import SyndromeForm from './syndrome/form';
import CategoryIndex from './categories';
import CategoryForm from './categories/form';
import DeploymentstatusIndex from './deploymentstatus';
import DeploymentstatusForm from './deploymentstatus/form';
import ExpertiseIndex from './expertise';
import ExpertiseForm from './expertise/form';
import RisklevelIndex from './risklevel';
import RisklevelForm from './risklevel/form';
import RoleIndex from './roles';
import RoleForm from './roles/form';
import Content from './content';
import MailSettingsForm from './mailsettings/form';
import WorldregionIndex from './worldregion';
import WorldregionForm from './worldregion/form';
import InstitutionTypeIndex from './institutiontypes';
import InstitutionTypeForm from './institutiontypes/form';
import InstitutionNetworkIndex from './institutionNetworks';
import InstitutionNetworkForm from './institutionNetworks/form';
import EventstatusForm from './eventstatuses/form';
import EventstatusIndex from './eventstatuses';
import OperationstatusIndex from './operationstatuses';
import OperationstatusForm from './operationstatuses/form';
import ProjectstatusIndex from './projectstatuses';
import ProjectstatusForm from './projectstatuses/form';
import UserIndex from './user';
import UserForm from './user/forms';
import LandingPageForm from './landingPage/form'
import LandingPageIndex from './landingPage';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Router = () => {
  const router = useRouter();
  const routes: any = router.query.routes || [];


  switch (routes[0]) {
    case "focal_point":
      return <FocalPointShow routes={routes} />;

    case "Vspace_point":
        return <VirtualSpaceShow routes={routes} />;

    case "institution_approval":
      return <InstitutionApproval routes={routes} />;

    case "country":
      return <CountryIndex routes={routes} />;

    case "create_country":
    case "edit_country":
      return <CountryForm routes={routes} />;

    case "hazard":
      return <HazardIndex routes={routes} />;

    case "create_hazard":
    case "edit_hazard":
      return <HazardForm routes={routes} />;

    case "hazardTypes":
      return <HazardTypeIndex routes={routes} />;

    case "create_hazard_types":
    case "edit_hazard_types":
      return <HazardTypeForm routes={routes} />;

    case "region":
      return <RegionIndex routes={routes} />;

    case "create_region":
    case "edit_region":
      return <RegionForm routes={routes} />;

    case "update_type":
      return <UpdateTypeIndex routes={routes} />;

    case "create_update_type":
    case "edit_update_type":
      return <UpdateTypeForm routes={routes} />;

    case "area_of_work":
      return <AreaOfWorkIndex routes={routes} />;

    case "create_area_of_work":
    case "edit_area_of_work":
      return <AreaOfWorkForm routes={routes} />;

    case "syndrome":
      return <SyndromeIndex routes={routes} />;

    case "create_syndrome":
    case "edit_syndrome":
      return <SyndromeForm routes={routes} />;

    case "landing":
      return <LandingPageIndex routes={routes} />;

    case "create_landing":
    case "edit_landing":
      return <LandingPageForm routes={routes} />;

    case "category":
      return <CategoryIndex routes={routes} />;

    case "create_category":
    case "edit_category":
      return <CategoryForm routes={routes} />;

    case "deploymentstatus":
      return <DeploymentstatusIndex routes={routes} />;

    case "create_deploymentstatus":
    case "edit_deploymentstatus":
      return <DeploymentstatusForm routes={routes} />;

    case "eventstatus":
      return <EventstatusIndex routes={routes} />;

    case "create_eventstatus":
    case "edit_eventstatus":
      return <EventstatusForm routes={routes} />;

    case "operationstatus":
      return <OperationstatusIndex routes={routes} />;

    case "create_operationstatus":
    case "edit_operationstatus":
      return <OperationstatusForm routes={routes} />;

    case "projectstatus":
      return <ProjectstatusIndex routes={routes} />;

    case "create_projectstatus":
    case "edit_projectstatus":
      return <ProjectstatusForm routes={routes} />;

    case "expertise":
      return <ExpertiseIndex routes={routes} />;

    case "create_expertise":
    case "edit_expertise":
      return <ExpertiseForm routes={routes} />;

    case "risklevel":
      return <RisklevelIndex routes={routes} />;

    case "create_risklevel":
    case "edit_risklevel":
      return <RisklevelForm routes={routes} />;

    case "role":
      return <RoleIndex routes={routes} />;

    case "create_role":
    case "edit_role":
      return <RoleForm routes={routes} />;

    case "users":
      return <UserIndex routes={routes} />;

    case "create_user":
    case "edit_user":
      return <UserForm routes={routes} />;

    case "smtp":
      return <MailSettingsForm routes={routes} />;

    case "worldregion":
      return <WorldregionIndex routes={routes} />;

    case "create_worldregion":
    case "edit_worldregion":
      return <WorldregionForm routes={routes} />;

    case "institution_type":
      return <InstitutionTypeIndex routes={routes} />;

    case "create_institution_type":
    case "edit_institution_type":
      return <InstitutionTypeForm routes={routes} />;

    case "institution_network":
      return <InstitutionNetworkIndex routes={routes} />;

    case "create_institution_network":
    case "edit_institution_network":
      return <InstitutionNetworkForm routes={routes} />;

    case "content":
      return <Content routes={routes} />;

    default:
      return null;
  }
};

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Router;
