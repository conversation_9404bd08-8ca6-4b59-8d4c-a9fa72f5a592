//Import Library
import React from "react";

//Import services/components
import Landing from "../../components/layout/landing";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetStaticPropsContext, GetStaticProps } from 'next'

const Landings = () => {
  return <Landing />;
};
export const getStaticProps: GetStaticProps = async (context: GetStaticPropsContext) => {
  const locale = context.locale || 'en'

  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}
export default Landings;
