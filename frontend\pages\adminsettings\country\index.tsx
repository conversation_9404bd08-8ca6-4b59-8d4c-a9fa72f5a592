//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import CountryTable from "./countryTable";
import PageHeading from "../../../components/common/PageHeading";
import { canAddCountry } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const CountryIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowCountryIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title={t("adminsetting.Countries.Table.Countries")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_country"
              >
              <Button variant="secondary" size="sm">
               {t("adminsetting.Countries.Table.AddCountry")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <CountryTable />
          </Col>
        </Row>
      </Container>
    );
  }

  const ShowAddCountry = canAddCountry(() => <ShowCountryIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.country?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddCountry />
  )
};

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default CountryIndex;
