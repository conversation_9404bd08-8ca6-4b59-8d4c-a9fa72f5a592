//Import Library
import { ListGroup } from "react-bootstrap";
import { useEffect, useState } from "react";
import Link from "next/link";

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

function InstitutionMapQuickInfo(props) {
    const initialVal = {
        institutions: "",
        german_operations: "",
        projects: "",
        german_countryId: "",
        german_institutions: "",
    };

    const [organizationDetails, setOrganizationDetails] = useState(initialVal);
    const { t } = useTranslation('common');
    const getOrganizationDetails = async (operationParams) => {
        const response = await apiService.get("/stats/institutions", operationParams);
        if (response) {
            setOrganizationDetails(response);
        }
    };

    useEffect(() => {
        const InstitutionParams = {
            query: { status: { $ne: "Request Pending" } },
            sort: { title: "asc" },
            limit: "~",
        };
        getOrganizationDetails(InstitutionParams);
    }, []);
    return (
        <div className="quick-info-filter">
            <ListGroup style={{ marginLeft: "-10px" }}>
                <ListGroup.Item>
                    <Link href={{ pathname: "/institution" }}>

                        <div className="info-item">
                            <div className="quickinfo-img">
                                <img
                                    src="/images/quickinfo1.png"
                                    width="27"
                                    height="30"
                                    alt="Organization Quick Info"
                                />
                            </div>
                            <span>
                                <b>{organizationDetails.institutions}</b> {t("AllOrganisations")}
                            </span>
                        </div>

                    </Link>
                </ListGroup.Item>
                <ListGroup.Item>
                    <Link href={{ pathname: "/institution", query: { country: organizationDetails.german_countryId } }}>

                        <div className="quickinfo-img">
                            <img
                                src="/images/quickinfo2.png"
                                width="24"
                                height="23"
                                alt="Organization Quick Info"
                            />
                        </div>
                        <span>
                            <b>{organizationDetails.german_institutions}</b> {t("GermanOrganisations")}
                        </span>

                    </Link>
                </ListGroup.Item>
                <ListGroup.Item>
                    <Link href={"/project"} as={"/project"}>

                        <div className="quickinfo-img">
                            <img
                                src="/images/quickinfo3.png"
                                width="24"
                                height="21"
                                alt="Organization Quick Info"
                            />
                        </div>
                        <span>
                            <b>{organizationDetails.projects}</b> {t("Projects")}
                        </span>

                    </Link>
                </ListGroup.Item>
            </ListGroup>
        </div>
    );
}

export default InstitutionMapQuickInfo;
