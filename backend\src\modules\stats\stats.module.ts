//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { StatsController } from './stats.controller';
import { StatsService } from './stats.service';
// SCHEMAS
import { CountrySchema } from 'src/schemas/country.schemas';
import { ProjectSchema } from 'src/schemas/project.schemas';
import { EventSchema } from 'src/schemas/event.schemas';
import { OperationSchema } from 'src/schemas/operation.schemas';
import { InstitutionSchema } from 'src/schemas/institution.schemas';
import { OperationStatusSchema } from 'src/schemas/operation_status.schemas';
import { EventStatusSchema } from 'src/schemas/event_status.schemas';
import { ProjectStatusSchema } from 'src/schemas/project_status.schemas';
import { VspaceSchema } from 'src/schemas/vspace.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Country', schema: CountrySchema },
      { name: 'Project', schema: ProjectSchema },
      { name: 'Event', schema: EventSchema },
      { name: 'Operation', schema: OperationSchema },
      { name: 'Institution', schema: InstitutionSchema },
      { name: 'OperationStatus', schema: OperationStatusSchema },
      { name: 'EventStatus', schema: EventStatusSchema },
      { name: 'ProjectStatus', schema: ProjectStatusSchema },
      { name: 'Vspace', schema: VspaceSchema }

    ])
  ],
  controllers: [StatsController],
  providers: [StatsService],
})

export class StatsModule { }