//Import Library
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Tooltip, OverlayTrigger } from "react-bootstrap";
import { useState, useEffect } from "react";

//Import services/components
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';


interface LayoutHelpModalProps {
  show: boolean;
  onHide: () => void;
  [key: string]: any;
}

export default function LayoutHelpModal(props: LayoutHelpModalProps) {

  const _initialVal = {
    title: '',
    description: '',
    pageCategory: '',
    isEnabled: true,
  }

  const [showHelpModal, setHelpModal] = useState(false);
  const handleClose = () => setHelpModal(false);
  const [, setHelp] = useState(_initialVal);
  const createMarkup = (htmlContent: string) => { return { __html: htmlContent } };
  const { t } = useTranslation('common');


  const fetchModalData = async () => {

    const helpParams = {
      query: { pageCategory: '' },
      sort: { title: "asc" },
      limit: "~",
    };

    const pageCategoryId: any = await fetchPageCategory();
    if (pageCategoryId.length > 0) {
      helpParams.query.pageCategory = pageCategoryId;
      const response = await apiService.get('/landingPage',helpParams);
      if (Array.isArray(response.data) && response.data.length > 0) {

        response.data[0].title = response && response.data[0].title.length > 0 && response.data[0].isEnabled === true ? response.data[0].title : defaultTitle;

        response.data[0].description = response && response.data[0].description.length > 0 && response.data[0].isEnabled === true ? response.data[0].description : defaultDesc;

        setHelp(response.data[0])
        fetchPageCategory();

      }
    }
  };

  useEffect(() => {
    fetchModalData();
  }, []);

  const fetchPageCategory = async () => {
    const response = await apiService.get('/pagecategory',{query:{title : "Help"}});
    if (response && response.data && response.data.length > 0) {
      const pageCategoryId = response.data[0]._id;
      return pageCategoryId
    }
    return false;
  };

  const defaultDesc = `The RKI Platform is accessible by invitation only to cooperation partner organisations in the field of Health Protection and their staff, through their organisations focal points. If your organisation is already part of the platform and you would like to join please check with your lead focal point as they will be able to add you. <br /><br />  If your organisation is not registered, but you would like to add your organization to our platform, you can email the team at  <EMAIL>.<br/><br/>  We ask that all users of the platform allow their name, title and email to be shared to facilitate communications across the network. We have data protection rules in place to protect against the misuse of data. For further information please click <a href='https://www.rki.de/DE/Service/Datenschutz/datenschutzerklaerung_node.html' target='_blank'>here</a><br /><br />Thank you for your interest.<br/>Kind regards,`;

  const defaultTitle = `How do I access the RKI Platform?`;

  return (
    <div>
      <a onClick={() => setHelpModal(true)} >
        <OverlayTrigger
          placement="bottom"
          delay={{ show: 250, hide: 400 }}
          overlay={<Tooltip id="print-tooltip" >{t("Help")}</Tooltip>} >
          <i className="fas fa-question-circle" /></OverlayTrigger></a>
      <Modal size="lg" show={showHelpModal} onHide={handleClose}>
        <Modal.Header closeButton>
          <Modal.Title id="help-modal">
          {t("Help")}
        </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <ul>
            <Accordion >
              <Accordion.Item eventKey="0">
                <li className="help-content-li-title">
                  <Accordion.Header>
                    <div
                      dangerouslySetInnerHTML={createMarkup(defaultTitle)}>
                    </div>
                  </Accordion.Header>
                </li>
                <Accordion.Body className="help-content-li-content">
                  <div
                    dangerouslySetInnerHTML={createMarkup(defaultDesc)}>
                  </div>
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>
          </ul>
        </Modal.Body>
      </Modal>
    </div>
  );
}