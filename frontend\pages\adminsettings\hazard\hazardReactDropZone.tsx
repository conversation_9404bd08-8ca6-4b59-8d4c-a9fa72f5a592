//Import Library
import React, { useMemo, useEffect, useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTimesCircle,
  faExclamationCircle,
  faCloudUploadAlt,

} from "@fortawesome/free-solid-svg-icons";
import _ from 'lodash';

//Import services/components
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';


let temp = [];

const limit: any = process.env.UPLOAD_LIMIT;

const baseStyle: any = {
  flex: 1,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  width: "100%",
  height: "100%",
  borderWidth: 0.1,
  borderColor: "#fafafa",
  backgroundColor: "#fafafa",
  color: "black",
  transition: "border  .24s ease-in-out",
};

const thumb: any = {
  display: "inline-flex",
  borderRadius: 2,
  border: "1px solid #ddd",
  marginBottom: 8,
  marginRight: 20,
  width: 100,
  height: 100,
  padding: 2,
  position: "relative",
  boxShadow: "0 0 15px 0.25px rgba(0,0,0,0.25)",
  boxSizing: "border-box"
};

const thumbsContainer: any = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "flex-start",
  flexWrap: "wrap",
  marginTop: 20,
};

const thumbInner: any = {
  display: "flex",
  minWidth: 0,
  overflow: "hidden",
};

const icon: any = {
  position: "absolute",
  fontSize: "22px",
  top: "-10px",
  right: "-10px",
  zIndex: 1000,
  cursor: "pointer",
  backgroundColor: "#fff",
  color: "#000",
  borderRadius: "50%"
};

const img = {
  display: "block",
  width: "auto",
  height: "100%",
};

const activeStyle: any = {
  borderColor: "#2196f3",
};

const HazardReactDropZone = (props: any) => {
  const { t } = useTranslation('common');
  const [files, setFiles] = useState([]);


  const imageDelete = async (id: any) => {
    const _res = await apiService.remove(`/image/${id}`);
  }

  const removeFile = (file: any) => {
    const obj = file && file._id ? { serverID: file._id } : { file: file }
    const _index = _.findIndex(temp, obj);
    imageDelete(temp[_index].serverID)
    temp.splice(_index, 1);
    props.getImgID(temp, props.index ? props.index : 0)
    const newFiles = [...files];
    newFiles.splice(newFiles.indexOf(file), 1);
    setFiles(newFiles);

  };

  const getComponent = (file: any) => {
    return (<img src={file.preview} style={img} />);
  }

  const thumbs: any = files.map((file: any, i) => {
    return (
      <div key={i}>
        <div style={thumb}>
          <div style={thumbInner}>
            {getComponent(file)}
          </div>

          <FontAwesomeIcon
            icon={faTimesCircle}
            style={icon}
            color="black"
            onClick={() => removeFile(file)}
          />
        </div>
      </div>
    );
  });

  useEffect(() => {
    files.forEach((file) => URL.revokeObjectURL(file.preview));
    temp = [];
  }, []);

  useEffect(() => {

    if (props && props.datas) {
      const newObj = props.datas.map((item, _i) => {

        temp.push({ serverID: item._id, index: props.index ? props.index : 0 });
        const previewState = { ...item, preview: `${process.env.API_SERVER}/image/show/${item._id}` }
        return previewState;
      });
      setFiles([...newObj])
    }
  }, [props.datas]);

  const filesUpload = async (files_initials: any, index: any) => {

    if (files_initials.length > index) {
      try {
        const form: any = new FormData();
        form.append("file", files_initials[index]);
        const res = await apiService.post(`/image`, form, { 'Content-Type': 'multipart/form-data' });
        temp.push({ serverID: res._id, file: files_initials[index], index: props.index ? props.index : 0 });
        filesUpload(files_initials, index + 1);
      } catch (error) {
        filesUpload(files_initials, index + 1);
      }
    } else {
      props.getImgID(temp, props.index ? props.index : 0);
    }
  }

  const onDrop = useCallback((drop_files: any) => {
    filesUpload(drop_files, 0);
    const accFiles = drop_files.map((file, i) =>
      Object.assign(file, {
        preview: URL.createObjectURL(file),
      }),
    );
    setFiles([...accFiles])
  }, []);

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
    fileRejections
  } = useDropzone({
    accept: "image/*",
    multiple: false,
    minSize: 0,
    maxSize: limit,
    onDrop
  })

  const style = useMemo(
    () => ({
      ...baseStyle,
      ...(isDragActive ? activeStyle : { outline: "2px dashed #bbb" }),
      ...(isDragAccept
        ? { outline: "2px dashed #595959" }
        : { outline: "2px dashed #bbb" }),
      ...(isDragReject ? { outline: "2px dashed red" } : {activeStyle}),
    }),
    [isDragActive, isDragReject]
  );

  const isFileTooLarge = fileRejections.length > 0 && fileRejections[0].file.size > limit;
  return (
    <>
      <div className=" d-flex justify-content-center align-items-center mt-3" style={{ width: "100%", height: "180px" }}>
        <div {...getRootProps({ style })}>
          <input {...getInputProps()} />
          <FontAwesomeIcon icon={faCloudUploadAlt} size="4x" color="#999" />
          <p style={{ color: '#595959', marginBottom: "0px" }}>{t("Drag'n'dropsomefileshere,orclicktoselectfiles")}</p>
          <small style={{ color: '#595959' }}>{t("ImageWeSupport")}</small>
          <small style={{ color: '#595959' }}><b>{t("Note:")}</b> {t("Onesingleimagewillbeaccepted")}</small>

          {isFileTooLarge && <small className="text-danger mt-2"> <FontAwesomeIcon icon={faExclamationCircle} size="1x" color="red" /> {t("FileistoolargeItshouldbelessthan20MB")}</small>}
          {isDragReject && <small className="text-danger" style={{ color: '#595959' }}><FontAwesomeIcon icon={faExclamationCircle} size="1x" color="red" /> {t("Filetypenotacceptedsorr")}</small>}
        </div>
      </div>
      <div style={thumbsContainer}>{thumbs}</div>
    </>
  );
};

export default HazardReactDropZone;
