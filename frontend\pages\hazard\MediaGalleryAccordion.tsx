//Import Library
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Card, Accordion } from "react-bootstrap";
import { useState } from "react";

//Import services/components
import ReactImages from "../../components/common/ReactImages";
import { useTranslation } from 'next-i18next';

interface MediaGalleryAccordionProps {
  images: any[];
  imgSrc: string[];
}

const MediaGalleryAccordion = (props: MediaGalleryAccordionProps) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return(
        <>
            <Accordion.Item eventKey="0">
              <Accordion.Header onClick={() => setSection(!section)}>
                <div className="cardTitle">{t("mediaGallery")}</div>
                <div className="cardArrow">
                  {section ? (
                    <FontAwesomeIcon icon={faMinus} color="#fff" />
                  ) : (
                    <FontAwesomeIcon icon={faPlus} color="#fff" />
                  )}
                </div>
              </Accordion.Header>
              <Accordion.Body>
                <ReactImages gallery={props.images} imageSource={props.imgSrc} />
              </Accordion.Body>
            </Accordion.Item>
        </>
    );
};

export default MediaGalleryAccordion;