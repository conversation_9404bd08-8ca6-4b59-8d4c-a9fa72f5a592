//Import Library
import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Col, Container, Form, Row } from "react-bootstrap";
import _ from "lodash";
import { confirmAlert } from "react-confirm-alert";
import { MultiSelect } from "react-multi-select-component";

import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import getApiService from "../../services/getApiService";
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';
import invitationService from "../../services/invitation.service";

import { canEditInstitution } from "./permission";

const InstitutionFocalPoint = ({ institution, routes }: any) => {

    const [focalpoints, setFocalpoints] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [primary, setPrimary] = useState([]);
    const [clearRows, setClearRows] = useState(false);
    const [initialState, setInitialState]: any = useState(institution);
    const [usersList, setUsersList] = useState([]);
    const [existingUser, setExistingUser] = useState([]);
    const [loading, setLoading] = useState(false);
    const [checkUser, setcheckUser] = useState([]);
    const { t } = useTranslation('common');

    const [userInvite, setUserInvite]: any = useState({
        username: "",
        email: "",
        _id: null,
    });

    const usersParams = {
        query: {},
        sort: { title: "asc" },
        limit: "~",
        select: "-firstname -lastname -password -role -country -region -status -is_focal_point -image   -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken",
    };

    const userListParams = {
        query: {},
        sort: { username: "asc" },
        limit: "~",
        select: "-firstname -lastname -password -role -country -region  -is_focal_point -image  -institution -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken",
    };

    const customSort = (rows: any[], field: string, direction: 'asc' | 'desc') => {
        const handleField = (row: any) => {
            if (row[field]) {
                return row[field].toLowerCase();
            }
            return row[field];
        };
        return _.orderBy(rows, handleField, direction);
    };

    useEffect(() => {
        getUsers(userListParams);
    }, []);

    const getExcludedInvitedUsers = (users) => {
        users = users.data ? [...users.data] : [];
        users = users.filter((user) => {
            let foundInstituteInvites = user.institutionInvites.filter(
                (invite) => invite.institutionId === routes[1] && invite.status !== "Rejected"
            );
            if (foundInstituteInvites.length === 0) {
                return user;
            }
        });
        return users;
    };

    const getUsers = async (userListParamsinit) => {
        const usersinit = await apiService.get("/users", userListParamsinit);
        usersinit.data = getExcludedInvitedUsers(usersinit);
        if (usersinit && Array.isArray(usersinit.data)) {
            const _users = usersinit.data.map((item, _i) => {
                return { label: item.username, value: item._id, email: item.email };
            });
            setUsersList(_users);
        }
        const users = await apiService.get("/users", userListParamsinit);
        if (users && Array.isArray(users.data)) {
            const _users = users.data.map((item, _i) => {
                return { label: item.username, value: item._id, email: item.email };
            });
            setcheckUser(_users);
        }
    };

    useEffect(() => {
        if (initialState.focal_points && initialState.focal_points.length > 0) {
            const focalPoints = _.map(initialState.focal_points, "_id");
            fetchUsersByID(focalPoints);
            getUsers(userListParams);
        }
        if (initialState.primary_focal_point) {
            getPrimaryFocalPoint();
        }
    }, [initialState]);

    const fetchUsersByID = async (users) => {
        setLoading(true);

        const usersQuery = await apiService.get("/users", {
            ...usersParams,
            query: { _id: users },
        });
        if (usersQuery.data && usersQuery.data.length > 0) {
            let tableData = [];
            usersQuery?.data.forEach((user) => {
                user?.institutionInvites.forEach((institutionInvite) => {
                    if (
                        institutionInvite &&
                        institutionInvite.institutionId === routes[1] &&
                        institutionInvite.status === "Approved"
                    ) {
                        tableData.push({
                            ...user,
                            ...{
                                institutionId: institutionInvite.institutionId,
                                institutionName: institutionInvite.institutionName,
                                institutionStatus: institutionInvite.status,
                            },
                        });
                    }
                });
            });
            setFocalpoints(tableData);
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        if (e.target) {
            const { name, value } = e.target;
            setUserInvite((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleUsers = (e) => {
        setExistingUser(e);
    };

    const primaryFocalPointColumns = [
        {
            name: t("Name"),
            selector: "focal_points_name",
            cell: (d) => <div>{d.username}</div>,
        },
        {
            name: t("Email"),
            selector: "focal_points_email",
            cell: (d) => <div>{d.email}</div>,
        },
        {
            name: t("Organisation"),
            selector: "focal_points_institution",
            cell: (d) => <div>{d.institution && d.institution.title}</div>,
        },
        {
            name: t("Telephone"),
            selector: "mobile_number",
            cell: (d) => <div>{`${d.dial_code ? d.dial_code : ""} ${d.mobile_number ? d.mobile_number : ""}`}</div>,
        },
    ];
    const focalPointColumns = [
        {
            name: t("Name"),
            selector: "username",
            cell: (d) => <div>{d.username}</div>,
            sortable: true,
        },
        {
            name: t("Email"),
            selector: "email",
            cell: (d) => <div>{d.email}</div>,
            sortable: true,
        },
        {
            name: t("Organisation"),
            selector: "focal_points_institution",
            cell: (d) => <div>{d.institutionName}</div>,
            sortable: true,
        },
        {
            name: t("Telephone"),
            selector: "mobile_number",
            cell: (d) => <div>{`${d.dial_code ? d.dial_code : ""} ${d.mobile_number ? d.mobile_number : ""}`}</div>,
        },
    ];

    const handleRowSelected = useCallback((state) => {
        setSelectedRows(state.selectedRows);
        setClearRows(false);
    }, []);

    const getPrimaryFocalPoint = async () => {
        setLoading(true);
        const primary_focal_point_id = initialState.primary_focal_point;
        const primaryFocalpoint = await apiService.get("/users", {
            ...usersParams,
            query: { _id: primary_focal_point_id },
        });
        if (primaryFocalpoint.data && primaryFocalpoint.data.length > 0) {
            setPrimary(primaryFocalpoint.data);
            setLoading(false);
        }
    };

    const handlePrimaryFocalPoints = async () => {
        if (selectedRows.length <= 0 || selectedRows.length > 1) {
            toast.error(t("toast.YoucannotaddmultipleFocalPointasPrimary"));
        } else {
            const primaryFocalPoint = selectedRows[0]._id;
            const response = await apiService.patch(`/institution/${routes[1]}`, {
                primary_focal_point: primaryFocalPoint,
                title: initialState.title,
            });
            if (response && response._id) {
                const primaryvalue = _.filter(focalpoints, ["_id", response.primary_focal_point]);
                setInitialState({ ...initialState, ...{ primary_focal_point: response.primary_focal_point } });
                setPrimary(primaryvalue);
                toast.success(t("toast.Primaryfocalpointschangedsuccessfully"));
                setClearRows(true);
            }
        }
    };

    const handleDeleteFocalPointFromOrganisation = async () => {
        if (selectedRows.length > 0) {
            confirmAlert({
                title: t("toast.deleteFocalPtFromOrg"),
                message: t("toast.deleteFocalPtFromOrgConfirm"),
                buttons: [
                    {
                        label: t("yes"),
                        onClick: () => deleteFocalpoints(selectedRows),
                    },
                    { label: t("No"), onClick: () => false },
                ],
            });
        } else {
            toast.error(t("toast.Pleaseselectanyfocalpointstodelete"));
        }
    };

    const checkAndEmptyPrimaryFocalPoint = async (_selectedFocalpoints) => {
        if (_selectedFocalpoints?.length) {
            if (_selectedFocalpoints.find((_fp) => _fp._id === initialState.primary_focal_point)) {
                const response = await apiService.patch(`/institution/${routes[1]}`, {
                    primary_focal_point: "",
                    title: initialState.title,
                });
                if (response && response._id) {
                    setPrimary([]);
                    setClearRows(true);
                }
            }
        }
    };

    const deleteFocalpoints = async (selectedFocalpoints: any[]) => {
        checkAndEmptyPrimaryFocalPoint(selectedFocalpoints);
        let clippedFocalPoints = [];
        const selectedIds = _.map(selectedFocalpoints, "_id");
        if (_.indexOf(selectedIds, initialState.primary_focal_point) < 0) {
            let remainingFocalpoints = _.difference(focalpoints, selectedFocalpoints);
            clippedFocalPoints = remainingFocalpoints;
            remainingFocalpoints = _.map(remainingFocalpoints, (item: any) => {
                return { _id: item._id };
            });
            const params = {
                title: initialState.title,
                focal_points: selectedFocalpoints,
            };
            selectedFocalpoints.forEach(async (fp) => {
                fp.institutionInvites = fp.institutionInvites.filter((invite) => invite.institutionId != routes[1]);
                const res = await apiService.patch(`/users/${fp._id}`, fp);
            });
            const institutionData = await apiService.patch(`/institution/${routes[1]}/updateFocalPoints`, params);
            if (institutionData && institutionData._id) {
                toast.success(t("toast.Focalpointsremovedfromorganisationsuccessfuly"));
                setFocalpoints(clippedFocalPoints);
                setClearRows(true);
                getUsers(userListParams);
            }
        } else {
            toast.error(t("toast.Youcannotdeleteaprimaryfocalpoint"));
        }
    };

    const inviteFocalPoints = async () => {
        if (userInvite.email == "") {
            toast.error(t("Emailidisempty"));
        } else {
            const name = userInvite.email.match(/^([^@]*)@/)[1];
            const isUser = checkUser.find((x) => x.email == userInvite.email);
            if (isUser !== undefined) {
                toast.error(t("Givenexistinguserinnewuserfield"));
                return;
            }
            Object.assign(userInvite, { username: name });
            const focalPointsarray = [];
            focalPointsarray.push(userInvite);
            const newFocalPoints = focalpoints.map((item, _i) => {
                return { _id: item._id };
            });
            const obj = {
                title: initialState.title,
                focal_points: [userInvite, ...newFocalPoints],
            };
            const institutionData = await apiService.patch(`/institution/${routes[1]}`, obj);
            if (institutionData && institutionData._id) {
                toast.success(t("toast.Invitationsentsuccessfully"));
                const getInstutionData = await getApiService.getApiData(`/institution/${routes[1]}`, false, {});
                if (getInstutionData && getInstutionData._id) {
                    setInitialState(getInstutionData);
                }
                invitationService.inviteNewUserWithEmail(
                    userInvite.email,
                    userInvite.username,
                    institutionData._id,
                    institutionData.title
                );
                getUsers(userListParams);
            } else {
                toast.error(institutionData);
            }
            setUserInvite({ email: "" });
        }
    };

    const inviteExistingUser = async () => {
        const extuser = existingUser.map((item, _i) => {
            return { _id: item.value };
        });
        if (extuser.length === 0) {
            toast.error(t("ExistingUsersisempty"));
        } else {
            const obj = {
                title: initialState.title,
                focal_points: [...focalpoints, ...extuser],
            };
            const institutionData = await apiService.patch(`/institution/${routes[1]}`, obj);
            if (institutionData && institutionData._id) {
                existingUser.forEach(async (user) => {
                    const userId = user.value;
                    let userData = await apiService.get(`/users/${userId}`);
                    let newInInvites = [];
                    if (userData?.institutionInvites?.length) {
                        newInInvites = [...userData.institutionInvites];
                        let dupInvite = newInInvites.filter((invite) => invite.institutionId === institutionData._id);
                        if (dupInvite.length <= 0) {
                            newInInvites.push({
                                institutionName: institutionData.title,
                                institutionId: institutionData._id,
                                status: "Request Pending",
                            });
                        } else {
                            newInInvites = newInInvites.map((invite) => {
                                if (invite.institutionId === institutionData._id && invite.status === "Rejected") {
                                    invite.status = "Request Pending";
                                }
                                return invite;
                            });
                        }
                    } else {
                        newInInvites = [
                            {
                                institutionName: institutionData.title,
                                institutionId: institutionData._id,
                                status: "Request Pending",
                            },
                        ];
                    }
                    userData.institutionInvites = newInInvites;
                    const res = await apiService.patch(`/users/${userId}`, userData);
                    getUsers(userListParams);
                });
                toast.success(t("toast.Invitationsentsuccessfully"));
                const institutionDataa = await getApiService.getApiData(`/institution/${routes[1]}`, false, {});
                if (institutionDataa && institutionDataa._id) {
                    setInitialState(institutionDataa);
                }
            }
            setExistingUser([]);
        }
    };

    const EditInstitutionLink = () => {
        return (
            <>
                {initialState && initialState._id ? (
                  <Link
                      href="/institution/[...routes]"
                      as={`/institution/edit/${routes[1]}`}
                      >
                    <Button variant="secondary" size="sm">
                      <i className="fas fa-pen" />
                      &nbsp;{t("Edit")}
                    </Button>
                  </Link>
                ) : null}
            </>
        );
    };

    const CanEditInstitution = canEditInstitution(() => <EditInstitutionLink />);

    return (
        <Container>
            <Row className="mb-4">
                <Col>
                    <h4 className="institutionTitle">
                        <Link href="/institution/[...routes]" as={`/institution/show/${routes[1]}`}>
                            {initialState.title}
                        </Link>
                        <CanEditInstitution />
                    </h4>
                </Col>
            </Row>
            <Row>
                <Col className="medium">
                    <b>{t("PrimaryFocalPoint")}</b>
                </Col>
            </Row>
            <Row className="primary-focal-point-table mb-3">
                <RKITable
                    columns={primaryFocalPointColumns}
                    data={primary}
                    loading={loading}
                    subheader={false}
                    pagination={false}
                    pagServer={true}
                    clearSelectedRows={clearRows}
                />
            </Row>
            <Row>
                <Col className="medium">
                    <b>{t("FocalPoints")}</b>
                </Col>
            </Row>
            <Row className="mb-3">
                <RKITable
                    columns={focalPointColumns}
                    data={focalpoints}
                    loading={loading}
                    pagServer={true}
                    selectableRows
                    subheader={false}
                    onSelectedRowsChange={handleRowSelected}
                    clearSelectedRows={clearRows}
                    persistTableHead
                    sortFunction={customSort}
                />
            </Row>
            <Row>
                <Col>
                    <Button className="me-1" onClick={handlePrimaryFocalPoints} variant="primary" size="sm">
                        {t("Promote")}
                    </Button>
                    <Button onClick={handleDeleteFocalPointFromOrganisation} variant="primary" size="sm">
                        {t("DeleteFocal")}
                    </Button>
                </Col>
            </Row>
            <br />
            <Row>
                <Col md={6}>
                    <Form.Group controlId="formBasicEmail">
                        <Form.Label>{t("external")}</Form.Label>
                        <Form.Control
                            name="email"
                            type="email"
                            placeholder={t("Enteremail")}
                            value={userInvite.email}
                            onChange={handleChange}
                        />
                    </Form.Group>
                    <Button onClick={inviteFocalPoints} variant="primary" type="submit">
                        {t("InviteFocalPoint")}
                    </Button>
                </Col>
                <Col md={6}>
                    <Form.Group controlId="formBasicEmail">
                        <Form.Label>{t("existing")}</Form.Label>
                        <MultiSelect
                            overrideStrings={{
                                selectSomeItems: t("SelectUsers"),
                            }}
                            options={usersList}
                            value={existingUser}
                            onChange={handleUsers}
                            className={"focal-users"}
                            labelledBy={"Select Users"}
                        />
                    </Form.Group>
                    <Button onClick={inviteExistingUser} variant="primary" type="submit">
                        {t("Adduser")}
                    </Button>
                </Col>
            </Row>
        </Container>
    );
};

export default InstitutionFocalPoint;
function data_request(users: any, setUsersList: React.Dispatch<React.SetStateAction<any[]>>) {
    if (users && Array.isArray(users.data)) {
        let _users = [];
        users.data.map((item, _i) => {
            if ((item && item.status && item.status !== "Request Pending") || !item.status) {
                _users.push({ label: item.username, value: item._id });
            }
        });
        setUsersList(_users);
    }
}
