//Import Library
import React from "react";
import { Col, Row } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import RKIMap1 from "../../../components/common/RKIMap1";
import RKIMapMarker from "../../../components/common/RKIMapMarker";
import { useTranslation } from 'next-i18next';


interface CountryCoverSectionProps {
  latlng: {
    lat: number;
    lng: number;
  };
  countryData: {
    title: string;
    health_profile: string;
    security_advice: string;
    _id: string;
  };
  operationStatusId: any;
  countryStatsData: {
    operation_count: number;
    project_count: number;
    event_count: number;
  };
  eventStatusId: any;
  projectStatusId: any;
}

const CountryCoverSection = (props: CountryCoverSectionProps) => {
    const { t } = useTranslation('common');
    return (
        <>
            <Row>
                <Col xs={12} style={{ display: "flex" }}>
                    <div className="countryMap">
                        {props?.latlng?.lat ? (
                            <RKIMap1 initialCenter={props.latlng}>
                                <RKIMapMarker
                                    icon={{
                                        url: "/images/map-marker-blue.svg",
                                    }}
                                    position={props.latlng}
                                />
                            </RKIMap1>
                        ) : null}
                    </div>
                    <div className="countryInfo">
                        <h4> {props?.countryData?.title} </h4>
                        {Country_func(
                            props.countryData,
                            props.operationStatusId,
                            t,
                            props.countryStatsData,
                            props.eventStatusId,
                            props.projectStatusId
                        )}
                    </div>
                </Col>
            </Row>
        </>
    )
}

export default CountryCoverSection;

function Country_func(
    countryData: {
      title: string;
      health_profile: string;
      security_advice: string;
      _id: string;
    },
    operationStatusId: any,
    t: (key: string) => string,
    countryStatsData: {
      operation_count: number;
      project_count: number;
      event_count: number;
    },
    eventStatusId: any,
    projectStatusId: any
  ) {
    return (
      <div className="countryInfoDetails">
        <Link
          href={{
            pathname: "/operation",
            query: {
              country: countryData?._id,
              status: operationStatusId,
            },
          }}
        >

          <div className="countryInfo-Item">
            <div className="countryInfo-img">
              <img
                src="/images/countryinfo1.png"
                width="25"
                height="25"
                alt="Organization Quick Info"
              />
            </div>
            <span>
              {t("CurrentOperation")}
              <br />
              <b>
                {countryStatsData?.operation_count
                  ? countryStatsData.operation_count
                  : 0}
              </b>
            </span>
          </div>

        </Link>
        <Link
          href={{
            pathname: "/event",
            query: {
              country: countryData?._id,
              status: eventStatusId,
            },
          }}
        >

          <div className="countryInfo-Item">
            <div className="countryInfo-img">
              <img
                src="/images/countryinfo2.png"
                width="30"
                height="22"
                alt="Organization Quick Info"
              />
            </div>
            <span>
              {t("CurrentEvent")}
              <br />
              <b>
                {countryStatsData?.event_count
                  ? countryStatsData.event_count
                  : 0}
              </b>
            </span>
          </div>

        </Link>
        <Link
          href={{
            pathname: "/project",
            query: { country: countryData._id, status: projectStatusId },
          }}
        >

          <div className="countryInfo-Item">
            <div className="countryInfo-img">
              <img
                src="/images/quickinfo3.png"
                width="24"
                height="21"
                alt="Organization Quick Info"
              />
            </div>
            <span>
              {t("CurrentProject")}
              <br />
              <b>
                {countryStatsData?.project_count
                  ? countryStatsData.project_count
                  : 0}
              </b>
            </span>
          </div>

        </Link>
      </div>
    );
  }