//Import Library
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { useRef, useState, useEffect } from "react";
import toast from 'react-hot-toast';
import Router from "next/router";
import Link from "next/link";

//Import services/components
import apiService from "../../../services/apiService";
import { Syndrome } from "../../../types";
import { useTranslation } from 'next-i18next';
import { EditorComponent } from "../../../shared/quill-editor/quill-editor.component";

interface SyndromeFormProps {
    [key: string]: any;
}

const SyndromeForm = (props: SyndromeFormProps) => {
    const _initialSyndrome = {
        title: "",
        code: "",
        description: "",
    };
    const { t } = useTranslation('common');
    const [initialVal, setInitialVal] = useState<Syndrome>(_initialSyndrome);

    const editform = props.routes && props.routes[0] === "edit_syndrome" && props.routes[1];

    const handleSubmit = async (event: any, values?: any) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
            code: initialVal.code,
            description: initialVal.description,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.syndrome.Syndromeisupdatedsuccessfully";
            response = await apiService.patch(`/syndrome/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.syndrome.Syndromeisaddedsuccessfully";
            response = await apiService.post("/syndrome", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/syndrome");
        } else {
            toast.error(response);
        }
    };

    const resetHandler = () => {
        setInitialVal(_initialSyndrome);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleDescription = (value: string) => {
        setInitialVal((prevState) => ({
            ...prevState,
            description: value,
        }));
    };

    useEffect(() => {
        const syndromeParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };

        if (editform) {
            const getSyndromeData = async () => {
                const response: Syndrome = await apiService.get(`/syndrome/${props.routes[1]}`, syndromeParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getSyndromeData();
        }
    }, []);

    const formRef = useRef(null);

    return (
        <Container className="formCard" fluid>
            <Card
                style={{
                    marginTop: "5px",
                    boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                }}
            >
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>{editform ? t("adminsetting.syndrome.EditSyndrome") : t("adminsetting.syndrome.AddSyndrome")}</Card.Title>
                            </Col>
                        </Row>
                        <hr />
                        <Row>
                            <Col md lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field">
                                        {t("adminsetting.syndrome.SyndromeName")}
                                    </Form.Label>
                                    <TextInput
                                        name="title"
                                        id="title"
                                        required
                                        value={initialVal.title}
                                        validator={(value) => value.trim() !== ""}
                                        errorMessage={{
                                            validator: t("adminsetting.syndrome.PleaseAddtheSyndromeName"),
                                        }}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.syndrome.Code")}</Form.Label>
                                    <TextInput
                                        name="code"
                                        id="code"
                                        required
                                        value={initialVal.code}
                                        errorMessage={{validator: t("adminsetting.syndrome.PleaseAddtheCode")}}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.syndrome.Description")}</Form.Label>
                                    <EditorComponent initContent={initialVal.description} onChange={(evt) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary">
                                    {t("adminsetting.syndrome.Submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("adminsetting.syndrome.Reset")}
                                </Button>
                                <Link
                                    href="/adminsettings/[...routes]"
                                    as={`/adminsettings/syndrome`}
                                    >
                                    <Button variant="secondary">{t("adminsetting.syndrome.Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
        </Container>
    );
};
export default SyndromeForm;
