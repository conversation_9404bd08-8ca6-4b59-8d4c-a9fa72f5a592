//Import Library
import {useEffect, useState} from "react";

//Import services/components
import ListContainer from "./ListContainer"
import apiService from "../../services/apiService";

interface ActiveProjectOperationsProps {
  t: (key: string) => string;
  ongoingProjects: any[];
  ongoingOperations: any[];
}

export default function ActiveProjectOperations(props: ActiveProjectOperationsProps) {

  const {t, ongoingOperations, ongoingProjects} = props;
  const [eventsData, setEventsData] = useState<any[]>([]);
  const fetchEvents = async () => {
    const eventParams = {
      query: { status: [] },
      sort: { created_at: "desc" },
      limit: 10,
      select: "-description -operation -world_region -country_regions -hazard_type -hazard -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at"
    };
    const fetchEventsStatus = await fetchEventtStatus();
    if (fetchEventsStatus) {
      eventParams.query.status.push(fetchEventsStatus);
    }
    const response = await apiService.get('event', eventParams);
    if (response && Array.isArray(response.data) && response.data.length > 0) {
      setEventsData(response.data);
    }
  }

  const fetchEventtStatus = async () => {
    const response = await apiService.get('/eventStatus', {query: {title: 'Current'}});
    if (response && response.data && response.data.length > 0) {
      return response.data[0]._id;
    }
    return false;
  };

  useEffect(() => {
    fetchEvents();
  },[]);

  return (
    <div className="active-projects-announcements">
      <h4>{t('allActivity')}</h4>
      <ListContainer t={t} currentEvents={eventsData} ongoingProjects={ongoingProjects} ongoingOperations={ongoingOperations} />
    </div>
  )
}