//Import Library
import React from "react";
import { faBell, faUsers } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Col, Row } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';


const operationStats = (props) => {
    const { t } = useTranslation('common');
    return (
        <>
            <div className="operationStats">
                <Row>
                    <Col className="operationInfo-Items" md={6}>
                        <div className="operationIcon">
                            <FontAwesomeIcon icon={faUsers} color="#fff" size="2x" />
                        </div>
                        <div className="operationInfo">
                            <h5>{t("Partners")}</h5>
                            <h4>{props.operation.partners.length}</h4>
                        </div>
                    </Col>
                    <Col className="operationInfo-Items" md={6}>
                        <div className="operationIcon">
                            <FontAwesomeIcon icon={faBell} color="#fff" size="2x" />
                        </div>
                        <div className="operationInfo">
                            <h5>{t("ActivitiesinField")}</h5>
                            <h4>{props.operation.timeline.length}</h4>
                        </div>
                    </Col>
                </Row>
            </div>
        </>
    )
}

export default operationStats;