//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { PageCategoryController } from './page-category.controller';
import { PageCategoryService } from './page-category.service';
// SCHEMAS
import { PageCategorySchema } from '../../schemas/page-category.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'PageCategory', schema: PageCategorySchema }
    ])
  ],
  controllers: [PageCategoryController],
  providers: [PageCategoryService],
})

export class PageCategoryModule { }