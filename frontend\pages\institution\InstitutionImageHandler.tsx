//Import Library
import React, { useState, useMemo, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimesCircle, faExclamationCircle, faCloudUploadAlt } from "@fortawesome/free-solid-svg-icons";

import toast from 'react-hot-toast';

//Import services/components
import InstitutionImageEditor from "./InstitutionImageEditor";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';


const InstitutionImageCropper = ({ getId, header, type }) => {
    const { t } = useTranslation('common');
    const [modal, setModal] = useState(false);
    const [files, setFiles]: any = useState([]);
    const [imageId, setImageId] = useState("");
    const [thumbUrl, setThumbUrl] = useState("");
        const endpoint = type === "application" ? "/files" : "/image";

    /*Display the cropped image in edit */
    useEffect(() => {
        header ? setThumbUrl(`${process.env.API_SERVER}/image/show/${header}`) : setThumbUrl(null);
    }, [header]);

    /*End*/

    /*Styles For the container*/
    const baseStyle: any = {
        flex: 1,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
        height: "100%",
        borderWidth: 0.1,
        borderColor: "#fafafa",
        backgroundColor: "#fafafa",
        color: "black",
        transition: "border  .24s ease-in-out",
    };

    const thumb: any = {
        display: "inline-flex",
        borderRadius: 2,
        border: "1px solid #ddd",
        marginBottom: 8,
        marginRight: 20,
        width: 170,
        height: 100,
        padding: 2,
        position: "relative",
        boxShadow: "0 0 15px 0.25px rgba(0,0,0,0.25)",
        boxSizing: "border-box",
    };

    const thumbsContainer: any = {
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-start",
        flexWrap: "wrap",
        marginTop: 20,
    };

    const thumbInner: any = {
        display: "flex",
    };

    const icon: any = {
        position: "absolute",
        fontSize: "22px",
        top: "-10px",
        right: "-10px",
        zIndex: 1000,
        cursor: "pointer",
        backgroundColor: "#fff",
        color: "#000",
        borderRadius: "50%",
    };

    const img = {
        display: "block",
        height: "100%",
    };

    const activeStyle: any = {
        borderColor: "#2196f3",
    };
    /*End of Styles*/

    /**Handle Modal Close**/
    const modalClose = (val) => {
        setModal(val);
    };
    /*End*/

    /*For Handle the dropFiles*/
    const onDrop = (file) => {
        const accFiles = file.map((file, i) =>
            Object.assign(file, {
                preview: URL.createObjectURL(file),
            })
        );
        setFiles(accFiles);
        if (file.length > 0) {
            setModal(true);
        }
    };
    /*End*/

    /*Setting the intial accept type & size e.t.c */
    const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject, fileRejections } = useDropzone({
        noClick: false,
        accept: "image/*",
        multiple: false,
        minSize: 0,
        maxSize: 2000000,
        onDrop,
        validator: nameLengthValidator,
    });
    /*End*/

    /*Styles for drag & drop Container*/
    const style = useMemo(
        () => ({
            ...baseStyle,
            ...(isDragActive ? activeStyle : { outline: "2px dashed #bbb" }),
            ...(isDragAccept ? { outline: "2px dashed #595959" } : { outline: "2px dashed #bbb" }),
            ...(isDragReject ? { outline: "2px dashed red" } : { activeStyle }),
        }),
        [isDragActive, isDragReject]
    );
    /*End*/

    /*Reject the file If length is greater than 2mb*/
    const isFileTooLarge = fileRejections.length > 0 && fileRejections[0].file.size > 2000000;
    /*End*/

    /*Get Id crop using callBack Function*/
    const getIdHandler = (id) => {
        setImageId(id);
        getId(id);
    };
    /*End*/

    /*Remove File Handler*/
    const removeFile = async () => {
        let res;
        if (header) {
            res = await apiService.remove(`image/${header}`);
        } else {
            res = await apiService.remove(`image/${imageId}`);
        }

        if (res && res._id) {
            setThumbUrl(null);
            getIdHandler(null);
        }
    };
    /*End*/

    //***Get Blob from the react avaatr editor for preview*/
    const blobHandler = (url) => {
        setThumbUrl(url);
    };
    /*End*/

    function nameLengthValidator(file) {
        if (endpoint === "/image") {
            if (file.type.substring(0, 5) === "image") {
                return;
            } else {
                toast.error(t("toast.filetypenotsupport"));
            }
        } else if (endpoint === "/files") {
            if (!(file.type.substring(0, 5) !== "image")) {
                toast.error(t("toast.filetypenotsupport"));
            }
        } else {
            return null;
        }
    }

    return (
        <>
            <div
                className=" d-flex justify-content-center align-items-center mt-3"
                style={{ width: "100%", height: "180px" }}
            >
                <div {...getRootProps({ style })}>
                    <input {...getInputProps()} />
                    <FontAwesomeIcon icon={faCloudUploadAlt} size="4x" color="#999" />
                    <p style={{ color: "#595959", marginBottom: "0px" }}>
                        {t("Drag'n'dropsomefileshere,orclicktoselectfiles")}
                    </p>
                    <small style={{ color: "#595959" }}>
                        <b>{t("Note:")}</b> {t("Onesingleimagewillbeaccepted")}
                    </small>
                    {isFileTooLarge && (
                        <small className="text-danger mt-2">
                            <FontAwesomeIcon icon={faExclamationCircle} size="1x" color="red" />
                            &nbsp;{t("FileistoolargeItshouldbelessthan2MB")}
                        </small>
                    )}
                    {isDragReject && (
                        <small className="text-danger" style={{ color: "red" }}>
                            <FontAwesomeIcon icon={faExclamationCircle} size="1x" color="red" />
                            {t("Filetypenotacceptedsorr")}
                        </small>
                    )}
                </div>
            </div>
            {thumbUrl && (
                <>
                    <div style={thumbsContainer}>
                        <div style={thumb}>
                            <div style={thumbInner}>
                                <img src={thumbUrl} style={img} />
                            </div>
                            <FontAwesomeIcon icon={faTimesCircle} style={icon} color="black" onClick={removeFile} />
                        </div>
                    </div>
                </>
            )}

            <InstitutionImageEditor
                isOpen={modal}
                getId={(id) => getIdHandler(id)}
                image={files && files[0] ? files[0].preview : ""}
                onModalClose={(val) => modalClose(val)}
                fileName={files && files[0] ? files[0].name : ""}
                getBlob={(url) => blobHandler(url)}
            />
        </>
    );
};

export default InstitutionImageCropper;
