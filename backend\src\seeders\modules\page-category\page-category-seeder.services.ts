//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { pageCategories } from "../../data/page-category";
import { PageCategoryInterface } from "src/interfaces/page-category.interface";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class PageCategorySeederService {

  constructor(
    @InjectModel('PageCategory') private pageCategoryModel: Model<PageCategoryInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<PageCategoryInterface>> {
    return pageCategories.map(async (pageCategory: any) => {
      return await this.pageCategoryModel
        .findOne({ title: pageCategory.title })
        .exec()
        .then(async dbPageCategory => {
          // We check if a Deployment Status already exists.
          // If it does don't create a new one.
          if (dbPageCategory) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.pageCategoryModel.create(pageCategory),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}