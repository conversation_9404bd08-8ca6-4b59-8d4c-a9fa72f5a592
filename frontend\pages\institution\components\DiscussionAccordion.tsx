//Import Library
import React from "react";
import { Accordion } from "react-bootstrap";

//Import services/components
import Discussion from "../../../components/common/disussion";
import { useTranslation } from 'next-i18next';

const DiscussionAccordion = (props) => {
    const { t } = useTranslation('common');
    return (
        <Accordion defaultActiveKey="2">
            <Accordion.Item eventKey="2">
                <Accordion.Header>
                    <div className="cardTitle">{t("Discussions")}</div>
                </Accordion.Header>
                <Accordion.Body>
                    <Discussion
                        type="hazard"
                        id={props && props.routes ? props.routes[1] : null}
                    />
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    );
};
export default DiscussionAccordion;