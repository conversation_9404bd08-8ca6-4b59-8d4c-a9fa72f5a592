//Import Library
import { useEffect, useState } from "react";
import _ from "lodash";

//Import services/components
import RKIMapMarker from "../../components/common/RKIMapMarker";
import RKIMAP1 from "../../components/common/RKIMap1";
import { useTranslation } from 'next-i18next';

interface CountriesMapProps {
  countries: {
    data: Array<{
      _id: string;
      title: string;
      coordinates?: Array<{
        latitude: string;
        longitude: string;
      }>;
    }>;
  };
}

const CountriesMap = (props: CountriesMapProps) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language;
  const [activeMarker, setactiveMarker] = useState<any>({});
  const [markerInfo, setMarkerInfo] = useState<any>({});
  const [points, setPoints] = useState<any[]>([]);
  const { countries } = props;

  const MarkerInfo = (Props: { info: { id?: string; name?: string } }) => {
    const { info } = Props;
    return <a href={`/${currentLang}/country/show/${info?.id}`}>{info?.name}</a>;
  };

  const resetMarker = () => {
    setactiveMarker(null);
    setMarkerInfo(null);
  };

  const onMarkerClick = (propsinit: { name: string; id: string }, marker: any, _e: any) => {
    resetMarker();
    setactiveMarker(marker);
    setMarkerInfo({
      name: propsinit.name,
      id: propsinit.id,
    });
  };

  const setPointsFromCountries = () => {
    const pointsvalue: any[] = [];
    if (countries && countries.data) {
      _.forEach(countries.data, (country) => {
        pointsvalue.push({
          title: country.title,
          id: country._id,
          lat:
            country.coordinates && country.coordinates[0]
              ? country.coordinates[0].latitude
              : null,
          lng:
            country.coordinates && country.coordinates[0]
              ? country.coordinates[0].longitude
              : null,
        });
      });
    }
    setPoints([...pointsvalue]);
  };

  useEffect(() => {
    setPointsFromCountries();
  }, [countries]);

  return (
    <RKIMAP1
      language={currentLang}
      points={points}
      height={300}
      activeMarker={activeMarker}
      markerInfo={<MarkerInfo info={markerInfo} />}
      onClose={resetMarker}
    >
      {points && points.length >= 1
        ? points.map((item, index) => {
            if (item && item.lat) {
              return (
                <RKIMapMarker
                  key={index}
                  name={item.title}
                  id={item.id}
                  icon={{
                    url: "/images/map-marker-white.svg",
                  }}
                  onClick={onMarkerClick}
                  position={item}
                />
              );
            }
          })
        : null}
    </RKIMAP1>
  );
};

CountriesMap.defaultProps = {
  countries: { data: [] },
};

export default CountriesMap;
