//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { LandingPageController } from './landingPage.controller';
import { LandingPageService } from './landingPage.service';
import { ImageModule } from '../image/image.module';
import { ImageService } from '../image/image.service';
import { FilesService } from './../files/files.service';
// SCHEMAS
import { LandingPageSchema } from '../../schemas/landing_page.schemas';
import { ImageSchema } from '../../schemas/image.schemas';
import { FilesSchema } from '../../schemas/files.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'LandingPage', schema: LandingPageSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Files', schema: FilesSchema }
    ]),
    ImageModule
  ],
  controllers: [LandingPageController],
  providers: [LandingPageService, ImageService, FilesService],
})

export class LandingPageModule { }