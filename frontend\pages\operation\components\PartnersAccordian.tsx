//Import Library
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useState } from "react";
import { Accordion, Card } from "react-bootstrap";

//Import services/components
import OperationPartners from "../OperationPartners";
import { useTranslation } from 'next-i18next';

const PartnersAccordian = (props) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("Partners")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <OperationPartners partners={props.operation.partners} />
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
}

export default PartnersAccordian;