//Import Library
import Link from "next/link";
import _ from "lodash";
import { Popover, OverlayTrigger } from "react-bootstrap";

//Import services/components
import RKITable from "../../components/common/RKITable";
import { useTranslation } from 'next-i18next';

const Networks = ({ networks }) => {
  if (networks && networks.length > 0) {
    return (
      <ul>
        {networks.map((item, index) => {
          return <li key={index}>{item.title}</li>;
        })}
      </ul>
    );
  }
  return null;
};

// For network popover
const Networkpopover = (
  <Popover id="popover-basic">
    <Popover.Header as="h3" className="text-center">
      NETWORKS
    </Popover.Header>
    <Popover.Body>
      <div className="m-2">
        <p>
          <b>EMLab</b> - European Mobile Lab
        </p>
        <p>
          <b>EMT</b> - Emergency Medical Teams
        </p>
        <p>
          <b>GHPP</b> - Global Health Protection Program
        </p>
        <p>
          <b>GOARN</b> - Global Outbreak Alert & Response Network
        </p>
        <p>
          <b>IANPHI</b> - International Association of National Public Health
          Institutes
        </p>
        <p>
          <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und
          Behandlungszentren
        </p>
        <p>
          <b>WHOCC</b>- World Health Organization Collaborating Centres
        </p>
      </div>
    </Popover.Body>
  </Popover>
);

function OperationPartners(props) {
  const { t } = useTranslation('common');
  const { partners } = props;
  const columns = [
    {
      name: t("Organisation"),
      selector: "title",
      cell: (d) =>
        d && d.institution ? (
          <Link
            href="/institution/[...routes]"
            as={`/institution/show/${d.institution._id}`}
          >
            {d.institution.title}
          </Link>
        ) : (
          ""
        ),
      sortable: true,
    },
    {
      name: t("Country"),
      selector: "country",
      cell: (d) =>
        d &&
        d.institution &&
        d.institution.address &&
        d.institution.address.country ? (
          <Link
            href="/country/[...routes]"
            as={`/country/show/${d.institution.address.country._id}`}
          >
            {d.institution.address.country.title}
          </Link>
        ) : (
          ""
        ),
      sortable: true,
    },
    {
      name: t("Type"),
      selector: "type.title",
      cell: (d) =>
        d.institution && d.institution.type && d.institution.type.title
          ? d.institution.type.title
          : "",
      sortable: true,
    },
    {
      name: (
        <OverlayTrigger
          trigger="click"
          placement="right"
          overlay={Networkpopover}
        >
          <span>
            {t("Network")}&nbsp;&nbsp;&nbsp;
            <i
              className="fa fa-info-circle"
              style={{ cursor: "pointer" }}
              aria-hidden="true"
            ></i>
          </span>
        </OverlayTrigger>
      ),
      selector: t("Networks"),
      cell: (d) =>
        d.institution &&
        d.institution.networks &&
        d.institution.networks.length > 0 ? (
          <Networks networks={d.institution.networks} />
        ) : (
          ""
        ),
    },
  ];

  const get_field = (row) => {
    if (row.institution.address && row.institution.address.country) {
      if (
        row.institution.address.country &&
        row.institution.address.country.title
      ) {
        return row.institution.address.country.title.toLowerCase();
      }
      return row.institution.address.country.title;
    }
  };

  const get_fieldtitle = (row) => {
    if (row.institution.type) {
      if (row.institution.type && row.institution.type.title) {
        return row.institution.type.title.toLowerCase();
      }
    }
  };

  const customSort = (rows, field, direction) => {
    const handleField = (row) => {
      if (field === "country") {
        get_field(row);
      } else if (field === "type.title") {
        get_fieldtitle(row);
      } else {
        if (row.institution && row.institution[field]) {
          return row.institution[field].toLowerCase();
        }
      }
    };
    return _.orderBy(rows, handleField, direction);
  };

  return (
    <RKITable
      columns={columns}
      data={partners}
      pagServer={true}
      persistTableHead
      sortFunction={customSort}
    />
  );
}

export default OperationPartners;
