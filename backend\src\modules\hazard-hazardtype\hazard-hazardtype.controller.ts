//Import Library
import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';

//Import services/components
import { HazardHazardTypeService } from "./hazard-hazardtype.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('hazard_hazard_type')
@UseGuards(SessionGuard)
export class HazardHazardTypeController {
  constructor(
    private readonly _hazardHazardTypeService: HazardHazardTypeService
  ) { }

  @Get(':id')
  findOne(@Param('id') hazardTypeId: string, @Query() query: any) {
    return this._hazardHazardTypeService.get(hazardTypeId, query);
  }
}
