//Import Library
import React, { useState, useEffect } from 'react';
import _ from 'lodash';
import { Form, Button } from "react-bootstrap";

//Import services/components
import apiService from '../../services/apiService';
import { useTranslation } from 'next-i18next';

// Define types for region items
interface RegionItem {
  _id: string;
  code: string;
  title: string;
  isChecked: boolean;
}

interface RegionsMultiCheckboxesProps {
  regionHandler: (regions: string[]) => void;
  selectedRegions: string[];
  filtreg: (regions: string[]) => void;
}

function RegionsMultiCheckboxes(props: RegionsMultiCheckboxesProps) {
  const {filtreg} = props;
  const [allregions, setAllregions] = useState(true);
  const [region, setRegion] = useState<RegionItem[]>([]);
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const { t } = useTranslation('common');
  const RegionParams = {
    "query": {},
    "limit": "~",
    "sort": { "title": "asc" }
  };

  const getworldregion = async (RegionParams_initial: typeof RegionParams) => {
    const response = await apiService.get('/worldregion', RegionParams_initial);
    if (response && Array.isArray(response.data)) {
      const finalRegions: RegionItem[] = [];
      const selectedIds: string[] = [];

      _.each(response.data, (item, _) => {
        const regionItem: RegionItem = {
          ...item,
          isChecked: true
        };
        finalRegions.push(regionItem);
        selectedIds.push(item._id);
      });

      filtreg(selectedIds);
      setSelectedRegions(selectedIds);
      setRegion(finalRegions);
    }
  };

  useEffect(() => {
    getworldregion(RegionParams);
  }, [])

  const handleAllChecked = (event: React.ChangeEvent<HTMLInputElement>) => {
    const updatedRegions = region.map(item => ({
      ...item,
      isChecked: event.target.checked
    }));

    let selected_Regions: string[] = [];
    if (event.target.checked) {
      selected_Regions = updatedRegions.map(item => item._id);
    }

    filtreg(selected_Regions);
    setSelectedRegions(selected_Regions);
    setAllregions(event.target.checked);
    setRegion(updatedRegions);
  };

  const handleIndividualRegionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updatedRegions = [...region];
    let updatedSelectedRegions = [...selectedRegions];

    updatedRegions.forEach((item, index) => {
      if (item.code === e.target.id) {
        updatedRegions[index].isChecked = e.target.checked;
        if (!e.target.checked) {
          updatedSelectedRegions = updatedSelectedRegions.filter(n => n !== item._id);
        } else {
          updatedSelectedRegions.push(item._id);
        }
      }
    });

    setSelectedRegions(updatedSelectedRegions);
    filtreg(updatedSelectedRegions);
    setAllregions(false);
    setRegion(updatedRegions);
  };

  const resetAllRegion = () => {
    const updatedRegions = region.map(item => ({
      ...item,
      isChecked: false
    }));

    setSelectedRegions([]);
    setAllregions(false);
    setRegion(updatedRegions);
    filtreg([]);
  };

  return (
    <div className="regions-multi-checkboxes">
      <Form.Check
        type="checkbox"
        id={`all`}
        label={t("AllRegions")}
        checked={allregions}
        onChange={handleAllChecked}
      />
      {region.map((item, index) => {
        return (
          <Form.Check
            key={index}
            type="checkbox"
            id={item.code}
            label={item.title}
            value={item.code}
            onChange={handleIndividualRegionChange}
            checked={region[index].isChecked}
          />
        )
      })}
      <Button onClick={resetAllRegion} className="btn-plain ps-2">{t("ClearAll")}</Button>
    </div>
  )
}

RegionsMultiCheckboxes.defaultProps = {
  filtreg: () => {""}
}

export default RegionsMultiCheckboxes;
