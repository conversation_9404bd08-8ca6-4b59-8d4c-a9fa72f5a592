import React from 'react';
import { Form } from 'react-bootstrap';
import { Field, ErrorMessage, useField } from 'formik';

// This component mimics the TextInput component from react-bootstrap4-form-validation
// but uses Formik under the hood
export const TextInput = ({
  name,
  id,
  required,
  validator,
  errorMessage,
  onChange,
  value,
  as,
  multiline,
  rows,
  pattern,
  ...props
}: any) => {
  // Create a custom validation function that mimics the original validator
  const validate = (val: any) => {
    // Safely convert value to string and trim
    const stringVal = typeof val === 'string' ? val : String(val || '');
    if (required && (!val || stringVal.trim() === '')) {
      return errorMessage?.validator || 'This field is required';
    }

    if (validator && !validator(val)) {
      return errorMessage?.validator || 'Invalid value';
    }

    if (pattern && val) {
      const regex = new RegExp(pattern);
      if (!regex.test(val)) {
        return errorMessage?.pattern || 'Invalid format';
      }
    }

    return undefined;
  };

  return (
    <Field name={name} validate={validate}>
      {({ field, meta }: any) => (
        <>
          <Form.Control
            {...field}
            {...props}
            id={id}
            as={as || 'input'}
            rows={rows}
            isInvalid={meta.touched && !!meta.error}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              field.onChange(e);
              if (onChange) onChange(e);
            }}
            value={value !== undefined ? value : field.value}
          />
          {meta.touched && meta.error ? (
            <Form.Control.Feedback type="invalid">
              {meta.error}
            </Form.Control.Feedback>
          ) : null}
        </>
      )}
    </Field>
  );
};

// This component mimics the SelectGroup component from react-bootstrap4-form-validation
// but uses Formik under the hood
export const SelectGroup = ({
  name,
  id,
  required,
  errorMessage,
  onChange,
  value,
  children,
  ...props
}: any) => {
  // Create a custom validation function that mimics the original validator
  const validate = (val: any) => {
    if (required && (!val || val === '')) {
      return errorMessage?.validator || 'This field is required';
    }

    return undefined;
  };

  return (
    <Field name={name} validate={validate}>
      {({ field, meta }: any) => (
        <>
          <Form.Control
            as="select"
            {...field}
            {...props}
            id={id}
            isInvalid={meta.touched && !!meta.error}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
              field.onChange(e);
              if (onChange) onChange(e);
            }}
            value={value !== undefined ? value : field.value}
          >
            {children}
          </Form.Control>
          {meta.touched && meta.error ? (
            <Form.Control.Feedback type="invalid">
              {meta.error}
            </Form.Control.Feedback>
          ) : null}
        </>
      )}
    </Field>
  );
};

// Export both components as named exports and as a default object
export default {
  TextInput,
  SelectGroup
};
