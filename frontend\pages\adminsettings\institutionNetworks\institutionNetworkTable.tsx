//Import Library
import Link from "next/link";
import { useState, useEffect } from "react";
import { Modal, Button, Popover, OverlayTrigger } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const InstitutionNetworkTable = (_props: any) => {
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectInstitutionNetwork, setSelectInstitutionNetwork] = useState({});
    const modalHide = () => setModal(false);
    const { t } = useTranslation('common');


    // For network popover
    const Networkpopover = (
        <Popover id="popover-basic">
            <Popover.Header as="h3" className="text-center">
                NETWORKS
            </Popover.Header>
            <Popover.Body>
                <div className="m-2">
                    <p>
                        <b>EMLab</b> - European Mobile Lab
                    </p>
                    <p>
                        <b>EMT</b> - Emergency Medical Teams
                    </p>
                    <p>
                        <b>GHPP</b> - Global Health Protection Program
                    </p>
                    <p>
                        <b>GOARN</b> - Global Outbreak Alert & Response Network
                    </p>
                    <p>
                        <b>IANPHI</b> - International Association of National Public Health Institutes
                    </p>
                    <p>
                        <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und Behandlungszentren
                    </p>
                    <p>
                        <b>WHOCC</b>- World Health Organization Collaborating Centres
                    </p>
                </div>
            </Popover.Body>
        </Popover>
    );
    const icons = (
        <OverlayTrigger trigger="click" placement="right" overlay={Networkpopover}>
            <span>
                {t("Title")}&nbsp;&nbsp;&nbsp;
                <i className="fa fa-info-circle" style={{ cursor: "pointer" }} aria-hidden="true"></i>
            </span>
        </OverlayTrigger>
    );
    // End

    const columns = [
        {
            name: icons,
            selector: "title",
        },
        {
            name: t("action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_institution_network/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>
                </div>
            ),
        },
    ];
    const institutionNetworkParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    useEffect(() => {
        getInstitutionNetworkData();
    }, []);

    const getInstitutionNetworkData = async () => {
        setLoading(true);
        const response = await apiService.get("/institutionnetwork", institutionNetworkParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        institutionNetworkParams.limit = perPage;
        institutionNetworkParams.page = page;
        getInstitutionNetworkData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        institutionNetworkParams.limit = newPerPage;
        institutionNetworkParams.page = page;
        setLoading(true);
        const response = await apiService.get("/institutionnetwork", institutionNetworkParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/institutionnetwork/${selectInstitutionNetwork}`);
            getInstitutionNetworkData();
            setModal(false);
            toast.success(t("adminsetting.Organisationnetworks.Table.orgNetworkDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.Organisationnetworks.Table.errorDeletingOrgNetwork"));
        }
    };

    const userAction = async (row) => {
        setSelectInstitutionNetwork(row._id);
        setModal(true);
    };

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.Organisationnetworks.Delete")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.Organisationnetworks.sure")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};
export default InstitutionNetworkTable;
