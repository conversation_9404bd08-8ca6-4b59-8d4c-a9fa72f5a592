//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response, Request } from 'express';

//Import services/components
import { CreateUsersDto } from './dto/create-users.dto';
import { UpdateUsersDto } from './dto/update-users.dto';
import { UsersService } from './users.service';
import { SessionGuard } from 'src/auth/session-guard';
import { EmailService } from './../email.service';
import { ImageService } from '../modules/image/image.service';

const SECRETKEY = process.env.CRYPTO_SECRETKEY;

const Cryptr = require('cryptr'),
  cryptr = new Cryptr(SECRETKEY);

@Controller('users')
@UseGuards(SessionGuard)
export class UsersController {
  constructor(
    private readonly _usersService: UsersService,
    private readonly _emailService: EmailService,
    private readonly _imageService: ImageService,
  ) {}

  @Post()
  async create(@Body() createUpdateDto: CreateUsersDto) {
    let _update = await this._usersService.create(createUpdateDto);
    _update = await this._usersService.protectPassword(_update);
    const _user = await this._usersService.get(_update['_id']);
    const languageCode = createUpdateDto['language'] ?? 'en';
    this._emailService.manualUserInvite(_user, languageCode);
    return _update;
  }

  @Get()
  async findAll(@Query() query: any, @Res() response: Response) {
    const count: any = await this._usersService.getTotalCount(query);
    response.set({
      'X-Total-Count': parseInt(count),
      'Access-Control-Expose-Headers': 'X-Total-Count',
    });
    const resp: any = await this._usersService.findAll(query);
    resp.data = this._usersService.protectPassword(resp.data);
    return response.send(resp);
  }

  @Get(':id')
  async findOne(@Param('id') updateId: string) {
    let _user = await this._usersService.get(updateId);
    _user = this._usersService.protectPassword(_user);
    return _user;
  }

  @Patch(':id')
  async update(
    @Param('id') updateId: string,
    @Body() updateUsersDto: UpdateUsersDto,
  ) {
    let _update = await this._usersService.update(updateId, updateUsersDto);
    _update = this._usersService.protectPassword(_update);
    return _update;
  }

  @Delete(':id')
  async remove(@Param('id') updateId: string) {
    const _update = await this._usersService.delete(updateId);
    return _update;
  }

  @Post('contactUs')
  async contactUs(@Body() obj: any, @Req() request: Request) {
    const user: any = request.user;
    const data = obj;
    const languageCode = obj.language ?? 'en';
    const _userDetails = await this._usersService.get(user['_id']);
    this._emailService.contactUsAcknowledgement(
      data,
      _userDetails['email'],
      languageCode,
    );
    // return;
  }

  @Post('getLoggedUser')
  async getLoggedUser(@Req() request: Request) {
    const user: any = request.user;
    const loggedUser: any = await this._usersService.get(user['_id']);
    loggedUser['password'] = undefined;
    return loggedUser;
  }

  @Post('updateProfile')
  async updateProfile(@Body() obj: any, @Req() request: Request) {
    const user: any = request.user;
    const _userDetails = await this._usersService.get(user['_id']);
    const _updateData = await this._usersService.myProfileUpdate(
      _userDetails['_id'],
      obj,
    );
    const imageIds = [];
    if (_updateData.image) {
      imageIds.push(_updateData.image);
      await this._imageService.bulkUpdate(imageIds);
    }
    return _updateData;
  }
}
