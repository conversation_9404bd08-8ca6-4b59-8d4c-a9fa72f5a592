//Import Library
import React, { useState } from "react";
import { faFolder<PERSON><PERSON>, faLayerGroup, faUsers } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Col, Row } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';
import InfoPopup from "../InfoPopup";

interface InstitutionInfoSectionProps {
  institutionData: {
    description: string;
    partners?: any[];
  };
  institutionStatus: {
    partners: number;
    operations: number;
    projects: number;
    operationData: any[];
    projectData: any[];
  };
}

const InstitutionInfoSection = (props: InstitutionInfoSectionProps) => {
    const { t } = useTranslation('common');

    /***For Qucik Info Modal***/
    const [popup, setPopup] = useState(false);
    const [quickInfo, setQuickInfo] = useState([]);
    const [fieldName, setFieldName] = useState("");

    /***Handle Partner List***/
    const partnerListHandler = (name) => {
        setPopup(!popup);
        setFieldName(t(name));
        switch (name) {
            case "Partners":
                const partners =
                props.institutionData && props.institutionData.partners
                        ? props.institutionData.partners
                        : [];
                setQuickInfo(partners);
                break;

            case "Operations":
                const operationData =
                    props.institutionStatus && props.institutionStatus.operationData
                        ? props.institutionStatus.operationData
                        : [];
                setQuickInfo(operationData);
                break;

            case "Projects":
                const projectData =
                props.institutionStatus && props.institutionStatus.projectData
                        ? props.institutionStatus.projectData
                        : [];
                setQuickInfo(projectData);
                break;
        }
    };
    const closeHandler = (val) => {
        setPopup(val);
    };


    return (
        <>
            <div className="institution-infographic-block">
                <Row>
                    {partner_func(partnerListHandler, t, props.institutionStatus)}
                    <Col>
                        <div
                            className="list-group-item d-flex clickable"
                            onClick={() => partnerListHandler("Operations")}
                        >
                            <div className="quickinfo-img">
                                <FontAwesomeIcon
                                    icon={faLayerGroup}
                                    color="#fff"
                                    size="2x"
                                />
                            </div>
                            <div className="quickinfoDesc">
                                <h5>{t("Operations")}</h5>
                                <h4>
                                    {props.institutionStatus && props.institutionStatus.operations
                                        ? props.institutionStatus.operations
                                        : 0}
                                </h4>
                            </div>
                        </div>
                    </Col>
                    <Col>
                        {project_func(partnerListHandler, t, props.institutionStatus)}
                    </Col>
                </Row>
                <InfoPopup
                    isShow={popup}
                    isClose={(val) => closeHandler(val)}
                    data={quickInfo}
                    name={fieldName}
                />
            </div>
        </>
    )
}

export default InstitutionInfoSection;

function partner_func(
    partnerListHandler: (name: any) => void,
    t,
    institutionStatus: {
        partners: number;
        operations: number;
        projects: number;
        operationData: any[];
        projectData: any[];
    }
) {
    return (
        <Col>
            <div
                className="list-group-item d-flex clickable"
                onClick={() => partnerListHandler("Partners")}
            >
                <div className="quickinfo-img">
                    <FontAwesomeIcon icon={faUsers} color="#fff" size="2x" />
                </div>
                <div className="quickinfoDesc">
                    <h5>{t("Partners")}</h5>
                    <h4>
                        {institutionStatus && institutionStatus.partners
                            ? institutionStatus.partners
                            : 0}
                    </h4>
                </div>
            </div>
        </Col>
    );
}

function project_func(
    partnerListHandler: (name: any) => void,
    t,
    institutionStatus: {
        partners: number;
        operations: number;
        projects: number;
        operationData: any[];
        projectData: any[];
    }
) {
    return (
        <div
            className="list-group-item d-flex clickable"
            onClick={() => partnerListHandler("Projects")}
        >
            <div className="quickinfo-img">
                {<FontAwesomeIcon icon={faFolderOpen} color="#fff" size="2x" />}
            </div>
            <div className="quickinfoDesc">
                <h5>{t("Projects")}</h5>
                <h4>
                    {institutionStatus && institutionStatus.projects
                        ? institutionStatus.projects
                        : 0}
                </h4>
            </div>
        </div>
    );
}

