//Import Library
import {Module} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { VspaceController } from './vspace.controller';
import { VspaceService } from './vspace.service';
import { EmailService } from './../../email.service';
import { ImageService } from './../image/image.service';
import { VspaceRequestSubscribersService } from './../vspace-request-subscribers/vspace-request-subscribers.service';
import { UsersService } from "../../users/users.service";
import {FlagService} from "../flag/flag.service";
import { FilesService } from './../files/files.service';
import { RegionService } from './../region/region.service';
import { InstitutionService } from './../institution/institution.service';
import {InstitutionModule} from "../institution/institution.module";
import { CountryService } from '../country/country.service';
import { UserInviteService } from '../user-invite/user-invite.service';
import { UpdateService } from '../updates/update.service';
// SCHEMAS
import { VspaceSchema } from '../../schemas/vspace.schemas';
import { ImageSchema } from '../../schemas/image.schemas';
import { VspaceRequestSubscribersSchema } from '../../schemas/vspace-request-subscribers.schemas';
import { UsersSchema } from "../../schemas/users.schemas";
import {FlagSchema} from "../../schemas/flag.schemas";
import { FilesSchema } from '../../schemas/files.schemas';
import { RegionSchema } from '../../schemas/region.schemas';
import { InstitutionSchema } from '../../schemas/institution.schemas';
import {CountrySchema} from "../../schemas/country.schemas";
import { ProjectSchema } from '../../schemas/project.schemas';
import { UpdateSchema } from 'src/schemas/update.schemas';
import { OperationSchema } from 'src/schemas/operation.schemas';
import { HttpModule } from '@nestjs/axios';


@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Vspace', schema: VspaceSchema },
      { name: 'VspaceRequestSubscribers', schema: VspaceRequestSubscribersSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Users', schema: UsersSchema },
      { name: 'Flag', schema: FlagSchema },
      { name: 'Files', schema: FilesSchema },
      { name: 'Region', schema: RegionSchema },
      { name: 'Institution', schema: InstitutionSchema },
      { name: 'Country', schema: CountrySchema },
      { name: 'Project', schema: ProjectSchema },
      { name: 'User', schema: UsersSchema },
      { name: 'Operation', schema: OperationSchema },
      { name: 'Update', schema: UpdateSchema}
    ]),InstitutionModule, HttpModule
  ],
  controllers: [VspaceController],
  providers: [VspaceService, EmailService, VspaceRequestSubscribersService, ImageService, UsersService, FlagService, FilesService, RegionService, InstitutionService, CountryService, UserInviteService, UpdateService],
})

export class VspaceModule { }