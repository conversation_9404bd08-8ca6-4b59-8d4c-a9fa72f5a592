//Import Library
import { useEffect, useState } from "react";
import { Col, Container, Row } from "react-bootstrap";
import { connect } from "react-redux";

//Import services/components
import CountriesMap from "./CountriesMap";
import CountriesGlossary from "./CountriesGlossary";
import CountriesNamesListing from "./CountriesNamesListing";
import PageHeading from "../../components/common/PageHeading";
import RegionsMultiCheckboxes from "../../components/common/RegionsMultiCheckboxes";
import { useTranslation } from 'next-i18next';
import apiService from "../../services/apiService";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Country = (props: any) => {
  const { t, i18n } = useTranslation('common');
  const titleSearch = i18n.language === 'de'? {title_de: "asc"} : {title: "asc"};
  const letter = t("All");
  const [selectedAlpha, setselectedAlpha] = useState<string>(letter);
  const [selectedRegions, setSelectedRegions] = useState<any[]>([]);
  const [countries, setCountries] = useState<any>({});
  const [activePage, setActivePage] = useState<number>(1);

  const countriesParams = {
    sort: titleSearch ,
    limit: 48,
    page: activePage,
    query: {},
    select: "-health_profile -security_advice -created_at -updated_at",
  }
  const fetchCountries = async (lang: any, params: any = countriesParams) => {
    // If no regions are selected, show no countries
    if (selectedRegions.length === 0) {
      setCountries({ data: [], totalCount: 0, totalPages: 0, page: 1, limit: 48 });
      return;
    }

    if (selectedAlpha === 'All' || selectedAlpha === 'Alle') {
      params = { ...params, query: { "world_region": selectedRegions } };
    } else {
      params = { ...params, page:1, query: { "languageCode": lang, "first_letter": selectedAlpha, "world_region": selectedRegions } };
    }
    const response = await apiService.get('/country', { ...params, languageCode: lang });
    if (response && response.data) {

      console.log("response.data",response.data)
      console.log("response.data",response)

      setCountries(response);

    }
  };

  useEffect(() => {
    fetchCountries(t("language"),countriesParams);
  }, [selectedAlpha, selectedRegions, activePage, t("language")]);

  return (
    <Container fluid className="p-0">
      <Row>
        <Col md={12}>
          <PageHeading title={t("menu.countries")} />
        </Col>
      </Row>
      <Row>
        <Col md={12}>
          <CountriesMap countries={countries} />
        </Col>
      </Row>
      <Row>
        <Col md={12}>
          <RegionsMultiCheckboxes
            filtreg={setSelectedRegions}
            selectedRegions={selectedRegions}
            regionHandler={setSelectedRegions}
          />
        </Col>
      </Row>
      <Row>
        <Col md={12}>
          <CountriesGlossary selectedAlpha={selectedAlpha} setselectedAlpha={setselectedAlpha} />
        </Col>
      </Row>
      <Row>
        <Col md={12}>
          <CountriesNamesListing setActivePage={setActivePage} countries={countries} />
        </Col>
      </Row>
    </Container>
  )
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default connect((state) => state)(Country);