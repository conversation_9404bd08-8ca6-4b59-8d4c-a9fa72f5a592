//Import Library
import { CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';

export class SessionGuard implements CanActivate {

    public async canActivate(context: ExecutionContext): Promise<any> {
        const httpContext = context.switchToHttp();
        const request = httpContext.getRequest();
        try {
            
            if (request.session.passport.user) {
                return true;
            }
        } catch (e) {
            throw new UnauthorizedException();
        }
    }
}