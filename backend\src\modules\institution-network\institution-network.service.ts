//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { InstitutionNetworkInterface } from '../../interfaces/institution-network.interface';
import { CreateInstitutionNetworkDto } from './dto/create-institution-network.dto';
import { UpdateInstitutionNetworkDto } from './dto/update-institution-network.dto';
const InstitutionNetwork = 'Could not find Institution Network.';
@Injectable()
export class InstitutionNetworkService {
  constructor(
    @InjectModel('InstitutionNetwork')
    private institutionModel: Model<InstitutionNetworkInterface>,
  ) {}

  async create(
    createInstitutionNetworkDto: CreateInstitutionNetworkDto,
  ): Promise<InstitutionNetworkInterface> {
    const createdInstitutionNetwork = new this.institutionModel(
      createInstitutionNetworkDto,
    );
    return createdInstitutionNetwork.save();
  }

  async findAll(query): Promise<InstitutionNetworkInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.institutionModel.paginate(_filter, options);
  }

  async get(InstitutionNetworkId): Promise<InstitutionNetworkInterface[]> {
    let _result;
    try {
      _result = await this.institutionModel
        .findById(InstitutionNetworkId)
        .exec();
    } catch (error) {
      throw new NotFoundException(InstitutionNetwork);
    }
    if (!_result) {
      throw new NotFoundException(InstitutionNetwork);
    }
    return _result;
  }

  async update(
    InstitutionNetworkId: any,
    updateInstitutionNetworkDto: UpdateInstitutionNetworkDto,
  ) {
    const getById: any = await this.institutionModel
      .findById(InstitutionNetworkId)
      .exec();
    const updatedData = new this.institutionModel(updateInstitutionNetworkDto);
    Object.keys(updateInstitutionNetworkDto).forEach((d) => {
      getById[d] = updatedData[d];
    });
    getById.updated_at = new Date();
    return getById.save();
  }

  async delete(InstitutionNetworkId: string) {
    const result = await this.institutionModel
      .deleteOne({ _id: InstitutionNetworkId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(InstitutionNetwork);
    }
  }
}
