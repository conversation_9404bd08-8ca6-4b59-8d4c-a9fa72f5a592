//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { RegionInterface } from '../../interfaces/region.interface';
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
const FindRegion = 'Could not find Region.'
@Injectable()
export class RegionService {
  constructor(
    @InjectModel('Region') private regionModel: Model<RegionInterface>
  ) { }

  async create(createRegionDto: CreateRegionDto): Promise<RegionInterface> {
    const createdRegion = new this.regionModel(createRegionDto);
    return createdRegion.save();
  }

  async findAll(query): Promise<RegionInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.regionModel.paginate(_filter, options);
  }

  async get(regionId): Promise<RegionInterface[]> {
    let _result;
    try {
      _result = await this.regionModel.findById(regionId).exec();
    } catch (error) {
      throw new NotFoundException(FindRegion);
    }
    if (!_result) {
      throw new NotFoundException(FindRegion);
    }
    return _result;
  }

  async getMultipleRegions(countryList): Promise<RegionInterface[]> {
    let _result;
    try {
      _result = await this.regionModel.find({"country" : {$in : countryList }}).sort({title : "asc"}).exec();
    } catch (error) {
      throw new NotFoundException(FindRegion);
    }
    if (!_result) {
      throw new NotFoundException(FindRegion);
    }
    return _result;
  }

  async update(regionId: any, updateRegionDto: UpdateRegionDto) {
    const getById: any = await this.regionModel.findById(regionId).exec();
    const updatedData = new this.regionModel(updateRegionDto);
    try {
      Object.keys(updateRegionDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Region.');
    }
    return getById;
  }

  async delete(regionId: string) {
    const result = await this.regionModel.deleteOne({ _id: regionId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindRegion);
    }
  }
}
