//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { EventStatusSeederService } from './event-status-seeder.services';
// SCHEMAS
import { EventStatusSchema } from 'src/schemas/event_status.schemas';

/**
 * Import and provide seeder classes for Event status.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'EventStatus', schema: EventStatusSchema }
      ]
    )
  ],
  providers: [EventStatusSeederService],
  exports: [EventStatusSeederService],
})

export class EventStatusSeederModule { }
