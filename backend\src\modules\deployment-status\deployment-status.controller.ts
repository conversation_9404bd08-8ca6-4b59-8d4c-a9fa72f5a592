//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateDeploymentStatusDto } from './dto/create-deployment-status.dto';
import { UpdateDeploymentStatusDto } from './dto/update-deployment-status.dto';
import { DeploymentStatusService } from './deployment-status.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('deploymentstatus')
@UseGuards(SessionGuard)
export class DeploymentStatusController {
  constructor(
    private readonly _deploymentStatusService: DeploymentStatusService,
  ) {}

  @Post()
  async create(@Body() createDeploymentStatusDto: CreateDeploymentStatusDto) {
    try {
      const resp = await this._deploymentStatusService.create(
        createDeploymentStatusDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  async findAll(@Query() query: any) {
    return this._deploymentStatusService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') deploymentStatusId: string) {
    return this._deploymentStatusService.get(deploymentStatusId);
  }

  @Patch(':id')
  async update(
    @Param('id') deploymentStatusId: string,
    @Body() updateDeploymentStatusDto: UpdateDeploymentStatusDto,
  ) {
    try {
      const resp = await this._deploymentStatusService.update(
        deploymentStatusId,
        updateDeploymentStatusDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') deploymentStatusId: string) {
    const deletedData =
      this._deploymentStatusService.delete(deploymentStatusId);
    return deletedData;
  }
}
