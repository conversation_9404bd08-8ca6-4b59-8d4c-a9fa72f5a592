//Import Library
import React, { Component } from "react";
import { connect } from "react-redux";
import Router from "next/router";
import CustomLoader from "../common/CustomLoader";

type IHocState = {
  isLoading: boolean;
  cookie: {};
};

interface AuthSyncProps {
  objCookies?: { [key: string]: string };
  [key: string]: any;
}

// Public routes used to handle layouts
const publicRoutes: string[] = [
  "/home",
  "/login",
  // "/admin/login",
  "/forgot-password",
  "/reset-password/[passwordToken]",
  "/declarationform/[...routes]",
];

// Gets the display name of a JSX component for dev tools
const getDisplayName = (Component1: any) =>
  Component1.displayName || Component1.name || "Component";

function withAuthSync(WrappedComponent: any) {
  class MainComponent extends Component<AuthSyncProps, IHocState> {
    static displayName = `withAuthSync(${getDisplayName(WrappedComponent)})`;
    static async getInitialProps(ctx: any) {
      const componentProps =
        WrappedComponent.getInitialProps &&
        (await WrappedComponent.getInitialProps(ctx));
      if (ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies) {
        const objCookies =
          ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies
            ? ctx.ctx.req.cookies
            : {};
        componentProps.objCookies = objCookies;
        return { ...componentProps };
      } else {
        return { ...componentProps };
      }
    }

    constructor(props: any) {
      super(props);
      this.state = {
        isLoading: true,
        cookie:
          this.props &&
          this.props.objCookies &&
          this.props.objCookies["connect.sid"]
            ? this.props.objCookies["connect.sid"]
            : null,
      };
    }

    componentDidMount() {
      const { route } = this.props.router;
      
      Router.events.on("routeChangeComplete", (url) => {
        if (url === "/home") {
          this.setState({ isLoading: false });
        }
      });
      setTimeout(() => {
        if (!this.state.cookie && publicRoutes.indexOf(route) === -1) {
        this.props.router.push("/home");
        return;
      }
      this.setState({ isLoading: false });
      }, 0) 
    }

    componentWillUnmount() {
      Router.events.off("routeChangeComplete", () => null);
    }

    componentDidUpdate(prevProps: AuthSyncProps): void {
      if(!prevProps.objCookies && this.props.objCookies){
        this.setState({cookie: this.props &&
          this.props.objCookies &&
          this.props.objCookies["connect.sid"]
            ? this.props.objCookies["connect.sid"]
            : null, isLoading: true})
      }
    }

    render() {
      const { router } = this.props;
      const isPublicRoute = publicRoutes.indexOf(router.route) > -1;
      return this.state.isLoading ? <CustomLoader/> : (
        <WrappedComponent
          isLoading={this.state.isLoading}
          isPublicRoute={isPublicRoute}
          {...this.props}
        />
      );
    }
  }

  return connect((state) => state)(MainComponent);
}

export default withAuthSync;
