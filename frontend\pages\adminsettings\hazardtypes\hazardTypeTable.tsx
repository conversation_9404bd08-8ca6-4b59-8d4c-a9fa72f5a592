//Import Library
import Link from "next/link";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const HazardTypeTable = (_props: any) => {
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectHazardType, setSelectHazardType] = useState({});
    const { t } = useTranslation('common');


    const columns = [
        {
            name: t("adminsetting.hazardtypes.HarzardType"),
            selector: "title",
        },
        {
            name: t("adminsetting.hazardtypes.Code"),
            selector: "code",
            cell: (d) => d.code,
        },
        {
            name: t("Description"),
            selector: "description",
            cell: (d) => d.description.replace(/<[^>]+>/g, ""),
        },
        {
            name: t("action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_hazard_types/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>
                </div>
            ),
        },
    ];

    useEffect(() => {
        getHazardsTypeData();
    }, []);

    const hazardTypeParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const getHazardsTypeData = async () => {
        setLoading(true);
        const response = await apiService.get("/hazardtype", hazardTypeParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        hazardTypeParams.limit = perPage;
        hazardTypeParams.page = page;
        getHazardsTypeData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        hazardTypeParams.limit = newPerPage;
        hazardTypeParams.page = page;
        setLoading(true);
        const response = await apiService.get("/hazardtype", hazardTypeParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectHazardType(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/hazardtype/${selectHazardType}`);
            getHazardsTypeData();
            setModal(false);
            toast.success(t("adminsetting.hazardtypes.Table.hazardTypeDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.hazardtypes.Table.errorDeletingHazardType"));
        }
    };

    const modalHide = () => setModal(false);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.hazardtypes.Deletehazardtype")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.hazardtypes.Areyousurewanttodeletethishazardtype")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default HazardTypeTable;
