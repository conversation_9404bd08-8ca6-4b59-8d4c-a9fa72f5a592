//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import OperationstatusTable from "./operationstatusTable";
import { useTranslation } from 'next-i18next';
import { canAddOperationStatus } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";


const OperationstatusIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowOperationstatusIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title= {t("adminsetting.OperationStatus.Operationstatus")} />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_operationstatus"
                >
                <Button variant="secondary" size="sm">
                {t("adminsetting.OperationStatus.AddOperationstatus")}
              </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              <OperationstatusTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  const ShowAddOperationStatus = canAddOperationStatus(() => <ShowOperationstatusIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.operation_status?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddOperationStatus />
  )  
}
export default OperationstatusIndex;