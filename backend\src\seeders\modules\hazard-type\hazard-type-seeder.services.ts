//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { HazardTypeInterface } from "src/interfaces/hazard_type.interface";
import { hazardTypes } from "../../data/hazard-type";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class HazardTypeSeederService {

  constructor(
    @InjectModel('HazardType') private hazardTypeModel: Model<HazardTypeInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<HazardTypeInterface>> {
    return hazardTypes.map(async (hazardType: any) => {
      return await this.hazardTypeModel
        .findOne({ title: hazardType.title })
        .exec()
        .then(async dbHazardType => {
          // We check if a hazard already exists.
          // If it does don't create a new one.
          if (dbHazardType) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.hazardTypeModel.create(hazardType),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}