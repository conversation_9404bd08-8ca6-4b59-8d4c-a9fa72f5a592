//Import Library
import React, { useState, useEffect, useRef } from "react";
import AvatarEditor from "react-avatar-editor";
import RangeSlider from "react-bootstrap-range-slider";
import { Modal, Button, Row, Col } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const InstitutionImageEditor = ({
  isOpen,
  onModalClose,
  image,
  getId,
  fileName,
  getBlob,
}) => {
  const [scale, setScale] = useState(1);
  const [name, setName] = useState("");
  const [img, setImg] = useState(null);
  const editorRef = useRef(null);
    const { t } = useTranslation('common');

  useEffect(() => {
    setName(fileName);
  }, [fileName]);
  const newLocal = "Something wrong in server || your data!";
  const cropHandler = async () => {
    /*****Helper Function to convert to blob******/
    const dataURLtoBlob = (dataurl) => {
      const arr = dataurl.split(",");
      const  mime = arr[0].match(/:(.*?);/)[1];
      const  bstr = atob(arr[1]);
      let n = bstr.length;
      const  u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    };
    /*****End ********/
    const canvas = editorRef.current.getImage().toDataURL("image/jpeg", 0.6);
    const blob = dataURLtoBlob(canvas);
    const urlCreator = window.URL || window.webkitURL; //For Creating the url for preview
    const blobUrl = urlCreator.createObjectURL(blob);
    getBlob(blobUrl);

    const fd = new FormData();
    fd.append("file", blob, name);

    try {
      const res = await apiService.post("/image", fd, {
        "Content-Type": "multipart/form-data",
      });

      if (res && res._id) {
        getId(res._id);
      }
    } catch {
      throw newLocal;
    }
    toast.success(t("toast.CroppedtheimageSuccessfully"));
    onModalClose(false);
    setImg(null);
    setName("none");
    setScale(1);
  };

  return (
    <>
      <div>
        <Modal
          show={isOpen}
          size="lg"
          aria-labelledby="ProfileEdit"
          onHide={() => onModalClose(false)}
          centered
        >
          <Modal.Body>
            <div className="d-flex flex-column justify-content-center align-items-center imgRotate">
              <AvatarEditor
                ref={editorRef}
                width={700}
                height={400}
                borderRadius={2}
                scale={scale}
                color={[0, 0, 0, 0.6]}
                image={img ? img : image}
                style={{width: "100%",height: "auto"}}
              />
              <div className="info-identifier">
                <span>{t("ThisareawillcontainyourInstitutionandfocalpointinformation")}</span>
              </div>
            </div>

            <div className="mx-2 my-3">
              <Row>
                <Col sm={1} md={1} lg={1} className="pe-0">
                  <b>{t("Zoom")}</b>
                </Col>
                <Col sm={11} md={11} lg={11}>
                  <RangeSlider
                    value={scale}
                    tooltip="auto"
                    min={1}
                    max={10}
                    step={0.01}
                    variant="primary"
                    onChange={(changeEvent) =>
                      setScale(Number(changeEvent.target.value))
                    }
                  />
                </Col>
              </Row>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button onClick={cropHandler}>{t("Crop")}</Button>
            <Button variant="danger" onClick={() => onModalClose(false)}>
              {t("Cancel")}
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    </>
  );
};

export default InstitutionImageEditor;
