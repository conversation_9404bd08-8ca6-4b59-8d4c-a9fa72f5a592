// Import Library
import { useEffect, useState } from "react";
import { Col, Container, FormControl, Form, Row } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';
import apiService from "../../services/apiService";

const ProjectsTableFilter = ({
  filterText,
  onFilter,
  onFilterStatusChange,
  onClear,
  filterStatus,
}) => {
  const [status, setStatus] = useState([]);
  const { t } = useTranslation('common');

  const getProjectStatus = async (projectParams) => {
    const response = await apiService.get("/projectstatus", projectParams);
    if (response && Array.isArray(response.data)) {setStatus(response.data)}
  };

  useEffect(() => {
    getProjectStatus({
      query: {},
      sort: { title: "asc" },
    });
  }, []);

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={6} className="ps-0 align-self-end mb-3">
          <FormControl
            type="text"
            className="searchInput"
            placeholder={t("vspace.Search")}
            aria-label="Search"
            value={filterText}
            onChange={onFilter}
          />
        </Col>
        <Col>
          <Form>
            <Form.Group as={Row} controlId="statusFilter">
              <Form.Label column sm="3" lg="2">
                Status
              </Form.Label>
              <Col className="ps-0 pe-1">
                <FormControl
                  as="select"
                  aria-label="Status"
                  onChange={onFilterStatusChange}
                  value={filterStatus}
                >
                  <option value={""}>All</option>
                  {status.map((item, index) => {
                    return (
                      <option key={index} value={item._id}>
                        {item.title}
                      </option>
                    );
                  })}
                </FormControl>
              </Col>
            </Form.Group>
          </Form>
        </Col>
      </Row>
    </Container>
  );
};

export default ProjectsTableFilter;
