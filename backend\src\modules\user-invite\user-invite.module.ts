//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { UserInviteController } from './user-invite.controller';
import { UserInviteService } from './user-invite.service';
// SCHEMAS
import { UsersSchema } from 'src/schemas/users.schemas';
import { InstitutionSchema } from 'src/schemas/institution.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'User', schema: UsersSchema },
      { name: 'Institution', schema: InstitutionSchema}
    ])
  ],
  controllers: [UserInviteController],
  providers: [UserInviteService],
})

export class UserInviteModule { }