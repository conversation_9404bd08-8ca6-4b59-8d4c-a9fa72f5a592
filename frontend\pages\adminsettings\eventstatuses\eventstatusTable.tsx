//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import { useTranslation } from 'next-i18next';
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";

const EventstatusTable = (_props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectEventstatus, setSelectEventstatus] = useState({});


    const eventstatusParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("adminsetting.EventStatus.Table.Title"),
            selector: "title",
        },
        {
            name: t("adminsetting.EventStatus.Table.Action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_eventstatus/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>
                </div>
            ),
        },
    ];

    const geteventstatusData = async () => {
        setLoading(true);
        const response = await apiService.get("/eventstatus", eventstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        eventstatusParams.limit = perPage;
        eventstatusParams.page = page;
        geteventstatusData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        eventstatusParams.limit = newPerPage;
        eventstatusParams.page = page;
        setLoading(true);
        const response = await apiService.get("/eventstatus", eventstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectEventstatus(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/eventstatus/${selectEventstatus}`);
            geteventstatusData();
            setModal(false);
            toast.success(t("adminsetting.EventStatus.Table.eventStatusDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.EventStatus.Table.errorDeletingEventStatus"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        geteventstatusData();
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.EventStatus.Table.DeleteEventstatus")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.EventStatus.Table.Areyousurewanttodeletethiseventstatus?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.EventStatus.Table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.EventStatus.Table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default EventstatusTable;
