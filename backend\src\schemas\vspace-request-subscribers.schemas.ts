//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const VspaceRequestSubscribersSchema = new mongoose.Schema({
  requested_by: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true },
  requested_to: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true },
  vspace: { type: mongoose.Schema.Types.ObjectId, ref: 'Vspace', autopopulate: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

VspaceRequestSubscribersSchema.plugin(mongoosePaginate);
