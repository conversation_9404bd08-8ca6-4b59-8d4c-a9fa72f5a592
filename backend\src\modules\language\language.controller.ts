//Import Library
import { Controller, Get, Query, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateLanguageDto } from './dto/create-language.dto';
import { UpdateLanguageDto } from './dto/update-language.dto';
import { LanguageService } from "./language.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('language')
@UseGuards(SessionGuard)
export class LanguageController {

  constructor(
    private readonly _languageService: LanguageService
  ) { }

  @Post()
  create(@Body() createLanguageDto: CreateLanguageDto) {
    const _language = this._languageService.create(createLanguageDto);
    return _language;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._languageService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') languageId: string) {
    return this._languageService.get(languageId);
  }

  @Patch(':id')
  update(@Param('id') languageId: string, @Body() updateLanguageDto: UpdateLanguageDto) {
    const _language = this._languageService.update(languageId, updateLanguageDto);
    return _language;
  }

  @Delete(':id')
  remove(@Param('id') languageId: string) {
    const _language = this._languageService.delete(languageId);
    return _language;
  }
}
