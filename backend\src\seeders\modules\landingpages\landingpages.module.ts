//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { LandingpagesSeederService } from './landingpages.services';
// SCHEMAS
import { LandingPageSchema } from 'src/schemas/landing_page.schemas';

/**
 * Import and provide seeder classes for landingpages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'landingpages', schema: LandingPageSchema }
      ]
    )
  ],
  providers: [LandingpagesSeederService],
  exports: [LandingpagesSeederService],
})
export class LandingpagesSeederModule { }