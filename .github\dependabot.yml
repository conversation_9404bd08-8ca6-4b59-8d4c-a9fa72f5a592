version: 2
updates:
- package-ecosystem: npm
  directory: "/backend"
  schedule:
    interval: daily
    time: "00:00"
  open-pull-requests-limit: 99
  versioning-strategy: increase-if-necessary
  target-branch: new-frontend
- package-ecosystem: npm
  directory: "/frontend"
  schedule:
    interval: daily
    time: "00:00"
  open-pull-requests-limit: 99
  versioning-strategy: increase-if-necessary
  target-branch: new-frontend
