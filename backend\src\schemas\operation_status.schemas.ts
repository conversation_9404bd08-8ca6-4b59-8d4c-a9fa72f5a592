//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const OperationStatusSchema = new mongoose.Schema({
  title: { type: String, required: true, unique: true },
  is_active: { type: Boolean },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
});

OperationStatusSchema.plugin(mongoosePaginate);
