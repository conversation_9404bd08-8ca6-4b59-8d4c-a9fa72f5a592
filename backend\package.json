{"name": "rki", "version": "0.0.1", "description": "", "author": "", "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed": "ts-node -r tsconfig-paths/register src/seeders/seed.ts", "migrate-up": "migrate-mongo up", "migrate-create": "migrate-mongo create", "migrate-down": "migrate-mongo down", "migrate-status": "migrate-mongo status", "preinstall": "npx force-resolutions"}, "dependencies": {"@nestjs/axios": "^3.0.0", "@nestjs/common": "^10.4.17", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.4.17", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.17", "@nestjs/serve-static": "^4.0.2", "axios": "^1.7.2", "bcrypt": "^5.1.1", "body-parser": "^1.19.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "connect-mongo": "^4.6.0", "cookiejar": "^2.1.4", "cryptr": "^6.0.2", "email-templates": "^9.0.0", "express-session": "^1.17.2", "follow-redirects": "^1.14.9", "formidable": "^2.1.2", "ftp": "^0.3.10", "helmet": "^7.0.0", "i": "^0.3.7", "json5": "^2.2.3", "minimatch": "^3.0.5", "minimist": "^1.2.6", "mongodb": "^4.4.1", "mongoose": "^6.11.3", "mongoose-autopopulate": "^0.16.0", "mongoose-field-encryption": "5.0.2", "mongoose-paginate-v2": "1.3.18", "mv": "^2.1.1", "nest-access-control": "^2.2.0", "node-fetch": "^2.0.0", "nodemailer": "^6.7.3", "nodemailer-smtp-transport": "^2.7.4", "npm": "^10.8.2", "nth-check": "^2.0.1", "passport": "^0.6.0", "passport-local": "^1.0.0", "qs": "^6.10.3", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "semver": "^6.3.1", "terser": "^5.14.2", "tough-cookie": "^4.1.3", "underscore": "^1.13.6", "web-resource-inliner": "^5.0.0", "word-wrap": "^1.2.5"}, "resolutions": {"@nestjs/common": "^10.4.17", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.4.17", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.17", "@nestjs/serve-static": "^4.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "connect-mongo": "^4.6.0", "cryptr": "^6.0.2", "ejs": "^3.1.6", "email-templates": "^9.0.0", "express-session": "^1.17.2", "follow-redirects": "^1.14.9", "ftp": "^0.3.10", "minimist": "^1.2.6", "mongodb": "^4.4.1", "mongoose": "^6.2.7", "mongoose-autopopulate": "^0.16.0", "mongoose-field-encryption": "5.0.2", "mongoose-paginate-v2": "1.3.18", "mv": "^2.1.1", "nest-access-control": "^2.2.0", "node-fetch": "^2.0.0", "nodemailer": "^6.7.3", "nodemailer-smtp-transport": "^2.7.4", "nth-check": "^2.0.1", "passport": "^0.6.0", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "underscore": "^1.13.2", "web-resource-inliner": "^5.0.0", "helmet": "^7.0.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.17", "@types/express": "^4.17.13", "@types/express-session": "^1.17.4", "@types/jest": "27.4.1", "@types/node": "^18.17.14", "@types/passport-local": "^1.0.34", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "ejs": "^3.1.7", "eslint": "^8.9.0", "eslint-config-prettier": "^8.4.0", "eslint-plugin-import": "^2.25.4", "jest": "^27.5.1", "migrate-mongo": "^8.2.3", "prettier": "^2.5.1", "supertest": "^6.2.2", "ts-jest": "^27.1.3", "ts-loader": "^9.2.6", "ts-node": "^10.9.2", "tsconfig-paths": "^3.12.0", "typescript": "^4.5.5"}, "overrides": {"multer": "1.4.5-lts.2", "path-to-regexp": ">=3.3.0", "express": {"path-to-regexp": "0.1.12"}, "cross-spawn": ">=7.0.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}