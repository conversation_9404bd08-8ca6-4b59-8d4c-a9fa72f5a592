//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Button } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';
const WorldregionTable = (_props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectWorldregion, setSelectWorldregion] = useState({});

    const worldregionParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("adminsetting.worldregion.table.Title"),
            selector: row => row.title,
            sortable: true,
        },
        {
            name: t("adminsetting.worldregion.table.Code"),
            selector: row => row.code,
            sortable: true,
            cell: (d) => d.code,
        },
        {
            name: t("adminsetting.worldregion.table.Action"),
            selector: row => row._id,
            sortable: false,
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_worldregion/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    const getWorldregionData = async () => {
        setLoading(true);
        const response = await apiService.get("/worldregion", worldregionParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };
    const handlePageChange = (page) => {
        worldregionParams.limit = perPage;
        worldregionParams.page = page;
        getWorldregionData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        worldregionParams.limit = newPerPage;
        worldregionParams.page = page;
        setLoading(true);
        const response = await apiService.get("/worldregion", worldregionParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectWorldregion(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/worldregion/${selectWorldregion}`);
            getWorldregionData();
            setModal(false);
            toast.success(t("adminsetting.worldregion.table.worldRegionDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.worldregion.table.errorDeletingWorldRegion"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        getWorldregionData();
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.worldregion.table.DeleteWorldregion")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.worldregion.table.Areyousurewanttodeletethisworldregion?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.worldregion.table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.worldregion.table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default WorldregionTable;
