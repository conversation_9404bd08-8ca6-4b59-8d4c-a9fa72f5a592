//Import Library
import React, { useState, useEffect } from 'react';
import { Tweet } from 'react-tweet';
import axios from 'axios';
import authService from '../../../services/authService';

interface TweetData {
  tweetId: string;
  text: string;
  createdAt: string;
}

const TwitterTimeline: React.FunctionComponent<{}> = () => {
  const [tweets, setTweets] = useState<TweetData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTweets = async () => {
      try {
        setLoading(true);
        const headers = await authService.getAuthHeader();
        // Request 2 tweets but handle if we get fewer
        const response = await axios.get(`${process.env.API_SERVER}/tweets?count=2`, { headers });
        
        if (response.data?.data) {
          setTweets(response.data.data);
        } else {
          setTweets([]);
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching tweets:', err);
        setError('Failed to load tweets');
        setTweets([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTweets();
  }, []);

  return (
    <div className='twitter__timeline'>
      <div className="timeline-Header timeline-InformationCircle-widgetParent" data-scribe="section:header">
        <h1 className="timeline-Header-title u-inlineBlock" data-scribe="element:title">
          {tweets.length > 1 ? 'Latest Tweets' : 'Latest Tweet'}
          <span className="timeline-Header-byline" data-scribe="element:byline">&nbsp;by&nbsp;
          <a className="customisable-highlight" href="https://twitter.com/rki_de" target="_blank" rel="noopener noreferrer" title="‎@rki_de on Twitter">‎@rki_de</a>
          </span>
        </h1>
      </div>
      <div style={{ padding: '10px' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>Loading tweets...</div>
        ) : error ? (
          <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
            {error}. <a href="https://x.com/rki_de" target="_blank" rel="noopener noreferrer">Visit @rki_de on X</a>
          </div>
        ) : (
          <>
            {tweets.length > 0 ? (
              <div>
                {tweets.map((tweet, index) => (
                  <div key={tweet.tweetId} style={{ marginBottom: index < tweets.length - 1 ? '20px' : '0' }}>
                    <Tweet id={tweet.tweetId} />
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
                No tweets to display. <a href="https://x.com/rki_de" target="_blank" rel="noopener noreferrer">Visit @rki_de on X</a>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default TwitterTimeline; 

