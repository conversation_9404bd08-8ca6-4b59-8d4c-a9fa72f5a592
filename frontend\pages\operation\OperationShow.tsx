//Import Library
import React, { useState, useEffect } from "react";
import { Container } from "react-bootstrap";

//Import services/components
import apiService from "../../services/apiService";
import UpdatePopup from "../../components/updates/UpdatePopup";
import { useTranslation } from 'next-i18next';
import OperationCoverSection from "./components/OperationCoverSection";
import OperationAccordianSection from "./components/OperationAccordianSection";
import OperationTimelineSection from "./components/OperationTimelineSection";

interface OperationShowProps {
  routes: string[];
}

const OperationShow = (props: OperationShowProps) => {
  const { t } = useTranslation('common');

  const [operationData, setOperationData] = useState<any>({
    title: "",
    timeline: [],
    description: "",
    hazard_type: { title: "" },
    hazard: [],
    syndrome: { title: "" },
    created_at: "",
    updated_at: "",
    country: { title: "" },
    status: { title: "" },
    start_date: "",
    end_date: "",
    partners: [],
    images: [],
    images_src: [],
    document: [],
    doc_src: [],
  });

  const [operationDataLoading, setOperationDataLoading] = useState(false);

  const [loading, setLoading] = useState(false);
  const [Document, setDocument] = useState([]);
  const [updateDocument, setUpdateDocuments] = useState([]);
  const [editAccess, setEditAccess] = useState(false);

  const operationFileParams: any = {
    sort: { doc_created_at: "asc" },
    Doctable: true,
  };

  const operationUpdateParams: any = {
    sort: { doc_created_at: "asc" },
    DocUpdatetable: true,
  };
  const sortProps = (data) => {
    operationFileParams.sort = {
      [data.columnSelector]: data.sortDirection,
    };
    fetchOperationFiles();
  };
  const sortUpdateProps = (data) => {
    operationUpdateParams.sort = {
      [data.columnSelector]: data.sortDirection,
    };
    fetchUpdateFiles();
  };

  const fetchOperationFiles = async () => {
    const _documents = [];
    setLoading(true);
    const operationFiles = await apiService.get(
      `/operation/${props.routes[1]}`,
      operationFileParams
    );
    if (
      operationFiles &&
      Array.isArray(operationFiles) &&
      operationFiles.length >= 1 &&
      operationFiles[0].document &&
      operationFiles[0].document.length >= 1
    ) {
      operationFiles.forEach((element) => {
        element.document &&
          element.document.length > 0 &&
          element.document.map((ele, i) => {
            const description = element.document[i].docsrc;
            ele.description = description;
            _documents.push(ele);
          });
      });
      setDocument(_documents);
    }
    setLoading(false);
  };

  const fetchUpdateFiles = async () => {
    const _documents = [];
    setLoading(true);
    const operationFiles = await apiService.get(
      `/operation/${props.routes[1]}`,
      operationUpdateParams
    );
    if (
      operationFiles &&
      Array.isArray(operationFiles) &&
      operationFiles.length >= 1 &&
      operationFiles[0].document &&
      operationFiles[0].document.length >= 1
    ) {
      operationFiles.forEach((element) => {
        element.document &&
          element.document.length > 0 &&
          element.document.map((ele, i) => {
            const description = element.document[i].docsrc;
            ele.description = description;
            _documents.push(ele);
          });
      });
      setUpdateDocuments(_documents);
    }
    setLoading(false);
  };
  useEffect(() => {
    getLoggedInUser();
  }, []);

  const getLoggedInUser = async () => {
    const data = await apiService.post("/users/getLoggedUser", {});
    if (data && data.roles && data.roles.length) {
      if (props.routes && props.routes[1]) {
        const getOperationData = async (operationParams) => {
          setOperationDataLoading(true);
          const response = await apiService.get(
            `/operation/${props.routes[1]}`,
            operationParams
          );
          if (response) {
            setOperationData(response);

            //Checking for Edit access
            getOperationEditAccess(response, data);
          }
          setOperationDataLoading(false);
        };
        getOperationData({});
        fetchOperationFiles();
        fetchUpdateFiles();
      }
    }
  };

  function getOperationEditAccess(operationData, loginUser) {
    setEditAccess(false);
    if (loginUser && loginUser['roles']) {
      if (loginUser['roles'].includes("SUPER_ADMIN")) {
        //SUPER_ADMIN can Edit all organisations
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "EMT_NATIONAL_FOCALPOINT").length > 0 && operationData.user['_id'] == loginUser['_id']) {
          setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "EMT").length > 0 && operationData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "INIG_STAKEHOLDER").length > 0 && operationData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "GENERAL_USER").length > 0 && operationData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "PLATFORM_ADMIN").length > 0 && operationData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      }
    }
  }

  const getNoDataDiv = () => {
    if (operationData && !operationData.title)
      return <div className="nodataFound">{t("vspace.Nodataavailable")}</div>;
  };

  let propData = {
    operationData : operationData,
    routeData : props,
    editAccess: editAccess,
    documentAccoirdianProps : {
      loading: loading,
      sortProps: sortProps,
      Document: Document,
      updateDocument: updateDocument,
      sortUpdateProps : sortUpdateProps
    }
  }

  return (
    <>
      { operationData?.title ? (
        <Container className="operationDetail" fluid>
          <UpdatePopup routes={props.routes} />
          <OperationCoverSection  {...propData} />
          <OperationTimelineSection operation = {operationData} />
          <OperationAccordianSection {...propData} />
        </Container>
      ) : (
        <></>
      )}
    </>
  );
};

export default OperationShow;
