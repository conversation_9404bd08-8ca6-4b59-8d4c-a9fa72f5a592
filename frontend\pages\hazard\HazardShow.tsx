//Import Library
import React, { useEffect, useState } from "react";
import { Accordion, Card, Row, Col, Spinner } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faMinus } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import UpdatePopup from "../../components/updates/UpdatePopup";
import { useTranslation } from 'next-i18next';
import { canViewDiscussionUpdate } from "./permission";
import HazardCoverSection from "./HazardCoverSection";
import HazardOperation from "./HazardOperation";
import HazardOrganisation from "./HazardOrganisation";
import HazardCurrentEvent from "./HazardCurrentEvent";
import HazardPastEvent from "./HazardPastEvent";
import HazardAccordianSection from "./HazardAccordianSection";
import Discussion from "../../components/common/disussion";
import apiService from "../../services/apiService";

const countrydescription =
  "-country -description -hazard_type -created_at -region -start_date -status -syndrome -title -timeline -user -world_region -_id -part";
const createMarkup = (htmlContent: string) => {
  return { __html: htmlContent };
};

interface HazardShowProps {
  routes: string[];
}

const HazardShow = (props: HazardShowProps) => {
  const { t, i18n } = useTranslation('common');
  const currentLang = i18n.language === "fr" ? "en" : i18n.language;
  const initialVal = {
    title: "",
    description: "",
    picture: "",
  };

  const [hazardData, setHazardData] = useState<any>(initialVal);
  const [hazardDataLoading, setHazardDataLoading] = useState<boolean>(false);
  const [hazardPastEventData, setHazardPastEventData] = useState([]);
  const [hazardCurrentEventData, setHazardCurrentEventData] = useState([]);
  const [hazardOperationData, setHazardOperationData] = useState([]);
  const [hazardInstitutionData, setHazardInstitutionData] = useState([]);
  const [sectionOne, setSectionOne] = useState(false);
  const [sectionTwo, setSectionTwo] = useState(false);
  const [sectionThree, setSectionThree] = useState(true);
  const [loading] = useState(false);
  const [source, setSource] = useState("");
  //Showing Image & document
  const [document, setDocuments] = useState([]);
  const [updateDocument, setUpdateDocument] = useState([]);
  const [images, setImages] = useState([]);
  const [docSrc] = useState([]);
  const [imgSrc, setImgSrc] = useState([]);
  const [sectionFour, setSectionFour] = useState(false);
  //end

  const hazardEventParams = {
    query: {},
    limit: "~",
    sort: { title: "asc" },
  };

  const hazardDocSort = (data: { columnSelector: string; sortDirection: string }) => {
    const operationDocParams = {
      sort: {},
      limit: "~",
      Doctable: true,
      collation: "en",
      select: countrydescription,
    };
    operationDocParams.sort = {
      [data.columnSelector]: data.sortDirection,
    };
    getDocuments(operationDocParams);
  };
  const hazardDocUpdateSort = (data: { columnSelector: string; sortDirection: string }) => {
    const UpdateParams = {
      sort: {},
      limit: "~",
      DocUpdatetable: true,
      collation: "en",
      select: countrydescription,
    };
    UpdateParams.sort = {
      [data.columnSelector]: data.sortDirection,
    };
    getUpdateDocuments(UpdateParams);
  };
  const getDocuments = async (operationParams: any) => {
    const _imgSrc = [];
    const _docSrc = [];
    const _documents = [];
    const _images = [];
    const response = await apiService.get(
      `/hazard/${props.routes[1]}`,
      operationParams
    );
    if (response && response.data && response.data.length > 0) {
      response.data.forEach((element, index) => {
        element.document &&
          element.document.length > 0 &&
          element.document.map((ele, i) => {
            const description = element.document[i].docsrc;
            ele.description = description;
            _documents.push(ele);
          });
        element.images &&
          element.images.length > 0 &&
          element.images.map((image, imageIndex) => {
            _images.push(image);
          });

        element.images_src &&
          element.images_src.length > 0 &&
          element.images_src.map((src, srcIndex) => {
            _imgSrc.push(src);
          });
      });
      setDocuments(_documents);
      var distinctImageIds = _images?.reduce((acc, cur) => {
        const existingItem = acc.find(item => cur._id === item._id);
        if(existingItem) {
           existingItem.count++;
        }
        else {
           acc.push({...cur, count: 1});
        }
        return acc;
     }, []);
      setImages(distinctImageIds.flat(Infinity));
      setImgSrc(_imgSrc.flat(Infinity));
    }
  };

  const getUpdateDocuments = async (UpdateParams: any) => {
    const _imgSrc = [];
    const _docSrc = [];
    const _documents = [];
    const _images = [];
    const response = await apiService.get(
      `/hazard/${props.routes[1]}`,
      UpdateParams
    );
    if (response && response.data && response.data.length > 0) {
      response.data.forEach((element, index) => {
        element.document &&
          element.document.length > 0 &&
          element.document.map((ele, i) => {
            const description = element.document[i].docsrc;
            ele.description = description;
            _documents.push(ele);
          });
      });
      setUpdateDocument(_documents);
    }
  };
  const getHazardData = async (hazardParams: any) => {
    setHazardDataLoading(true);
    const response = await apiService.get(
      `/hazard/${props.routes[1]}`,
      hazardParams
    );

    if (response) {
      response["picture"] =
        response?.picture && response.picture._id
          ? `${process.env.API_SERVER}/image/show/${response.picture._id}`
          : "/images/disease-placeholder.3f65b286.jpg";

      if (response?.picture_source) {
        setSource(response.picture_source);
      }
      setHazardData(response);
      setHazardDataLoading(false);
    }
    setHazardDataLoading(false);
  };

  const getPastEventsForCountry = async (eventParams: any) => {
    const response = await apiService.get(
      `/hazard/${props.routes[1]}/events/Closed`,
      eventParams
    );
    const dataResponse = response && response.data ? response.data : [];
    setHazardPastEventData(dataResponse);
  };

  const getCurrentEventsForCountry = async (eventParams: any) => {
    const response = await apiService.get(
      `/hazard/${props.routes[1]}/events/Current`,
      eventParams
    );
    const dataResponse = response && response.data ? response.data : [];
    setHazardCurrentEventData(dataResponse);
  };

  const getOperationForCountry = async (operationParams: any) => {
    const response = await apiService.get(
      `/hazard/${props.routes[1]}/operations`,
      operationParams
    );
    const dataResponse = response && response.data ? response.data : [];
    setHazardOperationData(dataResponse);
  };

  const getInsitutionForCountry = async (insitutionParams: any) => {
    const response = await apiService.get(
      `/hazard/${props.routes[1]}/institutions`,
      insitutionParams
    );
    const dataResponse = response && response.data ? response.data : [];
    setHazardInstitutionData(dataResponse);
  };

  useEffect(() => {
    const operationParams = {
      sort: { doc_created_at: "asc" },
      limit: "~",
      Doctable: true,
      collation: "en",
      select: countrydescription,
    };
    const UpdateParams = {
      sort: { doc_created_at: "asc" },
      limit: "~",
      DocUpdatetable: true,
      collation: "en",
      select: countrydescription,
    };
    if (props.routes && props.routes[1]) {
      getHazardData({});
      getPastEventsForCountry(hazardEventParams);
      getCurrentEventsForCountry(hazardEventParams);
      getOperationForCountry(hazardEventParams);
      getInsitutionForCountry(hazardEventParams);
      getDocuments(operationParams);
      getUpdateDocuments(UpdateParams);
    }
  }, []);

  const DiscussionComponent = () => {
    return (
      <Accordion.Item eventKey="1">
        <Accordion.Header onClick={() => setSectionThree(!sectionThree)}>
          <div className="cardTitle">{t("discussions")}</div>
          <div className="cardArrow">
            {sectionThree ? (
              <FontAwesomeIcon icon={faPlus} color="#fff" />
            ) : (
              <FontAwesomeIcon icon={faMinus} color="#fff" />
            )}
          </div>
        </Accordion.Header>
        <Accordion.Body>
          <Discussion
            type="hazard"
            id={props && props.routes ? props.routes[1] : null}
          />
        </Accordion.Body>
      </Accordion.Item>
    );
  };

  const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (
    <DiscussionComponent />
  ));

  const regex = new RegExp(Regex_symbol());

  const isValidLink = regex.test(source);
  let propData = {
    t: t,
    images: images,
    imgSrc: imgSrc,
    routeData: props,
    documentAccoirdianProps: {
      loading: loading,
      Document: document,
      updateDocument: updateDocument,
      hazardDocSort: hazardDocSort,
      hazardDocUpdateSort: hazardDocUpdateSort,
      docSrc: docSrc,
    }
  }

  return (
    <div className="hazardDetails">
      <UpdatePopup routes={props.routes} />
      {!hazardDataLoading && !hazardData.title ? (
        <div className="nodataFound">{t("vspace.Nodataavailable")}</div>
      ) : (
        <>
          {hazardData && hazardData.picture ? (
            <>
              {hazard_title_func(hazardData, currentLang)}
              <Row>
                <Col className="mt-2 ps-4" md={{ span: 6, offset: 6 }}>
                  <div>
                    <p className=" py-1" style={{ fontSize: "12px" }}>
                      <i>{t("imageSourceCredit")}: </i>
                      {source ? (
                        valid_func(isValidLink, source)
                      ) : (
                        <span style={{ color: "#234799" }}>
                          {t("noSourceFound")}
                        </span>
                      )}
                    </p>
                  </div>
                </Col>
              </Row>
            </>
          ) : (
            <div className="d-flex justify-content-center p-5">
              <Spinner animation="grow" />
            </div>
          )}
          <br />
          <Row>
            <Col>{hazard_func(t, hazardCurrentEventData)}</Col>
            <Col>{hazardOperation_func(t, hazardOperationData)}</Col>
          </Row>
          <br />
          <Row>
            <Col>{hazard_organisation_func(t, hazardInstitutionData)}</Col>
            <Col>{hazard_event_func(t, hazardPastEventData)}</Col>
          </Row>
          <HazardAccordianSection {...propData} />
        </>
      )}
    </div>
  );

  function Regex_symbol(): string | RegExp {
    return "^(http[s]?:\\/\\/(www\\.)?|ftp:\\/\\/(www\\.)?|www\\.){1}([0-9A-Za-z-\\.@:%_+~#=]+)+((\\.[a-zA-Z]{2,3})+)(/(.)*)?(\\?(.)*)?";
  }
};

export default HazardShow;
function valid_func(isValidLink: boolean, source: string): React.ReactNode {
  return isValidLink && source ? (
    <a target="_blank" href={source}>
      {source}
    </a>
  ) : (
    <span style={{ color: "#234799" }}>{source}</span>
  );
}

function hazard_title_func(hazardData: any, currentLang: string) {
  return <HazardCoverSection hazardData={hazardData} currentLang={currentLang} />
}

function hazard_event_func(t: (key: string) => string, hazardPastEventData: any[]) {
  return <HazardPastEvent t={t} hazardPastEventData={hazardPastEventData} />
}

function hazard_organisation_func(t: (key: string) => string, hazardInstitutionData: any[]) {
  return <HazardOrganisation t={t} hazardInstitutionData={hazardInstitutionData} />
}

function hazardOperation_func(t: (key: string) => string, hazardOperationData: any[]) {
  return <HazardOperation t={t} hazardOperationData={hazardOperationData} />
}

function hazard_func(t: (key: string) => string, hazardCurrentEventData: any[]) {
  return <HazardCurrentEvent t={t} hazardCurrentEventData={hazardCurrentEventData} />
}
