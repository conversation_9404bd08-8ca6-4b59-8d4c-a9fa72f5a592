//Import Library
import React from "react";
import { Accordion, Col, Row } from "react-bootstrap";

//Import services/components
import RiskAssessmentAccordion from "./RiskAssessmentAccordion";
import { canViewDiscussionUpdate } from "../permission";
import DiscussionAccordion from "./DiscussionAccordion";
import MoreInformationAccordion from "./MoreInformationAccordion";
import MediaGalleryAccordion from "./MediaGalleryAccordion";

const EventAccordionSection = (props) => {

    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionAccordion { ...props.routeData } />);

    return (
        <>
            <Row>
                <Col className="eventAccordion" xs={12}>
                    <Accordion>
                        <RiskAssessmentAccordion { ...props.eventData } />
                    </Accordion>
                    <Accordion>
                        <MoreInformationAccordion { ...props.eventData } />
                    </Accordion>
                    <Accordion>
                        <MediaGalleryAccordion { ...props.eventData } />
                    </Accordion>
                    <Accordion>
                        <CanViewDiscussionUpdate />
                    </Accordion>
                </Col>
            </Row>
        </>
    )
};

export default EventAccordionSection;