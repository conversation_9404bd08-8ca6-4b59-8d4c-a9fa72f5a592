//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const EventSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  operation: { type: mongoose.Schema.Types.ObjectId, ref: 'Operation', autopopulate: true },
  country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
  world_region: {type: mongoose.Schema.Types.ObjectId, ref: 'WorldRegion'},
  country_regions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true }],
  hazard_type: { type: mongoose.Schema.Types.ObjectId, ref: 'HazardType', autopopulate: true },
  hazard: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Hazard', autopopulate: true }],
  syndrome: { type: mongoose.Schema.Types.ObjectId, ref: 'Syndrome', autopopulate: true },
  status: { type: mongoose.Schema.Types.ObjectId, ref: 'EventStatus', autopopulate: true },
  laboratory_confirmed: { type: Boolean },
  officially_validated: { type: Boolean },
  rki_monitored: { type: Boolean },
  risk_assessment: {
    country: { type: mongoose.Schema.Types.ObjectId, ref: 'RiskLevel', autopopulate: true },
    region: { type: mongoose.Schema.Types.ObjectId, ref: 'RiskLevel', autopopulate: true },
    international: { type: mongoose.Schema.Types.ObjectId, ref: 'RiskLevel', autopopulate: true },
    description: { type: String }
  },
  date: { type: Date, required: true },
  more_info: { type: String },
  document: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Files', autopopulate: true }],
  doc_src: [{ type: String}],
  images: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true }],
  images_src: [{ type: String}],
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

EventSchema.plugin(mongoosePaginate);
