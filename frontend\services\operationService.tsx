//Import Library
import axios from 'axios';

//Import services/components
import authService from './authService';


class OperationService {
  findAll = async (params = {}) => {
    try {
      const response = await axios.get(`${process.env.API_SERVER}/operation`, { params: params, headers: await authService.getAuthHeader() });
      return response;
    } catch (error: any) {
      return error.response ? error.response : {};
    }
  };

  createOperation = async (params = {}) => {
    const head = await authService.getAuthHeader() 
    try {
      const response = await axios.post(`${process.env.API_SERVER}/operation`, { ...params }, { headers: head });
      return response;
    } catch (error: any) {
      return error.response ? error.response : {};
    }
  };
}

export default new OperationService();


