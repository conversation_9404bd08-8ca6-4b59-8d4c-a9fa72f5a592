//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { RiskLevelController } from './risk-level.controller';
import { RiskLevelService } from './risk-level.service';
// SCHEMAS
import { RiskLevelSchema } from '../../schemas/risk_level.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'RiskLevel', schema: RiskLevelSchema }
    ])
  ],
  controllers: [RiskLevelController],
  providers: [RiskLevelService],
})

export class RiskLevelModule { }