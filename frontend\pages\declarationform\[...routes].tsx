//Import Library
import React, {useState, useEffect} from 'react';
import {useRouter} from "next/router";
import {Spinner} from 'react-bootstrap';

//Import services/components
import apiService from '../../services/apiService';
import DeclarationForm from "./declarationform";
import InvalidLink from "./invalidLink";
import Router from "../operation/[...routes]";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Validation = () => {
  const router = useRouter();
  const [linkValid, setLinkValid] = useState(null);
  const [userData, setUserData] = useState(null);
  const token = router.query.routes[0];

  const checkToken = async (authParams) => {
    const response = await apiService.post("/userLinkValidate", authParams);
    const lang = router.query.languageCode ? router.query.languageCode : "en";
    return response ? [setLinkValid(true), setUserData({...response,languageCode:lang})] : setLinkValid(false)
  };

  useEffect(() => {
    checkToken({code: token})
  }, [])

  let page = (<div className="d-flex justify-content-center align-items-center" style={{padding: '24px'}}>
    <Spinner animation="border" variant="primary"/>
  </div>)

  switch (linkValid) {
    case true:
      page = <DeclarationForm userProfile={userData} authToken={token}/>
      break;
    case false:
      page = <InvalidLink/>
  }

  return (
    <div>
      {page}
    </div>
  )
}

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Validation;