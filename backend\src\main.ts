//Import Library
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import * as express from 'express';
import * as passport from 'passport';
const session = require('express-session');
const MongoStore = require('connect-mongo');
require('dotenv').config();
import helmet from 'helmet';

//Import services/components
import { AppModule } from './app.module';

async function bootstrap() {
  const cookieSetting = {
    maxAge: process.env.MAXAGE,
    samesite: process.env.SAMESITE,
  };
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix(process.env.API_VERSION);
  app.useGlobalPipes(new ValidationPipe());
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ limit: '50mb', extended: true }));
  app.use(
    session({
      secret: process.env.SESSION_SECRETKEY,

      resave: false,

      saveUninitialized: false,

      ...cookieSetting,

      store: new MongoStore({
        mongoUrl: process.env.MONGODB,

        autoRemove: 'interval',

        autoRemoveInterval: 1,
      }),

      unset: 'destroy',
    }),
  );
  app.use(passport.initialize());
  app.use(passport.session());
  app.use('/cors', require('cors'));

  // Sets "X-Content-Type-Options: nosniff"- added secutrity headers using helmet pacakage
  app.use(helmet());

  // Sets "Strict-Transport-Security: max-age=123456; includeSubDomains"
  app.use(
    helmet({
      strictTransportSecurity: {
        maxAge: 31536000,
      },
    }),
  );

  // Sets "Strict-Transport-Security: max-age=123456; includeSubDomains; preload"
  app.use(
    helmet({
      strictTransportSecurity: {
        maxAge: 31536000,
        preload: true,
      },
    }),
  );

  passport.serializeUser(function (user, done) {
    done(null, user['_doc']);
  });

  passport.deserializeUser(function (user, done) {
    done(null, user);
  });

  if (process.env.DEPLOY === 'local') {
    app.enableCors({
      origin: [/^(.*)/],
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
      preflightContinue: false,
      optionsSuccessStatus: 200,
      credentials: true,
      allowedHeaders:
        'Origin,X-Requested-With,Content-Type,Accept,Authorization,authorization,X-Forwarded-for,X-Total-Count',
    });
  }
  await app.listen(process.env.APP_PORT);
}

bootstrap();
