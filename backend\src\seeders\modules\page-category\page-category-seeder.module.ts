//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { PageCategorySeederService } from './page-category-seeder.services';
// SCHEMAS
import { PageCategorySchema } from 'src/schemas/page-category.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'PageCategory', schema: PageCategorySchema }
      ]
    )
  ],
  providers: [PageCategorySeederService],
  exports: [PageCategorySeederService],
})
export class PageCategorySeederModule { }