//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { RolesInterface } from '../../interfaces/roles.interface';
import { CreateRolesDto } from './dto/create-roles.dto';
import { UpdateRolesDto } from './dto/update-roles.dto';
const FindRoles= 'Could not find Roles.'
@Injectable()
export class RolesService {
  constructor(
    @InjectModel('Roles') private rolesModel: Model<RolesInterface>
  ) { }

  async create(createRolesDto: CreateRolesDto): Promise<RolesInterface> {
    const createdRoles = new this.rolesModel(createRolesDto);
    return createdRoles.save();
  }

  async findOne(query) {
    const foundRecord = await this.rolesModel.findOne(query);
    return foundRecord;
  }

  async findAll(query): Promise<RolesInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.rolesModel.paginate(_filter, options);
  }

  async get(roleId): Promise<RolesInterface[]> {
    let _result;
    try {
      _result = await this.rolesModel.findById(roleId).exec();
    } catch (error) {
      throw new NotFoundException(FindRoles);
    }
    if (!_result) {
      throw new NotFoundException(FindRoles);
    }
    return _result;
  }

  async update(roleId: any, updateRolesDto: UpdateRolesDto) {
    const getById: any = await this.rolesModel.findById(roleId).exec();
    const updatedData = new this.rolesModel(updateRolesDto);
    try {
      Object.keys(updateRolesDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Roles.');
    }
    return getById;
  }

  async delete(roleId: string) {
    const result = await this.rolesModel.deleteOne({ _id: roleId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindRoles);
    }
  }
}
