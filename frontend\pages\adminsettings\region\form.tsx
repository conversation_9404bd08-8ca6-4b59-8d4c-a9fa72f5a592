//Import Library
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
// import { ValidationForm, TextInput, SelectGroup } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput, SelectGroup } from "../../../components/common/FormValidation";
import { useRef, useState, useEffect } from "react";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import apiService from "../../../services/apiService";
import { RegionInterface } from "../../../components/interfaces/region.interface";
import { useTranslation } from 'next-i18next';

const RegionForm = (props: any) => {
    const { t, i18n } = useTranslation('common');
    const titleSearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };
    const currentLang = i18n.language;
    const _initialRegion = {
        title: "",
        country: "",
    };
    const [initialVal, setInitialVal] = useState<RegionInterface>(_initialRegion);
    const [country, setCountry] = useState([]);

    const editform = props.routes && props.routes[0] === "edit_region" && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialRegion);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
            country: initialVal.country,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.Regions.Regionisupdatedsuccessfully";
            response = await apiService.patch(`/region/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.Regions.Regionisaddedsuccessfully";
            response = await apiService.post("/region", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/region");
        } else {
            toast.error(response);
        }
    };

    const handleChange = (e) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const getCountry = async (regionParams) => {
        const response = await apiService.get("/country", regionParams);
        if (response) {
            setCountry(response.data);
        }
    };

    useEffect(() => {
        const regionParams = {
            query: {},
            sort: titleSearch,
            limit: "~",
            languageCode: currentLang,
        };

        if (editform) {
            const getRegionData = async () => {
                const response = await apiService.get(`/region/${props.routes[1]}`, regionParams);
                if (response) {
                    const { country } = response;
                    response.country = country && country._id ? country._id : "";
                    setInitialVal((prevState) => ({ ...prevState, ...response }));
                }
            };
            getRegionData();
        }
        getCountry(regionParams);
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{editform ? t("adminsetting.Regions.EditRegion") : t("adminsetting.Regions.AddRegion")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.Regions.Country")}
                                        </Form.Label>
                                        <SelectGroup
                                            name="country"
                                            id="country"
                                            required
                                            value={initialVal.country}
                                            validator={(value) => String(value || '').trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.Regions.PleaseAddtheCountry"),
                                            }}
                                            onChange={handleChange}
                                        >
                                            <option value="">{t("adminsetting.Regions.SelectCountry")}</option>
                                            {country.length >= 1
                                                ? country.map((item, _i) => {
                                                      return (
                                                          <option key={item._id} value={item._id}>
                                                              {item.title}
                                                          </option>
                                                      );
                                                  })
                                                : null}
                                        </SelectGroup>
                                    </Form.Group>
                                </Col>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.Regions.Region")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            errorMessage={{validator: t("adminsetting.Regions.PleaseAddtheRegion")}}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.Regions.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.Regions.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/region`}
                                        >
                                        <Button variant="secondary">{t("adminsetting.Regions.Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};

export default RegionForm;
