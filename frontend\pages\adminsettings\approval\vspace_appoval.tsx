//Import Library
import { Container } from "react-bootstrap";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import VspaceAdmin from "./VspaceAdmin";
import { canAddVspaceApproval } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const VirtualSpaceShow = (_props: any) => {
  const { t } = useTranslation('common');
  const ShowVirtualSpace = () => {
    return (
      <Container fluid className="p-0">
        <PageHeading title={t("adminsetting.VirtualspaceApproval")} />
        <VspaceAdmin />
      </Container>
    )
  };

  const ShowAddVspaceApproval = canAddVspaceApproval(() => <ShowVirtualSpace />);
  const state:any = useSelector((state: any) => state);
  if (!(state?.permissions?.institution_focal_point?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddVspaceApproval />
  );  
}
export default VirtualSpaceShow;
