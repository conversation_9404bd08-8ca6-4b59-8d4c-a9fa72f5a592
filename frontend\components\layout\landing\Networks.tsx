//Import Library
import React from "react";
import { Col } from "react-bootstrap";
import Link from 'next/link';

//Import services/components
import { networkData, networkImages } from "../../../data/landing";

const Networks: React.FunctionComponent<{}> = (): React.ReactElement => (
  <Col sm={8}>
    <div className="block-title network">Our Networks</div>
    {networkData.map((item, idx) => (
      <div className="network-news" key={idx}>
        <img src={networkImages[idx]} />
        <div className="newsContent">
          <div className="newsTitle">{item.title}</div>          
          <div className="newsDesc">{item.content}
          {item.href ? <Link href={item.href} target="_blank">
          Find more information...
        </Link> : null}
        </div>
        </div>
      </div>
    ))}
  </Col>
);

export default Networks;
