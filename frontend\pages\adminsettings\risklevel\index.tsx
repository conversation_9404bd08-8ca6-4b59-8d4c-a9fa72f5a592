//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import RisklevelTable from "./risklevelTable";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import PageHeading from "../../../components/common/PageHeading";
import { canAddRiskLevels } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";


const RisklevelIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowRisklevelIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title={t("adminsetting.RiskLevel.Risklevel")} />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_risklevel"
                >
                <Button variant="secondary" size="sm">
                {t("adminsetting.RiskLevel.AddRisklevel")}
              </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              < RisklevelTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  const ShowAddRiskLevels = canAddRiskLevels(() => <ShowRisklevelIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.risk_level?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddRiskLevels />
  )
}

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default RisklevelIndex;