//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, Res, UseGuards, UseInterceptors, UploadedFile } from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';

//Import services/components
import { CreateFilesDto } from './dto/create-files.dto';
import { UpdateFilesDto } from './dto/update-files.dto';
import { FilesService } from "./files.service";
import { SessionGuard } from 'src/auth/session-guard';


@Controller('files')
export class FilesController {

  constructor(
    private readonly _filesService: FilesService
  ) { }

  @Post()
  @UseGuards(SessionGuard)
  @UseInterceptors(FileInterceptor('file',
    {
      storage: diskStorage({
        destination: './temp',
        limits: {
          fileSize: 8000000
       },
        filename: (req, file, cb) => {
          const fileName = `${file.originalname}-${Date.now()}`;
          return cb(null, `${fileName}${extname(file.originalname)}`);
        }
      })
    }
  )
  )
  create(@UploadedFile() file) {
    const createFilesDto: CreateFilesDto = {
      name: file.filename,
      original_name: file.originalname,
      extension: extname(file.originalname),
      is_temp: true
    };
    return this._filesService.create(createFilesDto);
  }

  @Get()
  @UseGuards(SessionGuard)
  findAll(@Query() query: any) {
    return this._filesService.findAll(query);
  }

  @Get(':id')
  @UseGuards(SessionGuard)
  findOne(@Param('id') fileId: string) {
    return this._filesService.get(fileId);
  }

  @Get("download/:id")
  async downloadFile(@Param('id') fileId: string, @Res() response: Response) {
    const fileData:any = await this._filesService.get(fileId);
    const _filepath = fileData ? `${process.cwd()}/files/${fileData['name']}` : null;
    const _filepathoriginalname = fileData ? `${process.cwd()}/files/${fileData['original_name']}` : null;
    if (fileData?.name && fs.existsSync(`${process.cwd()}/files/${fileData['name']}`)) {
      response.download(_filepath, _filepathoriginalname);
    } else {
      response.send({
        error: "File not found"
      });
    }
  }

  @Patch(':id')
  @UseGuards(SessionGuard)
  update(@Param('id') fileId: string, @Body() updateFilesDto: UpdateFilesDto) {
    return this._filesService.update(fileId, updateFilesDto);
  }

  @Delete(':id')
  @UseGuards(SessionGuard)
  async remove(@Param('id') fileId: string) {
    return await this._filesService.delete(fileId);
  }

}
