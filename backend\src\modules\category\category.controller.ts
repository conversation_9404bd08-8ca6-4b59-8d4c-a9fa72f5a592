//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryService } from "./category.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('category')
@UseGuards(SessionGuard)
export class CategoryController {

  constructor(
    private readonly _categoryService: CategoryService
  ) { }

  @Post()
  create(@Body() createCategoryDto: CreateCategoryDto) {
    const resp = this._categoryService.create(createCategoryDto);
    return resp;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._categoryService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') categoryId: string) {
    return this._categoryService.get(categoryId);
  }

  @Patch(':id')
  update(@Param('id') categoryId: string, @Body() updateCategoryDto: UpdateCategoryDto) {
    const resp = this._categoryService.update(categoryId, updateCategoryDto);
    return resp;
  }

  @Delete(':id')
  remove(@Param('id') categoryId: string) {
    const deletedData = this._categoryService.delete(categoryId);
    return deletedData;
  }
}
