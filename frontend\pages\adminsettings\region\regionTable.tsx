//Import Library
import { useState, useEffect } from "react";

import toast from 'react-hot-toast';
import Link from "next/link";
import { Mo<PERSON>, Button } from "react-bootstrap";

//Import services/components
import apiService from "../../../services/apiService";
import RKITable from "../../../components/common/RKITable";
import RegionTableFilter from "./regionTableFilter";
import { useTranslation } from 'next-i18next';

const RegionTable = (_props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectRegionDetails, setSelectRegion] = useState({});
    const [countryId, setCountryId]: any = useState("");

    const regionParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("adminsetting.Regions.Region"),
            selector: row => row.title,
            sortable: true,
        },
        {
            name: t("adminsetting.Regions.Country"),
            selector: row => row.country?.title || '',
            sortable: true,
            cell: (d) => (d.country && d.country.title ? d.country.title : ""),
        },
        {
            name: t("adminsetting.Regions.Action"),
            selector: row => row._id,
            sortable: false,
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_region/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    const getRegionData = async () => {
        setLoading(true);
        const response = await apiService.get("/region", regionParams);
        if (response && response.data) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        regionParams.limit = perPage;
        regionParams.page = page;
        countryId && (regionParams.query = { country: countryId.value });
        getRegionData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        regionParams.limit = newPerPage;
        regionParams.page = page;
        countryId && (regionParams.query = { country: countryId.value });
        setLoading(true);
        const response = await apiService.get("/region", regionParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectRegion(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/region/${selectRegionDetails}`);
            getRegionData();
            setModal(false);
            toast.success(t("adminsetting.Regions.Table.regionDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.Regions.Table.errorDeletingRegion"));
        }
    };

    const modalHide = () => setModal(false);

    //Handle Country Search
    const handleCountry = (country) => {
        setCountryId(country);
    };

    useEffect(() => {
        getRegionData();
    }, []);

    useEffect(() => {
        countryId && (regionParams.query = { country: countryId.value });
        getRegionData();
    }, [countryId]);

    return (
        <div className="region__table">
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.Regions.DeleteRegion")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.Regions.Areyousurewanttodeletethisregion?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.Regions.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.Regions.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>
            <RegionTableFilter countryHandler={handleCountry} value={countryId} />
            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default RegionTable;
