//Import Library
import axios from 'axios';

//Import services/components
import authService from './authService';


class OperationStatusService {
  findAll = async (params = {}) => {
    try {
      const response = await axios.get(`${process.env.API_SERVER}/operation_status`, { params: params, headers: await authService.getAuthHeader() });
      return response;
    } catch (error: any) {
      return error.response ? error.response : {};
    }
  };
}

export default new OperationStatusService();


