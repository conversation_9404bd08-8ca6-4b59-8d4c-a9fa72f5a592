//Import Library
import Link from "next/link";
import React, { useEffect, useState, useMemo, useRef } from "react";
import _ from "lodash";
import { useRouter } from "next/router";

//Import services/components
import RKITable from "../../components/common/RKITable";
import ProjectsTableFilter from "./ProjectsTableFilter";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const partner = "partner_institutions.partner_country"
const CountryLink = ({ partner_institutions }: any) => {
  if (partner_institutions && partner_institutions.length > 0) {
    return (
      <ul>
        {partner_institutions.map((item: any, index: any) => {
          if (item.partner_country) {
            return (
              <li key={index}>
                <Link
                  href="/country/[...routes]"
                  as={`/country/show/${item.partner_country?._id}`}
                >
                  {item.partner_country.title}
                </Link>
              </li>
            );
          }
        })}
      </ul>
    );
  }
  return null;
};

function ProjectsTable(props: any) {
  const router = useRouter();
  const { t } = useTranslation('common');
  const { setProjects, selectedRegions } = props;
  const [filterText, setFilterText] = React.useState("");
  const [filterStatus, setFilterStatus] = React.useState("");
  const [resetPaginationToggle, setResetPaginationToggle] = React.useState(
    false
  );
  const [tabledata, setDataToTable] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [pageSort, setPageSort] = useState<any>(null);


  const projectParams: any = {
    sort: { created_at: "desc" },
    lean: true,
    limit: perPage,
    page: 1,
    query: {},
    populate: [
      { path: "area_of_work", select: "title" },
      {
        path: partner, // "partner_institutions.partner_country"
        select: "coordinates title",
      },
      { path: "status", select: "title" },
    ],
    select:
      "-website -description -start_date -end_date -country -region -partner_institutions.partner_region -partner_institutions.partner_institution -institution_invites -vspace -vspace_visibility -user -created_at -updated_at",
  };

  const [projParams, setProjParams] = useState(projectParams);

  const columns = [
    {
      name: t("Project(s)"),
      selector: "title",
      sortable: true,
      cell: (d: any) => (
        <Link href="/project/[...routes]" as={`/project/show/${d?._id}`}>
          {d.title}
        </Link>
      ),
    },
    {
      name: t("Country"),
      selector: "country",
      sortable: true,
      cell: (d: any) => (
        <CountryLink partner_institutions={d.partner_institutions} />
      ),
    },
    {
      name: t("AreaofWork"),
      selector: "area_of_work",
      cell: (d: any) => (d.area_of_work ? d.area_of_work.map((item: any) => item.title).join(", ") : ""),
    },
    {
      name: t("Status"),
      selector: "status",
      sortable: true,
      cell: (d: any) => (d.status && d.status.title ? d.status.title : ""),
    },
    {
      name: t("Fundedby"),
      selector: "funded_by",
      sortable: true,
    },
  ];

  const getProjectsData = async (projectParamsinitial: any) => {
    setLoading(true);

    if (router.query && router.query.country) {
      projectParamsinitial.query[partner] = [
        router.query.country,
      ];
    }

    // Handle selectedRegions with proper condition
    if (selectedRegions === null) {
      // First load: don't apply region filter
      delete projectParamsinitial.query["partner_institutions.world_region"];
    } else if (selectedRegions.length === 0) {
      // No regions selected: force zero results
      projectParamsinitial.query["partner_institutions.world_region"] = ["__NO_MATCH__"];
    } else {
      // Normal filtering
      projectParamsinitial.query["partner_institutions.world_region"] = selectedRegions;
    }

    const response = await apiService.get("/project", projectParamsinitial);
    if (response && Array.isArray(response.data)) {
      setDataToTable(response.data);
      setProjects(response.data);
      setTotalRows(response.totalCount);
    }

    setLoading(false);
  };


  const handlePageChange = (page: any) => {
    projectParams.limit = perPage;
    projectParams.page = page;

    if (filterStatus) {
      projectParams.query = { ...projectParams.query, status: filterStatus };
    }

    pageSort && (projectParams.sort = pageSort.sort);

    // Get the data
    getProjectsData(projectParams);
    setPageNum(page);
  };


  const handlePerRowsChange = async (newPerPage: any, page: any) => {
    projectParams.limit = newPerPage;
    projectParams.page = page;
    setLoading(true);

    if (router.query && router.query.country) {
      projectParams.query[partner] = [
        router.query.country,
      ];
    }

    // Handle selected regions similarly as in `getProjectsData()`
    if (selectedRegions === null) {
      delete projectParams.query["partner_institutions.world_region"];
    } else if (selectedRegions.length === 0) {
      projectParams.query["partner_institutions.world_region"] = ["__NO_MATCH__"];
    } else {
      projectParams.query["partner_institutions.world_region"] = selectedRegions;
    }

    filterStatus && (projectParams.query = { ...projectParams.query, status: filterStatus });
    pageSort && (projectParams.sort = pageSort.sort);

    const response = await apiService.get("/project", projectParams);
    if (response && Array.isArray(response.data)) {
      setDataToTable(response.data);
      setProjects(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }

    setPageNum(page);
  };


  useEffect(() => {
    projParams.page = 1;
    getProjectsData(projectParams);
  }, [selectedRegions, router]);

  useEffect(() => {
    getProjectsData(projParams);
  }, [projParams]);


  const handleSort = async (column: any, sortDirection: any) => {
    setLoading(true);
    projectParams.sort = {
      [column.selector]: sortDirection,
    };
    filterStatus && (projectParams.query = { ...projectParams.query, status: filterStatus });
    filterText !== "" && (projectParams.query = { ...projectParams.query, title: filterText });

    await getProjectsData(projectParams);
    setPageSort(projectParams);
    setLoading(false);
  };

  const sendQuery = (q: any, page: any) => {
    if (q) {
      projParams.query["title"] = q;
      projParams.page = page;
      setProjParams({ ...projParams });
    } else {
      delete projParams.query.title;
      setProjParams({ ...projParams });
    }
  };

  const handleSearchTitle = useRef(
    _.debounce((q, page) => sendQuery(q, page), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)
  ).current;

  const subHeaderComponentMemo = useMemo(() => {
    const handleClear = () => {
      if (filterText) {
        setResetPaginationToggle(!resetPaginationToggle);
        setFilterText("");
      }
    };

    const handleFilterStatusChange = (status: any) => {
      setFilterStatus(status);
      if (status) {
        projParams.query["status"] = status;
        projParams.page = pageNum;
        setProjParams({ ...projParams });
      } else {
        delete projParams.query.status;
        setProjParams({ ...projParams });
      }
    };

    const handleChange = (e: any) => {
      setFilterText(e.target.value);
      handleSearchTitle(e.target.value, pageNum);
    };

    return (
      <ProjectsTableFilter
        onFilter={handleChange}
        onFilterStatusChange={(e: any) => handleFilterStatusChange(e.target.value)}
        onClear={handleClear}
        filterText={filterText}
        filterStatus={filterStatus}
      />
    );
  }, [filterText, filterStatus, resetPaginationToggle, selectedRegions, pageNum]);

  return (
    <RKITable
      columns={columns}
      data={tabledata}
      totalRows={totalRows}
      loading={loading}
      subheader
      persistTableHead
      onSort={handleSort}
      sortServer
      pagServer={true}
      resetPaginationToggle={resetPaginationToggle}
      subHeaderComponent={subHeaderComponentMemo}
      handlePerRowsChange={handlePerRowsChange}
      handlePageChange={handlePageChange}
    />
  );
}

export default ProjectsTable;
