//Import Library
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

interface UserData {
    _id: string;
    username: string;
    email: string;
    institutionInvites: Array<{
        institutionId: string;
        institutionName: string;
        status: string;
    }>;
    [key: string]: any;
}

function AdminTable(_props: any) {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState<any[]>([]);
    const [, setLoading] = useState<boolean>(false);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [perPage, setPerPage] = useState<number>(10);
    const [isModalShow, setModal] = useState<boolean>(false);
    const [newStatus, setNewStatus] = useState<string>("");
    const [selectUserDetails, setSelectUserDetails] = useState<any>({});


    const usersParams = {
        sort: { created_at: "desc" },
        limit: "~",
        page: 1,
        query: { "institutionInvites.status": "Request Pending" },
    };

    const columns = [
        {
            name: t("adminsetting.FocalPointsApprovalTable.Username"),
            selector: "username",
            cell: (d: any) => d.username,
        },
        {
            name: t("OrganisationName"),
            selector: "institutionName",
            cell: (d: any) => d.institutionName,
        },
        {
            name: t("adminsetting.FocalPointsApprovalTable.Email"),
            selector: "email",
            cell: (d: any) => d.email,
        },
        {
            name: t("adminsetting.FocalPointsApprovalTable.Action"),
            selector: "",
            cell: (d: any) => (
                <div>
                    {d.institutionStatus === "Rejected" || d.institutionStatus === "Request Pending" ? (
                        <>
                            <Button variant="primary" size="sm" onClick={() => userAction(d, "approve")}>
                                {t("instu.Approves")}
                            </Button>
                            &nbsp;
                        </>
                    ) : (
                        <></>
                    )}
                    {d.institutionStatus === "Approved" || d.institutionStatus === "Request Pending" ? (
                        <Button variant="secondary" size="sm" onClick={() => userAction(d, "reject")}>
                            {t("instu.Reject")}
                        </Button>
                    ) : (
                        <></>
                    )}
                </div>
            ),
        },
    ];

    const getTableData = (data: UserData[]) => {
        let tableData: any[] = [];
        data.forEach((user) => {
            user?.institutionInvites.forEach((institutionInvite) => {
                if (institutionInvite && institutionInvite.status === "Request Pending") {
                    tableData.push({
                        ...user,
                        ...{
                            institutionId: institutionInvite.institutionId,
                            institutionName: institutionInvite.institutionName,
                            institutionStatus: institutionInvite.status,
                        },
                    });
                }
            });
        });
        return tableData;
    };

    const getUsersData = async () => {
        setLoading(true);
        const response = await apiService.get("/users", usersParams);
        if (response && response.data) {
            let tableData = getTableData(response.data);
            setDataToTable(localPaginate(tableData, perPage, 1));
            setTotalRows(tableData.length);
            setLoading(false);
        }
    };
    const handlePageChange = async (page: number) => {
        setLoading(true);
        const response = await apiService.get("/users", usersParams);
        if (response && response.data && response.data.length > 0) {
            let tableData = getTableData(response.data);
            setDataToTable(localPaginate(tableData, perPage, page));
            setLoading(false);
        }
    };

    const handlePerRowsChange = async (newPerPage: number, page: number) => {
        setLoading(true);
        const response = await apiService.get("/users", usersParams);
        if (response && response.data && response.data.length > 0) {
            let tableData = getTableData(response.data);
            setDataToTable(localPaginate(tableData, newPerPage, page));
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const localPaginate = (array: any[], page_size: number, page_number: number) => {
        return array.slice((page_number - 1) * page_size, page_number * page_size);
    };

    useEffect(() => {
        getUsersData();
    }, []);

    const userAction = async (d: any, status: string) => {
        console.log(d, status);
        setModal(true);
        setNewStatus(status);
        if (d && d._id) {
            const setStatus = status === "approve" ? "Approved" : "Rejected";
            setSelectUserDetails({ ...d, status: setStatus });
        }
    };

    const modalConfirm = async () => {
        selectUserDetails["is_focal_point"] = true;
        selectUserDetails["status"] = "Approved";
        if (
            selectUserDetails &&
            selectUserDetails["institutionInvites"] &&
            selectUserDetails["institutionInvites"].length
        ) {
            selectUserDetails["institutionInvites"].map((invite: any) => {
                if (invite.institutionId === selectUserDetails["institutionId"]) {
                    invite.status = newStatus === "approve" ? "Approved" : "Rejected";
                }
                return invite;
            });
        }
        let updatedData;
        if(newStatus !== "approve") {
            await apiService.remove(`/users/${selectUserDetails["_id"]}`);
        } else {
            updatedData = await apiService.patch(`/users/${selectUserDetails["_id"]}`, selectUserDetails);
        }
        if (updatedData && updatedData.status === 403) {
            toast.error(
                updatedData.response && updatedData.response.message
                    ? updatedData.response.message
                    : t("adminsetting.FocalPointsApprovalTable.Somethingwentswrong")
            );
            return;
        } else {
            getUsersData();
            if (newStatus === "approve") {
                toast.success(t("adminsetting.FocalPointsApprovalTable.Approvemm"));
            } else {
                toast.error(t("adminsetting.FocalPointsApprovalTable.Rejected"));
            }
            setSelectUserDetails({});
            setModal(false);
        }
    };

    const modalHide = () => setModal(false);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>
                        {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}{" "}
                        {t("adminsetting.FocalPointsApprovalTable.User")}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {t("adminsetting.FocalPointsApprovalTable.Areyousurewantto")} {newStatus}{" "}
                    {t("adminsetting.FocalPointsApprovalTable.thisuser?")}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.FocalPointsApprovalTable.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.FocalPointsApprovalTable.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
}

export default AdminTable;
