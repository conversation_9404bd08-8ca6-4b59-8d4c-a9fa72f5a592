//Import Library
import { Col, Container, Row } from "react-bootstrap";
import Select from 'react-select';
import React, { useEffect, useState } from "react";

//Import services/components
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';


const RegionTableFilter = ({ countryHandler, value }: any) => {
  const { t,i18n } = useTranslation('common');
  const titleSearch = i18n.language === 'de'? {title_de: "asc"} : {title: "asc"};
  const currentLang = i18n.language;
  const [countries, setCountreis] = useState([]);
  const countryParams = {
    sort: titleSearch,
    limit: "~",
    languageCode:currentLang
  };

  useEffect(() => {
    const fetchCountries = async () => {
      const response = await apiService.get("/country", countryParams);
      if (response && response.data && response.data.length > 0) {
        setCountreis(response.data);
      }
    }
    fetchCountries();
  }, [])
  return (
    <Container fluid>
      <Row>
        <Col md={4} className="ps-1">
          <Select
            value={[value]}
            placeholder={t("adminsetting.Regions.SelectCountry")}
            isClearable={true}
            onChange={countryHandler}
            options={countries.length > 0 ? countries.map((item, _i) => ({ value: item._id, label: item.title })) : []}
          />
        </Col>
      </Row>
    </Container>
  )
};

export default RegionTableFilter;