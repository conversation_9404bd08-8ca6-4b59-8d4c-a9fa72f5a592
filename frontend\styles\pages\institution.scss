.quick-info-filter {
  min-height: 300px;
  background: #1e2538;
  display: flex;
  align-items: center;
  justify-content: center;

  .list-group-item {
    background: transparent;
    margin: 3px 0;
    &:hover .quickinfo-img,
    &.active .quickinfo-img {
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
    }
    a,
    .info-item {
      display: flex;
      align-items: center;
      color: #fff;
      font-size: 15px;
      span {
        padding: 0 0 0 10px;
        b {
          font-weight: 500;
          font-size: 20px;
        }
      }
    }
  }

  .quickinfo-img {
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #fff;
    border-radius: 50%;
    background: #939393;
    background: radial-gradient(ellipse at center, #939393 0%, #585858 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#939393', endColorstr='#585858', GradientType=1);
  }
}

.organisationMap {
  width: calc(100% - 275px);
}

.organisationInfo {
  width: 275px;
}

.countryAccordionNew {
  // padding-top: 25px;
  // padding-bottom: 25px;

  .card {
    border: none;
    margin: 25px 0;
  }

  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;

    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }

    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }

    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;

      svg {
        font-size: 16px;
      }
    }
  }
}

.countryAccordion1 {
  // padding-top: 25px;
  // padding-bottom: 25px;

  .card {
    border: none;
    margin: 25px 0;
  }

  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;

    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }

    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }

    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;

      svg {
        font-size: 16px;
      }
    }
  }
}

.institutionDetails {
  color: #0b1627;
  p {
    margin: 0 0 13px;
    display: flex;
    li {
      list-style-type: none;
    }
    b {
      width: 170px;
      display: inline-block;
    }
    span {
      display: inline-block;
      width: calc(100% - 175px);
      margin: 0 0 0 5px;
    }
  }
}

.institutionDialCode {
  width: 110px;
}

.institutionTelephone {
  width: 300px;
}

.institution-image-block {
  height: 550px;
  overflow: hidden;
  margin: -8px -8px 0;
  width: calc(100% + 16px);
  position: relative;
  .institution-image-cover {
    width: 100%;
    max-height: 550px;
    object-fit: cover;
  }
  .institution-imageLoader {
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    position: relative;
  }
}

.institution-image-inner-content {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  width: 100%;
  top: 45%;
  position: absolute;
  background: rgba(0, 0, 0, 0.85);
  color: #fff;
  padding: 10px;
  height: 320px;
  overflow-y: scroll;
}
.institutionTitle {
  margin: 0;
  padding: 0 15px 0 0;
  font-size: 20px;
}

.instButton {
  width: 150px;
}

.institutionInfo {
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    li {
      margin-bottom: 0px;
      label {
        margin-bottom: 0;
        float: left;
        min-width: 86px;
        text-align: right;
        font-weight: bold;
        margin-right: 15px;
      }
      .field-value {
        margin-left: 10px;
        color: #2aa4f6;
        display: flex;
        word-break: break-all;
        overflow-wrap: break-word;
      }
    }
  }
  .address {
    display: inline-flex;
    width: 500px;
    padding: 0;
    margin: 0;
  }

  b {
    width: 80px;
    margin: 0 10px 0 0;
    font-weight: 600;
    display: inline-block;
  }
  p {
    display: inline-block;
    margin: 0px;
    span {
      color: #2aa4f6;
    }
    a {
      color: #2aa4f6;
    }
  }
}

h6.other-focal-points-header {
  text-align: center;
}
.focalPoints:last-child{padding: 0 0 0 18px;}
.focalPoints {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  list-style: none;
  margin: 0 0 50px;
  padding: 0 0 0 0px;
  
  //margin-right: 100px;

  &.primary {
    margin-top: 0px;
  }
  li {
    position: relative;
    &.isPrimary {
      display: flex;
      h6 {
        width: 60px;
      }
      span.image-container {
        width: 46px;
        height: 46px;
        border-radius: 100%;
        border-color: #228ddb;
      }
      &:hover {
        .focalpointDetails {
          margin-top: 3px;
          right: 0px;
        }
      }
    }
    span.image-container {
      display: inline-block;
      width: 43px;
      height: 43px;
      background: #ffffff;
      border-radius: 25px;
      text-align: center;
      line-height: 40px;
      margin: 0px 5px;
      font-size: 15px;
      font-weight: bold;
      overflow: hidden;
      border: 3px solid transparent;
      cursor: pointer;
      color: #212529;
      -webkit-transition: background-color 2s ease-out;
      -moz-transition: background-color 2s ease-out;
      -o-transition: background-color 2s ease-out;
      transition: background-color 2s ease-out;
      i {
        //padding: 15px;
        color: #229adb;
      }
      img {
        width: 100%;
      }
      &:hover {
        border: 3px solid #229adb;
      }
    }
    &:hover {
      .focalpointDetails {
        display: block;
      }
    }
  }
}

.focalpointDetails {
  display: none;
  color: #ffffff;
  list-style: none;
  font-size: 15px;
  margin: 5px 5px 0 5px;
  position: absolute;
  text-align: right;
  line-height: 22px;
  font-weight: 400;
  top: 40px;
  right: 12px;
  z-index: 1;
  b {
    color: #229adb;
    font-size: 16px;
    font-weight: bold;
    line-height: 16px;
  }
  span {
    display: block;
  }
}

.fpDetailsFixed {
  background: #343434f2;
  padding: 5px;
  border-radius: 4px;
  z-index: 10;
  //position: fixed;
}

.institution-infographic-block {
  .list-group-item {
    background: transparent;
    margin: 3px 0;
    border: none;
    a {
      display: flex;
      align-items: center;
      color: #1c587a;
    }
    .quickinfoDesc {
      color: #1c587a;
      margin: 0 0 0 15px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      h5 {
        font-size: 18px;
        font-weight: 400;
        margin: 0;
      }
      h4 {
        font-size: 38px;
        font-weight: 400;
        margin: 0;
        line-height: 34px;
      }
    }
  }
  .clickable {
    cursor: pointer;
  }

  .quickinfo-img {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 5px solid #ddd;
    border-radius: 50%;
    background: #229adb;
    background: -moz-radial-gradient(
      center,
      ellipse cover,
      #229adb 0%,
      #1e6090 80%,
      #174970 100%
    );
    background: -webkit-radial-gradient(
      center,
      ellipse cover,
      #229adb 0%,
      #1e6090 80%,
      #174970 100%
    );
    background: radial-gradient(
      ellipse at center,
      #229adb 0%,
      #1e6090 80%,
      #174970 100%
    );
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#174970', GradientType=1);
  }
}

.primary-focal-point-table {
  .rdt_Table {
    height: auto;
  }
  .rdt_Pagination {
    display: none;
  }
}

.readMoreBtn {
  height: 26px;
  font-size: 10px;
}

.imgRotate {
  height: 500px;
  overflow: hidden !important;
}

.info-identifier {
  display: flex;
  width: 96%;
  height: 218px;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 0;
  top: 46%;
  color: #ffffff;
  justify-content: center;
  align-items: center;
  font-weight: 300;
}

.institution__table {
  .mb-3 {
    margin-bottom: 0;
  }
}
._tabelw table{width: 98% !important; display: none;}
._readmore_d{overflow-x: scroll;}
._readmore_d table{width: 100% !important;}
.institution-image-inner-content::-webkit-scrollbar {
  width: 6px;
  border-radius: 10px;
}

.institution-image-inner-content::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
}

.institution-image-inner-content::-webkit-scrollbar-thumb {
background-color: darkgrey;
outline: 1px solid slategrey;
}