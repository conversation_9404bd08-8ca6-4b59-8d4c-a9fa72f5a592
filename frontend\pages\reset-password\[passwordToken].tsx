//Import Library
import Router, { useRouter } from 'next/router'
import { connect } from "react-redux";
import React, {useEffect, useRef, useState} from 'react';
import { TextInput } from "../../components/common/FormValidation";
import ValidationFormWrapper from '../../components/common/ValidationFormWrapper';
import toast from 'react-hot-toast';
import Link from "next/link";
import {faArrowCircleLeft, faExclamationCircle} from "@fortawesome/free-solid-svg-icons";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";

//Import services/components
import apiService from "../../services/apiService";
import { getUser } from '../../shared/services/local-storage';

const responseMessage = {
  "RESET_PASSWORD.CHANGE_PASSWORD_ERROR": "Unable to reset password. Contact Administrator",
  "RESET_PASSWORD.WRONG_CURRENT_PASSWORD": "Wrong Current password",
  "RESET_PASSWORD.PASSWORD_CHANGED": "Password Changed Successfully.",
  "RESET_PASSWORD.NO_TOKEN": "Invalid Token. Please reset password and try again",
  "RESET_PASSWORD.EXPIRED_TOKEN": "Token Expired. Please reset password again",
  "RESET_PASSWORD.EXPIRY_RESET_HOURS_NOT_SET": "Unable to reset password. Contact Administrator"
}

const ResetPassword = (props) => {

  const router = useRouter();
  const { passwordToken } = router.query;
  const formRef = useRef(null);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleChange = (e) => {
    if (e.target.name === "password") {
      setNewPassword(e.target.value);
    }

    if (e.target.name === "confirmPassword") {
      setConfirmPassword(e.target.value);
    }
  }

  const submitHandler = async (e, formData) => {
    e.preventDefault();
    if (formData.password === formData.confirmPassword) {
      const response = await apiService.post('/email/reset-password', {newPasswordToken: passwordToken, newPassword: confirmPassword});
      if (response && response.success) {
        toast.success(responseMessage[response.message]);
        Router.push('/login');
      } else {
        if (response.data && response.data.message) {
          toast.error(responseMessage[response.data.message]);
        } else {
          toast.error(responseMessage[response.message]);
        }
      }
    }
  }

  const matchPassword = (value) => {
    return value && value === newPassword;
  }

  useEffect(() => {
    const user = getUser();
    if (user && user.username) {
      setIsLoggedIn(true);
    }
  },[])

  return (
    <div className="loginContainer ">
      <div className='section'>
        <div className='container'>
          <div className='columns'>
            <div className='column  is-two-thirds'>
              <div className='column reset-password'>
                <div className="imgBanner">
                  <img src="/images/login-banner.jpg" alt="RKI Login Banner Image"/>
                </div>
                <ValidationFormWrapper className="formContainer" onSubmit={submitHandler} ref={formRef}>
                  <div className="logoContainer">
                    <Link href={'/'}>

                      <img src="/images/logo.jpg" alt="Rohert Koch Institut - Logo"/>

                    </Link>
                  </div>
                  <section className="fieldsContainer">
                    {!isLoggedIn ? (<>
                    <div>
                      <div className="mb-3">
                        <label htmlFor="password">Password</label>
                        <TextInput
                          name="password"
                          id="password"
                          type="password"
                          required
                          pattern="(?=.*[A-Z]).{8,}"
                          errorMessage={{
                            required: "Password is required",
                            pattern: "Password should be at least 8 characters and contains at least one upper case letter"
                          }}
                          value={newPassword}
                          onChange={handleChange}
                        />
                      </div>
                      <div className="mb-3">
                        <label htmlFor="confirmPassword">Confirm Password</label>
                        <TextInput
                          name="confirmPassword"
                          id="confirmPassword"
                          type="password"
                          required
                          validator={matchPassword}
                          errorMessage={{
                            required:"Confirm password is required",
                            validator: "Password does not match"
                          }}
                          value={confirmPassword}
                          onChange={handleChange}
                        />
                      </div>
                      <div className='field is-grouped'>
                        <div className='control'>
                          <button className='button is-primary' type='submit'>
                            Reset Password
                          </button>
                        </div>
                      </div>
                    </div>
                    </>):(<div>
                      <div className="d-flex flex-column justify-content-center align-items-center">
                        <FontAwesomeIcon icon={faExclamationCircle} color="#e8ba0d" size="5x" className="error-icon"/>
                        <p>Logged in user cannot use reset password</p>
                        <Link href="/" as="/" >
                          <button className="button is-primary">
                            <FontAwesomeIcon icon={faArrowCircleLeft} color="#ffff" size="1x"/> Back to RKI Dashboard
                          </button>
                        </Link>
                      </div>
                    </div>)}
                  </section>
                </ValidationFormWrapper>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default connect((state) => state)(ResetPassword);