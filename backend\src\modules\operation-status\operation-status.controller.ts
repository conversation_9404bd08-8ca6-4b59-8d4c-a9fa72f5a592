//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateOperationStatusDto } from './dto/create-operation-status.dto';
import { UpdateOperationStatusDto } from './dto/update-operation-status.dto';
import { OperationStatusService } from './operation-status.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('operation_status')
@UseGuards(SessionGuard)
export class OperationStatusController {
  constructor(
    private readonly _operationStatusService: OperationStatusService,
  ) {}

  @Post()
  async create(@Body() createOperationStatusDto: CreateOperationStatusDto) {
    try {
      const _operation_status = await this._operationStatusService.create(
        createOperationStatusDto,
      );
      return _operation_status;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._operationStatusService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') operationStatusId: string) {
    return this._operationStatusService.get(operationStatusId);
  }

  @Patch(':id')
  async update(
    @Param('id') operationStatusId: string,
    @Body() updateOperationStatusDto: UpdateOperationStatusDto,
  ) {
    try {
      const _operation_status = await this._operationStatusService.update(
        operationStatusId,
        updateOperationStatusDto,
      );
      return _operation_status;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') operationStatusId: string) {
    const _operation_status =
      this._operationStatusService.delete(operationStatusId);
    return _operation_status;
  }
}
