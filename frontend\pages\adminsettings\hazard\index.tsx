//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import HazardTable from "./hazardTable";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddHazards } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const HazardIndex = (_props) => {
  const { t } = useTranslation("common");
  const ShowHazardIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title={t("menu.hazards")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_hazard"
              >
              <Button variant="secondary" size="sm">
                {t("addHazard")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <HazardTable />
          </Col>
        </Row>
      </Container>
    );
  }

  const ShowAddHazards = canAddHazards(() => <ShowHazardIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.hazard?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddHazards />
  );
};

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default HazardIndex;
