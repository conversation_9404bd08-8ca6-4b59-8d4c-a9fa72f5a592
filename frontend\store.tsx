//Import Library
import { applyMiddleware, combineReducers, createStore } from 'redux'
import { persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import createSagaMiddleware from 'redux-saga'

//Import services/components
import UserReducer from "./stores/userReducer";
import PermissionReducer from "./stores/permissionsReducer";
import rootSaga from './saga'

const rootReducer = combineReducers(
  {
    user: UserReducer,
    permissions: PermissionReducer
  }
);

const persistConfig = {
  key: 'root',
  storage,
}

const persistedReducer = persistReducer(persistConfig, rootReducer as any)

const bindMiddleware = (middleware: any) => {
  if (process.env.NODE_ENV !== 'production') {
    const { composeWithDevTools } = require('@redux-devtools/extension')
    return composeWithDevTools(applyMiddleware(...middleware))
  }
  return applyMiddleware(...middleware)
}

function configureStore(initialState: any) {
  const sagaMiddleware = createSagaMiddleware()
  const store: any = createStore(
    persistedReducer,
    initialState,
    bindMiddleware([sagaMiddleware])
  );
  
  store.sagaTask = sagaMiddleware.run(rootSaga)
  
  return store;
}

export default configureStore;