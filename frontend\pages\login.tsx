//Import Library
import { useEffect, useState } from "react";
import { connect } from "react-redux";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";
import { Alert } from "react-bootstrap";

//Import services/components
import { loadLoggedinUserData } from "../stores/userActions";
import authService from "./../services/authService";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

interface HomeProps {
  isAdminLogin?: boolean;
  [key: string]: any;
}

const Home = (props: HomeProps) => {
    const { t } = useTranslation('common');
      const [contact, setContact] = useState<{ username: string; password: string; rememberMe: boolean }>({
        username: "",
        password: "",
        rememberMe: false,
    });
    const [, setShow] = useState(false);

    const handleKeypress = (e: React.KeyboardEvent) => {
        //it triggers by pressing the enter key
        if (e.keyCode === 13) {
            handleSubmit(e);
        }
    };

    const [erroMessage, setErroMessage] = useState({
        message: "",
        display: false,
    });

    useEffect(() => {
        const rememberMe = localStorage.getItem("rememberMe") === "true";
        const user = rememberMe ? localStorage.getItem("user") : "";
        const password = rememberMe ? localStorage.getItem("password") : "";
        setContact({ username: user, password: password, rememberMe: rememberMe });
    }, []);

    const RememberhandleChange = (event) => {
        const input = event.target;
        const value = input.type === "checkbox" ? input.checked : input.value;
        setContact({ ...contact, [input.name]: value });
        setErroMessage({ message: "", display: false });
    };
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setErroMessage({ message: "", display: false });
        setContact({ ...contact, [e.target.name]: e.target.value });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const { auth, logout } = authService;
        contact.username = contact.username.trim();
        const response = await auth(contact);
        if (response && response.status === 201 && response.data && response.data.isEnabled) {
            toast.success(t("login.success"));

            Router.push("/");
            props.dispatch(loadLoggedinUserData());
        } else if (response.status === 403) {
            await logout();
            setErroMessage({ display: true, message: t("login.unauthAccess") });
        } else {
            setShow(true);
            setContact((prevState) => ({ ...prevState, username: "", password: "" }));
            if (response.status === 401) {
                setErroMessage({ display: true, message: t("login.invalidUserPass") });
            }
        }
        localStorage.setItem("rememberMe", contact.rememberMe.toString());
        localStorage.setItem("user", contact.rememberMe ? contact.username : "");
        localStorage.setItem("password", contact.rememberMe ? contact.password : "");
    };

    return (
        <div className="loginContainer">
            {erroMessage.display ? (
                <Alert key={"danger"} variant={"danger"}>
                    {erroMessage.message}
                </Alert>
            ) : (
                <></>
            )}
            <div className="section">
                <div className="container">
                    <div className="columns">
                        <div className="column  is-two-thirds">
                            <div className="column loginForm">
                                <div className="imgBanner">
                                    <img src="/images/login-banner.jpg" alt="RKI Login Banner Image" />
                                </div>
                                <form className="formContainer" onSubmit={handleSubmit}>
                                    <div className="logoContainer">
                                        <Link href={"/"}>

                                            <img src="/images/logo.jpg" alt="Rohert Koch Institut - Logo" />

                                        </Link>
                                    </div>
                                    <div>
                                        <div className="mb-3">
                                            <label className="label">Username or Email</label>
                                            <input
                                                className="form-control"
                                                type="text"
                                                placeholder="Enter the Username or Email"
                                                name="username"
                                                value={contact.username}
                                                onChange={handleChange}
                                                onKeyPress={handleKeypress}
                                                required
                                            />
                                        </div>
                                        <div className="mb-3">
                                            <label className="label">Password</label>
                                            <input
                                                className="form-control"
                                                type="password"
                                                value={contact.password}
                                                placeholder="Password"
                                                name="password"
                                                onChange={handleChange}
                                                onKeyPress={handleKeypress}
                                                required
                                            />
                                        </div>
                                        <div className="mb-3 form-check d-flex justify-content-between">
                                            <label>
                                                <input
                                                    name="rememberMe"
                                                    checked={contact.rememberMe}
                                                    onChange={RememberhandleChange}
                                                    type="checkbox"
                                                />{" "}
                                                Remember me
                                            </label>
                                            <Link href="/forgot-password" as="/forgot-password" >
                                                <p className="text-primary btn p-0">Forgot Password?</p>
                                            </Link>
                                        </div>

                                        <div className="field is-grouped">
                                            <div className="control">
                                                <button className="button is-primary" type="submit">
                                                    Sign in
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div className="column" />
                    </div>
                </div>
            </div>
        </div>
    );
};

export async function getStaticProps({ locale }: { locale: string }) {
    return {
        props: {
            ...(await serverSideTranslations(locale, ['common'])),
        },
    }
}

export default connect()(Home);
