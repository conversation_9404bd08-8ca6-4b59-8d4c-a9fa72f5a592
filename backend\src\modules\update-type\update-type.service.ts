//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { UpdateTypeInterface } from '../../interfaces/update-type.interface';
import { CreateUpdateTypeDto } from './dto/create-update-type.dto';
import { UpdateUpdateTypeDto } from './dto/update-update-type.dto';
const FindUpdateType = 'Could not find UpdateType.';
@Injectable()
export class UpdateTypeService {
  constructor(
    @InjectModel('UpdateType')
    private updateTypeModel: Model<UpdateTypeInterface>,
  ) {}

  async create(
    createUpdateTypeDto: CreateUpdateTypeDto,
  ): Promise<UpdateTypeInterface> {
    const createdUpdateType = new this.updateTypeModel(createUpdateTypeDto);
    return createdUpdateType.save();
  }

  async findAll(query): Promise<UpdateTypeInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.updateTypeModel.paginate(_filter, options);
  }

  async get(updateTypeId): Promise<UpdateTypeInterface[]> {
    let _result;
    try {
      _result = await this.updateTypeModel.findById(updateTypeId).exec();
    } catch (error) {
      throw new NotFoundException(FindUpdateType);
    }
    if (!_result) {
      throw new NotFoundException(FindUpdateType);
    }
    return _result;
  }

  async update(updateTypeId: any, updateUpdateTypeDto: UpdateUpdateTypeDto) {
    const getById: any = await this.updateTypeModel
      .findById(updateTypeId)
      .exec();
    const updatedData = new this.updateTypeModel(updateUpdateTypeDto);
    Object.keys(updateUpdateTypeDto).forEach((d) => {
      getById[d] = updatedData[d];
    });
    getById.updated_at = new Date();
    return getById.save();
  }

  async delete(updateTypeId: string) {
    const result = await this.updateTypeModel
      .deleteOne({ _id: updateTypeId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(FindUpdateType);
    }
  }
}
