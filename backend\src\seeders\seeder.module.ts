//Import Library
import { Mo<PERSON><PERSON>, Logger } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ConfigModule } from '@nestjs/config';

//Import services/components
import { SeederService } from "./seeder.service";
import { WorldRegionSeederModule } from "./modules/world-region/world-region-seeder.module";
import { LanguageSeederModule } from "./modules/language/language-seeder.module";
import { AreaOfWorkSeederModule } from "./modules/area-of-work/area-of-work-seeder.module";
import { CountrySeederModule } from "./modules/country/country-seeder.module";
import { HazardSeederModule } from "./modules/hazard/hazard-seeder.module";
import { SyndromeSeederModule } from "./modules/syndrome/syndrome-seeder.module";
import { OperationStatusSeederModule } from "./modules/operation-status/operation-status-seeder.module";
import { HazardTypeSeederModule } from "./modules/hazard-type/hazard-type-seeder.module";
import { CategorySeederModule } from "./modules/category/category-seeder.module";
import { InstitutionTypeSeederModule } from "./modules/institution-type/institution-type-seeder.module";
import { ClassificationSeederModule } from "./modules/classification/classification-seeder.module";
import { RolesSeederModule } from "./modules/roles/roles-seeder.module";
import { UsersSeederModule } from "./modules/users/users-seeder.module";
import { ExpertiseSeederModule } from "./modules/expertise/expertise-seeder.module";
import { InstitutionNetworkSeederModule } from "./modules/institution-network/institution-network-seeder.module";
import { ProjectStatusSeederModule } from "./modules/project-status/project-status-seeder.module";
import { EventStatusSeederModule } from "./modules/event-status/event-status-seeder.module";
import { RegionSeederModule } from "./modules/regions/region-seeder.module";
import { RiskLevelSeederModule } from "./modules/risk-level/risk-level-seeder.module";
import { UpdateTypeSeederModule } from "./modules/update-type/update-type-seeder.module";
import { DeploymentStatusSeederModule } from "./modules/deployment-status/deployment-status-seeder.module";
import { FileCategorySeederModule } from "./modules/file-category/file-category-seeder.module";
import { PageCategorySeederModule } from "./modules/page-category/page-category-seeder.module";
import { LandingpagesSeederModule } from "./modules/landingpages/landingpages.module";

/**
 * Import and provide seeder classes.
 *
 * @module
 */
@Module({
  imports: [ 
    WorldRegionSeederModule,
    LanguageSeederModule,
    AreaOfWorkSeederModule,
    CountrySeederModule,
    HazardSeederModule,
    HazardTypeSeederModule,
    CategorySeederModule,
    ClassificationSeederModule,
    SyndromeSeederModule,
    OperationStatusSeederModule,
    InstitutionTypeSeederModule,
    RolesSeederModule,
    UsersSeederModule,
    ExpertiseSeederModule,
    InstitutionNetworkSeederModule,
    ProjectStatusSeederModule,
    RegionSeederModule,
    EventStatusSeederModule,
    RiskLevelSeederModule,
    UpdateTypeSeederModule,
    DeploymentStatusSeederModule,
    FileCategorySeederModule,
    PageCategorySeederModule,
    LandingpagesSeederModule,
    ConfigModule.forRoot(),
    MongooseModule.forRoot(process.env.MONGODB),
    
 ],
  providers: [Logger, SeederService],
})
export class SeederModule {}