//Import Library
import { useState } from "react";
import { <PERSON><PERSON>, Button } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';


const ReadMoreModal = ({ description }) => {
  const [modal, showModal] = useState(false);
  const { t } = useTranslation('common');
  const _string = ""
  return (
    <>
      <Modal
        show={modal}
        onHide={() => showModal(!modal)}
        backdrop="static"
        keyboard={false}
      >
        <Modal.Header closeButton>
          <Modal.Title>{t("Description")}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="_readmore_d" dangerouslySetInnerHTML={{ __html: description == undefined ? "" : description }}></div>
        </Modal.Body>
      </Modal>

      {description && description.length < 130 ? (
        <div dangerouslySetInnerHTML={{ __html: description == undefined ? "" : description }}></div>
      ) : description != '' ? (
        <div>
          <div className="_tabelw"  dangerouslySetInnerHTML={{ __html: description == undefined ? "" : description.substring(0, 130) + `${description.includes("<p") ? '...' : ''}${_string}` }}></div>
          <div className="pt-3">
            <Button
              onClick={() => showModal(!modal)}
              className="readMoreBtn mb-3"
              variant="outline-light"
            >
             {t("ReadMore")} 
            </Button>
          </div>
        </div>
      ) : ""}
    </>
  );
};

export default ReadMoreModal;
