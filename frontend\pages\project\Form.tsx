//Import Library
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col, Tab, Tabs } from "react-bootstrap";
import { MultiSelect } from "react-multi-select-component";
import Router from "next/router";
import Link from "next/link";
import moment from "moment";
import _ from "lodash";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus } from "@fortawesome/free-solid-svg-icons";

import toast from 'react-hot-toast';
import { TextInput, SelectGroup } from "../../components/common/FormValidation";
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";

//Import services/components
import RKIDatePicker from "../../components/common/RKIDatePicker";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';
import VspaceModal from "./../../components/common/VspaceModal";
import { EditorComponent } from "../../shared/quill-editor/quill-editor.component";

//***********Declaration for Initial State**************//
const initialState = {
    title: "",
    website: "",
    funded_by: "",
    status: null,
    countryTerritory: "",
    start_date: null,
    end_date: null,
    area_of_work: [],
    description: "",
    institution_invites: [],
    partner_institutions: [],
    checked: false,
};
//***********End of Declaration for Initial State**************//

const ProjectForm = (props: any) => {
    const buttonRef = useRef<any>(null);
    const startDateRef = useRef<any>(null);
    const { t, i18n } = useTranslation('common');
    const titleSearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };
    const currentLang = i18n.language;
    const [initialVal, setIntialVal] = useState<any>(initialState);
    const [groupVisibility] = useState<any>({
        invitesCountry: [],
        invitesRegion: [],
        invitesOrganisationType: [],
        invitesOrganisation: [],
        invitesExpertise: [],
        invitesNetWork: [],
        userList: [],
        visibility: true,
    });
    const [statusproject, setProjectStatus] = useState<any[]>([]);
    const [countryList, setcountryList] = useState<any[]>([]);
    const [, setUsersList] = useState<any[]>([]);
    const [, setExpertiseList] = useState<any[]>([]);
    const [organisationList, setorganisationList] = useState<any[]>([]);
    const [, setOrganisationTypeList] = useState<any[]>([]);
    const [, setNetworkList] = useState<any[]>([]);
    const [virtualSpace, setVirtualSpace] = useState<boolean>(false);
    const [modal, setModal] = useState<boolean>(false);
    const [areaofWorkList, setAreaOfWorkList] = useState<any[]>([]);
    const [resId, setResId] = useState<any>(null);
    const editform: boolean = props.routes && props.routes[0] == "edit" && props.routes[1];
    const [virtualSpaceAccessPermission, setVirtualSpaceAccessPermission] = useState<boolean>(true);
    const [partner_institutions, setpartner_institutions] = useState<any[]>([
        {
            partner_country: "",
            regions: [],
            partner_region: [],
            institutions: [],
            partner_institution: [],
            countryregions: [],
            world_region: "",
        },
    ]);
    const [institution_invites, setinstitution_invites] = useState<any[]>([
        {
            title: "",
            contact_name: "",
            email: "",
            _id: null,
        },
    ]);
    const [defaultActiveKey, setdefaultActiveKey] = useState<number>(1);
    const [partnerInstitutionError, setPartnerInstitutionError] = useState("");
    const [areaOfWorkError, setAreaOfWorkError] = useState("");

    const projectParams = {
        query: {},
        sort: titleSearch,
        limit: "~",
        languageCode: currentLang,
    };

    const getProjectInitialData = async (projectParamsinitalsvalue: any) => {
        const projectStatus = await apiService.get("/projectstatus", projectParamsinitalsvalue);
        if (projectStatus && Array.isArray(projectStatus.data)) {
            setProjectStatus(projectStatus.data);
        }

        await responseproject(projectParamsinitalsvalue, setcountryList, setExpertiseList);

        const institution = await apiService.get("/institution", projectParamsinitalsvalue);
        if (institution && Array.isArray(institution.data)) {
            const filtered = institution.data.map((item: any) => {
                if (item.status !== "Request Pending") {
                    return { label: item.title, value: item._id };
                }
            });
            setorganisationList(_.compact(filtered));
        }

        const institutionType = await apiService.get("/institutiontype", projectParamsinitalsvalue);
        if (institutionType && Array.isArray(institutionType.data)) {
            setOrganisationTypeList(institutionType.data);
        }

        const institutionNetwork = await apiService.get("/institutionnetwork", projectParamsinitalsvalue);
        if (institutionNetwork && Array.isArray(institutionNetwork.data)) {
            setNetworkList(institutionNetwork.data);
        }

        const areaOfWork = await apiService.get("/areaofwork", projectParamsinitalsvalue);
        let _area: any[] = [];
        if (areaOfWork && Array.isArray(areaOfWork.data)) {
            _area = areaOfWork.data.map((item: any, _i: any) => {
                return { label: item.title, value: item._id };
            });
            setAreaOfWorkList(_area);
        }
    };

    useEffect(() => {
        if (editform) {
            const getProjectData = async (projectParamsinitial: any) => {
                const response = await apiService.get(`/project/${props.routes[1]}`, projectParamsinitial);
                if (response) {
                    const normalizePartner: any[] = [];
                    const {
                        status,
                        country,
                        area_of_work,
                        partner_institutions,
                        start_date,
                        end_date,
                    } = response;
                    responceStartdate(response, start_date, end_date, status, country, area_of_work);
                    parternerInstution(partner_institutions, normalizePartner, countryRegion, projectParamsinitial);
                    setpartner_institutions(normalizePartner);
                    setIntialVal((prevState: any) => ({ ...prevState, ...response }));
                    const checkEndDate = end_date
                        ? setIntialVal((prevState: any) => ({ ...prevState, checked: true }))
                        : null;
                    return checkEndDate;
                }
            };
            getProjectData(projectParams);
        }
        getProjectInitialData(projectParams);
        initialData();
    }, []);

    const initialData = async () => {
        const currentUser = await apiService.post("/users/getLoggedUser", {});
        if (currentUser && currentUser['roles']) {
            const filteredRoles = currentUser['roles']?.filter((role: string) => (role == "EMT_NATIONAL_FOCALPOINT" || role === "NGOS"));
            if (filteredRoles.length > 0) {
                setVirtualSpaceAccessPermission(false);
            } else {
                setVirtualSpaceAccessPermission(true);
            }
        }
    };

    //*******To Clear the value of the fields*******//
    //*******Get the regions from the country_region api*******//
    const countryRegion = async (id: any, projectParamsinitial: any) => {
        let _regions: any[] = [];
        if (id) {
            const response = await apiService.get(`/country_region/${id}`, projectParamsinitial);
            if (response && response.data) {
                _regions = response.data.map((item: any, _i: any) => {
                    return { label: item.title, value: item._id };
                });
                _regions.sort((a: any, b: any) => a.label.localeCompare(b.label));
            }
        }
        return _regions;
    };
    //******Get the regions for addcountry form******//
    //*****Get the regions for GroupVisibility*****//
    //*****To handle the Date pickers*****//
    const onChangeDate = (date: any, key: any) => {
        if (key == "start_date" && date == null) {
            setIntialVal((prevState: any) => ({
                ...prevState,
                end_date: date,
                start_date: date,
            }));
        }
        setIntialVal((prevState: any) => ({
            ...prevState,
            [key]: date,
        }));
    };

    const formRef = useRef<any>(null);
    //*****To add the Tabs******//
    const tabAdd = () => {
        const _temp = [...institution_invites];
        _temp.push({ title: "", contact_name: "", email: "", _id: null });
        setinstitution_invites(_temp);
        setdefaultActiveKey(_temp.length);
    };
    //******To remove the Tabs*****//
    const removeTab = (_e: any, i: any) => {
        institution_invites.splice(i, 1);
        const _temp = [...institution_invites];
        setinstitution_invites(_temp);
        setdefaultActiveKey(_temp.length);
        if (institution_invites.length === 0) {
            tabAdd();
        }
    };

    function handleOrgTab(e: any, i: any) {
        const _tempCosts = [...institution_invites];
        _tempCosts[i].title = e.target.value;
        setinstitution_invites(_tempCosts);
    }

    function handleContTab(e: any, i: any) {
        const _tempCosts = [...institution_invites];
        _tempCosts[i].contact_name = e.target.value;
        setinstitution_invites(_tempCosts);
    }

    function handleMailTab(e: any, i: any) {
        const _tempCosts = [...institution_invites];
        _tempCosts[i].email = e.target.value;
        setinstitution_invites(_tempCosts);
    }
    //******To handle Add Country Button******//
    const addcountry = () => {
        const a = {
            partner_country: "",
            world_region: "",
            regions: [],
            partner_region: [],
            institutions: [],
            partner_institution: [],
            countryregions: [],
        };
        setpartner_institutions((oldArray: any) => [...oldArray, a]);
    };
    //******To handle Remove Country Button******//
    const removecountry = (_e: any, i: any) => {
        partner_institutions.splice(i, 1);
        setpartner_institutions([...partner_institutions]);
        if (partner_institutions.length === 0) {
            addcountry();
        }
    };

    const handleDescription = (value: any) => {
        setIntialVal((prevState: any) => ({
            ...prevState,
            description: value,
        }));
    };

    const handleEndDateCheckBox = () => {
        setIntialVal((prevState: any) => ({
            ...prevState,
            checked: !prevState.checked,
            end_date: null,
        }));
    };

    React.useEffect(() => {
        if (groupVisibility) {
            const normalizeGroup: any = {};
            Object.keys(groupVisibility).forEach((item: any, _i: any) => {
                const _data: any[] =
                    (groupVisibility as any)[item].length > 0 &&
                    (groupVisibility as any)[item].map((d: any, _i: any) => {
                        return d.value;
                    });
                normalizeGroup[item] = _data ? _data : [];
            });

            getUsers(normalizeGroup);
        } else {
            console.log("No threshold reached.");
        }
    }, [groupVisibility]);

    const getUsers = async (normalizeGroup: any) => {
        const { invitesCountry, invitesOrganisation } = normalizeGroup;
        const groupParams = {
            query: {
                country: invitesCountry,
                institution: invitesOrganisation,
            },
        };
        const userInvites = await apiService.post("/user-invite", groupParams);
        if (userInvites && Array.isArray(userInvites)) {
            const _users = userInvites.map((item: any, _i: any) => {
                return { label: item.username, value: item._id };
            });
            setUsersList(_users);
        }
    };

    //******To Handle Group Visibility******//

    //To handle Add Organisation//
    const handleForm = async (e: any, i: any, name: any) => {
        if (e.target) {
            const { name: nameInitial, value } = e.target;
            partner_institutions[i][nameInitial] = value;
            if (nameInitial == "partner_country") {
                partner_institutions[i].world_region =
                    e.target[e.target.selectedIndex].getAttribute("data-worldregion");
                partner_institutions[i].countryregions = await countryRegion(value, projectParams);
                partner_institutions[i].regions = [];
            }
        } else {
            if (name == "countries_regions") {
                partner_institutions[i].regions = e;
                partner_institutions[i].partner_region = e.map((item: any, _i: any) => {
                    return item.value;
                });
            }

            if (name == "partner_institutions") {
                partner_institutions[i].institutions = e;
                partner_institutions[i].partner_institution = e.map((item: any, _i: any) => {
                    return item.value;
                });
                console.log(partner_institutions[i].institutions);
                if(partner_institutions[i].institutions.length) {
                    setPartnerInstitutionError('')
                } else {
                    setPartnerInstitutionError(t("toast.PartnerInstitutionshouldnotbeempty"))
                }
                    
            }
        }
        setpartner_institutions([...partner_institutions]);
    };

    const handleChange = (e: any) => {
        const { name, value } = e.target;
        if (e.target) {
            setIntialVal((prevState: any) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const bindAreaOfWork = (e: any, _name: any) => {
        setIntialVal((prevState: any) => ({
            ...prevState,
            area_of_work: e,
        }));
        if(e.length){
            setAreaOfWorkError('')
        } else {
            setAreaOfWorkError(t("toast.AreaofWorkshouldnotbeempty"))
        }
    };

    const resetHandler = () => {
        setIntialVal(initialState);
        setpartner_institutions([]);
        setinstitution_invites([]);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    //******Form Submit********//
    const handleSubmit = async (e: any) => {
        e.preventDefault();

        if (initialVal.area_of_work.length == 0 && partner_institutions[0].partner_institution.length == 0) {
            toast.error(t("toast.AreaofWorkandPartnerInstitutionshouldnotbeempty"));
            window.scrollTo(0, 0);
            setPartnerInstitutionError(t("toast.PartnerInstitutionshouldnotbeempty"))
            setAreaOfWorkError(t("toast.AreaofWorkshouldnotbeempty"))
            return;
        }

        if (initialVal.area_of_work.length == 0) {
            toast.error(t("toast.AreaofWorkshouldnotbeempty"));
            window.scrollTo(0, 0);
            setAreaOfWorkError(t("toast.AreaofWorkshouldnotbeempty"));
            return;
        }

        if (partner_institutions[0].partner_institution.length == 0) {
            toast.error(t("toast.PartnerInstitutionshouldnotbeempty"));
            window.scrollTo(0, 0);
            setAreaOfWorkError(t("toast.PartnerInstitutionshouldnotbeempty"))
            return;
        }

        if (initialVal.start_date == null) {
            startDateRef.current?.focus();
        } else {
            if (buttonRef.current) {
                buttonRef.current.setAttribute("disabled", "disabled");
            }

            const mapped = _.map(
                partner_institutions,
                _.partialRight(_.pick, ["partner_country", "partner_region", "partner_institution", "world_region"])
            );
            initialVal.area_of_work = initialVal.area_of_work
                ? initialVal.area_of_work.map((item: any, _i: any) => item.value)
                : [];
            initialVal.institution_invites = institution_invites;
            initialVal.partner_institutions = mapped;
            e.preventDefault();
            let response: any;
            let toastMsg: string;
            if (editform) {
                toastMsg = "toast.Projectupdatedsuccessfully";
                response = await apiService.patch(`/project/${props.routes[1]}`, initialVal);
            } else {
                toastMsg = "toast.Projectaddedsuccessfully";
                response = await apiService.post("/project", initialVal);
            }
            vspace_condition_func(response, virtualSpace, setResId, setModal, t, toastMsg);
        }
    };
    return (
        <Container className="formCard" fluid>
            <Card>
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>{editform ? t("editProject") : t("addProject")}</Card.Title>
                            </Col>
                        </Row>
                        <hr />
                        <Row className="mb-3">
                            <Col md={6} lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("Title")}</Form.Label>
                                    <TextInput
                                        name="title"
                                        id="title"
                                        required
                                        value={initialVal.title}
                                        validator={(value: any) => value.trim() != ""}
                                        errorMessage={{
                                            validator: t("PleaseAddtheTitle"),
                                        }}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6} lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("Website")}</Form.Label>
                                    <TextInput
                                        name="website"
                                        id="website"
                                        pattern="^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+$"
                                        errorMessage={{ pattern: t("Pleaseentervalidwebsite") }}
                                        value={initialVal.website}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("Description")}</Form.Label>
                                    <EditorComponent initContent={initialVal.description} onChange={(evt: any) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="d-flex align-items-center mb-3">
                            <Col md={6} lg={6} sm={12}>
                                <Form.Group style={{ maxWidth: "500px" }}>
                                    <Form.Label className="required-field">
                                        {t("AreaofWorkthisprojectcovers")}
                                    </Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectAreaofwork"),
                                            allItemsAreSelected: t("AllAreaofwork'sareSelected"),
                                        }}
                                        options={areaofWorkList}
                                        value={initialVal.area_of_work}
                                        onChange={bindAreaOfWork}
                                        className={"project-covers"}
                                        labelledBy={t("Selectareaofwork")}
                                    />
                                    {areaOfWorkError && <p style={{ color: "red" }}>{areaOfWorkError}</p>}
                                </Form.Group>
                            </Col>
                            <Col md={6} lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("FundedBy")}</Form.Label>
                                    <TextInput
                                        name="funded_by"
                                        id="funded_by"
                                        value={initialVal.funded_by}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col lg={3} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("ProjectStatus")}</Form.Label>
                                    <SelectGroup
                                        name="status"
                                        id="status"
                                        value={initialVal.status === null ? "" : initialVal.status}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("SelectProjectStatus")}</option>
                                        {statusproject.map((item, i) => {
                                            return (
                                                <option key={i} value={item._id}>
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col lg={3} sm={4} className="align-self-center">
                                <Form.Group>
                                    <Row>
                                        <Col>
                                            <Form.Label className="required-field">{t("StartDate")}</Form.Label>
                                        </Col>
                                    </Row>
                                    <label className="date-validation w-100" ref={startDateRef}>
                                        <RKIDatePicker
                                            selected={initialVal.start_date}
                                            onChange={(date: any) => onChangeDate(date, "start_date")}
                                            dateFormat="MMMM d, yyyy"
                                            placeholderText={t("SelectStartDate")}
                                        />
                                    </label>
                                </Form.Group>
                            </Col>
                            <Col lg={2} sm={4}>
                                <Form.Check
                                    type="checkbox"
                                    checked={initialVal.checked}
                                    onChange={handleEndDateCheckBox}
                                    label={t("ShowEndDate")}
                                />
                            </Col>
                            {initialVal.checked && (
                                <Col lg={3} sm={4} className="align-self-center">
                                    <Form.Group>
                                        <Row>
                                            <Col>
                                                <Form.Label>{t("EndDate")}</Form.Label>
                                            </Col>
                                        </Row>
                                        <RKIDatePicker
                                            selected={initialVal.end_date}
                                            disabled={!initialVal.start_date}
                                            onChange={(date: any) => onChangeDate(date, "end_date")}
                                            dateFormat="MMMM d, yyyy"
                                            minDate={initialVal.start_date}
                                            placeholderText={t("SelectEndDate")}
                                        />
                                    </Form.Group>
                                </Col>
                            )}
                        </Row>
                        {partner_institutions.map((item: any, i: number) => {
                            return (
                                <div key={i}>
                                    <Col className="header-block pb-1 pt-2" lg={12}>
                                        <h6>
                                            <span>
                                                {t("Country")} {i + 1}
                                            </span>
                                        </h6>
                                    </Col>
                                    <Row className="mb-3">
                                        <Col lg={4} sm={6}>
                                            <Form.Group>
                                                <Form.Label className="required-field">
                                                    {t("CountryWheretheProjectistakingplace")}
                                                </Form.Label>
                                                <SelectGroup
                                                    name="partner_country"
                                                    id="partner_country"
                                                    value={item.partner_country}
                                                    onChange={(e: any) => handleForm(e, i, "countries")}
                                                    required
                                                    errorMessage={t("thisfieldisrequired")}
                                                >
                                                    <option value="">{t("SelectCountry")}</option>
                                                    {countryList.map((C_item, _i) => {
                                                        return (
                                                            <option
                                                                key={_i}
                                                                data-worldregion={C_item.world_region._id}
                                                                value={C_item._id}
                                                            >
                                                                {C_item.title}
                                                            </option>
                                                        );
                                                    })}
                                                </SelectGroup>
                                            </Form.Group>
                                        </Col>
                                        <Col lg={4} sm={6}>
                                            <Form.Group className="mw-100">
                                                <Form.Label>{t("CountryRegions")}</Form.Label>
                                                <MultiSelect
                                                    overrideStrings={{
                                                        selectSomeItems: t("SelectRegions"),
                                                        allItemsAreSelected: t("AllRegionsareSelected"),
                                                    }}
                                                    options={item.countryregions}
                                                    value={item.regions}
                                                    onChange={(e: any) => handleForm(e, i, "countries_regions")}
                                                    className={"region"}
                                                    labelledBy={t("SelectRegions")}
                                                />
                                            </Form.Group>
                                        </Col>
                                        <Col lg={4} sm={6}>
                                            <Form.Group style={{ maxWidth: "400px" }}>
                                                <Form.Label className="required-field">
                                                    {t("PartnerOrganisations(onplatform)")}
                                                </Form.Label>
                                                    <MultiSelect
                                                        overrideStrings={{
                                                            selectSomeItems: t("SelectOrganisations"),
                                                            allItemsAreSelected: t("AllOrganisationsareselected"),
                                                        }}
                                                        options={organisationList}
                                                        value={item.institutions}
                                                        onChange={(e: any) => handleForm(e, i, "partner_institutions")}
                                                        className={"organisation"}
                                                        labelledBy={t("SelectOrganisations")}
                                                        />
                                                        {partnerInstitutionError && <p style={{ color: "red" }}>{partnerInstitutionError}</p>}
                                            </Form.Group>
                                        </Col>
                                    </Row>
                                    <div>
                                        {i === 0 ? (
                                            <span></span>
                                        ) : (
                                            <Row className="mb-4">
                                                <Col xs lg="4">
                                                    <Button variant="secondary" onClick={(e: any) => removecountry(e, i)}>
                                                        {t("Remove")}
                                                    </Button>
                                                </Col>
                                            </Row>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                        <Row>
                            <Col xs lg="4">
                                <Button variant="secondary" onClick={addcountry}>
                                    {t("AddAnotherCountry")}
                                </Button>
                            </Col>
                        </Row>
                        <hr />
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("PartnerOrganisationnotlisted?Createnewandinvite")}</Form.Label>
                                    <Tabs
                                        activeKey={defaultActiveKey}
                                        onSelect={(k: any) => setdefaultActiveKey(k)}
                                        id="uncontrolled-tab-example"
                                    >
                                        {" "}
                                        {institution_invites.map((item: any, i: number) => {
                                            return (
                                                <Tab key={i} eventKey={`${i + 1}`} title={`Organisation ${i + 1}`}>
                                                    <Row>
                                                        <Col lg={4} sm={6}>
                                                            <Form.Group className="pt-4">
                                                                <Form.Label>{t("OrganisationName")}</Form.Label>
                                                                <TextInput
                                                                    name={`input${i + 1}-title`}
                                                                    id={`input${i + 1}`}
                                                                    value={item.title}
                                                                    onChange={(e: any) => handleOrgTab(e, i)}
                                                                />
                                                            </Form.Group>
                                                        </Col>
                                                        <Col lg={4} sm={6}>
                                                            <Form.Group className="pt-4">
                                                                <Form.Label>{t("ContactName")}</Form.Label>
                                                                <TextInput
                                                                    name={`input${i + 1}-contact_name`}
                                                                    id={`input${i + 1}`}
                                                                    value={item.contact_name}
                                                                    onChange={(e: any) => handleContTab(e, i)}
                                                                />
                                                            </Form.Group>
                                                        </Col>
                                                        <Col lg={4} sm={6}>
                                                            <Form.Group className="pt-4">
                                                                <Form.Label>{t("E-MailAddress")}</Form.Label>
                                                                <TextInput
                                                                    name={`input${i + 1}`}
                                                                    id={`input${i + 1}`}
                                                                    value={item.email}
                                                                    onChange={(e: any) => handleMailTab(e, i)}
                                                                    pattern="^[^@]+@[^@]+\.[^@]+$"
                                                                    errorMessage={{
                                                                        pattern: t("Pleaseenteravalidemail"),
                                                                    }}
                                                                />
                                                            </Form.Group>
                                                        </Col>
                                                    </Row>
                                                    <div>
                                                        {i === 0 ? (
                                                            <span></span>
                                                        ) : (
                                                            <Col xs lg="4" className="p-0">
                                                                <Button
                                                                    onSelect={(k: any) => setdefaultActiveKey(k)}
                                                                    variant="secondary"
                                                                    onClick={(e) => removeTab(e, i)}
                                                                >
                                                                    {t("Remove")}
                                                                </Button>
                                                            </Col>
                                                        )}
                                                    </div>
                                                </Tab>
                                            );
                                        })}
                                        <Tab
                                            eventKey="add"
                                            title={
                                                <div>
                                                    <span onClick={tabAdd}>
                                                        {" "}
                                                        <FontAwesomeIcon icon={faPlus} color="#808080" />
                                                    </span>
                                                </div>
                                            }
                                        ></Tab>
                                    </Tabs>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mt-4">
                            <Col>
                                <Card.Text>
                                    <b>{t("VirtualSpace")}</b>
                                </Card.Text>
                                <hr />
                                <Form.Check
                                    className="pb-4"
                                    disabled={!virtualSpaceAccessPermission}
                                    type="checkbox"
                                    onChange={() => setVirtualSpace(!virtualSpace)}
                                    name="virtula"
                                    checked={virtualSpace}
                                    label={t("WouldliketocreateaVirtualSpace")}
                                />
                            </Col>
                        </Row>
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary" ref={buttonRef} onClick={handleSubmit}>
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("reset")}
                                </Button>
                                <Link href="/project" as="/project" >
                                    <Button variant="secondary">{t("Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
            {modal && <VspaceModal type="Project" id={resId} />}
        </Container>
    );
};

export default ProjectForm;
function vspace_condition_func(
    response: any,
    virtualSpace: boolean,
    setResId: React.Dispatch<any>,
    setModal: React.Dispatch<React.SetStateAction<boolean>>,
    t: any,
    toastMsg: any
) {
    if (response && response._id) {
        if (virtualSpace) {
            setResId(response?._id && response._id);
            setModal(true);
        } else {
            toast.success(t(toastMsg))
            Router.push("/project/[...routes]", `/project/show/${response._id}`);
        }
    } else {
        toast.error(response)
    }
}

async function responseproject(
    projectParams: any,
    setcountryList: React.Dispatch<React.SetStateAction<any[]>>,
    setExpertiseList: React.Dispatch<React.SetStateAction<any[]>>
) {
    const country = await apiService.get("/country", projectParams);
    if (country && Array.isArray(country.data)) {
        setcountryList(country.data);
    }

    const expertise = await apiService.get("/expertise", projectParams);
    if (expertise && Array.isArray(expertise.data)) {
        setExpertiseList(expertise.data);
    }
}

function responceStartdate(
    response: any,
    start_date: any,
    end_date: any,
    status: any,
    country: any,
    area_of_work: any
) {
    response.start_date = start_date ? moment(start_date).toDate() : null;
    response.end_date = end_date ? moment(end_date).toDate() : null;
    response.status = status && status._id ? status._id : null;
    response.country = country && country._id ? country._id : null;
    response.area_of_work =
        area_of_work && area_of_work.length > 0
            ? area_of_work.map((item: any, _i: any) => {
                  return { label: item.title, value: item._id };
              })
            : [];
}

function parternerInstution(
    partner_institutions: any,
    normalizePartner: any[],
    countryRegion: (id: any, projectParams: any) => Promise<any[]>,
    projectParams: any
) {
    partner_institutions &&
        partner_institutions.forEach(async (item: any, _i: any) => {
            const initRegions =
                item.partner_region &&
                item.partner_region.map((item_val: any, _i: any) => {
                    return { label: item_val.title, value: item_val._id };
                });
            const _regionsId =
                item.partner_region &&
                item.partner_region.map((initial: any, _i: any) => {
                    return initial._id;
                });

            const initInstitutions =
                item.partner_institution &&
                item.partner_institution.map((_item: any) => {
                    return { label: _item.title, value: _item._id };
                });
            const _institutionsId =
                item.partner_institution &&
                item.partner_institution.map((item_i: any) => {
                    return item_i._id;
                });
            normalizePartner.push({
                partner_country: item.partner_country._id,
                regions: initRegions,
                partner_region: _regionsId,
                institutions: initInstitutions,
                partner_institution: _institutionsId,
                world_region: item.partner_country.world_region._id,
                countryregions: await countryRegion(item.partner_country._id, projectParams),
            });
        });
}
