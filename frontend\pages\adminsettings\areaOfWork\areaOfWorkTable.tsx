//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const AreaOfWorkTable = (_props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [AreaOfWorkCountry, setAreaOfWorkCountry] = useState({});


    const areaOfWorkParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("adminsetting.areaofwork.Table.Title"),
            selector: "title",
        },
        {
            name: t("adminsetting.areaofwork.Table.Action"),
            selector: "",
            cell: (d: any) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_area_of_work/${d._id}`}>

                        {" "}
                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    const getAredOfWorkData = async (areaOfWorkParamsinitials: any) => {
        setLoading(true);
        const response = await apiService.get("/areaofwork", areaOfWorkParamsinitials);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page: any) => {
        areaOfWorkParams.limit = perPage;
        areaOfWorkParams.page = page;
        getAredOfWorkData(areaOfWorkParams);
    };

    const handlePerRowsChange = async (newPerPage: any, page: any) => {
        areaOfWorkParams.limit = newPerPage;
        areaOfWorkParams.page = page;
        setLoading(true);
        const response = await apiService.get("/areaofwork", areaOfWorkParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row: any) => {
        setAreaOfWorkCountry(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/areaofwork/${AreaOfWorkCountry}`);
            getAredOfWorkData(areaOfWorkParams);
            setModal(false);
            toast.success(t("adminsetting.areaofwork.Table.areaOfWorkDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.areaofwork.Table.errorDeletingAreaOfWork"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        getAredOfWorkData(areaOfWorkParams);
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.areaofwork.Table.DeleteAreaofwork")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.areaofwork.Table.Areyousurewanttodeletethisareaofwork?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.areaofwork.Table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.areaofwork.Table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default AreaOfWorkTable;
