//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateRiskLevelDto } from './dto/create-risk-level.dto';
import { UpdateRiskLevelDto } from './dto/update-risk-level.dto';
import { RiskLevelService } from './risk-level.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('risklevel')
@UseGuards(SessionGuard)
export class RiskLevelController {
  constructor(private readonly _risklevelService: RiskLevelService) {}

  @Post()
  async create(@Body() createRiskLevelDto: CreateRiskLevelDto) {
    try {
      const _risklevel = await this._risklevelService.create(
        createRiskLevelDto,
      );
      return _risklevel;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._risklevelService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') risklevelId: string) {
    return this._risklevelService.get(risklevelId);
  }

  @Patch(':id')
  async update(
    @Param('id') risklevelId: string,
    @Body() updateRiskLevelDto: UpdateRiskLevelDto,
  ) {
    try {
      const _risklevel = await this._risklevelService.update(
        risklevelId,
        updateRiskLevelDto,
      );
      return _risklevel;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') risklevelId: string) {
    const _language = this._risklevelService.delete(risklevelId);
    return _language;
  }
}
