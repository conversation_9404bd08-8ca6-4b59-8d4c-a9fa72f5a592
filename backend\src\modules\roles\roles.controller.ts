//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateRolesDto } from './dto/create-roles.dto';
import { UpdateRolesDto } from './dto/update-roles.dto';
import { RolesService } from "./roles.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('roles')
@UseGuards(SessionGuard)
export class RolesController {

  constructor(
    private readonly _rolesService: RolesService
  ) { }

  @Post()
  create(@Body() createRolesDto: CreateRolesDto) {
    const resp = this._rolesService.create(createRolesDto);
    return resp;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._rolesService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') roleId: string) {
    return this._rolesService.get(roleId);
  }

  @Patch(':id')
  update(@Param('id') roleId: string, @Body() updateRolesDto: UpdateRolesDto) {
    const resp = this._rolesService.update(roleId, updateRolesDto);
    return resp;
  }

  @Delete(':id')
  remove(@Param('id') roleId: string) {
    const deletedData = this._rolesService.delete(roleId);
    return deletedData;
  }
}
