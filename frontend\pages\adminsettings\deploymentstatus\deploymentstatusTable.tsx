//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const DeploymentstatusTable = (props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectdeploymentstatus, setSelectdeploymentstatus] = useState({});


    const deploymentstatusParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("adminsetting.DeploymentStatus.Table.Title"),
            selector: "title",
        },
        {
            name: t("adminsetting.DeploymentStatus.Table.Action"),
            selector: "",
            cell: (d: any) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_deploymentstatus/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>
                </div>
            ),
        },
    ];

    const getdeploymentstatusData = async () => {
        setLoading(true);
        const response = await apiService.get("/deploymentstatus", deploymentstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page: any) => {
        deploymentstatusParams.limit = perPage;
        deploymentstatusParams.page = page;
        getdeploymentstatusData();
    };

    const handlePerRowsChange = async (newPerPage: any, page: any) => {
        deploymentstatusParams.limit = newPerPage;
        deploymentstatusParams.page = page;
        setLoading(true);
        const response = await apiService.get("/deploymentstatus", deploymentstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row: any) => {
        setSelectdeploymentstatus(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/deploymentstatus/${selectdeploymentstatus}`);
            getdeploymentstatusData();
            setModal(false);
            toast.success(t("adminsetting.DeploymentStatus.Table.deploymentStatusDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.DeploymentStatus.Table.errorDeletingDeploymentStatus"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        getdeploymentstatusData();
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.DeploymentStatus.Table.DeleteDeploymentstatus")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {t("adminsetting.DeploymentStatus.Table.Areyousurewanttodeletethisdeploymentstatus?")}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.DeploymentStatus.Table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.DeploymentStatus.Table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default DeploymentstatusTable;
