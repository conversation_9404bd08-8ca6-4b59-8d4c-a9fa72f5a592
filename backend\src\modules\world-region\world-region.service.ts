//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { WorldRegionInterface } from '../../interfaces/world-region.interface';
import { CreateWorldRegionDto } from './dto/create-world-region.dto';
import { UpdateWorldRegionDto } from './dto/update-world-region.dto';

const CouldnotfindWorldRegion = 'Could not find World Region.';
@Injectable()
export class WorldRegionService {
  constructor(
    @InjectModel('WorldRegion')
    private worldRegionModel: Model<WorldRegionInterface>,
  ) {}

  async create(
    createWorldRegionDto: CreateWorldRegionDto,
  ): Promise<WorldRegionInterface> {
    const createdWorldRegion = new this.worldRegionModel(createWorldRegionDto);
    return createdWorldRegion.save();
  }

  async findAll(query): Promise<WorldRegionInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.worldRegionModel.paginate(_filter, options);
  }

  async get(worldRegionId): Promise<WorldRegionInterface[]> {
    let worldRegion;
    try {
      worldRegion = await this.worldRegionModel.findById(worldRegionId).exec();
    } catch (error) {
      throw new NotFoundException(CouldnotfindWorldRegion);
    }
    if (!worldRegion) {
      throw new NotFoundException(CouldnotfindWorldRegion);
    }
    return worldRegion;
  }

  async update(worldRegionId: any, updateWorldRegionDto: UpdateWorldRegionDto) {
    const getworldRegionById: any = await this.worldRegionModel
      .findById(worldRegionId)
      .exec();
    const updatedWorldRegion = new this.worldRegionModel(updateWorldRegionDto);
    Object.keys(updateWorldRegionDto).forEach((d) => {
      getworldRegionById[d] = updatedWorldRegion[d];
    });
    getworldRegionById.updated_at = new Date();
    return getworldRegionById.save();
  }

  async delete(worldRegionId: string) {
    const result = await this.worldRegionModel
      .deleteOne({ _id: worldRegionId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(CouldnotfindWorldRegion);
    }
  }
}
