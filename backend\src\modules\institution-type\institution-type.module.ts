//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { InstitutionTypeController } from './institution-type.controller';
import { InstitutionTypeService } from './institution-type.service';
// SCHEMAS
import { InstitutionTypeSchema } from '../../schemas/institution_type.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'InstitutionType', schema: InstitutionTypeSchema }
    ])
  ],
  controllers: [InstitutionTypeController],
  providers: [InstitutionTypeService],
})

export class InstitutionTypeModule { }