.projectBanner {
  margin: -8px -8px 0;
  width: calc(100% + 16px);
  position: relative;
}

.projectTitleBlock {
  position: absolute;
  bottom: 0;
  z-index: 9;
  background: rgba(0, 0, 0, 0.8);
  width: 100%;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  h4 {
    color: #fff;
    margin: 0;
    padding: 0 15px;
    font-size: 20px;
  }
}

.projectDate {
  display: flex;
  padding: 0 15px;
  h6 {
    font-size: 14px;
    margin:0;
  }
  h5 {
    font-size: 15px;
    font-weight: 400;
    margin: 0;
    color: #2ca8ff;
  }
  i {
    color: #fff;
    font-size: 30px;
    margin: 0 10px 0 0px;
    display: flex;
    align-items: center;
  }
  .projectStart {
    margin:0 20px 0 0;
  }
  .projectStart, .projectStatus {
    display: flex;
    flex-direction: row;
    align-self: center;
  }
}

.projectDetails {
  color: #0b1627;
  p {
    margin: 0 0 13px;
    display: flex;
    b {
      width: 170px;
      display: inline-block;
    }
    span {
      display: inline-block;
      width: calc(100% - 175px);
      margin: 0 0 0 5px;
    }
  }
}

.prjButton{
  width:150px
}


.projectImg {
  width: 100%;
  height: 330px;
  overflow: hidden;
  position: relative;
  img {
    width: 100%;
    height: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.projectInfoBlock {
  padding-top: 20px;
  margin-top: 15px;
}


// .diamond {
//   width: 10px;
//   height: 10px;
//   border: 1px solid #5b5b5b;
//   position: relative;
//   top: 13px;
//   transform: rotate(45deg);
// }
// .diamond:after {
//   content: '';
//   position: absolute;
//   right: -1px;
//   top: -4px;
//   width: 0;
//   height: 0;
//   border-top: 6px solid transparent;
//   border-end: 6px solid #5b5b5b;
//   border-bottom: 6px solid transparent;
//   transform: rotate(-225deg);
// }

.projectPartner {
  margin: 0;
  padding: 0;
  li {
    list-style: none;
    border-bottom: 1px solid #dedede;
    color: rgba(0, 0, 0, 0.7) !important;
    text-align: left;
    padding: 12px 10px 12px 30px;
    position: relative;
    &:before {
      content: '';
      width: 10px;
      height: 10px;
      border: 1px solid #5b5b5b;
      position: absolute;
      top: 18px;
      transform: rotate(45deg);
      left: 8px;
    }
    &:after {
      content: '';
      position: absolute;
      left: 13px;
      top: 17px;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-start: 6px solid #5b5b5b;
      border-bottom: 6px solid transparent;
    }
    &:last-child {
      border:none;
    }
  }
}

.projectInfo {
  padding: 0 7px 0 15px;
  .infoCard {
    margin: 0 0 20px;
    &:nth-child(1) .card-header:after {
      content: "\f05a";
    }
    &:nth-child(2) .card-header:after {
      content: "\f1ad";
    }
    &:nth-child(3) .card-header:after {
      content: "\f5a0";
    }
    .card-header {
      text-align: left;
      border-bottom: 1px solid #ededed;
      padding: 12px 15px;
      &:after {
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font-style: normal;
        font-variant: normal;
        text-rendering: auto;
        line-height: 1;
        // font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        font-size: 22px;
        color: #2ca8ff;
        float: right;
      }
    }
  }
  .projetInfoItems {
    text-align: left;
    padding: 10px 0;
    border-bottom: 1px solid #ededed;
    &:last-child {
      border: none;
    }
    h6 {
      color: rgba(0, 0, 0, 0.9);
    }
    p,a {
      color: rgba(0, 0, 0, 0.7) !important;
    }
  }
}

.projectDescBlock {
  border-end: 1px solid #ddd;
}

.projectAccordion {
  padding-top: 25px;
  padding-bottom: 25px;
  .card {
    border: none;
    margin: 25px 0;
  }
  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;
    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }
    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }
    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;
      svg {
        font-size: 16px;
      }
    }
  }
}

// Responsive 
@media screen and (max-width: 767px) {
  .main-container {
    .projectDescBlock {
      border-end: none;
    }
  }
}