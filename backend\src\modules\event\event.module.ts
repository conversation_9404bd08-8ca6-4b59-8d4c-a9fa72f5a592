//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { EventController } from './event.controller';
import { EventService } from './event.service';
import {FlagService} from "../flag/flag.service";
import {EmailService} from "../../email.service";
import { ImageModule } from './../image/image.module';
import { ImageService } from './../image/image.service';
import { FilesService } from './../files/files.service';
import { UpdateService } from '../updates/update.service';
import {UsersService} from "../../users/users.service";
// SCHEMAS
import { EventSchema } from '../../schemas/event.schemas';
import { ImageSchema } from '../../schemas/image.schemas';
import { VspaceSchema } from "../../schemas/vspace.schemas";
import {FlagSchema} from "../../schemas/flag.schemas";
import {UsersSchema} from "../../schemas/users.schemas";
import { FilesSchema } from '../../schemas/files.schemas';
import { UpdateSchema } from 'src/schemas/update.schemas';


@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Event', schema: EventSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Users', schema: UsersSchema },
      { name: 'Flag', schema: FlagSchema },
      { name: 'Files', schema: FilesSchema },
      { name: 'Vspace', schema: VspaceSchema },
      { name: 'Update', schema: UpdateSchema}
    ]),
    ImageModule
  ],
  controllers: [EventController],
  providers: [EventService, ImageService, FlagService, UsersService, EmailService, FilesService, UpdateService],
})

export class EventModule { }