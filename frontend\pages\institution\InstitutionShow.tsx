//Import Library
import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { Container } from "react-bootstrap";
import _ from "lodash";

//Import services/components
import UpdatePopup from "../../components/updates/UpdatePopup";
import apiService from "../../services/apiService";
import InstitutionCoverSection from "./components/InstitutionCoverSection";
import InstitutionInfoSection from "./components/InstitutionInfoSection";
import InstitutionAccordionSection from "./components/InstitutionAccordionSection";

interface InstitutionShowProps {
  routes: string[];
}

const InstitutionShow = (props: InstitutionShowProps) => {
    const initialVal: any = {
        title: "",
        description: "",
        dial_code: "",
        telephone: "",
        address: {
            line_1: "",
            line_2: "",
            city: "",
        },
        website: "",
        type: {
            title: "",
        },
        networks: [],
        expertise: [],
        focal_points: [],
        primary_focal_point: "",
        images: [],
        header: "",
        department: "",
        unit: "",
        partners: [],
        images_src: [],
    };

    const [institutionData, setInstitutionData] = useState(initialVal);
    const [institutionStatus, setInstitutionStatus] = useState({
        partners: 0,
        operations: 0,
        projects: 0,
        operationData: [],
        projectData: [],
    });
    const [activeOperations, setActiveOperations] = useState([]);
    const [activeProjects, setActiveProjects] = useState([]);
    const [focalPoints, setFocalPoints] = useState([]);
    /***End***/
    const [imageLoading, setImageLoading] = useState(true);
    const [editAccess, setEditAccess] = useState(false);

    const fetchInstitutionData = async (loginUserData) => {
        setImageLoading(true);
        const institutionDatavalue = await apiService.get(
            `/institution/${props.routes[1]}`
        );
        setImageLoading(false);
        getOrgEditAccess(institutionDatavalue, loginUserData);
        institutionDatavalue.dial_code =
            institutionDatavalue && institutionDatavalue.dial_code
                ? institutionDatavalue.dial_code
                : "";
        setInstitutionData(institutionDatavalue);
    };

    const fetchInstitutionStatus = async () => {
        const institutionStatusvalue = await apiService.get(
            `/stats/institution/${props.routes[1]}`
        );
        setInstitutionStatus(institutionStatusvalue);
    };

    function getOrgEditAccess(institutionData, loginUser) {
        setEditAccess(false);
        if (loginUser && loginUser['roles']) {
            if (loginUser['roles'].includes("SUPER_ADMIN")) {
                //SUPER_ADMIN can Edit all organisations
                setEditAccess(true);
            } else if (loginUser['roles'].includes("PLATFORM_ADMIN") && institutionData.user == loginUser['_id']) {
                //PLATFORM_ADMIN can Edit organisations which is added by them only
                setEditAccess(true);
            }
            else if (loginUser['roles'].includes("GENERAL_USER") && institutionData.user == loginUser['_id']) {
                //"GENERAL_USER" can Edit organisations which is added by them only
                setEditAccess(true);
            }
        }
    }

    const getLoggedInUser = async () => {
        const data = await apiService.post("/users/getLoggedUser", {});
        if (data && data.roles && data.roles.length) {
            if (data.roles.includes("SUPER_ADMIN")) {
                fetchDatas("Approved", data);
            } else {
                let invites = data.institutionInvites.filter(
                    (invite) => invite.institutionId === props.routes[1]
                );
                if (invites && invites.length) {
                    fetchDatas(invites[0].status, data);
                } else {
                    fetchDatas("You dont have access.", data);
                }
            }
        }
    };

    const fetchDatas = (status, loginUserData) => {
        // if (status === "Approved") {
        fetchInstitutionData(loginUserData);
        fetchInstitutionStatus();
        fetchActiveOperationsByInstitution(props.routes[1]);
        fetchActiveProjectssByInstitution(props.routes[1]);
        // }
    };

    useEffect(() => {
        if (props.routes && props.routes[1]) {
            getLoggedInUser();
        }
    }, []);

    useEffect(() => {
        if (institutionData && institutionData.focal_points && institutionData.focal_points.length > 0) {
            const focalPointsvalu = _.map(institutionData.focal_points, "_id");
            fetchUsersByID(focalPointsvalu);
        }
    }, [institutionData]);

    const fetchActiveOperationsByInstitution = async (institutionId) => {
        const statusId = await fetchOperationStatus();
        if (statusId && statusId.length > 0) {
            const opResponse = await apiService.get("/operation", {
                query: {
                    "partners.institution": [institutionId],
                    status: statusId,
                },
            });
            if (opResponse && opResponse.data && opResponse.data.length > 0) {
                setActiveOperations(opResponse.data);
            }
        }
    };

    const fetchOperationStatus = async () => {
        const response = await apiService.get("/operation_status");
        if (response && response.data && response.data.length > 0) {
            const statusId = [];
            _.forEach(response.data, function (item) {
                if (
                    item.title === "Deployed" ||
                    item.title === "Mobilizing" ||
                    item.title === "Monitoring" ||
                    item.title === "Ongoing"
                ) {
                    statusId.push(item._id);
                }
            });
            return statusId;
        }
        return [];
    };

    const fetchActiveProjectssByInstitution = async (institutionId) => {
        const statusId = await fetchProjectStatus();
        if (statusId && statusId.length > 0) {
            const projectResponse = await apiService.get("/project", {
                query: {
                    "partner_institutions.partner_institution": [institutionId],
                    status: statusId,
                },
            });
            if (projectResponse && projectResponse.data && projectResponse.data.length > 0) {
                setActiveProjects(projectResponse.data);
            }
        }
    };

    const fetchProjectStatus = async () => {
        const response = await apiService.get("/projectStatus");
        if (response && response.data && response.data.length > 0) {
            const statusId = [];
            _.forEach(response.data, function (item) {
                if (item.title === "Ongoing" || item.title === "Planning") {
                    statusId.push(item._id);
                }
            });
            return statusId;
        }
        return [];
    };

    const fetchUsersByID = async (users) => {
        const usersParams = {
            query: { _id: users },
            sort: { title: "asc" },
            limit: "~",
            select:
                "-firstname -lastname -password -role -country -region -institution -status -is_focal_point -mobile_number -enabled -created_at -updated_at -dataConsentPolicy -restrictedUsePolicy -acceptCookiesPolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -emailActivateToken",
        };
        const usersQuery = await apiService.get("/users", usersParams);
        let tableData = [];
        usersQuery?.data.forEach((user) => {
            user?.institutionInvites.forEach((institutionInvite) => {
                if (
                    institutionInvite &&
                    institutionInvite.status === "Approved" &&
                    institutionInvite.institutionId === props.routes[1]
                ) {
                    tableData.push({
                        ...user,
                        ...{
                            institutionId: institutionInvite.institutionId,
                            institutionName: institutionInvite.institutionName,
                            institutionStatus: institutionInvite.status,
                        },
                    });
                }
            });
        });
        if (tableData && tableData.length > 0) {
            const isPrimary = tableData
                .map((item) => [
                    {
                        ...item,
                        isPrimary: item._id === institutionData.primary_focal_point,
                    },
                ])
                .flat();
            const sortPrimaryArr = isPrimary
                .sort((a, b) => a.isPrimary - b.isPrimary)
                .reverse();
            setFocalPoints(sortPrimaryArr);
        }
    };

    let propsData = {
        prop: props,
        imageLoading: imageLoading,
        institutionData: institutionData,
        editAccess: editAccess,
        focalPoints: focalPoints,
    }

    let infoSectionData = {
        institutionData: institutionData,
        institutionStatus: institutionStatus
    }

    let accordionSectionData = {
        institutionData: institutionData,
        prop: props,
        activeProjects: activeProjects,
        activeOperations: activeOperations
    }
    return (
        <div>
            {
                (
                    <>
                        <Container fluid className="_institutionfocalpoint">
                            <UpdatePopup routes={props.routes} />
                        </Container>

                        <InstitutionCoverSection {...propsData} />

                        <InstitutionInfoSection {...infoSectionData} />

                        <InstitutionAccordionSection {...accordionSectionData} />
                    </>
                )
            }
        </div>
    );
};

export default connect((state) => state)(InstitutionShow);
