//Import Library
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import apiService from "../../../services/apiService";
import RKITable from "../../../components/common/RKITable";
import { useTranslation } from 'next-i18next';

function VspaceAdmin(_props: any) {
  const { t } = useTranslation('common');
  const [tabledata, setDataToTable] = useState<any[]>([]);
  const [, setLoading] = useState<boolean>(false);
  const [totalRows, setTotalRows] = useState<number>(0);
  const [perPage, setPerPage] = useState<number>(10);
  const [isModalShow, setModal] = useState<boolean>(false);
  const [newStatus, setNewStatus] = useState<string>("");
  const [selectUserDetails, setSelectUserDetails] = useState<any>({});


  const usersParams = {
    sort: { created_at: "desc" },
    limit: perPage,
    page: 1,
    query: { vspace_status: "Request Pending" },
  };

  const columns = [
    {
      name: t("adminsetting.FocalPointsApprovalTable.Username"),
      selector: "username",
      cell: (d: any) => d.username,
    },
    {
      name: t("adminsetting.FocalPointsApprovalTable.Email"),
      selector: "email",
      cell: (d: any) => d.email,
    },
    {
      name: t("adminsetting.FocalPointsApprovalTable.Action"),
      selector: "",
      cell: (d: any) => (
        <div>
          <Button
            variant="primary"
            size="sm"
            onClick={() => userAction(d, "approve")}
          >
            {t("adminsetting.FocalPointsApprovalTable.aprov")}
          </Button>
          &nbsp;
          <Button
            variant="secondary"
            size="sm"
            onClick={() => userAction(d, "reject")}
          >
            {t("adminsetting.FocalPointsApprovalTable.Reject")}
          </Button>
        </div>
      ),
    },
  ];

  const getUsersData = async () => {

    setLoading(true);
    const response = await apiService.get("/users", usersParams);
    if (response && response.data) {
      setDataToTable(response.data);
      setTotalRows(response.totalCount);
      setLoading(false);
    }
  };
  const handlePageChange = (page: number) => {
    usersParams.limit = perPage;
    usersParams.page = page;
    getUsersData();
  };

  const handlePerRowsChange = async (newPerPage: number, page: number) => {
    usersParams.limit = newPerPage;
    usersParams.page = page;
    setLoading(true);
    const response = await apiService.get("/users", usersParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }
  };

  useEffect(() => {
    getUsersData();
  }, []);

  const userAction = async (d: any, status: string) => {
    setModal(true);
    setNewStatus(status);
    if (d && d._id) {
      const setStatus = status === "approve" ? "Approved" :"Rejected";
      setSelectUserDetails({ ...d, vspace_status: setStatus });
    }

  };

  const modalConfirm = async () => {

    if( selectUserDetails['vspace_status'] === "Rejected" ){

      await apiService.remove(`/users/${selectUserDetails["_id"]}`);
      getUsersData();
      toast.error(t("adminsetting.FocalPointsApprovalTable.Rejected"));
      setSelectUserDetails({});
      setModal(false);

    } else {
      const updatedData = await apiService.patch(
        `/users/${selectUserDetails["_id"]}`,
        selectUserDetails
      );
      if (updatedData && updatedData.status === 403) {
        toast.error(
          updatedData.response && updatedData.response.message
            ? updatedData.response.message
            : t("adminsetting.FocalPointsApprovalTable.Somethingwentswrong")
        );
        return;
      } else {
        getUsersData();
        toast.success(t("adminsetting.FocalPointsApprovalTable.Approvemm"));
        setSelectUserDetails({});
        setModal(false);
      }
    }
  };

  const modalHide = () => setModal(false);

  return (
    <div>
      <Modal show={isModalShow} onHide={modalHide}>
        <Modal.Header closeButton>
          <Modal.Title>
            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t("adminsetting.FocalPointsApprovalTable.User")}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>{t("adminsetting.FocalPointsApprovalTable.Areyousurewantto")} {newStatus} {t("adminsetting.FocalPointsApprovalTable.thisuser?")}</Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={modalHide}>
            {t("adminsetting.FocalPointsApprovalTable.Cancel")}
          </Button>
          <Button variant="primary" onClick={modalConfirm}>
            {t("adminsetting.FocalPointsApprovalTable.Yes")}
          </Button>
        </Modal.Footer>
      </Modal>

      <RKITable
        columns={columns}
        data={tabledata}
        totalRows={totalRows}
        pagServer={true}
        handlePerRowsChange={handlePerRowsChange}
        handlePageChange={handlePageChange}
      />
    </div>
  );
}

export default VspaceAdmin;
