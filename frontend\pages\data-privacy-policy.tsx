//Import Library
import { Card } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

const DataPrivacyPolicy = () => {
  const { t } = useTranslation('common');
  return (
    <Card className="privacyCard">
      <Card.Body>
        <h2>{t("dataPolicy.mainheader")}</h2>
        <p>{t("dataPolicy.info1")}</p>
        <p>
          <strong> {t("dataPolicy.info2")}</strong>
          <br />
          {t("dataPolicy.info3")}
          <br />
          {t("dataPolicy.info4")}
          <br />
          {t("dataPolicy.info5")}
        </p>
        <p>{t("dataPolicy.info6")}</p>
        <p>
          <strong>{t("dataPolicy.info7")}</strong>
          <br />
          {t("dataPolicy.info8")}
          <br />
          {t("dataPolicy.info9")}
          <br />
          {t("dataPolicy.info10")}
          <br />
          {t("dataPolicy.info11")}
          <br />
          {t("dataPolicy.info12")}
        </p>
        <p>
          {t("dataPolicy.info13")}
          <br />
          {t("dataPolicy.info14")}
          <br />
          {t("dataPolicy.info15")}
          <br />
          {t("dataPolicy.info16")}{" "}
          <a href={process.env.HOME_PAGE}>{process.env.HOME_PAGE}</a>
        </p>
        <p>
          <strong>{t("dataPolicy.info17")}</strong>
          <br />
          {t("dataPolicy.info18")}
        </p>
        <p>
          {t("dataPolicy.info19")}
          <br />
          {t("dataPolicy.info20")}
        </p>
        <p>
          <strong> {t("dataPolicy.info21")}</strong>
          <br />
          {t("dataPolicy.info22")}
        </p>
        <p>
          <strong>{t("dataPolicy.info23")}</strong>
          <br />
          {t("dataPolicy.info24")}
          <br />
          <a target="_blank" href={process.env.ADAPPT_HOME_PAGE}>
            {process.env.ADAPPT_HOME_PAGE}
          </a>
        </p>
        <h3>{t("dataPolicy.subheader.header")}</h3>
        <p>
          <strong>{t("dataPolicy.subheader.text1")}</strong>
        </p>
        <p>{t("dataPolicy.subheader.text2")}</p>
        <p>
          <strong> {t("dataPolicy.subheader.text3")}</strong>
        </p>
        <p>{t("dataPolicy.subheader.text4")}</p>
        <p>{t("dataPolicy.subheader.text5")}</p>
        <ul>
          <li>{t("dataPolicy.subheader.text6")}</li>
          <li>{t("dataPolicy.subheader.text7")}</li>
          <li>{t("dataPolicy.subheader.text8")}</li>
          <li>{t("dataPolicy.subheader.text9")}</li>
          <li>{t("dataPolicy.subheader.text10")}</li>
          <li>{t("dataPolicy.subheader.text11")}</li>
        </ul>
        <p>{t("dataPolicy.subheader.text12")}</p>
        <p>{t("dataPolicy.subheader.text13")}</p>
        <p>
          {t("dataPolicy.subheader.text14")}{" "}
          <a target="_blank" href={t("dataPolicy.subheader.text14a")}>
            {t("dataPolicy.subheader.text14b")}
          </a>
        </p>
      </Card.Body>
    </Card>
  );
};

export default DataPrivacyPolicy;
