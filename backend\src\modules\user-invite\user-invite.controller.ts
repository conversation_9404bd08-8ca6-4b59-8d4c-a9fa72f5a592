//Import Library
import { Controller, Post, Body, UseGuards } from '@nestjs/common';

//Import services/components
import { UserInviteService } from "./user-invite.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('user-invite')
@UseGuards(SessionGuard)
export class UserInviteController {
  constructor(
    private readonly _userInviteService: UserInviteService
  ) { }

  @Post()
  find(@Body() query: any) {
    return this._userInviteService.getUserForInvite(query);
  }
}
