//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const WorldRegionSchema = new mongoose.Schema({
  code: { type: String, required: true, unique: true },
  title: { type: String, required: true, unique: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
});

WorldRegionSchema.plugin(mongoosePaginate);
