//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ProjectStatusSeederService } from './project-status-seeder.services';
// SCHEMAS
import { ProjectStatusSchema } from 'src/schemas/project_status.schemas';

/**
 * Import and provide seeder classes for project status.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'ProjectStatus', schema: ProjectStatusSchema }
      ]
    )
  ],
  providers: [ProjectStatusSeederService],
  exports: [ProjectStatusSeederService],
})

export class ProjectStatusSeederModule { }
