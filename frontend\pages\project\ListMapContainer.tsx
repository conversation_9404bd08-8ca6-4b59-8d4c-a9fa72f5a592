//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";

//Import services/components
import { useTranslation } from 'next-i18next';
import RKIMAP1 from "../../components/common/RKIMap1";
import RKIMapMarker from "../../components/common/RKIMapMarker";

const ListMapContainer = (props) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language;
  const { projects, selectedRegions } = props;
  const [points, setPoints] = useState([]);
  const [activeMarker, setactiveMarker]: any = useState({});
  const [markerInfo, setMarkerInfo]: any = useState({});
  const [groupedProjects, setGroupedProjects]: any = useState({});

  const MarkerInfo = (Markerprops) => {
    const { info } = Markerprops;
    if (info && info.countryId && groupedProjects[info.countryId]) {
      return (
        <ul>
          {groupedProjects[info.countryId].map((item, index) => {
            return (
              <li key={index}>
                <a href={`/${currentLang}/project/show/${item.id}`}>{item.title}</a>
              </li>
            );
          })}
        </ul>
      );
    }
    return null;
  };

  const resetMarker = () => {
    setactiveMarker(null);
    setMarkerInfo(null);
  };

  const onMarkerClick = (propsinitial, marker, e) => {
    resetMarker();
    setactiveMarker(marker);
    setMarkerInfo({
      name: propsinitial.name,
      id: propsinitial.id,
      countryId: propsinitial.countryId,
    });
  };

  const part = (pointer) =>
    pointer.partner_country && pointer.partner_country.coordinates;
  const part1 = (pointer) =>
    pointer.partner_country && pointer.partner_country.coordinates;
  const getpointer = (project) => {
    const projectParterner = [];
    _.forEach(project.partner_institutions, (pointer) => {
      console.log("pointer", pointer);

      projectParterner.push({
        title: project && project.title ? project.title : "",
        id: project && project._id ? project._id : "",
        lat:
          part(pointer) &&
          parseFloat(pointer.partner_country.coordinates[0].latitude),
        lng:
          part1(pointer) &&
          parseFloat(pointer.partner_country.coordinates[0].longitude),
        world_region: pointer.world_region,
        countryId: pointer.partner_country && pointer.partner_country._id,
      });
    });
    return projectParterner[0];
  };

  const setPointsFromProjects = () => {
    const filterProjectpoints = [];
    _.forEach(projects, (project) => {
      const partner = getpointer(project);
      filterProjectpoints.push(partner);
    });
    const filteredPoints = _.filter(filterProjectpoints, function (point) {
      if (selectedRegions.length > 0) {
        return selectedRegions.includes(point.world_region);
      }
    });
    setPoints(filteredPoints);
  };

  const getProjectsGroupBCountry = () => {
    const countriesList = [];
    _.forEach(projects, (project) => {
      if (
        project.partner_institutions &&
        project.partner_institutions.length > 0
      ) {
        _.forEach(project.partner_institutions, (pi) => {
          pi.title = project.title;
          pi.id = project._id;
          countriesList.push(pi);
        });
      }
    });
    setGroupedProjects(_.groupBy(countriesList, "partner_country._id"));
  };

  useEffect(() => {
    setPointsFromProjects();
    getProjectsGroupBCountry();
  }, [projects]);

  return (
    <RKIMAP1
      onClose={resetMarker}
      language={currentLang}
      activeMarker={activeMarker}
      markerInfo={<MarkerInfo info={markerInfo} />}
    >
      {points.length >= 1
        ? points.map((item, index) => {
            return (
              <RKIMapMarker
                key={index}
                name={item.title}
                id={item.id}
                countryId={item.countryId}
                icon={{
                  url: "/images/map-marker-white.svg",
                }}
                onClick={onMarkerClick}
                position={item}
              />
            );
          })
        : null}
    </RKIMAP1>
  );
};

export default ListMapContainer;
