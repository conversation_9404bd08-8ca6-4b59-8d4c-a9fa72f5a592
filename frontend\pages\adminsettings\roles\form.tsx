//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput } from "../../../components/common/FormValidation";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from 'next/link';

//Import services/components
import { RoleInterface } from "../../../components/interfaces/role.interface";
import { useTranslation } from 'next-i18next';
import apiService from "../../../services/apiService";


const RoleForm = (props: any) => {

  const _initialrole = {
    title: '',
  }

  const [initialVal, setInitialVal] = useState<RoleInterface>(_initialrole);
    const editform = props.routes && props.routes[0] === "edit_role" && props.routes[1];
  const { t } = useTranslation('common');


  const formRef = useRef(null);

  const resetHandler = () => {
    setInitialVal(_initialrole);
    // Reset validation state (Formik handles this automatically)
    window.scrollTo(0, 0);
  };

  const handleChange = e => {
    if (e.target) {
      const { name, value } = e.target;
      setInitialVal(prevState => ({
        ...prevState,
        [name]: value
      }));
    }
  }

  const handleSubmit = async (event, values) => {
    if (event) event.preventDefault();
    // Use Formik values if available, otherwise fall back to initialVal
    const formValues = values || initialVal;
    const obj = {
      title: formValues.title.trim(),
    };

    let response;
    if (editform) {
      response = await apiService.patch(`/roles/${props.routes[1]}`, obj);
    } else {
      response = await apiService.post("/roles", obj);
    }
    if (response && response._id) {
      toast.success(t("Roleisaddedsuccessfully"));
      Router.push("/adminsettings/role");
    } else {
      toast.error(response)
    }
  }

  useEffect(() => {
    const roleParams = {
      query: {},
      sort: { title: "asc" },
      limit: "~",
    };
    if (editform) {
      const getRoleData = async () => {
        const response = await apiService.get(`/roles/${props.routes[1]}`, roleParams);
        setInitialVal((prevState) => ({ ...prevState, ...response }));
      }
      getRoleData();
    }
  }, []);

  return (
    <div>
      <Container className="formCard" fluid>
        <Card style={{ marginTop: "5px", boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)" }}>
          <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
            <Card.Body>
              <Row>
                <Col>
                  <Card.Title>{t("Role")}</Card.Title>
                </Col>
              </Row>
              <hr />
              <Row>
                <Col md lg={6} sm={12}>
                  <Form.Group>
                    <Form.Label className="required-field">{t("Role")}</Form.Label>
                    <TextInput
                      name="title"
                      id="title"
                      required value={initialVal.title}
                      validator={((value) => String(value || '').trim() !== "")}
                      errorMessage={{
                        validator: t("PleaseAddtheRole")}}
                      onChange={handleChange}
                    />
                  </Form.Group>
                </Col>
              </Row>
              <Row className="my-4">
                <Col>
                  <Button className="me-2" type="submit" variant="primary">{t("submit")}</Button>
                  <Button className="me-2" onClick={resetHandler} variant="info">{t("reset")}</Button>
                  <Link
                    href="/adminsettings/[...routes]"
                    as={`/adminsettings/role`}
                    ><Button variant="secondary">{t("cancel")}</Button></Link>
                </Col>
              </Row>
            </Card.Body>
          </ValidationFormWrapper>
        </Card>
      </Container>
    </div>
  );
}
export default RoleForm;
