//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { InstitutionNetworkController } from './institution-network.controller';
import { InstitutionNetworkService } from './institution-network.service';
// SCHEMAS
import { InstitutionNetworkSchema } from '../../schemas/institution_network.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'InstitutionNetwork', schema: InstitutionNetworkSchema }
    ])
  ],
  controllers: [InstitutionNetworkController],
  providers: [InstitutionNetworkService],
})

export class InsitutionNetworkModule { }