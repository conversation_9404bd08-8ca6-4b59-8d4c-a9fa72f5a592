//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { RiskLevelSeederService } from './risk-level-seeder.services';
// SCHEMAS
import { RiskLevelSchema } from 'src/schemas/risk_level.schemas';

/**
 * Import and provide seeder classes for Syndrome.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'RiskLevel', schema: RiskLevelSchema }
      ]
    )
  ],
  providers: [RiskLevelSeederService],
  exports: [RiskLevelSeederService],
})
export class RiskLevelSeederModule { }
