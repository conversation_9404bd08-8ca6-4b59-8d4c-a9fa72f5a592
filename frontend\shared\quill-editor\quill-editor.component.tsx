// React Imports
import React from "react";
import SimpleRichTextEditor from "../../components/common/SimpleRichTextEditor";

interface IEditorComponentProps {
  initContent: string;
  onChange: Function;
}

export const EditorComponent: React.FC<IEditorComponentProps> = (props) => {
  const { initContent, onChange } = props;

  return (
    <SimpleRichTextEditor
      value={initContent || ""}
      onChange={(content) => onChange(content)}
    />
  );
};
