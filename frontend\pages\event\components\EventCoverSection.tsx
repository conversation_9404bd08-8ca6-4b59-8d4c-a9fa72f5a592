//Import Library
import React from 'react';
import { Col, Row } from "react-bootstrap";

//Import services/components
import ShowMapContainer from "../../../components/common/maps/ShowMapContainer";
import { useTranslation } from 'next-i18next';

interface EventCoverSectionProps {
  eventData: {
    _id: string;
    title: string;
    country?: {
      title: string;
      coordinates?: Array<{
        latitude: string;
        longitude: string;
      }>;
    };
    operation?: {
      title: string;
    };
    status?: {
      title: string;
    };
    hazard_type?: {
      title: string;
    };
    syndrome?: {
      title: string;
    };
    officially_validated?: boolean;
    rki_monitored?: boolean;
    laboratory_confirmed?: boolean;
    country_regions?: Array<{
      title: string;
    }>;
    hazard?: Array<{
      title: {
        en: string;
      };
    }>;
  };
}

const EventCoverSection = (props: EventCoverSectionProps) => {
    const { t } = useTranslation('common');
    return (
        <>
            <Row>
                <Col md={6}>
                    <ShowMapContainer mapdata={props.eventData} />
                </Col>
                <Col className="eventDetails ps-md-0" md={6}>
                    <p>
                        {" "}
                        <b>{t("Events.show.EventId")}</b>: <span>{props.eventData ? props.eventData.title : ""}</span>{" "}
                    </p>
                    {operation_func(t, props.eventData)}
                    <p>
                        <b>{t("Events.show.Country&Territory")}</b>:
                        <span>{props.eventData?.country ? props.eventData.country.title : ""}</span>
                    </p>
                    {event_Monitored_func(t, props.eventData)}
                    {Event_countrty_func(t, props.eventData)}
                    <p>
                        <b>{t("Events.show.Status")}</b>:
                        <span>{props.eventData?.status ? props.eventData.status.title : ""}</span>
                    </p>
                    {hazard_func()}
                    {hazard_show_func(t, props.eventData)}
                    <p>
                        <b>{t("Events.show.Syndrome")}</b>:
                        <span>{props.eventData?.syndrome ? props.eventData.syndrome.title : ""}</span>
                    </p>
                    {laboratory_func(t, props.eventData)}
                    <p style={{ margin: 0 }}>
                        <b>{t("Events.show.Validatedbyofficial")}</b>:
                        <span>
                            {props.eventData?.officially_validated
                                ? props.eventData.officially_validated === true && "Yes"
                                : "No"}
                        </span>
                    </p>
                </Col>
            </Row>
        </>
    );

    function hazard_func() {
        return (
            <p>
                <b>{t("Events.show.HazardType")}</b>:
                <span>{props.eventData?.hazard_type ? props.eventData.hazard_type.title : ""}</span>
            </p>
        );
    }
}

export default EventCoverSection;

function operation_func(t: (key: string) => string, eventData: any) {
    return (
        <p>
            <b>{t("Events.show.OperationName")}</b>:
            <span>{eventData && eventData.operation ? eventData.operation.title : ""}</span>
        </p>
    );
}

function event_Monitored_func(t: (key: string) => string, eventData: any) {
    return (
        <p>
            <b>{t("Events.show.MonitoredbyRKI")}</b>:
            <span>{eventData && eventData.rki_monitored ? eventData.rki_monitored === true && "Yes" : "No"}</span>
        </p>
    );
}

function Event_countrty_func(t: (key: string) => string, eventData: any) {
    return (
        <p>
            <b>{t("Events.show.CountryRegion")}</b>:
            <span>
                {eventData && eventData.country_regions
                    ? eventData.country_regions.map((item) => item.title).join(", ")
                    : ""}
            </span>
        </p>
    );
}

function hazard_show_func(t: (key: string) => string, eventData: any) {
    return (
        <p>
            <b>{t("Events.show.Hazard")}</b>:
            <span>
                {eventData && eventData.hazard
                    ? eventData.hazard.map((item) => (item ? item.title.en : "")).join(", ")
                    : ""}
            </span>
        </p>
    );
}

function laboratory_func(t: (key: string) => string, eventData: any) {
    return (
        <p>
            <b>{t("Events.show.LaboratoryConfirmed")}</b>:
            <span>
                {eventData && eventData.laboratory_confirmed ? eventData.laboratory_confirmed === true && "Yes" : "No"}
            </span>
        </p>
    );
}