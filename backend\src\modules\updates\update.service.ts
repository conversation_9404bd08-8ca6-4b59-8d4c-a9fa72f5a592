//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { UpdateInterface } from '../../interfaces/update.interface';
import { CreateUpdateDto } from './dto/create-update.dto';
import { UpdateUpdateDto } from './dto/update-update.dto';

@Injectable()
export class UpdateService {
  constructor(
    @InjectModel('Update') private updateModel: Model<UpdateInterface>
  ) { }

  async create(createUpdateDto: CreateUpdateDto): Promise<UpdateInterface> {
    const createdUpdate = new this.updateModel(createUpdateDto);
    return createdUpdate.save();
  }

  async findAll(query): Promise<UpdateInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options: any = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.skip) {
      options.offset = query.skip ? query.skip : 0
    }

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.updateModel.paginate(_filter, options);
  }

  async get(updateId): Promise<UpdateInterface[]> {
    let _update;
    try {
      _update = await this.updateModel.findById(updateId).exec();
    } catch (error) {
      throw new NotFoundException('Could not find Update.');
    }
    if (!_update) {
      throw new NotFoundException('Could not find Update.');
    }
    return _update;
  }  

  async update(updateId: any, updateUpdateDto: UpdateUpdateDto) {
    const getUpdateById: any = await this.updateModel.findById(updateId).exec();
    try {
      Object.keys(updateUpdateDto).forEach((d) => {
        getUpdateById[d] = updateUpdateDto[d];
      });
      getUpdateById.updated_at = new Date();
      getUpdateById.save();
    } catch (e) {
      throw new NotFoundException('Could not update hazard.');
    }

    return getUpdateById;
  }

  async delete(updateId: string) {
    const result = await this.updateModel.findOneAndDelete({ _id: updateId }).exec();
    if (!result) {
      throw new NotFoundException('Could not find Update details');
    }
    return result;
  }

  async deleteUpdateAssociatedWithEntity(entityId: string, entityType: string) {
    await this.updateModel
      .deleteMany({ [entityType]: entityId })
      .exec();
  }
}
