//Import Library
import { useRouter } from 'next/router';

//Import services/components
import ProjectForm from './Form';
import ProjectShow from './ProjectShow';
import { canAddProjectForm } from "./permission";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Router = () => {
  const router = useRouter()
  const routes:any = router.query.routes || []

  const CanAccessCreateForm  = canAddProjectForm(() => <ProjectForm routes={routes} />)

  switch (routes[0]) {
    case 'create':
      return <CanAccessCreateForm />

    case 'edit':
      return <ProjectForm routes={routes} />

    case 'show':
      return (<ProjectShow routes={routes} />)

    default:
      return null;
  }
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Router
