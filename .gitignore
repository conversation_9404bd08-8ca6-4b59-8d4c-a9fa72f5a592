# compiled output
backend/dist
backend/node_modules
backend/temp/*
backend/upload
backend/files
frontend/node_modules

# Application based gitignore
.env
# package-lock.json
# yarn.lock

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

### macOS ###
# General
.DS_Store
.AppleDouble
.LSOverride

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
