//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";
import { Form, Button } from "react-bootstrap";

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

// Define types for hazard items
interface HazardItem {
    _id: string;
    code: string;
    title: string;
    isChecked: boolean;
}

function HazardMultiCheckboxes(props: any) {
    const { t } = useTranslation('common');
    const { filthaz } = props;
    const [allhazard, setAllhazard] = useState(true);
    const [hazard, setHazard] = useState<HazardItem[]>([]);
    const [selectedHazard, setSelectedHazard] = useState<string[]>([]);
    const RegionParams = {
        query: {},
        limit: "~",
        sort: { title: "asc" },
    };

    const gethazard = async (RegionParams_initial: any) => {
        const response = await apiService.get("/hazardtype", RegionParams_initial);
        if (response && Array.isArray(response.data)) {
            const finalHazard: HazardItem[] = [];
            const selectedIds: string[] = [];

            _.each(response.data, (item: any, _i: number) => {
                const hazardItem: HazardItem = {
                    ...item,
                    isChecked: true
                };
                finalHazard.push(hazardItem);
                selectedIds.push(item._id);
            });

            filthaz(selectedIds);
            setSelectedHazard(selectedIds);
            setHazard(finalHazard);
        }
    };

    useEffect(() => {
        gethazard(RegionParams);
    }, []);

    const handleAllChecked = (event: React.ChangeEvent<HTMLInputElement>) => {
        const updatedHazard = hazard.map((item: HazardItem) => ({
            ...item,
            isChecked: event.target.checked
        }));

        let selected_Hazard: string[] = [];
        if (event.target.checked) {
            selected_Hazard = updatedHazard.map(item => item._id);
        }

        filthaz(selected_Hazard);
        setSelectedHazard(selected_Hazard);
        setAllhazard(event.target.checked);
        setHazard(updatedHazard);
    };

    const handleIndividualHazardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const updatedHazard = [...hazard];
        let updatedSelectedHazard = [...selectedHazard];

        updatedHazard.forEach((item, index) => {
            if (item.code === e.target.id) {
                updatedHazard[index].isChecked = e.target.checked;
                if (!e.target.checked) {
                    updatedSelectedHazard = updatedSelectedHazard.filter(n => n !== item._id);
                } else {
                    updatedSelectedHazard.push(item._id);
                }
            }
        });

        setSelectedHazard(updatedSelectedHazard);
        filthaz(updatedSelectedHazard);
        setAllhazard(false);
        setHazard(updatedHazard);
    };

    const resetAllHazard = () => {
        const updatedHazard = hazard.map((item: HazardItem) => ({
            ...item,
            isChecked: false
        }));

        setSelectedHazard([]);
        setAllhazard(false);
        setHazard(updatedHazard);
        filthaz([]);
    };

    return (
        <div className="hazards-multi-checkboxes">
            <Form.Check
                type="checkbox"
                id={`all`}
                checked={allhazard}
                label={t("Events.forms.AllHazards")}
                onChange={handleAllChecked}
            />
            {hazard.map((item, index) => {
                return (
                    <Form.Check
                        key={index}
                        type="checkbox"
                        id={item.code}
                        label={item.title}
                        value={item.code}
                        onChange={handleIndividualHazardChange}
                        checked={hazard[index].isChecked}
                    />
                );
            })}

            <Button onClick={resetAllHazard} className="btn-plain ps-2">
                {t("ClearAll")}
            </Button>
        </div>
    );
}

HazardMultiCheckboxes.defaultProps = {
    filthaz: () => {
        ("");
    },
};

export default HazardMultiCheckboxes;

function Hazard_setSelected(setSelectedHazard: React.Dispatch<React.SetStateAction<any[]>>, selectedHazard: any[]) {
    setSelectedHazard(selectedHazard);
}

function Hazard_selected(setSelectedHazard: React.Dispatch<React.SetStateAction<any[]>>, selectedHazard: any[]) {
    setSelectedHazard(selectedHazard);
}
