//Import Library
import { useEffect, useState } from "react";
import _ from "lodash";
import {
  Col,
  Container,
  FormControl,
  FormGroup,
  FormLabel,
  Row,
} from "react-bootstrap";

//Import services/components
import apiService from "../../services/apiService";
import React from "react";
import { useTranslation } from 'next-i18next';


const EventsFilter = ({
  filterText,
  onFilter,
  onClear,
  onFilterHazardChange,
  filterHazard,
}) => {
  const [hazardType, setHazardType] = useState([]);
  const { t } = useTranslation('common');



  const getNetworks = async (params) => {
    const response = await apiService.get("/hazardtype", params);

    if (response && Array.isArray(response.data)) {
      setHazardType(response.data);
    }
  };

  useEffect(() => {
    getNetworks({ query: {}, sort: { title: "asc" } });
  }, []);

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={6} className="p-0">
          <FormControl
            type="text"
            className="searchInput"
            placeholder= {t("Events.table.Search")}
            aria-label="Search"
            value={filterText}
            onChange={onFilter}
          />
        </Col>

        <Col xs={6}>
          <FormControl
            as="select"
            aria-label="HazardType"
            aria-placeholder="Hazard Type"
            onChange={onFilterHazardChange}
            value={filterHazard}
          >
            <option value={""}>{t("Events.forms.SelectHazardType")}</option>
            {hazardType.map((item, index) => {
              return (
                <option key={index} value={item._id}>
                  {item.title}
                </option>
              );
            })}
          </FormControl>
        </Col>
      </Row>
    </Container>
  );
};

export default EventsFilter;
