//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { OperationStatusController } from './operation-status.controller';
import { OperationStatusService } from './operation-status.service';
// SCHEMAS
import { OperationStatusSchema } from 'src/schemas/operation_status.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'OperationStatus', schema: OperationStatusSchema }
    ])
  ],
  controllers: [OperationStatusController],
  providers: [OperationStatusService],
})

export class OperationStatusModule { }