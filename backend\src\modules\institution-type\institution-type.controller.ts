//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateInstitutionTypeDto } from './dto/create-institution-type.dto';
import { UpdateInstitutionTypeDto } from './dto/update-institution-type.dto';
import { InstitutionTypeService } from './institution-type.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('institutiontype')
@UseGuards(SessionGuard)
export class InstitutionTypeController {
  constructor(
    private readonly _institutionTypeService: InstitutionTypeService,
  ) {}

  @Post()
  async create(@Body() createInstitutionTypeDto: CreateInstitutionTypeDto) {
    try {
      const resp = await this._institutionTypeService.create(
        createInstitutionTypeDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._institutionTypeService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') institutionTypeId: string) {
    return this._institutionTypeService.get(institutionTypeId);
  }

  @Patch(':id')
  async update(
    @Param('id') institutionTypeId: string,
    @Body() updateInstitutionTypeDto: UpdateInstitutionTypeDto,
  ) {
    try {
      const resp = await this._institutionTypeService.update(
        institutionTypeId,
        updateInstitutionTypeDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') institutionTypeId: string) {
    const deletedData = this._institutionTypeService.delete(institutionTypeId);
    return deletedData;
  }
}
