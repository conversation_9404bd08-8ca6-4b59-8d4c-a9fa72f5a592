//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { RegionSeederService } from './region-seeder.services';
// SCHEMAS
import { RegionSchema } from 'src/schemas/region.schemas';
import { CountrySchema } from 'src/schemas/country.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Region', schema: RegionSchema },
        { name: 'Country', schema: CountrySchema }
      ]
    )
  ],
  providers: [RegionSeederService],
  exports: [RegionSeederService],
})
export class RegionSeederModule { }