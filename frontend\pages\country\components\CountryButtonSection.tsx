//Import Library
import React from "react";
import { Button, Col, Row } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

interface CountryButtonSectionProps {
  health_profile?: string;
  security_advice?: string;
}

const CountryButtonSection = (props: CountryButtonSectionProps) => {
    const { t } = useTranslation('common');
    return (
        <>
            <Row>
                <Col>
                    {props.health_profile ? (
                        <a href={props.health_profile} target="_blank">
                            <Button className="countryBtn d-grid" variant="primary" size="lg">
                                {t("healthprofile")}
                            </Button>
                        </a>
                    ) : (
                        ""
                    )}
                </Col>
                <Col>
                    {props.security_advice ? (
                        <a href={props.security_advice} target="_blank">
                            <Button className="countryBtn d-grid" variant="primary" size="lg">
                                {t("securityadvice")}
                            </Button>
                        </a>
                    ) : (
                        ""
                    )}
                </Col>
            </Row>
        </>
    )
}

export default CountryButtonSection;