//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { AreaOfWorkInterface } from '../../interfaces/area-of-work.interface';
import { CreateAreaOfWorkDto } from './dto/create-area-of-work.dto';
import { UpdateAreaOfWorkDto } from './dto/update-area-of-work.dto';

const FindArea = 'Could not find Area of Work.';
@Injectable()
export class AreaOfWorkService {
  constructor(
    @InjectModel('AreaOfWork')
    private areaOfWorkModel: Model<AreaOfWorkInterface>,
  ) {}

  async create(
    createAreaOfWorkDto: CreateAreaOfWorkDto,
  ): Promise<AreaOfWorkInterface> {
    const createdAreaOfWork = new this.areaOfWorkModel(createAreaOfWorkDto);
    return createdAreaOfWork.save();
  }

  async findAll(query): Promise<AreaOfWorkInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.areaOfWorkModel.paginate(_filter, options);
  }

  async get(areaOfWorkId): Promise<AreaOfWorkInterface[]> {
    let _areaOfWork;
    try {
      _areaOfWork = await this.areaOfWorkModel.findById(areaOfWorkId).exec();
    } catch (error) {
      throw new NotFoundException(FindArea);
    }
    if (!_areaOfWork) {
      throw new NotFoundException(FindArea);
    }
    return _areaOfWork;
  }

  async update(areaOfWorkId: any, updateAreaOfWorkDto: UpdateAreaOfWorkDto) {
    const getAreaOfWorkById: any = await this.areaOfWorkModel
      .findById(areaOfWorkId)
      .exec();
    const updatedAreaOfWork = new this.areaOfWorkModel(updateAreaOfWorkDto);
    if (getAreaOfWorkById.title) {
      getAreaOfWorkById.title = updatedAreaOfWork.title;
    }
    getAreaOfWorkById.updatedAt = new Date();
    return getAreaOfWorkById.save();
  }

  async delete(areaOfWorkId: string) {
    const result = await this.areaOfWorkModel
      .deleteOne({ _id: areaOfWorkId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(FindArea);
    }
  }
}
