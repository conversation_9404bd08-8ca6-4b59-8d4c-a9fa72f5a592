//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { ClassificationInterface } from '../../interfaces/classification.interface';
import { CreateClassificationDto } from './dto/create-classification.dto';
import { UpdateClassificationDto } from './dto/update-classification.dto';
const Couldnot = "Could not find Classification.";

@Injectable()
export class ClassificationService {
  let  
  constructor(
    @InjectModel('Classification') private classificationModel: Model<ClassificationInterface>
  ) { }

  async create(createClassificationDto: CreateClassificationDto): Promise<ClassificationInterface> {
    const createdClassification = new this.classificationModel(createClassificationDto);
    return createdClassification.save();
  }

  async getTotalCount(filter: any) {
    let _filter = {};
    try {
      _filter = filter.query ? filter.query : {};
    } catch (e) {
    }
    return this.classificationModel.count(_filter).exec();
  }

  async findAll(query): Promise<ClassificationInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.classificationModel.paginate(_filter, options);
  }

  async get(classificationId): Promise<ClassificationInterface[]> {
    let _result;
    try {
      _result = await this.classificationModel.findById(classificationId).exec();
    } catch (error) {
      throw new NotFoundException(Couldnot);
    }
    if (!_result) {
      throw new NotFoundException(Couldnot);
    }
    return _result;
  }

  async update(classificationId: any, updateClassificationDto: UpdateClassificationDto) {
    const getById: any = await this.classificationModel.findById(classificationId).exec();
    const updatedData = new this.classificationModel(updateClassificationDto);
    try {
      Object.keys(updateClassificationDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException(Couldnot);
    }
    return getById;
  }

  async delete(classificationId: string) {
    const result = await this.classificationModel.deleteOne({ _id: classificationId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(Couldnot);
    }
  }
}
