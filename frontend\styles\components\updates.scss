.updatesPopupBlock {
  background-color: #f5f5f5;
  margin: -8px -8px 15px;
  padding: 15px;
}

.updateActions {
  position: absolute;
  right: 6px;
  top: 6px;
  display: flex;
  i {
    margin: 0 6px;
    background: #e4dbdb;
    width: 35px;
    height: 35px;
    border-radius: 50px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d2d1d1;
    color: #5a5a5a;
    cursor: pointer;
  }
}

.updatesPopMain {
  .spinner-border {
    top: 20px;
  }
}

/* .react-datepicker__input-container {
  width: 230px !important;
} */
