//Import Library
import { Container, <PERSON>, <PERSON> } from "react-bootstrap";
import _ from "lodash";
import { useEffect, useState } from "react";

//Import services/components
import RKICalendar from "../../components/common/RKICalendar";
import apiService from "../../services/apiService";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const eventsCalendar = (_props) => {
  const [events, setEvents] = useState([]);

  const fetchUpdateType = async () => {
    const response = await apiService.get("/updateType", {
      query: { title: "Calendar Event" },
    });
    if (response && response.data) {
      fetchCalendarEvents(response.data[0]._id);
    }
  };

  const fetchCalendarEvents = async (updateTypeId) => {
    const calendarEventsParams = {
      query: { update_type: updateTypeId },
      sort: { created_at: "desc" },
      limit: 20,
      select:
        "-created_at -updated_at -update_type -contact_details -description -document -images -link -media -parent_operation -reply -show_as_announcement -type",
    };
    const response = await apiService.get("/updates", calendarEventsParams);
    if (response && response.data) {
      _.forEach(response.data, function (val, i) {
        response.data[i].start_date = new Date(val.start_date);
        response.data[i].end_date = new Date(val.end_date);
      });
      setEvents(response.data);
    }
  };

  useEffect(() => {
    fetchUpdateType();
  }, []);

  return (
    <Container>
      <Row>
        <Col className="pe-0" xs="12">
          <RKICalendar
            views={["month", "week", "day"]}
            style={{ height: 800 }}
            eventsList={events}
          />
        </Col>
      </Row>
    </Container>
  );
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default eventsCalendar;
