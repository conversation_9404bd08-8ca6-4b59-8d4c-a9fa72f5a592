//Import Library
import { Model } from 'mongoose-paginate-v2';
import {
  Injectable,
  NotFoundException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
require('dotenv').config();
import * as bcrypt from 'bcrypt';
import * as mongoose from 'mongoose';
import * as async from 'async';

//Import services/components
import { UsersInterface } from '../interfaces/users.interface';
import { VspaceInterface } from '../interfaces/vspace.interface';
import { CreateUsersDto } from './dto/create-users.dto';
import { UpdateUsersDto } from './dto/update-users.dto';
// SCHEMAS
import { UsersSchema } from '../schemas/users.schemas';
const crypto = require('crypto');

const Message = mongoose.model('Message', UsersSchema);

const SALT_WORK_FACTOR = process.env.SALT_WORK_FACTOR
  ? parseInt(process.env.SALT_WORK_FACTOR)
  : 10;

@Injectable()
export class UsersService {
  constructor(
    @InjectModel('Users') private usersModel: Model<UsersInterface>,
    @InjectModel('Vspace') private vspaceModel: Model<VspaceInterface>,
  ) {}

  randomString(length) {
    let result = '';
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;

    for (let i = 0; i < length; i++) {
      const randomVal = crypto.randomInt(0, 60);
      result += characters.charAt(Math.floor(randomVal * charactersLength));
    }
    return result;
  }

  async create(createUsersDto: CreateUsersDto): Promise<UsersInterface> {
    const username = createUsersDto['username']
      ? createUsersDto['username']
      : '';
    const email = createUsersDto['email'] ? createUsersDto['email'] : '';
    const messageToSearchWith: any = new Message({ username, email });
    messageToSearchWith.encryptFieldsSync();
    const _query = {
      $or: [
        { username: messageToSearchWith['username'] },
        { email: messageToSearchWith['email'] },
      ],
    };
    const userNameExists = await this.usersModel.findOne(_query);
    if (userNameExists) {
      throw new HttpException(
        {
          status: HttpStatus.FORBIDDEN,
          message: ['username or email already exists'],
          user: userNameExists._id,
        },
        HttpStatus.FORBIDDEN,
      );
    }
    const createdUsers = new this.usersModel(createUsersDto);
    const salt = await bcrypt.genSaltSync(SALT_WORK_FACTOR);
    const hash = await bcrypt.hashSync(createdUsers.password, parseInt(salt));
    createdUsers.password = hash;
    if (!createdUsers.emailActivateToken) {
      createdUsers.emailActivateToken = this.randomString(16);
    }
    return createdUsers.save();
  }

  async bulkCreate(
    createUsersDto: Array<CreateUsersDto>,
  ): Promise<UsersInterface[]> {
    try {
      const createdUsers = await this.usersModel.insertMany(createUsersDto);
      return createdUsers;
    } catch (err) {
      return [];
    }
  }

  async getCount(query): Promise<number> {
    const count = await this.usersModel.count(query);
    return count;
  }

  async validPassword(hashedPassword, incomingPlainPassword) {
    const isValid = bcrypt.compareSync(incomingPlainPassword, hashedPassword); // true
    return isValid;
  }

  protectPassword(arr) {
    if (arr && Array.isArray(arr)) {
      arr = arr.map((d) => {
        d.password = undefined;
        return d;
      });
    } else if (arr.password) {
      arr.password = undefined;
    }
    return arr;
  }

  async setPassword(email: string, newPassword: string): Promise<boolean> {
    const User = mongoose.model('Users', UsersSchema);
    const emailToSearch: any = new User({ email });
    const salt = await bcrypt.genSaltSync(SALT_WORK_FACTOR);

    emailToSearch.encryptFieldsSync();
    const userFromDb = await this.usersModel.findOne({
      email: emailToSearch['email'],
    });
    if (!userFromDb) {
      throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);
    }

    userFromDb.password = await bcrypt.hashSync(newPassword, parseInt(salt));

    await userFromDb.save();
    return true;
  }

  async getTotalCount(filter: any) {
    let _filter = {};
    try {
      _filter = filter.query ? filter.query : {};
    } catch (e) {}
    return this.usersModel.count(_filter).exec();
  }

  async findAll(query): Promise<UsersInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      populate: query.populate ? query.populate : '',
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    if (_filter.username || _filter.email) {
      if (_filter.username) {
        const username = _filter.username;
        const messageToSearchWith: any = new Message({ username });
        messageToSearchWith.encryptFieldsSync();
        _filter['username'] = messageToSearchWith['username'];
      }

      if (_filter.email) {
        const email = _filter.email;
        const messageToSearchWith: any = new Message({ email });
        messageToSearchWith.encryptFieldsSync();
        _filter['email'] = messageToSearchWith['email'];
      }
    }
    return this.usersModel.paginate(_filter, options);
  }

  async get(userId): Promise<UsersInterface[]> {
    let _result;
    try {
      _result = await this.usersModel.findById(userId).exec();
    } catch (error) {
      throw new NotFoundException('Could not find Users.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Users.');
    }
    return _result;
  }

  async update(userId: any, updateUsersDto: UpdateUsersDto) {
    const getById: any = await this.usersModel.findById(userId).exec();
    const updatedData = new this.usersModel(updateUsersDto);
    try {
      Object.keys(updateUsersDto).forEach(async (d) => {
        if (d !== 'password') {
          getById[d] = updatedData[d];
        }
      });
      if (updateUsersDto['password']) {
        const salt = await bcrypt.genSaltSync(SALT_WORK_FACTOR);
        const hash = await bcrypt.hashSync(
          updateUsersDto['password'],
          parseInt(salt),
        );
        getById.password = hash;
      }
      getById.updated_at = new Date();
      getById.__enc_username = false;
      getById.__enc_email = false;
      getById.__enc_firstname = false;
      getById.__enc_lastname = false;
      getById.__enc_mobile_number = false;
      const _id = { _id: getById._id };

      const username = updateUsersDto['username']
        ? updateUsersDto['username']
        : '';
      const email = updateUsersDto['email'] ? updateUsersDto['email'] : '';
      const messageToSearchWith: any = new Message({ username, email });
      messageToSearchWith.encryptFieldsSync();
      const _query = {
        $or: [
          { username: messageToSearchWith['username'] },
          { email: messageToSearchWith['email'] },
        ],
        _id: { $nin: [_id] },
      };
      const userNameExists = await this.usersModel.findOne(_query);
      if (userNameExists) {
        return new HttpException(
          {
            status: HttpStatus.FORBIDDEN,
            message: ['username or email already exists'],
          },
          HttpStatus.FORBIDDEN,
        );
      }

      return await this.usersModel.findOneAndUpdate(_id, getById, {
        new: true,
        upsert: true,
        rawResult: true, // Return the raw result from the MongoDB driver
      });
    } catch (e) {
      throw new NotFoundException('Could not update Users.', e);
    }
  }
  async bulkUpdate(query, data) {
    const updatedData = await this.usersModel.updateMany(query, data).exec();
    return updatedData;
  }

  async findOne(query: any): Promise<UsersInterface | undefined> {
    const _user = await this.usersModel.findOne(query);
    return _user;
  }

  async delete(userId: string) {
    const result = await this.usersModel.deleteOne({ _id: userId }).exec();
    if (result.n === 0) {
      throw new NotFoundException('Could not find User.');
    } else {
      return result;
    }
  }

  async myProfileUpdate(userId: any, data: any) {
    const getById: any = await this.usersModel.findById(userId).exec();
    try {
      if (data.password) {
        const salt = await bcrypt.genSaltSync(SALT_WORK_FACTOR);
        const hash = await bcrypt.hashSync(data.password, parseInt(salt));
        getById.password = hash;
      }
      this.userServicesbyid(getById, data);

      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update userProfile.');
    }
    return getById;
  }

  private userServicesbyid(getById: any, data: any) {
    getById['email'] = data.email ? data.email : getById['email'];
    const imageByid = getById['image'] ? getById['image'] : null;
    getById['image'] = data.image ? data.image : imageByid;
    const institutionByid = getById['institution']
      ? getById['institution']
      : null;
    getById['institution'] = data.institution
      ? data.institution
      : institutionByid;
    const userNmaebyid = getById['username'] ? getById['username'] : '';
    getById['username'] = data.username ? data.username : userNmaebyid;
    const firstNamebyid = getById['firstname'] ? getById['firstname'] : '';
    getById['firstname'] = data.firstname ? data.firstname : firstNamebyid;
    const lastNamebyid = getById['lastname'] ? getById['lastname'] : '';
    this.userServiceslatname(getById, data, lastNamebyid);
  }

  private userServiceslatname(getById: any, data: any, lastNamebyid: any) {
    getById['lastname'] = data.lastname ? data.lastname : lastNamebyid;
    const positionByid = getById['position'] ? getById['position'] : '';
    getById['position'] = data.position ? data.position : positionByid;
    const jobTitlebyid = getById['jobTitle'] ? getById['jobTitle'] : '';
    getById['jobTitle'] = data.jobTitle ? data.jobTitle : jobTitlebyid;
    const dataConsentbyid = getById['dataConsentPolicy']
      ? getById['dataConsentPolicy']
      : false;
    getById['dataConsentPolicy'] = data.dataConsentPolicy
      ? data.dataConsentPolicy
      : dataConsentbyid;
    const restrictedByid = getById['restrictedUsePolicy']
      ? getById['restrictedUsePolicy']
      : false;
    this.userServicespolicy(getById, data, restrictedByid);
  }

  private userServicespolicy(getById: any, data: any, restrictedByid: any) {
    getById['restrictedUsePolicy'] = data.restrictedUsePolicy
      ? data.restrictedUsePolicy
      : restrictedByid;
    const acceptCookiesbyid = getById['acceptCookiesPolicy']
      ? getById['acceptCookiesPolicy']
      : false;
    getById['acceptCookiesPolicy'] = data.acceptCookiesPolicy
      ? data.acceptCookiesPolicy
      : acceptCookiesbyid;
    const withdrawByid = getById['withdrawConsentPolicy']
      ? getById['withdrawConsentPolicy']
      : false;
    getById['withdrawConsentPolicy'] = data.withdrawConsentPolicy
      ? data.withdrawConsentPolicy
      : withdrawByid;
    const medicalByid = getById['medicalConsentPolicy']
      ? getById['medicalConsentPolicy']
      : false;
    getById['medicalConsentPolicy'] = data.medicalConsentPolicy
      ? data.medicalConsentPolicy
      : medicalByid;
    const fulldataByid = getById['fullDataProtectionConsentPolicy']
      ? getById['fullDataProtectionConsentPolicy']
      : false;
    getById['fullDataProtectionConsentPolicy'] =
      data.fullDataProtectionConsentPolicy
        ? data.fullDataProtectionConsentPolicy
        : fulldataByid;
  }

  async saveUser(decryptedString: any, data: any) {
    const query = {
      emailActivateToken: decryptedString,
    };
    const _userDetails = await this.usersModel.findOne(query);
    try {
      if (data.password) {
        const salt = await bcrypt.genSaltSync(SALT_WORK_FACTOR);
        const hash = await bcrypt.hashSync(data.password, parseInt(salt));
        _userDetails.password = hash;
      }
      const userNamedetails = _userDetails['username']
        ? _userDetails['username']
        : '';
      _userDetails['username'] = data.username
        ? data.username
        : userNamedetails;
      const firstNamedetails = _userDetails['firstname']
        ? _userDetails['firstname']
        : '';
      _userDetails['firstname'] = data.firstname
        ? data.firstname
        : firstNamedetails;
      const lastNamedetails = _userDetails['lastname']
        ? _userDetails['lastname']
        : '';
      _userDetails['lastname'] = data.lastname
        ? data.lastname
        : lastNamedetails;
      _userDetails['enabled'] = data.enabled ? data.enabled : false;
      this.userDailcode(_userDetails, data);
      this.userCookies(_userDetails, data);

      _userDetails.updated_at = new Date();
      _userDetails.emailActivateToken = undefined;
      _userDetails.save();

      const vspaceData = await this.vspaceModel.find({
        nonMembers: { $in: _userDetails.email },
      });
      if (vspaceData?.length) {
        const series = [];
        vspaceData.forEach((d) => {
          series.push((next) => {
            this.updateVspaceDb(d, _userDetails, next);
          });
        });
        async.series(series, (err) => {
          if (err) {
            return err;
          } else {
            return 0;
          }
        });
      }
      return _userDetails;
    } catch (e) {
      throw new NotFoundException('Could not save user`.');
    }
  }

  private userDailcode(_userDetails: any, data: any) {
    const emailDetails = _userDetails['email'] ? _userDetails['email'] : '';
    _userDetails['email'] = data.email ? data.email : emailDetails;
    const dailcodeDetails = _userDetails['dial_code']
      ? _userDetails['dial_code']
      : '';
    _userDetails['dial_code'] = data.dial_code
      ? data.dial_code
      : dailcodeDetails;
    const mobilenumberDetails = _userDetails['mobile_number']
      ? _userDetails['mobile_number']
      : '';
    _userDetails['mobile_number'] = data.mobile_number
      ? data.mobile_number
      : mobilenumberDetails;
    const dataconsentDetails = _userDetails['dataConsentPolicy']
      ? _userDetails['dataConsentPolicy']
      : false;
    _userDetails['dataConsentPolicy'] = data.dataConsentPolicy
      ? data.dataConsentPolicy
      : dataconsentDetails;
    const policyDetails = _userDetails['restrictedUsePolicy']
      ? _userDetails['restrictedUsePolicy']
      : false;
    _userDetails['restrictedUsePolicy'] = data.restrictedUsePolicy
      ? data.restrictedUsePolicy
      : policyDetails;
  }

  private userCookies(_userDetails: any, data: any) {
    const cookiesDetails = _userDetails['acceptCookiesPolicy']
      ? _userDetails['acceptCookiesPolicy']
      : false;
    _userDetails['acceptCookiesPolicy'] = data.acceptCookiesPolicy
      ? data.acceptCookiesPolicy
      : cookiesDetails;
    const withdrawDetails = _userDetails['withdrawConsentPolicy']
      ? _userDetails['withdrawConsentPolicy']
      : false;
    _userDetails['withdrawConsentPolicy'] = data.withdrawConsentPolicy
      ? data.withdrawConsentPolicy
      : withdrawDetails;
    const medicalDetails = _userDetails['medicalConsentPolicy']
      ? _userDetails['medicalConsentPolicy']
      : false;
    _userDetails['medicalConsentPolicy'] = data.medicalConsentPolicy
      ? data.medicalConsentPolicy
      : medicalDetails;
    const fulldataDetails = _userDetails['fullDataProtectionConsentPolicy']
      ? _userDetails['fullDataProtectionConsentPolicy']
      : false;
    _userDetails['fullDataProtectionConsentPolicy'] =
      data.fullDataProtectionConsentPolicy
        ? data.fullDataProtectionConsentPolicy
        : fulldataDetails;
  }

  async updateVspaceDb(data, _userDetails, next) {
    data.subscribers.push(_userDetails._id);
    data.members.push(_userDetails._id);
    data.nonMembers.splice(data.nonMembers.indexOf(_userDetails.email), 1);
    await this.vspaceModel.update(
      { _id: data._id },
      {
        members: data.members,
        nonMembers: data.nonMembers,
        subscribers: data.subscribers,
      },
      {},
    );
    next();
  }

  async linkValidate(userId) {
    const getById: any = await this.usersModel.findById(userId).exec();
    try {
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('invalid Link');
    }
    return getById;
  }

  async userEncrypt(data) {
    try {
      data.data.forEach(async (d) => {
        const messageToSearchWith: any = new Message({
          email: d.email,
          username: d.username,
          mobile_number: d.mobile_number,
        });
        await messageToSearchWith.encryptFieldsSync();
        await d.save();
      });
    } catch (e) {
      throw new NotFoundException('Error in running patch code');
    }
    return true;
  }

  async findusernameoremail(
    email: any,
    username: any,
  ): Promise<UsersInterface | undefined> {
    const _user = await this.usersModel.find({ $or: [email, username] });
    return _user;
  }
}
