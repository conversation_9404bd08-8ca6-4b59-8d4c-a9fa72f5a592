//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const LandingPageSchema = new mongoose.Schema({

  title: { type: String },
  description: { type: String },
  images: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true }],
  images_src: [{ type: String}],
  document: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Files', autopopulate: true }],
  doc_src: [{ type: String}],
  isEnabled: { type: Boolean, default: false },
  language: { type: String },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

LandingPageSchema.plugin(mongoosePaginate);
