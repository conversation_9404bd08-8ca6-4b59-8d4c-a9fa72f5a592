//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { InstitutionNetworks } from "../../data/institution-network";
import { InstitutionNetworkInterface } from "src/interfaces/institution-network.interface";

/**
 * Service dealing with Institution Network.
 *
 * @class
 */
@Injectable()
export class InstitutionNetworkSeederService {

  constructor(
    @InjectModel('InstitutionNetwork') private institutionNetworkModel: Model<InstitutionNetworkInterface>
  ) {}

  /**
   * Seed all Insitution Network.
   *
   * @function
   */
  create(): Array<Promise<InstitutionNetworkInterface>> {
    return InstitutionNetworks.map(async (insNetwork: any) => {
      return await this.institutionNetworkModel
        .findOne({ title: insNetwork.title })
        .exec()
        .then(async dbInsNetwork => {
          // We check if a Institution Network already exists.
          // If it does don't create a new one.
          if (dbInsNetwork) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.institutionNetworkModel.create(insNetwork),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}