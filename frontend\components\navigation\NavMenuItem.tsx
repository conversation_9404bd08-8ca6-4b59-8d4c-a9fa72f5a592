//Import Library
import Link from 'next/link';
import { useRouter } from "next/navigation";

//Import services/components
import { useTranslation } from 'next-i18next';

interface NavMenuItemProps {
  item: {
    title: string;
    route: string;
    icon: string;
  };
}

export default function NavMenuItem(props: NavMenuItemProps) {
  const { t } = useTranslation('common');
  const router = useRouter();
  const { item } = props;
  let active: string = "";
  const url = new RegExp(`/${item.route}`);

  // Get the current path from window.location instead of router.asPath
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';

  //TODO:Need to fix the bug here as on each refresh , we see a warning
  if (item.route.length!== 0) {
    active = (url.test(currentPath)) ? "active" : "";
  }

  if (item.route.length === 0 && currentPath === "/") {
    active ="active"
  }

  return (
    <li>
      <Link href={`/${item.route}`} className={active}>

        <div className="leftmenuIcon" style={{width:30, height: 30, backgroundImage: `url(${item.icon})`}}></div>
        <span>{t(item.title)}</span>

      </Link>
    </li>
  );
}
