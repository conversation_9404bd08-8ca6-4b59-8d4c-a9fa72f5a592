//Import Library
import React from "react";
import { <PERSON><PERSON>, <PERSON> } from "react-bootstrap";
import Link from 'next/link';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPen } from "@fortawesome/free-solid-svg-icons";
import moment from 'moment';

//Import services/components
import { useTranslation } from 'next-i18next';
import { canEditProject } from "../permission";
import Bookmark from "../../../components/common/Bookmark";


interface ProjectCoverSectionProps {
  projectData: {
    title: string;
    website: string;
    area_of_work: any[];
    status: any;
    funded_by: string;
    country: any;
    description: string;
    end_date: string;
    start_date: string;
    partner_institutions: any[];
    partner_institution: any;
    created_at: string;
    updated_at: string;
  };
  routeData: {
    routes: string[];
  };
  editAccess: boolean;
}

const ProjectCoverSection = (props: ProjectCoverSectionProps) => {
    const { t } = useTranslation('common');
    const formatDateStartDate = "DD-MM-YYYY";

    const EditProjectComponent = () => {
        return (
          <>
            {
            props.editAccess ?
              <Link
                href="/project/[...routes]"
                as={`/project/edit/${props.routeData.routes[1]}`}
                >
                <Button variant="secondary" size="sm">
                  <FontAwesomeIcon icon={faPen} />&nbsp;{t("Edit")}
                </Button>
              </Link> : ""
            }
          </>
        );
      }

    const CanEditProject = canEditProject(() => <EditProjectComponent />);
    return (
        <>
            <Row className="projectRow">
                <div className="projectBanner">
                    <div className="projectImg">
                        <img src="/images/project-banner.jpg" alt="Project Detail" />
                    </div>
                    {project_start_func(props.projectData, props.routeData, CanEditProject, t, formatDateStartDate)}
                </div>
            </Row>
        </>
    )
}

export default ProjectCoverSection;
function project_start_func(projectData: { title: string; website: string; area_of_work: any[]; status: any; funded_by: string; country: any; description: string; end_date: string; start_date: string; partner_institutions: any[]; partner_institution: any; created_at: string; updated_at: string; }, props: any, CanEditProject: any, t: (key: string) => string, formatDateStartDate: string) {
    return <div className="projectTitleBlock">
      <h4 className="projectTitle">
        {projectData.title}&nbsp;&nbsp;
        {props.routes && props.routes[1] ? <CanEditProject project={projectData} /> : null}
      </h4>
      <div className="projectDate">
        <div className="projectStart">
          <i className="fas fa-calendar-alt" />
          <div>
            <h6 style={{ color: "white" }}>{t("StartDate")}:</h6>
            <h5>{projectData.start_date ? (moment(projectData.start_date).format(formatDateStartDate)) : null}</h5>
          </div>
        </div>
        <div className="projectStatus me-2">
          <i className="fas fa-hourglass-half" />
          <div>
            <h6 style={{ color: "white" }}>{t("Status")}:</h6>
            <h5>{projectData.status && projectData.status["title"]}</h5>
          </div>
        </div>
        <Bookmark entityId={props.routes[1]} entityType="project" />
      </div>
    </div>;
  }