//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Req,
  UseGuards,
  HttpStatus,
  HttpCode
} from '@nestjs/common';
import { Request } from 'express';
import { ACGuard, InjectRolesBuilder, RolesBuilder, UseRoles } from "nest-access-control";

//Import services/components
import { CreateFlagDto } from "./dto/create-flag.dto";
import { FlagService } from "./flag.service";
import { SessionGuard } from 'src/auth/session-guard';
import { IResponse } from "../../common/interfaces/response.interface";
import { ResponseError } from "../../common/dto/response.dto";



@Controller('flag')
@UseGuards(SessionGuard)
export class FlagController {
  constructor(
    private readonly _flagService: FlagService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) { }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'flag',
    action: 'create',
    possession: 'any',
  })
  @Post()
  @HttpCode(HttpStatus.OK)
  async create(@Body() createFlagDto: CreateFlagDto, @Req() request: Request): Promise<IResponse> {
    try {
      const _flag: any = await this._flagService.create(createFlagDto);
      return _flag;
    } catch (error) {
      return new ResponseError("FLAG.CREATE_CONTENT_ERROR", error);
    }
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'flag',
    action: 'read',
    possession: 'own',
  })
  @Get()
  async findAll(@Query() query: any, @Req() request: Request) {
    const user: any = request.user;
    if (this.roleBuilder.can(user.roles).readAny('flag').granted) {
      return this._flagService.findAll(query);
    } else {
      query.user = user._id;
      return this._flagService.findAll(query);
    }
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'flag',
    action: 'read',
    possession: 'own',
  })
  @Get(':id')
  async findOne(@Param('id') flagId: string, @Req() request: Request) {
    const user: any = request.user;
    try {
      const _flag: any = await this._flagService.get(flagId);
      const flagUserId = _flag.user ? _flag.user._id : null;
      const permission = (user._id === flagUserId) ?
        this.roleBuilder.can(user.roles).readOwn('flag').granted :
        this.roleBuilder.can(user.roles).readAny('flag').granted;

      if (permission) {
        return _flag;
      } else {
        return new ResponseError("FLAG.ERROR.ACCESS_DENIED");
      }
    } catch (error) {
      return new ResponseError("FLAG.ERROR.FLAG_NOT_FOUND");
    }
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'flag',
    action: 'delete',
    possession: 'own',
  })
  @Delete(':id')
  async remove(@Param('id') flagId: string, @Req() request: Request) {
    const user: any = request.user;
    try {
      const _flag: any = await this._flagService.get(flagId);
      const flagUserId = _flag.user ? _flag.user._id : null;
      const permission = (user._id === flagUserId) ?
        this.roleBuilder.can(user.roles).readOwn('flag').granted :
        this.roleBuilder.can(user.roles).readAny('flag').granted;
      if (permission) {
        return await this._flagService.delete(flagId);
      } else {
        return new ResponseError("FLAG.ERROR.ACCESS_DENIED");
      }
    } catch (error) {
      return new ResponseError("FLAG.ERROR.FLAG_NOT_FOUND");
    }
  }

}
