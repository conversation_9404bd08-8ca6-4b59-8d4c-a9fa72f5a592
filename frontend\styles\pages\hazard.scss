.hazard-image-block {
  height: 325px;
  width: 100%;
  overflow: hidden;
  position: relative;
  .hazard-image-cover {
    width: 100%;
    height: auto;
    min-height: 325px;
  }
}

//RKI Cards related styles here
.infoCard {
  &.card {
    border-top: 3px solid #2ca8ff;
    box-shadow: 1px 1px 15px rgba(150, 150, 150, 0.2);
  }
  .card-header {
    background: transparent;
    border: none;
    font-size: 18px;
    color:rgba(0, 0, 0, 0.7);
    font-weight:500;
  }
  .card-body {
    padding: 5px 10px;
    p, a {
      text-decoration: none;
      margin: 0 0 5px;
      color: #22a3f7;
    }
  }
  .card-footer {
    background: transparent;
    padding: 5px;
    border: none;
    cursor: pointer;
    a {
      color: #202020;
    }
  }
}


.countryAccordion {
  padding-top: 25px;
  padding-bottom: 25px;

  .card {
    border: none;
    margin: 25px 0;
  }

  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;

    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }

    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }

    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;

      svg {
        font-size: 16px;
      }
    }
  }
}

p.card-text.hazardCurrentEvent {
  overflow: auto;
  height: 89px;
}

span.text-center {
  text-align: center;
  display: block;
}

// Responsive
@media screen and (max-width: 767px) {
  .main-container {
    .hazard-image-block .hazard-image-cover {
      width: auto;
    }
    .hazards-multi-checkboxes {
      padding-left: 10px;
      padding-top: 10px;
      .form-check {
        max-width: 50%;
        flex-basis: 50%;
      }
    }
  }
}

//For current and past event,organisation and current operations
.hazardDetails{
  .rki-carousel-card .infoCard
   {
    .card-header {
    font-weight: bold;
        }
      }
    }

.hazardBody{
  overflow:auto;
  max-height: 100px;

}


.ulItems{
   padding-left: 16px;
   margin: 0px;
   padding-top: 0px;
    .liItems{
      list-style-type: square;
      border-bottom: 1px dashed rgb(241, 238, 238);
      padding-bottom: 1px;
    }
}


.search-icon {
  position: absolute;
  right: 15px;
  top: 7px;
  z-index: 9999;
}

.hazards-pagination {
  // float: right;
  margin: 20px 0;
  ul {
    margin-bottom: 0;
    .page-item {
      .page-link {
        border: none;
        color: #5d5d5d;
        padding: 6px 0.75rem;
      }
      &.active {
        .page-link {
          background-color: #5d5d5d;
          color: #FFFFFF;
        }
      }
    }
  }
}

