//Import Library
import React, { useMemo, useEffect, useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faExclamationCircle,
  faCloudUploadAlt,
} from "@fortawesome/free-solid-svg-icons";
import { Form, Button, Modal, Col } from "react-bootstrap";
import _ from "lodash";

import toast from 'react-hot-toast';

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

// Define type for temp array items
interface TempItem {
  serverID: string;
  file?: any;
  index: number;
  type: string;
}

let temp: TempItem[] = [];

const baseStyle: any = {
  flex: 1,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  width: "100%",
  height: "100%",
  borderWidth: 0.1,
  borderColor: "#fafafa",
  backgroundColor: "#fafafa",
  color: "black",
  transition: "border  .24s ease-in-out",
  padding: "15px",
};

const thumb: any = {
  display: "flex",
  borderRadius: 2,
  border: "1px solid #ddd",
  margin: 8,
  height: 175,
  boxShadow: "0 0 15px 0.25px rgba(0,0,0,0.15)",
  boxSizing: "border-box",
};

const thumbsContainer: any = {
  display: "flex",
  padding: "10px",
  width: "100%",
  border: "2px solid gray",
  flexDirection: "column",
  justifyContent: "flex-start",
  flexWrap: "wrap",
  marginTop: 20,
};

const thumbInner: any = {
  display: "flex",
  minWidth: 0,
};

const deleteIcon: any = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  marginLeft: 30,
};

const img = {
  width: "150px",
};

const activeStyle: any = {
  borderColor: "#2196f3",
};

const ReactDropZone = (props: any) => {
  const { t } = useTranslation('common');
  const [modalShow, setModalShow] = useState(false);
  const [deleteFile, setDeleteFile] = useState();
  const limit: any =
    props.type == "application" ? 20971520 : process.env.UPLOAD_LIMIT;
  const [files, setFiles] = useState<any[]>([]);
  const [multi, setMulti] = useState(true);
  const [imageSource, setImageSource] = useState<string[]>([]);

  const endpoint = props && props.type === "application" ? "/files" : "/image";
  const imageDelete = async (id: string) => {
    const _res = await apiService.remove(`${endpoint}/${id}`);
  };

  const removeFile = (file: any) => {
    setDeleteFile(file);
    setModalShow(true);
  };

  const handleSource = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {
    const items = [...imageSource];
    items[index] = e.target.value;
    setImageSource(items);
  };

  const getComponent = (file: any) => {
    const fileType = file && file.name.split(".").pop();
    switch (fileType) {
      case "JPG":
      case "jpg":
      case "jpeg":
      case "jpg":
      case "png":
        return <img src={file.preview} style={img} />;
      case "pdf":
        return (
          <img
            src="/images/fileIcons/pdfFile.png"
            className={
              props.type === "application" ? "docPreview" : "imgPreview"
            }
          />
        );
      case "docx":
        return (
          <img
            src="/images/fileIcons/wordFile.png"
            className={
              props.type === "application" ? "docPreview" : "imgPreview"
            }
          />
        );
      case "xls":
      case "xlsx":
        return (
          <img
            src="/images/fileIcons/xlsFile.png"
            className={
              props.type === "application" ? "docPreview" : "imgPreview"
            }
          />
        );
      default:
        return (
          <img
            src="/images/fileIcons/wordFile.png"
            className={
              props.type === "application" ? "docPreview" : "imgPreview"
            }
          />
        );
    }
  };

  const modalHide = () => setModalShow(false);

  const cancelHandler = () => {
    setModalShow(false);
  };

  const submitHandler = (fileselector: any) => {
    fileselector = deleteFile;
    const obj =
      fileselector && fileselector._id
        ? { serverID: fileselector._id }
        : { file: fileselector };
    const _index = _.findIndex(temp, obj);
    //**Delete the source Field**//
    const removeSrc = [...imageSource];
    removeSrc.splice(_index, 1);
    setImageSource(removeSrc);
    //**End**/
    imageDelete(temp[_index].serverID);
    temp.splice(_index, 1);
    props.getImgID(temp, props.index ? props.index : 0);
    const newFiles = [...files];
    newFiles.splice(newFiles.indexOf(fileselector), 1);
    setFiles(newFiles);
    setModalShow(false);
  };

  const thumbs: any = files.map((file: any, i) => {
    return (
      <div key={i}>
        <Col xs={12}>
          <div className="row">
            <Col
              md={4}
              lg={3}
              className={
                props.type === "application text-center align-self-center"
                  ? "docImagePreview text-center align-self-center"
                  : "imgPreview text-center align-self-center"
              }
            >
              {getComponent(file)}
            </Col>
            <Col md={5} lg={7} className="align-self-center">
              <Form>
                <Form.Group controlId="filename">
                  <Form.Label className="mt-2">{t("FileName")}</Form.Label>
                  <Form.Control
                    size="sm"
                    type="text"
                    disabled
                    value={file.original_name ? file.original_name : file.name}
                  />
                </Form.Group>
                <Form.Group controlId="description">
                  <Form.Label>
                    {props.type === "application"
                      ? t("ShortDescription/(Max255Characters)")
                      : t("Source/Description")}
                  </Form.Label>
                  <Form.Control
                    maxLength={props.type === "application" ? 255 : undefined}
                    size="sm"
                    type="text"
                    placeholder={
                      props.type === "application"
                        ? t("`Enteryourdocumentdescription`")
                        : t("`Enteryourimagesource/description`")
                    }
                    value={imageSource[i]}
                    onChange={(e) => handleSource(e, i)}
                  />
                </Form.Group>
              </Form>
            </Col>
            <Col
              md={3}
              lg={2}
              className="align-self-center text-center"
              onClick={() => removeFile(file)}
            >
              <Button variant="dark">{t("Remove")}</Button>
            </Col>
          </div>
        </Col>
        <Modal show={modalShow} onHide={modalHide}>
          <Modal.Header closeButton>
            <Modal.Title>{t("DeleteFile")}</Modal.Title>
          </Modal.Header>
          <Modal.Body>{t("Areyousurewanttodeletethisfile?")}</Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={cancelHandler}>
              {t("Cancel")}
            </Button>
            <Button variant="primary" onClick={() => submitHandler(file)}>
              {t("yes")}
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    );
  });

  useEffect(() => {
    files.forEach((file) => URL.revokeObjectURL(file.preview));
    temp = [];
  }, []);

  useEffect(() => {
    props.getImageSource(imageSource);
  }, [imageSource]);

  useEffect(() => {
    setImageSource(props.srcText);
  }, [props.srcText]);

  useEffect(() => {
    props && props.singleUpload === "true" && setMulti(false);
    if (props && props.datas) {
      const newObj = props.datas.map((item: any, _i: number) => {
        temp.push({
          serverID: item._id,
          index: props.index ? props.index : 0,
          type: item.name.split(".")[1],
        });
        const previewState = {
          ...item,
          preview: `${process.env.API_SERVER}/image/show/${item._id}`,
        };
        return previewState;
      });
      setFiles([...newObj]);
    }
  }, [props.datas]);

  const filesUpload = async (filesinitial: any[], index: number) => {
    if (filesinitial.length > index) {
      try {
        const form: any = new FormData();
        form.append("file", filesinitial[index]);
        const res = await apiService.post(endpoint, form, {
          "Content-Type": "multipart/form-data",
        });
        temp.push({
          serverID: res._id,
          file: filesinitial[index],
          index: props.index ? props.index : 0,
          type: filesinitial[index].name.split(".")[1],
        });
        filesUpload(filesinitial, index + 1);
      } catch (error) {
        filesUpload(filesinitial, index + 1);
      }
    } else {
      props.getImgID(temp, props.index ? props.index : 0);
    }
  };

  const onDrop = useCallback(async (ondrop_files: any[]) => {
    await filesUpload(ondrop_files, 0);
    const accFiles = ondrop_files.map((file: any) =>
      Object.assign(file, {
        preview: URL.createObjectURL(file),
      })
    );
    multi
      ? setFiles((prevState) => [...prevState, ...accFiles])
      : setFiles([...accFiles]);
  }, []);

  function nameLengthValidator(file: File) {
    if (endpoint === "/image") {
      if (file.type.substring(0, 5) === "image") {
        return null;
      } else {
        toast.error(t("toast.filetypenotsupport"));
        return { code: "file-invalid-type", message: "File type not supported" };
      }
    } else if (endpoint === "/files") {
      if (!(file.type.substring(0, 5) !== "image")) {
        toast.error(t("toast.filetypenotsupport"));
        return { code: "file-invalid-type", message: "File type not supported" };
      }
    }
    return null;
  }
  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
    fileRejections,
  } = useDropzone({
    accept:
      props && props.type
        ? "application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.oasis.opendocument.text,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv"
        : "image/*",
    multiple: multi,
    minSize: 0,
    maxSize: limit,
    onDrop,
    validator: nameLengthValidator,
  });

  const style = useMemo(
    () => ({
      ...baseStyle,
      ...(isDragActive ? activeStyle : { outline: "2px dashed #bbb" }),
      ...(isDragAccept
        ? { outline: "2px dashed #595959" }
        : { outline: "2px dashed #bbb" }),
      ...(isDragReject ? { outline: "2px dashed red" } : { activeStyle }),
    }),
    [isDragActive, isDragReject]
  );

  let dropZoneMsg;
  if (props && props.type === "application") {
    dropZoneMsg = (
      <small style={{ color: "#595959" }}>{t("DocumentWeSupport")}</small>
    );
  } else {
    dropZoneMsg = (
      <small style={{ color: "#595959" }}>{t("ImageWeSupport")}</small>
    );
  }

  const isFileTooLarge =
    fileRejections.length > 0 && fileRejections[0].file.size > limit;
  return (
    <>
      <div
        className=" d-flex justify-content-center align-items-center mt-3"
        style={{ width: "100%", height: "180px" }}
      >
        <div {...getRootProps({ style })}>
          <input {...getInputProps()} />
          <FontAwesomeIcon icon={faCloudUploadAlt} size="4x" color="#999" />
          <p style={{ color: "#595959", marginBottom: "0px" }}>
            {t("Drag'n'dropsomefileshere,orclicktoselectfiles")}
          </p>

          {!multi && (
            <small style={{ color: "#595959" }}>
              <b>Note:</b> One single image will be accepted
            </small>
          )}
          {dropZoneMsg}
          {props.type === "application"
            ? isFileTooLarge && (
                <small className="text-danger mt-2">
                  <FontAwesomeIcon
                    icon={faExclamationCircle}
                    size="1x"
                    color="red"
                  />{" "}
                  {t("FileistoolargeItshouldbelessthan20MB")}
                </small>
              )
            : isFileTooLarge && (
                <small className="text-danger mt-2">
                  <FontAwesomeIcon
                    icon={faExclamationCircle}
                    size="1x"
                    color="red"
                  />{" "}
                  {t("FileistoolargeItshouldbelessthan20MB")}
                </small>
              )}
          {isDragReject && (
            <small className="text-danger" style={{ color: "#595959" }}>
              <FontAwesomeIcon
                icon={faExclamationCircle}
                size="1x"
                color="red"
              />{" "}
              {t("Filetypenotacceptedsorr")}
            </small>
          )}
        </div>
      </div>
      {files.length > 0 && <div style={thumbsContainer}>{thumbs}</div>}
    </>
  );
};

export default ReactDropZone;
