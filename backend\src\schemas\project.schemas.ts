//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const ProjectSchema = new mongoose.Schema({
  title: { type: String, required: true },
  website: { type: String },
  description: { type: String },
  funded_by: { type: String },
  status: { type: mongoose.Schema.Types.ObjectId, ref: 'ProjectStatus', autopopulate: true },
  start_date: { type: Date, required: true },
  end_date: { type: Date },
  country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
  region: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true }],
  area_of_work: [{ type: mongoose.Schema.Types.ObjectId, ref: 'AreaOfWork', autopopulate: true }],
  partner_institutions: [{
    partner_country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
    partner_region: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true }],
    partner_institution: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Institution', autopopulate: true }],
    world_region: {type: mongoose.Schema.Types.ObjectId, ref: 'WorldRegion'}
  }],
  institution_invites: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Institution', autopopulate: true }],
  vspace: { type: mongoose.Schema.Types.ObjectId, ref: 'Vspace', autopopulate: true },
  vspace_visibility: { type: Boolean },
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true }, // project created user
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

ProjectSchema.plugin(mongoosePaginate);
