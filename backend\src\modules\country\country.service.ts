//Import Library
import * as mongoose from 'mongoose';
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { CountryInterface } from '../../interfaces/country.interface';
import { ProjectInterface } from '../../interfaces/project.interface';
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';
import { InstitutionInterface } from 'src/interfaces/institution.interface';
import { OperationInterface } from 'src/interfaces/operation.interface';
import { UpdateInterface } from 'src/interfaces/update.interface';
const FindCountry = 'Could not find Country.';
const ArrayIndex = '$arrayIndex';
const FindDocuments = 'Could not find Documents.';
@Injectable()
export class CountryService {
  constructor(
    @InjectModel('Country') private countryModel: Model<CountryInterface>,
    @InjectModel('Institution')
    private institutionModel: Model<InstitutionInterface>,
    @InjectModel('Project') private projectModel: Model<ProjectInterface>,
    @InjectModel('Operation') private operationModel: Model<OperationInterface>,
    @InjectModel('Update') private updateModel: Model<UpdateInterface>,
  ) {}

  async create(createCountryDto: CreateCountryDto): Promise<CountryInterface> {
    const createdCountry = new this.countryModel(createCountryDto);
    return createdCountry.save();
  }

  async findAll(query): Promise<CountryInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = this.setOptions(query, myCustomLabels);
    let _filter = query.query ? query.query : {};
    if (_filter.title) {
      _filter.title = _filter.title
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(`^${_filter.title}`, 'gi');
      _filter['title'] = regex;
    }

    if (query.languageCode && _filter.first_letter) {
      switch (query.languageCode) {
        case 'de':
          _filter = {
            'first_letter.de': _filter.first_letter,
            world_region: _filter.world_region,
          };
          break;
        case 'fr':
          _filter = {
            'first_letter.fr': _filter.first_letter,
            world_region: _filter.world_region,
          };
          break;
        case 'en':
          _filter = {
            'first_letter.en': _filter.first_letter,
            world_region: _filter.world_region,
          };
          break;
      }
    }

    let _data = await this.countryModel.paginate(_filter, options);

    if (query.languageCode) {
      _data.data.forEach((element) => {
        const foundIndex = element.label.findIndex(
          (x) => x.languageCode === query.languageCode,
        );
        if (foundIndex > -1) {
          element.title = element.label[foundIndex].title;
          element.first_letter = element.label[foundIndex].first_letter;
          element.health_profile = element.label[foundIndex].health_profile;
        }
      });
    }
    return _data;
  }

  setOptions(query: any, myCustomLabels: any) {
    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      // sort:{"title_de": "asc"},
      collation: { locale: query.languageCode },

      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }
    return options;
  }

  async get(countryId): Promise<CountryInterface[]> {
    let country;
    try {
      country = await this.countryModel.findById(countryId).exec();
    } catch (error) {
      throw new NotFoundException(FindCountry);
    }
    if (!country) {
      throw new NotFoundException(FindCountry);
    }
    return country;
  }

  async getSortedInstiList(countryId, query): Promise<CountryInterface[]> {
    let _result = [];
    try {
      const options = {
        sort: query.sort ? query.sort : {},
        collation: query.collation ? query.collation : '',
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
      };
      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }
      if (
        options.sort &&
        (options.sort.title ||
          options.sort.contact_name ||
          options.sort.created_at)
      ) {
        const list = this.getListForSorting(options, countryId);
        const totalCount = list[0].totalCount[0].count;
        let institutionList: any = {};
        const limit = options.limit;
        institutionList = this.setInstitutionList(
          institutionList,
          list,
          totalCount,
          limit,
          options,
        );
        _result = institutionList;
      }
    } catch (error) {
      throw new NotFoundException('Could not find Institutions.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Institutions.');
    }
    return _result;
  }

  getListForSorting(options: any, countryId: any) {
    const collation_key = options.collation ? options.collation : 'en';
    let sortOrder = '';
    if (options.sort.title) {
      sortOrder = options.sort.title;
    } else {
      sortOrder = options.sort.created_at
        ? options.sort.created_at
        : options.sort.contact_name;
    }
    const sortNumber = sortOrder === 'asc' ? 1 : -1;
    const sortArray = this.sortListData(options, sortNumber);
    const searchID = new mongoose.Types.ObjectId(countryId);
    const skip = (options.page - 1) * options.limit;
    const limit = options.limit;
    const list = this.createListforSorting(
      searchID,
      sortArray,
      limit,
      skip,
      collation_key,
    );
    return list;
  }

  sortListData(options: any, sortNumber: any) {
    let sortArray = null;
    if (options.sort.title) {
      sortArray = { title: sortNumber };
    } else {
      sortArray = options.sort.created_at
        ? { created_at: sortNumber }
        : { contact_name: sortNumber };
    }
    return sortArray;
  }

  setInstitutionList(
    institutionList: any,
    list: any,
    totalCount: any,
    limit: any,
    options: any,
  ) {
    institutionList.data = list[0].totalData;
    institutionList.totalCount = totalCount;
    institutionList.limit = limit;
    institutionList.totalPages = Math.ceil(totalCount / limit);
    institutionList.page = options.page;
    institutionList.pagingCounter = options.page;
    institutionList.hasNextPage = options.page !== institutionList.totalPages;
    institutionList.hasPrevPage = !(
      options.page === 1 && options.page === institutionList.totalPages
    );
    institutionList.prevPage =
      options.page === 1 && options.page === institutionList.totalPages
        ? null
        : options.page - 1;
    institutionList.nextPage =
      options.page === institutionList.totalPages ? null : options.page + 1;
    return institutionList;
  }

  async createListforSorting(
    searchID: any,
    sortArray: any,
    limit: any,
    skip: any,
    collation_key: any,
  ) {
    const list = await this.institutionModel
      .aggregate([
        { $match: { 'address.country': searchID } },
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'docArray',
          },
        },
        {
          $lookup: {
            from: 'expertise',
            localField: 'expertise',
            foreignField: '_id',
            as: 'expertiseArray',
          },
        },
        {
          $lookup: {
            from: 'regions',
            localField: 'address.region',
            foreignField: '_id',
            as: 'regionArray',
          },
        },
        { $unwind: '$docArray' },
        { $set: { user: '$docArray' } },
        { $set: { expertise: '$expertiseArray' } },
        { $set: { 'address.region': '$regionArray' } },
        { $sort: sortArray },
        {
          $project: {
            title: 1,
            contact_name: 1,
            user: 1,
            expertise: 1,
            'address.region': 1,
          },
        },
        {
          $facet: {
            totalData: [
              { $match: {} },
              { $limit: limit + skip },
              { $skip: skip },
            ],
            totalCount: [
              {
                $group: {
                  _id: null,
                  count: { $sum: 1 },
                },
              },
            ],
          },
        },
      ])
      .collation({ locale: collation_key, strength: 1 })
      .exec();
    return list;
  }

  async getSortedUPdateDocList(countryId, query): Promise<CountryInterface[]> {
    let _result = [];

    try {
      const options = {
        sort: query.sort ? query.sort : {},
        collation: query.collation ? query.collation : 'en',
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
      };
      console.log('query', query);

      console.log('options', options);
      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }

      if (
        options.sort &&
        (options.sort.document_title || options.sort.doc_created_at)
      ) {
        const { sortArray, collation_key } =
          this.prepareUpdateModelListPara(options);
        const searchID = new mongoose.Types.ObjectId(countryId);
        const skip = (options.page - 1) * options.limit;
        const limit = options.limit;
        const list = await this.updateModel
          .aggregate([
            { $match: { parent_country: searchID } },
            {
              $unwind: {
                path: '$document',
                preserveNullAndEmptyArrays: true,
                includeArrayIndex: 'arrayIndex',
              },
            },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            { $set: { 'docArray.index': ArrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', ArrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
              },
            },
            {
              $facet: {
                totalData: [
                  { $match: {} },
                  { $limit: limit + skip },
                  { $skip: skip },
                ],
                totalCount: [
                  {
                    $group: {
                      _id: null,
                      count: { $sum: 1 },
                    },
                  },
                ],
              },
            },
          ])
          .collation({ locale: collation_key, strength: 1 })
          .exec();
        const totalCount = list[0].totalCount[0].count;
        let DocList: any = {};
        DocList = this.setDocListDetails(
          DocList,
          list,
          totalCount,
          limit,
          options,
        );
        _result = DocList;
      }
    } catch (error) {
      throw new NotFoundException(FindDocuments);
    }
    if (!_result) {
      throw new NotFoundException(FindDocuments);
    }
    return _result;
  }

  prepareUpdateModelListPara(options: any) {
    const collation_key = options.collation ? options.collation : 'en';
    const sortOrder = options.sort.document_title
      ? options.sort.document_title
      : options.sort.doc_created_at;
    const sortNumber = sortOrder === 'asc' ? 1 : -1;
    const sortArray = options.sort.document_title
      ? { 'docArray.original_name': sortNumber }
      : { 'docArray.created_at': sortNumber };
    return { sortArray, collation_key };
  }

  setDocListDetails(
    DocList: any,
    list: any,
    totalCount: any,
    limit: any,
    options: any,
  ) {
    DocList.data = list[0].totalData;
    DocList.totalCount = totalCount;
    DocList.limit = limit;
    DocList.totalPages = Math.ceil(totalCount / limit);
    DocList.page = options.page;
    DocList.pagingCounter = options.page;
    DocList.hasNextPage = options.page != DocList.totalPages;
    DocList.hasPrevPage = !(
      options.page === 1 && options.page == DocList.totalPages
    );
    DocList.prevPage =
      options.page === 1 && options.page == DocList.totalPages
        ? null
        : options.page - 1;
    DocList.nextPage =
      options.page === DocList.totalPages ? null : options.page + 1;
    return options;
  }

  async getSortedDocList(countryId, query): Promise<CountryInterface[]> {
    let _result = [];
    try {
      const options = {
        sort: query.sort ? query.sort : {},
        collation: query.collation ? query.collation : 'en',
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
      };
      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }

      if (
        options.sort &&
        (options.sort.document_title || options.sort.doc_created_at)
      ) {
        const collation_key = options.collation ? options.collation : 'en';
        const sortOrder = options.sort.document_title
          ? options.sort.document_title
          : options.sort.doc_created_at;

        const { searchID, sortArray, skip, limit } = this.GetSortingData(
          sortOrder,
          options,
          countryId,
        );

        const list = await this.operationModel
          .aggregate([
            { $match: { country: searchID } },
            {
              $unwind: {
                path: '$document',
                preserveNullAndEmptyArrays: true,
                includeArrayIndex: 'arrayIndex',
              },
            },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            { $set: { 'docArray.index': ArrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', ArrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
              },
            },
            {
              $facet: {
                totalData: [
                  { $match: {} },
                  { $limit: limit + skip },
                  { $skip: skip },
                ],
                totalCount: [
                  {
                    $group: {
                      _id: null,
                      count: { $sum: 1 },
                    },
                  },
                ],
              },
            },
          ])
          .collation({ locale: collation_key, strength: 1 })
          .exec();
        const totalCount = list[0].totalCount[0].count;
        let DocList: any = {};
        DocList = this.setSortListDocLIst(
          DocList,
          list,
          totalCount,
          limit,
          options,
        );
        _result = DocList;
      }
    } catch (error) {
      throw new NotFoundException(FindDocuments);
    }
    if (!_result) {
      throw new NotFoundException(FindDocuments);
    }
    return _result;
  }

  GetSortingData(sortOrder: any, options: any, countryId: any) {
    const sortNumber = sortOrder === 'asc' ? 1 : -1;
    const sortArray = options.sort.document_title
      ? { 'docArray.original_name': sortNumber }
      : { 'docArray.created_at': sortNumber };
    const searchID = new mongoose.Types.ObjectId(countryId);
    const skip = (options.page - 1) * options.limit;
    const limit = options.limit;
    return { sortNumber, searchID, sortArray, skip, limit };
  }

  setSortListDocLIst(
    DocList: any,
    list: any,
    totalCount: any,
    limit: any,
    options: any,
  ) {
    DocList.data = list[0].totalData;
    DocList.totalCount = totalCount;
    DocList.limit = limit;
    DocList.totalPages = Math.ceil(totalCount / limit);
    DocList.page = options.page;
    DocList.pagingCounter = options.page;
    DocList.hasNextPage = options.page !== DocList.totalPages;
    DocList.hasPrevPage = !(
      options.page === 1 && options.page == DocList.totalPages
    );
    DocList.prevPage =
      options.page === 1 && options.page == DocList.totalPages
        ? null
        : options.page - 1;
    DocList.nextPage =
      options.page === DocList.totalPages ? null : options.page + 1;
    return DocList;
  }

  async update(countryId: any, updateCountryDto: UpdateCountryDto) {
    const getCountryById: any = await this.countryModel
      .findById(countryId)
      .exec();
    const updatedCountry = new this.countryModel(updateCountryDto);
    Object.keys(updateCountryDto).forEach((d) => {
      getCountryById[d] = updatedCountry[d];
    });
    getCountryById.updated_at = new Date();
    return getCountryById.save();
  }

  async delete(countryId: string) {
    const result = await this.countryModel.deleteOne({ _id: countryId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindCountry);
    }
  }

  async getInstitution(countryId: string, query: any) {    
    let institutions;
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      populate: query.populate ? query.populate : '',
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
      collation: { locale: 'en' },
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    try {
      institutions = await this.institutionModel.paginate(
        { 'address.country': countryId },
        options,
      );
    } catch (error) {
      throw new NotFoundException(FindCountry, error);
    }
    if (!institutions) {
      throw new NotFoundException(FindCountry);
    }
    return institutions;
  }

  async getInstitutionAndProject(countryId: string) {
    let institutions;
    try {
      institutions = await this.institutionModel.paginate({
        'address.country': countryId,
      });
      const _result = await this.projectModel
        .find({ 'partner_institutions.partner_country': { $in: countryId } })
        .exec();
      await _result.forEach((element) => {
        institutions.docs.push(element);
      });
    } catch (error) {
      throw new NotFoundException('Could not find the data.', error);
    }
    if (!institutions) {
      throw new NotFoundException('Could not find the data.');
    }
    return institutions;
  }
}
