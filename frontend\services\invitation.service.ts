//Import services/components
import apiService from "./apiService";

class InvitationService {
    arrayDifference(array1: any[], array2: any[]) {
        // returns array
        // element present in array one but not in array 2
        const difference: any[] = [];
        array1.forEach((ar1El: any) => {
            if (array2.filter((ar2El: any) => ar2El._id === ar1El.value).length === 0) {
                difference.push(ar1El.value);
            }
        });
        return difference;
    }

    async deleteInvitations(userIds: any[], instituteId: any) {
        userIds.forEach(async (userId: any) => {
            let userData = await apiService.get(`/users/${userId}`);
            if (userData.institutionInvites.length) {
                userData.institutionInvites = userData.institutionInvites.filter(
                    (invite: any) => invite.institutionId !== instituteId
                );
                await apiService.patch(`/users/${userId}`, userData);
            }
        });
    }

    private isDuplicateInvite(userInvites: any[], institutionId: any) {
        let dups = userInvites.filter((ui: any) => {
            return ui.institutionId === institutionId;
        });
        return dups.length ? true : false;
    }

    private getNewInviteMeta(institutionId: any, institutionTitle: any) {
        return {
            institutionName: institutionTitle,
            institutionId: institutionId,
            status: "Request Pending",
        };
    }

    private async getUserData(userId: any, userEmail?: any, userName?: any) {
        let userData: any = {};
        if (userEmail && userName) {
            userData = await apiService.get(`/users`, {
                query: {
                    email: userEmail,
                    username: userName,
                },
            });
        } else {
            userData = await apiService.get(`/users/${userId}`);
        }
        return userData;
    }

    async sendInvitations(users: any[], institutionId: any, institutionTitle: any) {
        users.forEach(async (user: any) => {
            const userId = user._id;
            if (userId) {
                let userData = await this.getUserData(userId);
                if (userData) {
                    if (!userData.institutionInvites) userData.institutionInvites = [];
                    userData.institutionInvites = userData.institutionInvites.map((invite: any) => {
                        if (invite.institutionId === institutionId && invite.status === "Rejected") {
                            invite.status = "Request Pending";
                        }
                        return invite;
                    });
                    if (!this.isDuplicateInvite(userData.institutionInvites || [], institutionId)) {
                        userData.institutionInvites.push(this.getNewInviteMeta(institutionId, institutionTitle));
                    }
                    await apiService.patch(`/users/${userId}`, userData);
                }
            } else {
                //means new user
                this.inviteNewUserWithEmail(user.email, user.username, institutionId, institutionTitle);
            }
        });
    }

    public async inviteNewUserWithEmail(email: any, username: any, institutionId: any, institutionTitle: any) {
        const query = { email, username };
        let userData = await apiService.get(`/users`, { query });
        if (userData && userData.data[0]) {
            userData = userData.data[0];
            userData.institutionInvites.push(this.getNewInviteMeta(institutionId, institutionTitle));
            const res = await apiService.patch(`/users/${userData._id}`, userData);
        }
    }
}

export default new InvitationService();
