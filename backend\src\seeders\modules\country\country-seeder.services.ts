//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { CountryInterface } from "src/interfaces/country.interface";
import { countries } from "../../data/country";
import { WorldRegionInterface } from "src/interfaces/world-region.interface";
import { worldRegions } from "../../data/world-region";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class CountrySeederService {
  regions = [];
  constructor(
    @InjectModel('Country') private countryModel: Model<CountryInterface>,
    @InjectModel('WorldRegion') private worldRegionModel: Model<WorldRegionInterface>

  ) {
    this.worldRegionModel.find().exec().then(async region => {
      this.regions = region;
    });
  }

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<CountryInterface>> {
    return countries.map(async (country: any) => {
      const worldRegion = await this.worldRegionModel.findOne({ title: worldRegions[country.world_region - 1].title }).exec();
      country.world_region = worldRegion ? worldRegion._id : null;
      return await this.countryModel
        .findOne({ title: country.title })
        .exec()
        .then(async dbCountry => {
          // We check if a country already exists.
          // If it does don't create a new one.
          if (dbCountry) {
            const getCountryById: any = await this.countryModel.findById(dbCountry._id).exec();
            const updatedCountry = new this.countryModel(country);
            Object.keys(country).forEach((d) => {
                getCountryById[d] = updatedCountry[d];
            });
            getCountryById.updatedAt = new Date();
            getCountryById.save();
            return Promise.resolve(getCountryById);
          }

          return Promise.resolve(
            await this.countryModel.create(country),
          );
        })
        .catch(error => Promise.reject(error));
    });
    // })

  }
}