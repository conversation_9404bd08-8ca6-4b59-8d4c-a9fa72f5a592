//Import Library
import React, { useState, useEffect } from "react";
import { Form, Row, Col } from "react-bootstrap";
import {MultiSelect} from "react-multi-select-component";
import Select from "react-select";
import makeAnimated from "react-select/animated";
import CreatableSelect from 'react-select/creatable';

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const GroupVisibility = (props: any) => {
  const animatedComponents = makeAnimated();
  const [countryList, setcountryList] = useState([]);
  const [inputValue, setInputValue] = useState("");
  const [value, setValue] = useState<any>([]);
  const [organisationTypeList, setOrganisationTypeList] = useState([]);
  const [validEmail, setValidEmail] = useState(false);
  const [alreadyMember, setAlreadyMember] = useState(false);
  const [lastAddedEmail, setLastAddedEmail] = useState("");
  const { t,i18n } = useTranslation('common');
  const titleSearch = i18n.language === 'de'? {title_de: "asc"} : {title: "asc"};
  const currentLang = i18n.language;

  useEffect(() => {
    // Create an scoped async function in the hook
    const getProjectInitialData = async (projectParams_value: any) => {
      const country = await apiService.get("/country", projectParams_value);
      if (country && Array.isArray(country.data)) {
        const _country = country.data.map((item: any, _i: number) => {
          return { label: item.title, value: item._id };
        });
        setcountryList(_country);
      }
      const institutionType = await apiService.get(
        "/institutiontype",
        projectParams_value
      );
      if (institutionType && Array.isArray(institutionType.data)) {
        const _institutionType = institutionType.data.map((item: any, _i: number) => {
          return { label: item.title, value: item._id };
        });
        setOrganisationTypeList(_institutionType);
      }
    };

    // Get the data from project API
    const projectParams = {
      query: {},
      sort: titleSearch,
      limit: "~",
      languageCode:currentLang
    };
    getProjectInitialData(projectParams);
  }, []);


  //Send email to the form
  useEffect(() => {
    value && props.nonMember && props.nonMember(value);
  }, [value])

  /***user Select handler ***/
  const userHandler = (selected: any) => {
    const allOptionValue = props.allOption?.value || "*";
    if (selected && selected.length > 0 && selected[selected.length - 1]?.value === allOptionValue) {
      return props.onChange(
        props.multiUserOptions || [],
        "userList"
      );
    } else {
      return props.onChange(selected || [], "userList");
    }
  }

  //Hanlde user Invite Functionality
  const handleChange = (visible: any) => {
    return (
      setValue(visible && Array.isArray(visible) ? visible : [])
    )
  }

  const handleInputChange = (VisibleHandle: any) => setInputValue(VisibleHandle || "");
  const addEmailToList = async (email: string) => {
    if (!email) return;

    //Validate it is email or not
    const re = /\S+@\S+\.\S+/;
    if (re.test(email)) {
      const data = {email : email}
      const _users= await apiService.post("/vspace/filterNonmember", data);

      if(_users.message === '') {
        setValue([...(value || []), { label: email, value: email }]);
        setInputValue('');
      } else {
        setLastAddedEmail(email);
        setAlreadyMember(true);
        setTimeout(() => {
          setAlreadyMember(false);
          setLastAddedEmail("");
        }, 1200);
        setInputValue('');
      }
    } else {
      setValidEmail(true);
      setInputValue("");
      setTimeout(() => {
        setValidEmail(false);
      }, 1200);
    }
  };

  const handleKeyDown = async (event: React.KeyboardEvent) => {
    if (!inputValue){
      return;
    }
    switch (event.key) {
      case 'Enter':
      case 'Tab':
        await addEmailToList(inputValue);
        event.preventDefault();
        break;
    }
  };

  const handleBlur = async () => {
    if (inputValue && inputValue.trim()) {
      await addEmailToList(inputValue.trim());
    }
  };

  const {
    invitesCountry,
    invitesRegion,
    invitesOrganisationType,
    invitesOrganisation,
    invitesExpertise,
    invitesNetWork,
    visibility,
    userList  } = props;
  const VisibilityClass = "visibility-space";
  return (
    <div>
      <Col className="header-block" lg={12}>
        <h6>
          <span>{t("vspace.Admin")}</span>
        </h6>
      </Col>
      <Row>
        <Col>
          <Form.Group>
            <Form.Label>{t("vspace.GroupVisibility")}</Form.Label>
            <Form.Check
            className="check"
              checked={visibility}
              name="visibility"
              onClick={props.handleVisibility}
              type="radio"
              label= {t("vspace.Public")}
            />
          </Form.Group>
        </Col>
        <Col style={{ marginTop: "1.6rem" }}>
          <Form.Check
            type="radio"
            checked={!visibility}
            name="visibility"
            value="off"
            onClick={props.handleVisibility}
            label={t("vspace.Private")}
          />
        </Col>
      </Row>
      {!visibility && (
        <div>
          <Col className="header-block" lg={12}>
            <h6>
              <span>{t("vspace.Invites")}</span>
            </h6>
          </Col>
          <Row>
            <Col md={4} sm={4} lg={4}>
              <Form.Group>
                <Form.Label>{t("CountryOrTerritory")}</Form.Label>
                <MultiSelect
                  overrideStrings={{
                    selectSomeItems: t("SelectCountry"),
                  }}
                  options={countryList || []}
                  onChange={(e: any) => props.onChange(e, "invitesCountry")}
                  value={invitesCountry}
                  className={VisibilityClass}
                  labelledBy={t("SelectCountry")}
                />
              </Form.Group>
            </Col>
            <Col md={4} sm={4} lg={4}>
              <Form.Group>
                <Form.Label>{t("CountryRegions")}</Form.Label>
                <MultiSelect
                  overrideStrings={{
                    selectSomeItems: t("SelectRegions"),
                  }}
                  options={props.multiRegionOptions || []}
                  value={invitesRegion}
                  onChange={(e: any) => props.onChange(e, "invitesRegion")}
                  className={VisibilityClass}
                  labelledBy={t("SelectRegions")}
                />
              </Form.Group>
            </Col>
            <Col md={4} sm={4} lg={4}>
              <Form.Group>
                <Form.Label>{t("OrganisationType")}</Form.Label>
                <MultiSelect
                  overrideStrings={{
                    selectSomeItems: t("SelectOrganisationType"),
                  }}
                  options={organisationTypeList || []}
                  onChange={(e: any) => props.onChange(e, "invitesOrganisationType")}
                  value={invitesOrganisationType}
                  className={VisibilityClass}
                  labelledBy={t("vspace.SelectOrganisationType")}
                />
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col md={4} sm={4} lg={4}>
              <Form.Group>
                <Form.Label>{t("Organisation")}</Form.Label>
                <MultiSelect
                  overrideStrings={{
                    selectSomeItems: t("SelectOrganisation"),
                  }}
                  options={props.multiOrganisationOptions || []}
                  onChange={(e: any) => props.onChange(e, "invitesOrganisation")}
                  value={invitesOrganisation}
                  className={VisibilityClass}
                  labelledBy={t("SelectOrganisation")}
                />
              </Form.Group>
            </Col>

            <Col md={4} sm={4} lg={4}>
              <Form.Group style={{ maxWidth: "450px" }}>
                <Form.Label>{t("Expertise")}</Form.Label>
                <MultiSelect
                  overrideStrings={{
                    selectSomeItems: t("SelectExpertise"),
                  }}
                  options={props.multiExpertsOptions || []}
                  onChange={(e: any) => props.onChange(e, "invitesExpertise")}
                  value={invitesExpertise}
                  className={"visibility-space"}
                  labelledBy={"Select Organisation Type"}
                />
              </Form.Group>
            </Col>
            <Col md={4} sm={4} lg={4}>
              <Form.Group>
                <Form.Label>{t("Network")}</Form.Label>
                <MultiSelect
                  overrideStrings={{
                    selectSomeItems: t("SelectNetwork"),
                  }}
                  options={props.multiNetworkOptions || []}
                  onChange={(e: any) => props.onChange(e, "invitesNetWork")}
                  value={invitesNetWork}
                  className={"visibility-space"}
                  labelledBy={"Select Organisation Type"}
                />
              </Form.Group>
            </Col>
          </Row>
          <Col className="header-block" lg={12}>
            <h6>
              <span>{t("vspace.PlatformMemberInvites")}</span>
            </h6>
          </Col>
          <Row>
            <Col md={12} lg={12} sm={12}>
              <Select
                closeMenuOnSelect={false}
                components={animatedComponents}
                isMulti
                value={userList || []}
                placeholder= {t("SelectUsers")}
                onChange={userHandler}
                options={[
                  props.allOption || { label: "All users", value: "*" },
                  ...(props.multiUserOptions || [])
                ]}
              />
              {/*
              /***** In future We add async Paginate
              <AsyncPaginate
                  value={userList}
                  placeholder="Select User"
                  loadOptions={props.loadusers}
                  onChange={userHandler}
                  isMulti
                  additional={{
                    page: 1,
                  }}
                />
                */}
            </Col>
          </Row>
          <Col className="header-block" lg={12}>
            <h6 className="mb-1">
              <span>{t("vspace.NonPlatform")}</span>
            </h6>
          </Col>
          <Row>
            <Col md={12} lg={12} sm={12}>
              <small>{t("vspace.PressTab")}</small>
              <CreatableSelect
                components={animatedComponents}
                inputValue={inputValue || ""}
                isClearable
                isMulti
                menuIsOpen={false}
                onChange={handleChange}
                onInputChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleBlur}
                placeholder= {t("vspace.Typeemail")}
                value={value || []}
              />
              {validEmail && <small className="text-danger">{t('PleaseenterValidEmailid')}</small>}
              {alreadyMember && <small className="text-danger"> {lastAddedEmail}  {t('isalreadyexist')}</small>}
            </Col>
          </Row>
        </div>
      )
      }
    </div >
  );
};

GroupVisibility.defaultProps = {
  allOption: {
    label: "All users",
    value: "*",
  },
};

export default GroupVisibility;
