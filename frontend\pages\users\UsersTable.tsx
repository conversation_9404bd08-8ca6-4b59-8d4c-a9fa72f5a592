//Import Library
import Link from "next/link";
import React , {useEffect, useState} from "react";
import { <PERSON><PERSON>, Button } from 'react-bootstrap';

//Import services/components
import RKITable from '../../components/common/RKITable';
import apiService from "../../services/apiService";


function UsersTable(_props) {
  const [tabledata, setDataToTable] = useState([]);
  const [, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [isModalShow, setModal] = useState(false);
  const [removeUserDetails, setRemoveUser] = useState({});

  const usersParams = {
    "sort": {"created_at": "desc"},
    "limit": perPage,
    "page": 1,
    "query": {}
  };

  const columns = [
    {
      name: 'Username',
      selector: 'username',
      cell: d => d.username
    },
    {
      name: 'Em<PERSON>',
      selector: "email",
      cell: d => d.email
    },
    {
      name: 'Role',
      selector: "role",
      cell: d => d.role ? d.role.title : ""
    },
    {
      name: 'Institution',
      selector: "institution",
      cell: d => d.institution ? d.institution.title : ""
    },
    {
      name: 'Action',
      selector: "",
      cell: d => <div><Link href="/users/[...routes]" as={`/users/edit/${d._id}`}>Edit</Link>&nbsp;<a onClick={() => removeUser(d)}>Delete</a></div>
    }
  ];

  const getUsersData = async () => {
    setLoading(true);
    const response = await apiService.get('/users', usersParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setTotalRows(response.totalCount);
      setLoading(false);
    }
  };
  const handlePageChange = page => {
    usersParams.limit = perPage;
    usersParams.page = page;
    getUsersData();
  };

  const handlePerRowsChange = async (newPerPage, page) => {
    usersParams.limit = newPerPage;
    usersParams.page = page;
    setLoading(true);
    const response = await apiService.get('/users', usersParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }
  };

  useEffect(() => {
    getUsersData();
  }, []);

  const removeUser = async (user: any) => {
    setRemoveUser(user);
    setModal(true);
  }

  const modalConfirm = async () => {
    await apiService.remove(`/users/${removeUserDetails['_id']}`);
    getUsersData();
    setRemoveUser({});
    setModal(false);
  }

  const modalHide = () => setModal(false);

  return (
    <div>
      <Modal show={isModalShow} onHide={modalHide}>
        <Modal.Header closeButton>
          <Modal.Title>Delete User</Modal.Title>
        </Modal.Header>
        <Modal.Body>Are you sure want to delete this user ?</Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={modalHide}>
            Cancel
          </Button>
          <Button variant="primary" onClick={modalConfirm}>
            Yes
          </Button>
        </Modal.Footer>
      </Modal>

      <RKITable
        columns={columns}
        data={tabledata}
        totalRows={totalRows}
        pagServer={true}
        handlePerRowsChange={handlePerRowsChange}
        handlePageChange={handlePageChange}
      />
      </div>
  )
}

export default UsersTable;