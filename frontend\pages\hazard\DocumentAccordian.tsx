//Import Library
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Card, Accordion } from "react-bootstrap";
import { useState } from "react";

//Import services/components
import DocumentTable from "../../components/common/DocumentTable";
import { useTranslation } from 'next-i18next';

const DocumentAccordian = (props) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return (
        <>
            <Accordion.Item eventKey="0">
              <Accordion.Header onClick={() => setSection(!section)}>
                <div className="cardTitle">{t("documents")}</div>
                <div className="cardArrow">
                  {section ? (
                    <FontAwesomeIcon icon={faMinus} color="#fff" />
                  ) : (
                    <FontAwesomeIcon icon={faPlus} color="#fff" />
                  )}
                </div>
              </Accordion.Header>
              <Accordion.Body>
                <DocumentTable
                  loading={props.documentAccoirdianProps.loading}
                  sortProps={props.documentAccoirdianProps.hazardDocSort}
                  docs={props.documentAccoirdianProps.Document || []}
                  docsDescription={props.documentAccoirdianProps.docSrc}
                />
                <h6 className="mt-3">{t("DocumentsfromUpdates")}</h6>
                <DocumentTable
                  loading={props.documentAccoirdianProps.loading}
                  sortProps={props.documentAccoirdianProps.hazardDocUpdateSort}
                  docs={props.documentAccoirdianProps.updateDocument || []}
                  docsDescription={props.documentAccoirdianProps.docSrc}
                />
              </Accordion.Body>
            </Accordion.Item>
        </>
    )
};

export default DocumentAccordian;