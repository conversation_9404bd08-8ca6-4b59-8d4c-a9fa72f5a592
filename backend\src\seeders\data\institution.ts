
export const insitutions = [
  {
    "title": "Bundeswehr Institute of Microbiology",
    "description": "Sunt consequatur voluptatem quia perferendis. Quasi sint odio sint quos est. Perspiciatis vel sunt ipsum reiciendis nihil. Non ut nesciunt magni cum magni. Vitae quae molestiae pariatur nostrum est dolorem velit omnis explicabo. Aut aut aut expedita molestiae praesentium fugit. Aut in et. Aut autem qui perspiciatis praesentium non. Ipsam doloribus asperiores expedita et. Magnam facere nisi consequatur sit iusto et.",
    "type": 9,
    "expertise": [
      15,
      17,
      8,
      25
    ],
    "hazard_types": [
      2
    ],
    "hazards": [
      244,
      257,
      302,
      333,
      250,
      275,
      249,
      202
    ],
    "address": [
      {
        "country": 14,
        "city": "Lake Dane",
        "line_1": "5443 Sanford Loaf Suite 483",
        "line_2": "Suite 454"
      }
    ],
    "website": "https://Lawson.biz/",
    "phone": "************"
  },
  {
    "title": "Deutsche Gesellschaft fur Internationale Zusammenarbeit",
    "description": "Et aut ut. Veritatis velit incidunt vel. Qui iusto pariatur eius libero. Numquam autem natus asperiores cumque a animi distinctio. Eligendi qui occaecati consequatur quos in iure quasi doloremque. Fuga dolor voluptatum ratione explicabo sit enim maiores iure aliquam. Qui temporibus rem qui necessitatibus assumenda. Maxime qui minima id commodi. Eligendi temporibus fugit facilis nesciunt animi porro animi. In vel dolores aut voluptatem quos et pariatur.",
    "type": 2,
    "networks": [
      2
    ],
    "expertise": [
      7
    ],
    "hazard_types": [
      5,
      7
    ],
    "hazards": [
      322,
      313,
      237,
      232
    ],
    "address": [
      {
        "country": 167,
        "city": "Jenkinsshire",
        "line_1": "0430 Viva Stravenue Apt. 718",
        "line_2": "Apt. 640"
      }
    ],
    "website": "https://Cornell.io/",
    "phone": "************"
  },
  {
    "title": "European Mobile Laboratory (EMLab)",
    "description": "Odit occaecati possimus est impedit enim enim et. Quam culpa tenetur magnam dolores sed commodi in. Dolor atque perferendis non totam eos non velit est nostrum. Aspernatur suscipit blanditiis omnis expedita. Accusantium iusto ut sint et. Officiis rerum blanditiis aut aliquam. Vel laboriosam iste fugiat est. Ut quas voluptatem pariatur suscipit nobis quas non earum. Nemo et assumenda temporibus. Aut labore atque et maxime est explicabo quia.",
    "type": 4,
    "networks": [
      3
    ],
    "expertise": [
      27
    ],
    "hazard_types": [
      4
    ],
    "hazards": [
      108,
      31,
      205
    ],
    "address": [
      {
        "country": 192,
        "city": "East Brielle",
        "line_1": "9435 Kian Centers",
        "line_2": "Apt. 460"
      }
    ],
    "website": "https://www.Zulauf.tv/",
    "phone": "************",
  },
  {
    "title": "Institute for Tropical Medicine",
    "description": "Quia id quis. Repellendus voluptatem quia qui quasi accusantium corporis rerum et. Aut eum amet. Occaecati soluta accusamus totam in laboriosam reprehenderit dolores minima. Facere expedita cumque. Aperiam id asperiores sed a aut est ad. Maxime modi minima aliquid quae consequatur cumque odio. Pariatur rem et et ducimus nisi nostrum veritatis. Autem aut sunt vel. Aut odio sit natus omnis ea impedit.",
    "type": 7,
    "networks": [
      4
    ],
    "expertise": [
      10,
      3
    ],
    "hazard_types": [
      8,
      9
    ],
    "hazards": [
      356
    ],
    "address": [
      {
        "country": 214,
        "city": "Franeckiview",
        "line_1": "552 Keeling Pines Apt. 305",
        "line_2": "Apt. 666"
      }
    ],
    "website": "https://www.Rice.org/",
    "phone": "************",
  },
  {
    "title": "Institute fuer Virologie",
    "description": "Sit in sed amet officiis voluptatum qui libero accusantium. Inventore nihil quas explicabo deserunt earum. Temporibus aut inventore est nihil aperiam dolor iusto ad et. Perspiciatis facilis expedita debitis quasi dicta illum sunt numquam quae. Aut quia delectus doloremque fuga et. Quam expedita est natus qui. Ut eligendi sit sapiente quas. Mollitia at eligendi suscipit magnam totam. Incidunt nihil dolor voluptatum voluptatem reprehenderit. Provident sunt quo expedita molestiae nisi enim voluptas nulla.",
    "type": 6,
    "networks": [
      3,
      2
    ],
    "expertise": [
      18,
      13,
      16,
      2
    ],
    "hazard_types": [
      10,
      9
    ],
    "hazards": [
      345,
      355
    ],
    "address": [
      {
        "country": 29,
        "city": "New Athenaville",
        "line_1": "020 Bechtelar Summit",
        "line_2": "Suite 158"
      }
    ],
    "website": "https://www.Aracely.us/",
    "phone": "************",
  },
  {
    "title": "Institute of Virology",
    "description": "Natus deleniti quasi alias doloribus beatae a. Quo magni rerum et error id et suscipit. A ea aut in. Ut aspernatur id dolorem provident. Non veniam nihil non. Unde consequuntur omnis numquam quo rem velit. Similique consectetur debitis numquam. Molestiae ea doloribus. Et tempora ipsum mollitia similique beatae labore qui repellendus. Animi eos quisquam officiis repellat ut hic et aut praesentium.",
    "type": 8,
    "expertise": [
      15
    ],
    "hazard_types": [
      5,
      1
    ],
    "hazards": [
      314
    ],
    "address": [
      {
        "country": 77,
        "city": "South Laney",
        "line_1": "17 Willa Highway Apt. 518",
        "line_2": "Apt. 570"
      }
    ],
    "website": "https://West.us/",
    "phone": "************",
  },
  {
    "id": 9,
    "created": 1584451474,
    "changed": 1584451474,
    "author": 1,
    "title": "Johanniter-Unfall-Hilfe e.V. Headquarters Berlin",
    "description": "Sint nisi id rerum fugit laboriosam est temporibus est nobis. Corporis labore reprehenderit in voluptas. Illo provident omnis voluptatum aut quo quo voluptatem est. Dolorem optio velit. Minima itaque fuga. Sint nisi recusandae voluptatem nobis perferendis dolores. Modi reprehenderit omnis voluptatem et omnis enim eum. Quis qui quos quia aut hic sunt deserunt cupiditate in. Ullam in accusamus. Nihil officiis quisquam id a iste ipsum rem et.",
    "type": 2,
    "expertise": [
      26
    ],
    "hazard_types": [
      10
    ],
    "address": [
      {
        "country": 190,
        "city": "Lexusfurt",
        "line_1": "829 Haag Ports Apt. 416",
        "line_2": "Apt. 918"
      }
    ],
    "website": "https://Dasia.name/",
    "phone": "************",
  },
  {
    "title": "Medical Mission Institute, Departament of Tropical Medicine",
    "description": "Quia nisi magni quis. Iure esse est provident est quisquam sed voluptate aliquam. Ut blanditiis quam non incidunt minus id dolores quia. Debitis est consequuntur voluptas dolor earum quo modi. Et est quo fuga quaerat nemo asperiores. Et id temporibus incidunt voluptas sit ut aut aut adipisci. Sapiente pariatur quibusdam qui. Quo nulla quis fuga veritatis. Minus doloribus quo animi quis sed soluta molestiae dolorum eveniet. Quia ea quae aut.",
    "type": 4,
    "networks": [
      2
    ],
    "expertise": [
      10
    ],
    "hazard_types": [
      9
    ],
    "address": [
      {
        "country": 47,
        "city": "New Alexandria",
        "line_1": "196 Name Plains Suite 055",
        "line_2": "Apt. 329"
      }
    ],
    "website": "https://Satterfield.io/",
    "phone": "************",
  },
  {
    "title": "Research Institute for Animal Health",
    "description": "Culpa nihil recusandae id nihil et. Dolore autem officia occaecati ut saepe pariatur maiores. Numquam eius assumenda expedita neque praesentium ut et. Totam blanditiis eos rem officia est ea aperiam maxime. Earum repellendus libero iure aut eum molestias minima. Iure consequatur iure et. Enim fugit est inventore nihil non. Molestiae dolores sit sint necessitatibus eos nam quo. Est vel laboriosam omnis fugit tenetur ratione aperiam doloribus. Velit optio eligendi.",
    "type": 3,
    "networks": [
      3
    ],
    "expertise": [
      9,
      16,
      10,
      21
    ],
    "hazard_types": [
      9,
      4
    ],
    "hazards": [
      168,
      33,
      341,
      189,
      213,
      217
    ],
    "address": [
      {
        "country": 204,
        "city": "Walshton",
        "line_1": "36 Bridgette Wells",
        "line_2": "Suite 145"
      }
    ],
    "website": "https://www.Gillian.ca/",
    "phone": "************",
  },
  {
    "title": "Robert Koch Institut (RKI)",
    "description": "Sit quaerat enim dolorum nulla aut perferendis dolores quam sed. Ut maxime cupiditate in voluptas pariatur dolorem non unde. Voluptatem nulla ipsum est recusandae molestiae sed debitis saepe iste. Quis explicabo at illo consequatur. Eius dicta voluptas commodi saepe et dolores. Dolorem fugit pariatur voluptas eaque. Fugiat dicta ut eaque amet. Tempora voluptatem rem provident eum. Accusamus sapiente sunt. Tempore ut dolorem minima.",
    "type": 1,
    "expertise": [
      5,
      7
    ],
    "hazard_types": [
      11
    ],
    "address": [
      {
        "country": 51,
        "city": "West Mckenna",
        "line_1": "14 Leannon Street Suite 946",
        "line_2": "Apt. 738"
      }
    ],
    "website": "https://www.Rutherford.com/",
    "phone": "************",
  }
]