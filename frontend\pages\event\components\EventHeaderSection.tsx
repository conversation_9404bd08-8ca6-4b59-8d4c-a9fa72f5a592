//Import Library
import React from 'react';
import { Button, Col, Row } from "react-bootstrap";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPen } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import ReadMoreContainer from "../../../components/common/readMore/readMore";
import Bookmark from "../../../components/common/Bookmark";
import { useTranslation } from 'next-i18next';
import { canEditEvent } from "../permission";


const EventHeaderSection = (props) => {
    const { t } = useTranslation('common');

    const EditEventComponent = () => {
        return (
            <Link
                href="/event/[...routes]"
                as={`/event/edit/${props.routeData.routes[1]}`}
                >
                <Button variant="secondary" size="sm">
                    <FontAwesomeIcon icon={faPen} />
                    &nbsp;{t("Events.show.Edit")}
                </Button>
            </Link>
        );
    };

    const CanEditEvent = canEditEvent(() => <EditEventComponent />);
    
    return (
        <>
            <Row>
                <Col md={11}>
                    {eventdata_func()}
                    <hr />
                    <ReadMoreContainer description={props.eventData.description} />
                </Col>
                <Col md={1}>
                    <Bookmark entityId={props.routeData.routes[1]} entityType="event" />
                </Col>
            </Row>
        </>
    );

    function eventdata_func() {
        return (
            <div className="d-flex justify-content-between">
                <h4>
                    { props?.eventData?.country
                        ? `${props.eventData.country.title} | ${String(
                            props.eventData.hazard
                                  .map((item) => (item && item.title && item.title.en ? item.title.en : ""))
                                  .join(", ")
                          )} (${props.eventData.title})`
                        : ""}
                    &nbsp;&nbsp;
                    { props?.editAccess && props?.routeData?.routes[1] ? <CanEditEvent event={props.eventData} /> : null}
                </h4>
            </div>
        );
    }
}

export default EventHeaderSection;