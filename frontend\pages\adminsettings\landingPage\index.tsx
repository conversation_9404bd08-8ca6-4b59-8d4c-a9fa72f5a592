//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, But<PERSON> } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import LandingPageTable from "./landingPageTable";
import { useTranslation } from 'next-i18next';
import PageHeading from "../../../components/common/PageHeading";
import { canAddLandingPage } from "../permissions";
import NoAccessMessage from "../../rNoAccess";
import { useSelector } from "react-redux";

const LandingPageIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowLandingPageIndex = () => {
    return(
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title={t("adminsetting.landing.form.EditableContent")} />
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <LandingPageTable />
          </Col>
        </Row>
    </Container>

      
    );
  }
  
  const ShowcAddLandingPage = canAddLandingPage(() => <ShowLandingPageIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.landing_page?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(    
    <ShowcAddLandingPage />
  );
}

export default LandingPageIndex;