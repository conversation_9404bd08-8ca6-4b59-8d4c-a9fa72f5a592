//All dashboard styles should go here

.dashboardScreen {
    h2 {
        padding: 5px 15px 6px;
        border-bottom: 1px solid #ddd;
        margin: 0 -8px 10px;
        font-weight: 400;
        //text-transform: capitalize;
    }
    .dashboardLeft {
        text-align: center;
        .aboutUs {
            margin: 0 0 25px;
        }
        .infoCard.card {
            height: 100%;
        }
        .active-op-project {
            .link {
                display: block;
            }
        }
    }
}

.logoImg {
    width: 269px;
    height: 81.39px;
}

//Announcements
.announcements {
    margin: 25px 0 0;
    .announcementItem {
        padding: 0 0 13px;
        margin: 0 0 13px;
        border-bottom: 1px solid #eee;
    }
    .announceImg {
        width: 120px;
        height: 75px;
        background: #eee;
        float: left;
        margin: 0 10px 0 0;
    }
    i {
        &.announceImg {
            font-size: 2.5rem;
            color: #c5c5c5;
            padding: 16px 36px;
        }
    }
    .carousel-control {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .carousel-navigation {
            display: flex;
            a {
                padding: 4px 8px;
                background-color: #f3f3f3;
                border: #dddddd;
                margin: 0 2px;
                cursor: pointer;
                color: #949494;
            }
        }
    }
}

button.btn-plain {
    background: transparent;
    border: none;
    padding: 0;
    height: 20px;
    font-weight: 500;
    color: #202020;
    &:hover,
    &:focus,
    &:active {
        color: #595959 !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }
}

// Responsive
@media screen and (max-width: 992px) {
    .dashboardScreen {
        .rbc-calendar {
            min-height: 370px;
            position: relative;
            .fas {
                position: absolute;
                top: 0px;
                &.fa-chevron-left {
                    left: 0px;
                }
                &.fa-chevron-right {
                    right: 0px;
                }
            }
        }
        .ongoingBlock {
            margin-bottom: 20px;
        }
    }
}
@media screen and (max-width: 767px) {
    .dashboardScreen {
        .rbc-calendar {
            .fas {
                &.fa-chevron-right {
                    top: -24px;
                }
            }
        }
    }
}
