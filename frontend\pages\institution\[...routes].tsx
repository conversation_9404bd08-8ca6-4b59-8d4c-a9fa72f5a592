//Import Library
import { useState, useEffect } from 'react';

//Import services/components
import InstitutionForm from "./Form";
import InstitutionShow from "./InstitutionShow";
import InstitutionFocalPoint from './InstitutionFocalPoint'
import apiService from "../../services/apiService";
import {canAddInstitutionForm, canEditInstitutionForm, canManageFocalPoints} from "./permission";
import NoAccessMessage from '../rNoAccess';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';


const Router = ({ router }) => {
  const routes: any = router.query.routes || [];
  const [institution, setInstitution] = useState('');
  const [editAccess, setEditAccess] = useState(false);
  const [loggedInUser, setLoggedInUser] = useState('');

  const fetchInstitutionData = async () => {
    const institutionData = await apiService.get(`/institution/${routes[1]}`);
    setInstitution(institutionData);
    getLoggedInUser(institutionData);
  }

  useEffect(() => {
    fetchInstitutionData();
  }, []);

  const getLoggedInUser = async (institutionData) => {
    const data = await apiService.post("/users/getLoggedUser", {});
    if (data && data.roles && data.roles.length) {
        setLoggedInUser(data);
        setEditAccess(false);
        if (data && data['roles']) {
          if (data['roles'].includes("SUPER_ADMIN")) {
            //SUPER_ADMIN can Edit all organisations
            setEditAccess(true);
          } else if (data['roles'].includes("PLATFORM_ADMIN") && institutionData['user'] == data['_id']) {
              //PLATFORM_ADMIN can Edit organisations which is added by them only
              setEditAccess(true);
          } else if (data['roles'].includes("GENERAL_USER") && institutionData.user == data['_id']) {
            //"GENERAL_USER" can Edit organisations which is added by them only
            setEditAccess(true);
          }
        }
    }
};

  const CanAccessCreateForm  = canAddInstitutionForm(() => <InstitutionForm institution={''} routes={routes} />)
  const CanAccessEditForm = canEditInstitutionForm(() => <InstitutionForm institution={institution} routes={routes} />)
  const CanAccessFocalPointPage = canManageFocalPoints(() => <InstitutionFocalPoint institution={institution} routes={routes} />)

  switch (routes[0]) {
    case "create":
      return <CanAccessCreateForm />
    case "edit":
      if (editAccess) { return <CanAccessEditForm institution={institution} routes={routes} /> }
      else { if (loggedInUser != '') return <NoAccessMessage /> }
    case "show":
      return <InstitutionShow routes={routes} />;

    case "focalpoint":
      if (institution !== null) {
        return <CanAccessFocalPointPage institution={institution} />;
      }
      break;
    default:
      return null;
  }
};

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Router;