//Import Library
import { Injectable } from '@nestjs/common';
import * as Email from 'email-templates';

//Import services/components
import { UsersService } from './users/users.service';
const SECRETKEY = process.env.CRYPTO_SECRETKEY;

const logo = '/images/home/<USER>';
const declarationform = '/declarationform/';
const languageCodes = '?languageCode=';
const Cryptr = require('cryptr'),
  cryptr = new Cryptr(SECRETKEY);
@Injectable()
export class EmailService {
  private email;
  constructor(private readonly _usersService: UsersService) {
    const nodeMailers = require('nodemailer');
    const transport = nodeMailers.createTransport({
      host: process.env.SES_HOST,
      port: parseInt(process.env.SES_PORT),
      auth: { user: process.env.SES_USER, pass: process.env.SES_PASS },
    });
    this.email = new Email({
      message: {
        from: process.env.FROM_EMAIL,
      },
      transport: process.env.SES_HOST
        ? transport
        : {
            jsonTransport: true,
          },
      views: {
        options: {
          extension: 'ejs',
        },
      },
      send: true,
    });
  }
  async requestVspaceSubscribe(data, emailTo, languageCode) {
    data.logo = process.env.BASE_URL + logo;
    const resp = await this.email.send({
      template: `${languageCode}/vspace-request-subscribe`,
      message: {
        from: process.env.FROM_EMAIL,
        to: emailTo,
      },
      locals: data,
    });
    return resp;
  }

  async vspaceInvite(data, languageCode, vspaceId) {
    const vspaceLink = `/vspace/show/${vspaceId}`;
    const _data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: data.username,
      virtualSpace: data.title,
      activateLink: process.env.BASE_URL + vspaceLink,
    };

    const resp = await this.email.send({
      template: `${languageCode}/vspace-invite`,
      message: {
        from: process.env.FROM_EMAIL,
        to: data.email,
      },
      locals: _data,
    });
    return resp;
  }

  async vspaceOldUserInvite(data, vspaceData, languageCode) {
    const _data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: data.username,
      virtualSpace: vspaceData.title,
      vspaceLink: `${process.env.BASE_URL}/vspace/show/${vspaceData._id}`,
    };
    const resp = await this.email.send({
      template: `${languageCode}/vspace-invite-existinguser`,
      message: {
        from: process.env.FROM_EMAIL,
        to: data.email,
      },
      locals: _data,
    });
    return resp;
  }

  async focalPointInviteNewUser(focalPoint, institution, languageCode) {
    const token = focalPoint?.emailActivateToken ?? '';
    const data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: focalPoint.username,
      activateLink:
        process.env.BASE_URL +
        declarationform +
        token +
        languageCodes +
        languageCode,
      invitedInstitutionName: institution.title,
      inviteLink: `${process.env.BASE_URL}/institution/show/${institution._id}`,
    };

    const resp = await this.email.send({
      template: `${languageCode}/fp-invite`,
      message: {
        from: process.env.FROM_EMAIL,
        to: focalPoint.email,
      },
      locals: data,
    });
    return resp;
  }

  async manualUserInvite(user, languageCode) {
    const token = user?.emailActivateToken ? user.emailActivateToken : '';
    const data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: user.username,
      activateLink:
        process.env.BASE_URL +
        declarationform +
        token +
        languageCodes +
        languageCode,
    };

    const resp = await this.email.send({
      template: `${languageCode}/user-invite`,
      message: {
        from: process.env.FROM_EMAIL,
        to: user.email,
      },
      locals: data,
    });
    return resp;
  }

  async vspaceInviteNonExistingUser(user, vspaceDetails, languageCode) {
    const token = user?.emailActivateToken ? user?.emailActivateToken : '';
    const data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: user.username,
      virtualSpace: vspaceDetails.title,
      activateLink:
        process.env.BASE_URL +
        declarationform +
        token +
        languageCodes +
        languageCode,
    };

    const resp = await this.email.send({
      template: `${languageCode}/vspace-invite-nonexistinguser`,
      message: {
        from: process.env.FROM_EMAIL,
        to: user.email,
      },
      locals: data,
    });
    return resp;
  }

  async focalPointInviteExistingUser(focalPoint, institution, languageCode) {
    const existingUser: any = await this._usersService.get(focalPoint);
    const data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: existingUser.username,
      invitedInstitutionName: institution.title,
      inviteLink: `${process.env.BASE_URL}/institution/show/${institution._id}`,
    };

    const resp = await this.email.send({
      template: `${languageCode}/fp-invite-existinguser`,
      message: {
        from: process.env.FROM_EMAIL,
        to: existingUser.email,
      },
      locals: data,
    });
    return resp;
  }

  async operationInvite(user, operation, languageCode) {
    const data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: user.username,
      operationTitle: operation.title,
      operationLink: `${process.env.BASE_URL}/operation/show/${operation._id}`,
    };

    const resp = await this.email.send({
      template: `${languageCode}/operation-invite`,
      message: {
        from: process.env.FROM_EMAIL,
        to: user.email,
      },
      locals: data,
    });
    return resp;
  }

  async projectInvite(user, project, languageCode) {
    const data = {
      logo: process.env.BASE_URL + logo,
      invitedUser: user.username,
      projectTitle: project.title,
      projectLink: `${process.env.BASE_URL}/project/show/${project._id}`,
    };

    const resp = await this.email.send({
      template: `${languageCode}/project-invite`,
      message: {
        from: process.env.FROM_EMAIL,
        to: user.email,
      },
      locals: data,
    });
    return resp;
  }

  async institutionRequest(user, institution, languageCode) {
    const data = {
      logo: process.env.BASE_URL + logo,
      adminUser: user.username,
      institutionTitle: Array.isArray(institution)
        ? institution.map((d) => d.title).join(', ')
        : institution.title,
      institutionLink: `${process.env.BASE_URL}/institution/show/${institution._id}`,
    };

    const resp = await this.email.send({
      template: `${languageCode}/institution-request`,
      message: {
        from: process.env.FROM_EMAIL,
        to: user.email,
      },
      locals: data,
    });
    return resp;
  }

  async institutionAcknowledge(user, institution, languageCode) {
    const data = {
      logo: process.env.BASE_URL + logo,
      addedUser: user.username,
      institutionTitle: Array.isArray(institution)
        ? institution.map((d) => d.title).join(', ')
        : institution.title,
    };

    if (user.email) {
      const resp = await this.email.send({
        template: `${languageCode}/institution-acknowledge`,
        message: {
          from: process.env.FROM_EMAIL,
          to: user.email,
        },
        locals: data,
      });
      return resp;
    } else {
      return null;
    }
  }

  async institutionApproval(user, institution, languageCode) {
    const data = {
      logo: process.env.BASE_URL + logo,
      username: user.username,
      institutionTitle: institution.title,
      institutionLink: `${process.env.BASE_URL}/institution/show/${institution._id}`,
    };

    const resp = await this.email.send({
      template: `${languageCode}/institution-approval`,
      message: {
        from: process.env.FROM_EMAIL,
        to: user.email,
      },
      locals: data,
    });
    return resp;
  }

  async requestAcknowledgement(data, emailTo, languageCode) {
    data.logo = process.env.BASE_URL + logo;

    const resp = await this.email.send({
      template: `${languageCode}/vspace-request-acknowledge`,
      message: {
        from: process.env.FROM_EMAIL,
        to: emailTo,
      },
      locals: data,
    });
    return resp;
  }

  async contactUsAcknowledgement(data, emailTo, languageCode) {
    data.logo = process.env.BASE_URL + logo;
    const resp = await this.email.send({
      template: `${languageCode}/contact-us`,
      message: {
        from: process.env.FROM_EMAIL,
        to: emailTo,
      },
      locals: data,
    });
    return resp;
  }

  async resetPasswordEmail(email, username, token) {
    const data = {
      logo: process.env.BASE_URL + logo,
      username: username,
      tokenUrl: `${process.env.BASE_URL}/reset-password/${token}`,
    };
    const resp = await this.email.send({
      template: 'en/resetPassword',
      message: {
        from: process.env.FROM_EMAIL,
        to: email,
      },
      locals: data,
    });
    return resp;
  }

  async alertSubscribers(username, email, title, id, entityType, languageCode) {
    let message;
    const deEntityTypes = {
      operation: 'Der Inhalt Ihrer abonnierten Operation wurde aktualisiert.',
      institution:
        'Der Inhalt Ihrer abonnierten Einrichtung wurde aktualisiert.',
      project: 'Der Inhalt Ihres abonnierten Projekts wurde aktualisiert.',
      vspace:
        'In Ihrem abonnierten virtuellen Raum wurden Inhalte aktualisiert.',
      event: 'Der Inhalte Ihrer abonnierten Veranstaltung wurden aktualisiert.',
    };

    const frEntityTypes = {
      operation:
        "Le contenu de l' opération à laquelle vous êtes abonnée a été mis à jour.",
      institution: 'Le contenu de votre institution abonnée a été mis à jour.',
      project: 'Le contenu du projet auquel vous êtes abonné a été mis à jour.',
      vspace:
        'Le contenu de la salle virtuelle à laquelle vous êtes abonnée a été mis à jour.',
      event:
        "Le contenu de l'événement auquel vous êtes abonné a été mis à jour.",
    };
    switch (languageCode) {
      case 'de':
        message = deEntityTypes[entityType];
        break;
      case 'fr':
        message = frEntityTypes[entityType];
        break;
      default:
        break;
    }
    const url = `${process.env.BASE_URL}/${entityType}/show/${id}`;
    const subject = `${
      entityType.charAt(0).toUpperCase() + entityType.slice(1)
    } - ${title} has an update - RKI`;
    const resp = await this.email.send({
      template: `${languageCode}/subscriberAlerts`,
      message: {
        from: process.env.FROM_EMAIL,
        to: email,
      },
      locals: {
        logo: process.env.BASE_URL + logo,
        username: username,
        title: title,
        url: url,
        entity: entityType,
        subject: subject,
        message: message,
      },
    });
    return resp;
  }
}
