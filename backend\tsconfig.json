{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true}, "exclude": ["node_modules", "dist"], "max-line-length": [true, {"limit": 120, "ignore-pattern": "^import |^export {(.*?)}", "check-strings": true, "check-regex": true}]}