@import "variables";


@media print {
  * {
    color-adjust: exact !important;
  } 
  .active-projects-announcements{
    display: none;
  }
}


body {
  color: rgba(0, 0, 0, 0.7);
  a:hover {
    text-decoration: none;
  }
}

ul.comma-separated {
  display: inline;
  list-style: none;
  padding: 0px;
  li {
    display: inline;
    &:not(:last-child):after {
      content: ", ";
    }
  }
}

.loginContainer {
  display: flex;
  background: url(/images/login-bg.jpg);
  min-height: 100vh;
  align-items: center;
  flex-direction: column;
  background-size: cover;
  justify-content: center;
  background-repeat: no-repeat;
  font-family: "Fira Sans", sans-serif;
}

.loginForm, .reset-password {
  border-radius: 4px;
  border: 1px solid #eaeaea;
  margin: 10px;
  padding: 10px;
  box-shadow: 1px 1px 15px rgba(150, 150, 150, 0.2) !important;
  display: flex;
  min-width: 300px;
  flex-direction: row;
  background: #fff;
  .imgBanner {
    max-width: 480px;
    img {
      width: 100%;
    }
  }
  .img--forgetPass {
    max-width: 300px;
    img {
      width: 100%;
    }
  }
}

.formContainer {
  display: flex;
  padding: 0 10px 0 20px;
  max-width: 350px;
  flex-direction: column;
  // justify-content: center;
  .mb-3 {
    padding-top: 10px;
    padding-bottom: 7px;
  }
  .button {
    padding: 6px 16px;
    box-sizing: border-box;
    line-height: 1.75;
    border-radius: 4px;
    letter-spacing: 0.02857em;
    text-transform: uppercase;
    color: #fff;
    background-color: #3f51b5;
    border: none;
    width: 100%;
    &:hover {
      background-color: #303f9f;
    }
  }
}

.logoContainer {
  margin: 1em;
  display: flex;
  justify-content: center;
}

// Heeader
.main-container nav.navbar {
  color: #303030;
  background-color: #fff !important;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 1px 1px 15px rgba(150, 150, 150, 0.4);
  padding: 0 5px 0 20px;
  align-items: stretch;
}

.navbar-brand img {
  height: 60px;
}

.headerIcons {
  border-end: 1px solid #d4d4d4;
  border-start: 1px solid #d4d4d4;
  margin: 0 3px;
  padding: 13px 4px 13px 0;
  display: flex;
  align-items: center;
}

.language {
  color: rgb(90, 90, 90);
  padding: 0;
  width: 36px;
  height: 36px;
  text-align: center;
  background: rgba(0, 0, 0, .1);
  border-radius: 50%;
  text-transform: uppercase;
  margin: 0 4px 0 10px;
  cursor: pointer;
  box-sizing: content-box;
  display: block;
  line-height: 36px;
  .nav-link {
    color: #5a5a5a;
    padding: 0 0 0 4px;
    &::after {
      border: 0;
    }
  }
  .dropdown-menu.show {
    padding: 0;
  }
  .dropdown-divider {
    margin: 0;
  }
  .dropdown-item {
    padding: 0 0.5rem;
    color: #595959;
    &.active {
      color: #fff;
    }
  }
}

.headerMenu {
  display: flex;
  ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    li a {
      color: #595959;
      padding: 0 8px;
    }
  }
}

.topiconLinks {
  color: #5a5a5a;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 2px;
  &:hover {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    color: #5a5a5a;
  }
  i {
    font-size: 16px;
  }
}

.headerUser {
  display: flex;
  align-items: center;
  width: 132px;
  padding: 0 0 0 3px;
  .dropdown {
    padding: 0 0 0 5px;
    width: 95px;
    .dropdown-toggle {
      // width: 98px;
      text-overflow: ellipsis;
      overflow: hidden;
      padding-right: 10px;
      position: relative;
      font-size: 14px;
      // text-transform: capitalize;
      &:after {
        position: absolute;
        right: 0;
        top: 45%;
      }
    }
    .dropdown-menu {
      right: -3px;
      left: auto;
      padding: 0;
      top: 40px;
    }
    a.dropdown-item {
      padding: 2px 5px;
      text-transform: capitalize;
      border-bottom: 1px solid #dedede;
    }
  }
  a {
    color: #595959;
    padding: 0;
    i {
      font-size: 24px;
      color: #b6b6b6;
    }
  }
}

.page-heading {
  padding: 5px 15px 6px;
  border-bottom: 1px solid $page-heading-bottom-border-color;
  margin: 0 -8px 10px;
  font-weight: 400;
}

// Sidebar Left
.sidebar-region {
  position: relative;
  width: auto;
  .sideMenu {
    padding-top: 1.5em;
    display: flex;
    overflow-y: auto;
    background-color: $sidebar-bg;
    min-height: calc(100vh - 70px);
    width: 160px;
    padding-left: 9px;
    height: 100%;
  }
  ul {
    padding: 0;
    margin: 0;
    text-align: center;
    width: 140px;
    a {
      display: flex;
      flex-direction: column;
      text-transform: capitalize;
      padding: 15px 5px !important;
      font-size: 13px !important;
      font-family: "Roboto", "Helvetica", "Arial", sans-serif;
      color: #5d8aad;
      align-items: center;
      .leftmenuIcon {
        background-repeat: no-repeat;
      }
      &.active,
      &:hover {
        color: #fff;
        .leftmenuIcon {
          background-position: left bottom;
        }
      }
      span {
        display: block;
        padding: 10px 0 0;
      }
    }
  }
}

// Sidebar Right
.sidebar-right {
  width: 316px;
  max-width: 316px;
  border-start: 1px solid #d4d4d4;
  height: calc(100vh - 50px);
  z-index: 999;
  background-color: #fafafa;
  position: fixed;
  right: 0;
  &:before {
    background-color: #cccccc;
    content: " ";
    left: 30px;
    margin-left: -1.5px;
    position: absolute;
    width: 3px;
    z-index: 100;
    height:100%;
  }
}

.updatesBlock {
  background-color: $updates-block-bg;
  height: calc(100vh - 70px);
  .updatesScrollbar {
    width: 315px;
    height: calc(100vh - 70px);
    .dropdown-toggle{
      width: 125px;
      text-overflow: ellipsis;
      overflow: hidden;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      -webkit-box-orient: vertical;
    }
  }
  .dashboardUpdateScroll {
    height: calc(56vh - 70px)
  }
  .updates-block{
    padding-bottom:30px;
    ul {
      padding: 0;
      margin: 0;
      list-style: none;
      li {
        display: flex;
        flex-direction: row;
        padding: 16px 5px 8px 60px;
        position: relative;
        &:after {
          content: " ";
          width: calc(100% - 30px);
          height: 1px;
          background: #ccc;
          position: absolute;
          bottom: 0;
          left: 30px;
        }
        &.updates-load-more {
          padding: 0px 0px 0px 31px;
          a {
            display: inline-block;
            width: 100%;
            text-align: center;
            padding: 10px 0;
            cursor: pointer;
            background-color: #eeeeee;
            color: #007bff;
          }
        }
      }
      .updatesAction {
        display: flex;
        justify-content: flex-end;
        color: #898989;
        div {
          margin: 0 0 0 5px;
        }
      }
      .timeline-badge {
        background: #fff;
        width: 44px;
        height: 44px;
        left: 8px;
        position: absolute;
        z-index: 100;
        border: 3px solid #ccc;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #999;
        font-size: 16px;
        cursor: pointer;
      }
      .timeline-isActive {
        background-color: #34485f;
      }
      .timeline-content {
        width: 100%;
        span.title {
          color: #494949;
          font-weight: 600;
          text-transform: none;
          line-height: 18px;
          margin: 0 0 5px;
          display: block;
          font-size: 15px;
          cursor: pointer;
        }
        .description {
          font-size: 14px;
          margin: 0 0 8px;
        }
        span.date {
          display: block;
          font-size: 14px;
          line-height: 16px;
        }
        i.icon {
          padding: 6px;
          font-size: 14px;
          cursor: pointer;
        }
      }
    }
  }
  .dropdown {
    margin-right: 5px;
  }
}

.isIconActive {
  color: white;
}

.rightTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  background: #fafafa;
  z-index: 99999;
  position: relative;
  h3 {
    margin: 0;
    padding: 18px 15px;
    font-size: 18px;
  }
}

#main-content {
  width: calc(100% - 160px);
  > .row {
    min-height: 100%;
  }
}

#main {
  min-height: 100%;
  padding-bottom: 40px;
}

.content-block {
  width: calc(100% - 323px);
  position: relative;
}

.footer {
  margin: 15px 0 0 -8px;
  border-top: 1px solid #dedede;
  padding: 2px 15px;
  font-size: 14px;
  bottom: 0;
  position: absolute;
  width: 100%;
  clear: both;
  a {
    cursor: pointer;
    color: #007bff;
  }
}

// Help
.help-content-li-title {
  cursor: pointer;
}

.help-content-li-content {
  margin-left: 20px;
  margin-right: 10px;
}

.mobile-leftmenu,
.mobile-rightmenu {
  display: none;
}
.accordion{
  .container-fluid{
    padding: 0px;
  }
}

// Responsive
@media screen and (max-width: 1200px) {
  .mobile-rightmenu {
    display: block;
    width: 35px;
    height: 35px;
    border-radius: 20px 0 0 20px;
    background: #232c3d;
    position: fixed;
    margin: 7px 0 0;
    top: 60px;
    right: 0;
    color: #fff;
    z-index: 99;
    align-items: center;
    justify-content: center;
    transition: right 150ms cubic-bezier(0.4, 0, 0.2, 1) 15ms;
    padding: 5px;
  }
  .sidebar-rightregion {
    position: relative;
    overflow: hidden;
    z-index: 1021;
    &.show-rightmenu {
      .sidebar-right {
        right: 0;
        top: 60px;          
      }
      .mobile-rightmenu {
        right: 275px;
      }
    }
    .sidebar-right {
      right: -275px;
      transition: right 150ms cubic-bezier(0.4, 0, 0.2, 1) 15ms;
      position: fixed;
      z-index: 99;
      padding: 0 !important;
      width: 275px;
      &:before {
        left: 26px;
      }
    }
    .updates-block ul {
      .timeline-badge {
        width: 35px;
        height: 35px;
        z-index: 100;
        font-size: 14px;
      }
      li {
        padding: 16px 5px 8px 50px;
      }      
    }
  }
  .main-container {
    .navbar-brand img {
      height: 50px;
    }
    .headerIcons {
      padding: 10px 4px 10px 0;
    }
    .content-block {
      width: 100%;
    }
  }
}

@media screen and (max-width: 767px) {
  .headerMenu {
    position: absolute;
    top: 60px;
    left: 0;
    background: #fff;
    height: 49px;
    box-shadow: inset 0 7px 10px -7px rgba(150, 150, 150, 0.4);
    padding: 0 8px 0 34px;
    border-bottom: 1px solid #ededed;
    width: 52%;
    ul li a {
      padding: 0 6px;
    }
  }
  .headerIcons {
    position: absolute;
    top: 60px;
    padding: 10px 40px 10px 0;
    margin: 0;
    right: 0;
    border-end: none;
    box-shadow: inset 0 7px 10px -7px rgba(150, 150, 150, 0.4);
    height: 49px;
    background: #fff;
    border-bottom: 1px solid #ededed;
    width: 48%;
    .topiconLinks:nth-child(3) {
      display: none;
    }
  }
  .mobile-leftmenu {
    display: block;
    width: 35px;
    height: 35px;
    border-radius: 0 20px 20px 0;
    background: #232c3d;
    position: fixed;
    margin: 7px 0 0;
    color: #fff;
    z-index: 99;
    align-items: center;
    justify-content: center;
    transition: margin 150ms cubic-bezier(0.4, 0, 0.2, 1) 15ms;
    padding: 5px 13px;
  }
  .main-container {
    .navbar-brand img {
      height: 50px;
    }
    .headerUser {
      position: absolute;
      top: 15px;
      right: 8px;
    }
    .content-block {
      width: 100%;
      margin-top: 40px;
    }
    .footer {
      position: relative;
      margin: 0;
      text-align: center;
    }
    .sidebar-region {
      z-index: 1021;
      &.show-leftmenu {
        .sideMenu {
          margin-left: 0;
        }
        .mobile-leftmenu {
          margin: 0 0 0 158px;
        }
      }
      ul a {
        padding: 10px !important;
        border-bottom: 1px solid #10151f;
        border-top: 1px solid #313948;
        span {
          padding: 2px 0 0;
        }
      }
      .sideMenu {
        margin-left: -160px;
        transition: margin 150ms cubic-bezier(0.4, 0, 0.2, 1) 15ms;
        position: fixed;
        height: calc(100% - 65px);
        z-index: 99;
        padding: 0 !important;
      }
    }
    .sidebar-rightregion {
      position: relative;
      overflow: hidden;
      z-index: 1021;
      &.show-rightmenu {
        .sidebar-right {
          right: 0;                 
        }
        .mobile-rightmenu {
          right: 275px;
        }
      }
      .sidebar-right {
        right: -275px;
        transition: right 150ms cubic-bezier(0.4, 0, 0.2, 1) 15ms;
        position: fixed;
        z-index: 99;
        padding: 0 !important;
        width: 275px;
        &:before {
          left: 26px;
        }
      }
      .updates-block ul {
        .timeline-badge {
          width: 35px;
          height: 35px;
          z-index: 100;
          font-size: 14px;
        }
        li {
          padding: 16px 5px 8px 50px;
        }        
      }
    }
  }
}
.organisationmap_div{
  display: flex;
}
@media screen and (max-width: 767px){
  .landing .slider .myslider {
    height: auto !important;
}
.landing .slider .myslider .sliderContent {
  position: static !important;
  width: 100% !important;
}
.landing .welcome {
  padding: 0px 0 !important;
}
.landing .network-news {
  text-align: center !important;
}
.landing .network-news img {
  width: auto !important;
  float: none !important;
  margin-right: 0% !important;
}
.landing .network-news .newsContent {
  float: none !important;
  width: 100% !important;
  text-align: left !important;
}
.headerMenu a {
  font-size: 13px;
}
.organisationmap_div{
  display: block;
  .organisationMap{
    width: 100%;
  }
  .organisationInfo {
    width: 100%;
    .quick-info-filter{
      justify-content: start;
    }
  }
}
}
@media screen and (max-width:1200px){
  .updatesBlock {
    .dropdown {
      .dropdown-toggle {
        width: 100px;
      }
      .dropdown-menu {
        transform: translate3d(-68px, 40px, 0px) !important;
      }
    }
  }
}
@media screen and (max-width: 575px){
  .landing .header-block .quick-menu li{
    padding: 3px 4px !important; 
  }
  .landing .header-block .quick-menu li a {
    font-size: 12px !important; 
  }
  .logo-image{
    text-align: center;
  }
  .quick-menu{
    justify-content: center !important;
  }
}

@media screen and (max-width: 340px){
  .institution_switch{
    left: 35px;
  }
}