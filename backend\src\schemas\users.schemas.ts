//Import Library
import * as mongoose from 'mongoose';
require('dotenv').config();
import * as mongoosePaginate from 'mongoose-paginate-v2';
const mongooseFieldEncryption =
  require('mongoose-field-encryption').fieldEncryption;

//Import services/components
import { AppRoles } from '../app.roles';

export const UsersSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  firstname: { type: String },
  lastname: { type: String },
  email: { type: String, required: true, unique: true },
  position: { type: String },
  password: { type: String, required: true },
  roles: [{ type: String, required: true, enum: Object.keys(AppRoles) }],
  institutionInvites: { type: Array },
  country: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Country',
    autopopulate: true,
  },
  region: [
    { type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true },
  ],
  institution: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Institution',
    autopopulate: true,
  },
  by_instution: { type: Boolean },
  status: String,
  vspace_status: String,
  is_vspace: { type: Boolean, default: false },
  is_focal_point: { type: Boolean, default: false },
  image: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Image',
    autopopulate: true,
  },
  mobile_number: String,
  dial_code: { type: String },
  enabled: { type: Boolean, default: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
  dataConsentPolicy: { type: Boolean, default: false },
  restrictedUsePolicy: { type: Boolean, default: false },
  acceptCookiesPolicy: { type: Boolean, default: false },
  withdrawConsentPolicy: { type: Boolean, default: false },
  medicalConsentPolicy: { type: Boolean, default: false },
  fullDataProtectionConsentPolicy: { type: Boolean, default: false },
  emailActivateToken: { type: String },
});

UsersSchema.plugin(mongooseFieldEncryption, {
  fields: ['username', 'email', 'firstname', 'lastname', 'mobile_number'],
  secret: process.env.USER_SECRET_KEY,
  saltGenerator: function () {
    return process.env.SALT_KEY;
  },
});

UsersSchema.plugin(mongoosePaginate);
