//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateUpdateTypeDto } from './dto/create-update-type.dto';
import { UpdateUpdateTypeDto } from './dto/update-update-type.dto';
import { UpdateTypeService } from './update-type.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('updatetype')
@UseGuards(SessionGuard)
export class UpdateTypeController {
  constructor(private readonly _updateTypeService: UpdateTypeService) {}

  @Post()
  async create(@Body() createUpdateTypeDto: CreateUpdateTypeDto) {
    try {
      const resp = await this._updateTypeService.create(createUpdateTypeDto);
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._updateTypeService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') updateTypeId: string) {
    return this._updateTypeService.get(updateTypeId);
  }

  @Patch(':id')
  async update(
    @Param('id') updateTypeId: string,
    @Body() updateUpdateTypeDto: UpdateUpdateTypeDto,
  ) {
    try {
      const resp = await this._updateTypeService.update(
        updateTypeId,
        updateUpdateTypeDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') updateTypeId: string) {
    const deletedData = this._updateTypeService.delete(updateTypeId);
    return deletedData;
  }
}
