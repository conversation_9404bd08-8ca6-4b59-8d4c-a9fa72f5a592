//Import Library
import { Container, Row, Col } from "react-bootstrap";
import Link from 'next/link';
import Button from 'react-bootstrap/Button';
import React, {useState} from "react";

//Import services/components
import RegionsMultiCheckboxes from "../../components/common/RegionsMultiCheckboxes";
import PageHeading from "../../components/common/PageHeading";
import OperationsTable from "./OperationsTable";
import ListMapContainer from "./ListMapContainer";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddOperation } from "./permission";

const Operation = (_props) => {
  const { t } = useTranslation('common');
  const [operations, setOperations] = useState([]);
  const [selectedRegions, setSelectedRegions] = useState(null);

  const AddOperationComponent = () => {
    return (
      <Link href="/operation/[...routes]" as="/operation/create" >
        <Button variant="secondary" size="sm">
          {t('addOperation')}
        </Button>
      </Link>
    );
  };

  const CanAddOperation = canAddOperation(() =>  <AddOperationComponent />);

  const regionHandler = (val: any) => {

    setSelectedRegions(val);
    }



  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={12}>
          <PageHeading title={t('menu.operations')} />
        </Col>
      </Row>
      <Row>
        <Col xs lg={12} >
          <ListMapContainer operations={operations} />
        </Col>
      </Row>
      <Row>
        <Col xs lg={12}>
          <RegionsMultiCheckboxes
            filtreg={(val)=> regionHandler(val)}
            selectedRegions={[]}
            regionHandler={regionHandler}
          />
        </Col>
      </Row>
      <Row>
        <Col xs={12} className="ps-4">
          <CanAddOperation />
        </Col>
      </Row>
      <Row className="mt-3">
        <Col xs={12}>
          <OperationsTable selectedRegions={selectedRegions} setOperations={setOperations} />
        </Col>
      </Row>
    </Container>
  )
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Operation;