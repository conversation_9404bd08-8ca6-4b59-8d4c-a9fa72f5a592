//Import Library
import { Container } from "react-bootstrap";

//Import services/components
import InstitutionTable from "./InstitutionTable";
import PageHeading from "../../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddOrganisationApproval } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";


const InstitutionApproval = (_props: any) => {
  const { t } = useTranslation('common');
  const ShowInstitutionApproval = () => {
    return (
      <Container fluid className="p-0">
        <PageHeading title={t("adminsetting.approval.OrganisationApproval")} />
        <InstitutionTable />
      </Container>
    )
  };

  const ShowAddOrganisationApproval = canAddOrganisationApproval(() => <ShowInstitutionApproval />);
  const state:any = useSelector((state: any) => state);
  if (!(state?.permissions?.institution?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddOrganisationApproval />
  )
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default InstitutionApproval;
