//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";

//Import services/components
import RKIMAP1 from "../../components/common/RKIMap1";
import RKIMapMarker from "../../components/common/RKIMapMarker";
import { useTranslation } from 'next-i18next';

interface MapLegendsProps {
  t: (key: string) => string;
}

const MapLegends = (props: MapLegendsProps) => {
  const { t } = props;
  const Event = t("Event");
  return (
    <div className="map-legends">
      <ul>
        <li className="marker-yellow-legend">
          <i className="fas fa-circle" /> {t("Projects")}
        </li>
        <li className="marker-green-legend">
          <i className="fas fa-circle" /> {t("Operations")}
        </li>
        <li className="marker-red-legend">
          <i className="fas fa-circle" /> {Event}{" "}
        </li>
      </ul>
    </div>
  );
};

interface ListMapContainerProps {
  t: (key: string) => string;
  ongoingOperations: any[];
  ongoingProjects: any[];
  currentEvents: any[];
}

const ListMapContainer = (props: ListMapContainerProps) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language;
  const { t, ongoingOperations, ongoingProjects, currentEvents } = props;
  const [dataCollector, setDataCollector]: any = useState({
    events: false,
    projects: false,
    operations: false,
  });
  const [mapdata, setMapdata] = useState<any[]>([]);
  const [activeMarker, setactiveMarker]: any = useState({});
  const [markerInfo, setMarkerInfo]: any = useState({});

  const MarkerInfo = (Markerprops: any) => {
    const { info } = Markerprops;
    const markersInformation = markerDetails(info);
    if (
      info &&
      Object.keys(info).length > 0 &&
      markersInformation != undefined
    ) {
      return (
        <ul>
          {markersInformation.map((item: any, index: number) => {
            return (
              <li key={index}>
                <a href={`/${currentLang}/${info?.type}/show/${info?.id}`}>{item?.title}</a>
              </li>
            );
          })}
        </ul>
      );
    } else {
      return null;
    }

    function markerDetails(infoinit: any) {
      switch (infoinit?.type) {
        case "operation":
          return ongoingOperations.filter(
            (x) => x.country && x.country._id == infoinit.countryId
          );
        case "project":
          return ongoingProjects.filter(
            (x) =>
              x.partner_institutions &&
              x.partner_institutions.length > 0 &&
              x.partner_institutions[0].partner_country &&
              x.partner_institutions[0].partner_country._id ==
                infoinit.countryId
          );
        case "event":
          return currentEvents.filter(
            (x) => x.country && x.country._id == infoinit.countryId
          );
      }
    }
  };

  const resetMarker = () => {
    setactiveMarker(null);
    setMarkerInfo(null);
  };

  const onMarkerClick = async (props: any, marker: any, e: any) => {
    resetMarker();
    setactiveMarker(marker);
    setMarkerInfo({
      name: props.name,
      id: props.id,
      type: props.type,
      countryId: props.countryId,
    });
  };

  const fetchOperations = () => {
    const operations = ongoingOperations;
    const dashboardOperationFilter = [];
    _.forEach(operations, (op: any) => {
      if (op.country) {
        dashboardOperationFilter.push({
          title: op.title,
          type: "operation",
          id: op._id,
          countryId: op.country && op.country._id,
          lat: op.country.coordinates[0].latitude,
          lng: op.country.coordinates[0].longitude,
          icon: "/images/map-marker-green.svg",
        });
      }
    });
    dataCollector.operations = true;
    Data_setfunc(setDataCollector, dataCollector);
    setMapdata([...mapdata, ...dashboardOperationFilter]);
  };

  const fetchProjects = () => {
    const projects = ongoingProjects;
    const dashboardProjectsFilter = [];
    _.forEach(projects, (val: any) => {
      if (val.partner_institutions && val.partner_institutions.length > 0) {
        _.forEach(val.partner_institutions, (country: any) => {
          if (country.partner_country) {
            dashboardProjectsFilter.push({
              title: val.title,
              type: "project",
              id: val._id,
              countryId:
                val.partner_institutions.length > 0 &&
                val.partner_institutions[0].partner_country &&
                val.partner_institutions[0].partner_country._id,
              lat: country.partner_country.coordinates[0].latitude,
              lng: country.partner_country.coordinates[0].longitude,
              icon: "/images/map-marker-yellow.svg",
            });
          }
        });
      }
    });
    dataCollector.projects = true;
    DataCollector_func(setDataCollector, dataCollector);
    setMapdata([...mapdata, ...dashboardProjectsFilter]);
  };

  const fetchEvents = () => {
    const dashbordEventFilter = [];
    _.forEach(currentEvents, (event: any) => {
      if (event.country) {
        dashbordEventFilter.push({
          title: event.title,
          type: "event",
          id: event._id,
          countryId: event.country && event.country._id,
          lat: event.country.coordinates[0].latitude,
          lng: event.country.coordinates[0].longitude,
          icon: "/images/map-marker-red.svg",
        });
      }
    });
    dataCollector.events = true;
    data_select(setDataCollector, dataCollector);
    setMapdata([...mapdata, ...dashbordEventFilter]);
  };

  useEffect(() => {
    fetchProjects();
  }, [ongoingProjects]);

  useEffect(() => {
    fetchOperations();
  }, [ongoingOperations]);

  useEffect(() => {
    fetchEvents();
  }, [currentEvents]);

  return (
    <>
      <RKIMAP1
        onClose={resetMarker}
        language={currentLang}
        points={mapdata}
        activeMarker={activeMarker}
        markerInfo={<MarkerInfo info={markerInfo} />}
      >
        {dataCollector.projects &&
        dataCollector.operations &&
        dataCollector.events &&
        mapdata.length >= 1
          ? mapdata.map((item: any, index: number) => {
              if (item.lat && item.lng) {
                return (
                  <RKIMapMarker
                    key={index}
                    name={item.title}
                    id={item.id}
                    countryId={item.countryId}
                    type={item.type}
                    icon={{
                      url: item.icon,
                    }}
                    onClick={onMarkerClick}
                    position={item}
                  />
                );
              }
            })
          : null}
      </RKIMAP1>
      <MapLegends t={t} />
    </>
  );
};

export default ListMapContainer;
function data_select(setDataCollector: any, dataCollector: any) {
  setDataCollector(dataCollector);
}

function DataCollector_func(setDataCollector: any, dataCollector: any) {
  setDataCollector(dataCollector);
}

function Data_setfunc(setDataCollector: any, dataCollector: any) {
  setDataCollector(dataCollector);
}
