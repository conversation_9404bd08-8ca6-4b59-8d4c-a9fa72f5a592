//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { CountryRegionController } from './country-region.controller';
import { CountryRegionService } from './country-region.service';
// SCHEMAS
import { RegionSchema } from '../../schemas/region.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Region', schema: RegionSchema }
    ])
  ],
  controllers: [CountryRegionController],
  providers: [CountryRegionService],
})

export class CountryRegionModule { }