//Import Library
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';


const CategoryTable = (_props: any) => {
  const [tabledata, setDataToTable] = useState([]);
  const [ ,setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [isModalShow, setModal] = useState(false);
  const [selectCategory, setSelectCategory] = useState({});
  const { t } = useTranslation('common');


  const categoryParams = {
    "sort": { "title": "asc" },
    "limit": perPage,
    "page": 1,
    "query": {}
  };

  const columns = [
    {
      name: 'Title',
      selector: 'title',
    },
    {
      name: 'Action',
      selector: "",
      cell: (d: any) => <div><Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_category/${d._id}`} ><i className="icon fas fa-edit" /></Link>&nbsp;<Link href="#" onClick={() => userAction(d)}><i className="icon fas fa-trash-alt" /></Link> </div>
    }
  ];

  const getCategoryData = async () => {
    setLoading(true);
    const response = await apiService.get('/category', categoryParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setTotalRows(response.totalCount);
      setLoading(false);
    }
  };

  const handlePageChange = (page: any) => {
    categoryParams.limit = perPage;
    categoryParams.page = page;
    getCategoryData();
  };

  const handlePerRowsChange = async (newPerPage: any, page: any) => {
    categoryParams.limit = newPerPage;
    categoryParams.page = page;
    setLoading(true);
    const response = await apiService.get('/category', categoryParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }
  };

  const userAction = async (row: any) => {
    setSelectCategory(row._id);
    setModal(true);
  }

  const modalConfirm = async () => {
    await apiService.remove(`/category/${selectCategory}`);
    getCategoryData();
    setModal(false);
  }

  const modalHide = () => setModal(false);

  useEffect(() => {
    getCategoryData();
  }, []);

  return (
    <div>
      <Modal show={isModalShow} onHide={modalHide}>
        <Modal.Header closeButton>
          <Modal.Title>{t("DeleteCategory")}</Modal.Title>
        </Modal.Header>
        <Modal.Body>{t("Areyousurewanttodeletethiscategory")} </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={modalHide}>
          {t("cancel")}
        </Button>
          <Button variant="primary" onClick={modalConfirm}>
          {t("yes")}
        </Button>
        </Modal.Footer>
      </Modal>

      <RKITable
        columns={columns}
        data={tabledata}
        totalRows={totalRows}
        pagServer={true}
        handlePerRowsChange={handlePerRowsChange}
        handlePageChange={handlePageChange}
      />
    </div>
  );
};

export default CategoryTable;