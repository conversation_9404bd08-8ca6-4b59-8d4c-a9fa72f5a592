//Import Library
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import UserTableFilter from "./userTableFilter";
import { useTranslation } from 'next-i18next';

export default function UserTable(props: any) {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [loading, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [perPageCount, setPerPageCount] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectUser, setSelectUser] = useState({});
    const [filterText, setFilterText] = React.useState("");
    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);
        const [loggedInUser, setloggedInUser] = useState<any>({});

    const userParams = {
        sort: { created_at: "desc" },
        limit: perPageCount,
        page: 1,
        query: {},
    };

    let columns = [
        {
            name: t("adminsetting.user.table.Username"),
            selector: "username",
            cell: (d: any) => d.username,
        },
        {
            name: t("adminsetting.user.table.Email"),
            selector: "email",
            cell: (d: any) => d.email,
        },
        {
            name: t("adminsetting.user.table.Role"),
            selector: "role",
            cell: (d: any) => (d.roles ? d.roles : ""),
        },
        {
            name: t("adminsetting.user.table.Organisation"),
            selector: "institution",
            cell: (d: any) => (d.institution ? d.institution.title : ""),
        },
        {
            name: t("adminsetting.user.table.Action"),
            selector: "",
            cell: (d: any) => (
                <>
                    {d.isEdit ?
                        <div>
                            <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_user/${d._id}`}>

                                <i className="icon fas fa-edit" />

                            </Link>
                            &nbsp;
                            <a onClick={() => userAction(d)}>
                                <i className="icon fas fa-trash-alt" />
                            </a>
                        </div>
                    :
                        ""
                    }

                </>
            ),
        },
    ];

    const getUserData = async (userParamsinit: any) => {
        setLoading(true);
        const response = await apiService.get("/users", userParamsinit);
        if (response && Array.isArray(response.data)) {
            if (!loggedInUser['roles']?.includes('SUPER_ADMIN')) {
                response.data.filter((x: any) => x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = false);
                response.data.filter((x: any) => !x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = true);

            } else {
                response.data.map((x: any) => x.isEdit = true);
            }
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
            loggedInUserData(response.data);
        }
    };

    const handlePageChange = (page: any) => {
        userParams.limit = perPageCount;
        userParams.page = page;
        getUserData(userParams);
    };

    const handlePerRowsChange = async (newPerPage: any, page: any) => {
        userParams.limit = newPerPage;
        userParams.page = page;
        setLoading(true);
        const response = await apiService.get("/users", userParams);
        if (response && Array.isArray(response.data)) {
            if (!loggedInUser['roles']?.includes('SUPER_ADMIN')) {
                response.data.filter((x: any) => x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = false);
                response.data.filter((x: any) => !x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = true);

            } else {
                response.data.map((x: any) => x.isEdit = true);
            }
            setDataToTable(response.data);
            setPerPageCount(newPerPage);
            setLoading(false);
        }
    };

    useEffect(() => {
        getUserData(userParams);
    }, []);

    const loggedInUserData = async (allUserData: any) => {
        const currentUser = await apiService.post("/users/getLoggedUser", {});
        if (currentUser && currentUser.username) {
            setloggedInUser(currentUser);
            console.log(allUserData);
            if (!currentUser.roles.includes('SUPER_ADMIN')) {
                allUserData.filter((x: any) => x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = false);
                allUserData.filter((x: any) => !x.roles.includes('SUPER_ADMIN')).map((x: any) => x.isEdit = true);

            } else {
                allUserData.map((x: any) => x.isEdit = true);
            }
            setDataToTable(allUserData);
            setPerPage(allUserData);
        }
    };
    const userAction = async (row: any) => {
        setSelectUser(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/users/${selectUser}`);
            getUserData(userParams);
            setModal(false);
            toast.success(t("adminsetting.user.table.userDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.user.table.errorDeletingUser"));
        }
    };

    const modalHide = () => setModal(false);

    const subHeaderComponentMemo = React.useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText("");
            }
        };

        const handleSearchTitle = (query: any) => {
            if (query) {
                const emailRegex = new RegExp(Regexp());
                if (emailRegex.test(query.toLowerCase())) {
                    userParams.query = { email: query };
                } else {
                    userParams.query = { username: query };
                }
            }
            getUserData(userParams);
            userParams.query = {};
            // setFilterText("")

            function Regexp(): string | RegExp {
                return "^[^@]+@[^@]+\\.[^@]+$";
            }
        };

        const handleChange = (e: any) => {
            if (e && e.label) {
                setFilterText(e.label);
                handleSearchTitle(e.label);
            } else {
                userParams.query = {};
                setFilterText("");
                getUserData(userParams);
            }
        };

        const onSearch = () => {
            handleSearchTitle(filterText);
        };

        const handleKeypress = () => {
            onSearch();
        };

        const userdataKeypress = (event: any) => {
            if (event.key === "Enter") {
                handleKeypress();
            }
        };

        return (
            <UserTableFilter
                onFilter={handleChange}
                onClear={handleClear}
                filterText={filterText}
                onHandleSearch={onSearch}
                onKeyPress={userdataKeypress}
            />
        );
    }, [filterText]);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.user.table.DeleteUser")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.user.table.Areyousurewanttodeletethisuser?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.user.table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.user.table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={props.trim && props.trim === "actions" ? columns.slice(0, -1) : columns}
                data={tabledata}
                totalRows={totalRows}
                loading={loading}
                subheader
                pagServer={true}
                resetPaginationToggle={resetPaginationToggle}
                subHeaderComponent={subHeaderComponentMemo}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
}
