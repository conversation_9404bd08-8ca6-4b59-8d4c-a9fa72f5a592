//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { HazardTypeInterface } from '../../interfaces/hazard_type.interface';
import { CreateHazardTypeDto } from './dto/create_hazard_type.dto';
import { UpdateHazardTypeDto } from './dto/update_hazard_type.dto';
const HazardType = 'Could not find hazard type.';
@Injectable()
export class HazardTypeService {
  constructor(
    @InjectModel('HazardType')
    private hazardTypeModel: Model<HazardTypeInterface>,
  ) {}

  async create(
    createHazardTypeDto: CreateHazardTypeDto,
  ): Promise<HazardTypeInterface> {
    const createdHazardType = new this.hazardTypeModel(createHazardTypeDto);
    return createdHazardType.save();
  }

  async findAll(query): Promise<HazardTypeInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.hazardTypeModel.paginate(_filter, options);
  }

  async get(hazardTypeId): Promise<HazardTypeInterface[]> {
    let _hazardType;
    try {
      _hazardType = await this.hazardTypeModel.findById(hazardTypeId).exec();
    } catch (error) {
      throw new NotFoundException(HazardType);
    }
    if (!_hazardType) {
      throw new NotFoundException(HazardType);
    }
    return _hazardType;
  }

  async update(hazardTypeId: any, updateHazardTypeDto: UpdateHazardTypeDto) {
    const getHazardTypeById: any = await this.hazardTypeModel.findById(
      hazardTypeId,
    );
    const updatedHazardType = new this.hazardTypeModel(updateHazardTypeDto);
    Object.keys(updateHazardTypeDto).forEach((d) => {
      getHazardTypeById[d] = updatedHazardType[d];
    });
    getHazardTypeById.updated_at = new Date();
    return getHazardTypeById.save();
  }

  async delete(hazardTypeId: string) {
    const result = await this.hazardTypeModel
      .deleteOne({ _id: hazardTypeId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(HazardType);
    }
  }
}
