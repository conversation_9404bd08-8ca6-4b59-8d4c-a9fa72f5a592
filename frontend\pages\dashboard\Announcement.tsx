//Import Library
import React, { useEffect, useState } from 'react';
import { Container, Row } from "react-bootstrap";
import _ from 'lodash';
import Carousel from 'react-bootstrap/Carousel'
import Col from "react-bootstrap/Col";

//Import services/components
import AnnouncementItem from "./AnnouncementItem";
import apiService from "../../services/apiService";


//TODO: Need to use RKISingleItemCarousel component to reuse our exisiting component
interface ListOfAnnouncementItemProps {
  announcements: any[][];
}

function ListOfAnnouncementItem(props: ListOfAnnouncementItemProps) {
  const { announcements } = props;
  return (
    <div>
      {announcements.map((item: any, index: number) => {
        return (
          <Row className="announcementItem" key={index}>
            <AnnouncementItem item={item} />
          </Row>
        )
      })}
    </div>
  )
}

interface AnnouncementProps {
  t: (key: string) => string;
}

function Announcement({ t }: AnnouncementProps) {
  const [announcements, setAnnouncements] = useState<any[][]>([]);

  const [cindex, setCindex] = useState(0);

  const [carouselItemCount] = useState(3);

  const setEmptyNotice = () => {
    setAnnouncements([]);
  };

  const updatesParams = {
    query: { show_as_announcement: true },
    sort: { created_at: "desc" },
    limit: 9,
    select: "-created_at -update_type -contact_details -document -end_date -link -media -reply -start_date -updated_at -user"
  };

  const fetchAnnouncements = async (params = updatesParams) => {
    const response = await apiService.get('/updates', params);
    if (response && response.data && response.data.length > 0) {
      const partition = _.chunk(response.data, 3);
      setAnnouncements(partition)
    } else {
      setEmptyNotice()
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [])

  const toggleCarousel = (direction: 'next' | 'prev') => {
    let index = cindex;
    const [_min, max] = [0, carouselItemCount - 1];

    if (direction === 'next' && index < max) {
      index++
    }
    else if (direction === 'prev' && index > 0 ) {
      index--
    }


    if ((announcements.length - index) === 1) {
      index = announcements.length - 1;
    }

    if ((announcements.length - index) === 0) {
      index = 1;
    }
    setCindex(index);
  };


  return (
    <div className="announcements">
      {announcements && announcements.length > 0 ? (
        <>
          <Container fluid>
            <Row>
              <Col xs={10} className="p-0">
                <h4>{t('announcements')}</h4>
              </Col>
              {announcements && announcements.length > 1 ?
                <Col xs={2} className="text-end carousel-control p-0">
                  <div className="carousel-navigation">
                    <a className="left carousel-control" onClick={() => toggleCarousel('prev')}>
                      <i className="fa fa-chevron-left" />
                    </a>
                    <a className="right carousel-control" onClick={() => toggleCarousel('next')}>
                      <i className="fa fa-chevron-right" />
                    </a>
                  </div>
                </Col>
                : null}
            </Row>
          </Container>
          <Container fluid>
            <Row>
              <Col xs={12} className="p-0">
                <Carousel indicators={false} controls={false} interval={null} activeIndex={cindex}>
                  {announcements.map((item: any, index: number) => {
                    return (
                      <Carousel.Item key={index}>
                        <ListOfAnnouncementItem announcements={item} />
                      </Carousel.Item>
                    )
                  })}
                </Carousel>
              </Col>
            </Row>
          </Container>
        </>
      ) : (
          <Container fluid={true}>
            <Row>
              <Col xs={10} className="p-0">
                <h4>{t('announcements')}</h4>
              </Col>
              <Col xs={12} className="p-0">
                <div className="border p-3">{t("NoAnnouncementsavailabletodisplay")}</div><br /></Col>
            </Row>
          </Container>
        )}
    </div>
  )
}

export default Announcement;