//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

//Import services/components
import { ProjectInterface } from '../../interfaces/project.interface';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { InstitutionService } from './../institution/institution.service';
import { EmailService } from './../../email.service';
import { UsersService } from './../../users/users.service';
import { RolesService } from './../roles/roles.service';
import { FlagService } from '../flag/flag.service';
import { VspaceInterface } from 'src/interfaces/vspace.interface';
const FindProject = 'Could not find Project.';
@Injectable()
export class ProjectService {
  constructor(
    @InjectModel('Project') private projectModel: Model<ProjectInterface>,
    @InjectModel('Vspace') private vspaceModel: Model<VspaceInterface>,
    private readonly institutionService: InstitutionService,
    private readonly emailService: EmailService,
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
    private readonly _flagService: FlagService,
  ) {}

  async create(
    createProjectDto: CreateProjectDto,
    user: object,
  ): Promise<ProjectInterface> {
    const worldRegionAndCountry = {
      world_region: createProjectDto['partner_institutions'][0].world_region,
      country: createProjectDto['partner_institutions'][0].partner_country,
    };
    if (
      createProjectDto['institution_invites'] &&
      createProjectDto['institution_invites'].length > 0
    ) {
      const instList = [];
      createProjectDto['institution_invites'].forEach((inst) => {
        this.projectServicescreat(inst, instList, user, worldRegionAndCountry);
      });
      const instIds = await this.institutionService.bulkCreate(instList);

      if (instIds && instIds.length > 0) {
        const { loggedUser, languageCode, adminUser } = await this.projectLang(
          createProjectDto,
          user,
        );
        if (loggedUser) {
          await this.emailService.institutionAcknowledge(
            loggedUser,
            instIds,
            languageCode,
          );
        }
        if (adminUser) {
          await this.emailService.institutionRequest(
            adminUser,
            instIds,
            languageCode,
          );
        }
      }
      createProjectDto['institution_invites'] =
        instIds && instIds.length > 0 ? instIds.map((d) => d['_id']) : [];
    }
    const createdProject = new this.projectModel(createProjectDto);
    createdProject.save();

    return createdProject;
  }

  private async projectLang(createProjectDto: CreateProjectDto, user: object) {
    const languageCode = createProjectDto['language']
      ? createProjectDto['language']
      : 'en';
    const adminUser = await this.usersService.findOne({
      username: process.env.SEED_USERNAME,
    });
    const loggedUser = await this.usersService.findOne({ _id: user['userId'] });
    return { loggedUser, languageCode, adminUser };
  }

  private projectServicescreat(
    inst: any,
    instList: any[],
    user: object,
    worldRegionAndCountry: { world_region: any; country: any },
  ) {
    if (inst.title) {
      instList.push({
        title: inst.title,
        contact_name: inst.contact_name,
        email: inst.email,
        status: 'Request Pending',
        user: user['_id'],
        address: {
          ...worldRegionAndCountry,
        },
      });
    }
  }

  async findAll(query): Promise<ProjectInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = this.projectOption(query, myCustomLabels);

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    if (_filter.title) {
      _filter.title = _filter.title
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(`^${_filter.title}`, 'gi');
      _filter['title'] = regex;
    }
    if (options.sort && (options.sort.country || options.sort.status)) {
      const sortOrder = options.sort.country
        ? options.sort.country
        : options.sort.status;
      const sortNumber = sortOrder === 'asc' ? 1 : -1;
      const skipValue = (options.page - 1) * options.limit;
      const sortArray = options.sort.country
        ? { 'countryArray.title': sortNumber }
        : { 'statusArray.title': sortNumber };
      const list = await this.getSortedList(
        _filter,
        sortArray,
        skipValue,
        options.limit,
        options.page,
      );
      return list;
    } else {
      return this.projectModel.paginate(_filter, options);
    }
  }

  private projectOption(
    query: any,
    myCustomLabels: { totalDocs: string; docs: string },
  ) {
    return {
      lean: query.lean ? query.lean : false,
      populate: query.populate ? query.populate : '',
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
      collation: { locale: 'en' },
    };
  }

  async getSortedList(filter, arr, skip, limit, page) {
    let filterType;
    const worldregionArray = [];
    this.projectParterner(filter, worldregionArray);

    if (filter.status && filter.title) {
      filterType = {
        $and: [
          { status: new mongoose.Types.ObjectId(filter.status) },
          { title: filter['title'] },
          { 'partner_institutions.world_region': { $in: worldregionArray } },
        ],
      };
    } else if (filter.status && !filter.title) {
      filterType = {
        $and: [
          { status: new mongoose.Types.ObjectId(filter.status) },
          { 'partner_institutions.world_region': { $in: worldregionArray } },
        ],
      };
    } else if (filter.title) {
      filterType = filter.title
        ? {
            $and: [
              { title: filter['title'] },
              {
                'partner_institutions.world_region': { $in: worldregionArray },
              },
            ],
          }
        : {};
    } else {
      filterType = {
        'partner_institutions.world_region': { $in: worldregionArray },
      };
    }
    const list = await this.projectModel.aggregate([
      {
        $match: filterType,
      },
      {
        $unwind: {
          path: '$partner_institutions',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'countries',
          localField: 'partner_institutions.partner_country',
          foreignField: '_id',
          as: 'countryArray',
        },
      },
      { $unwind: { path: '$countryArray', preserveNullAndEmptyArrays: true } },
      { $set: { 'partner_institutions.partner_country': '$countryArray' } },
      {
        $lookup: {
          from: 'areaofworks',
          localField: 'area_of_work',
          foreignField: '_id',
          as: 'areaofworkArray',
        },
      },
      { $set: { area_of_work: '$areaofworkArray' } },
      {
        $lookup: {
          from: 'projectstatuses',
          localField: 'status',
          foreignField: '_id',
          as: 'statusArray',
        },
      },
      { $unwind: { path: '$statusArray', preserveNullAndEmptyArrays: true } },
      { $set: { status: '$statusArray' } },
      { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
      { $sort: arr },
      {
        $project: {
          _id: 1,
          area_of_work: {
            _id: 1,
            title: 1,
          },
          title: 1,
          funded_by: 1,
          status: {
            _id: 1,
            title: 1,
          },
          partner_institutions: [
            {
              partner_country: {
                _id: '$$ROOT.partner_institutions.partner_country._id',
                title: '$$ROOT.partner_institutions.partner_country.title',
              },
            },
          ],
        },
      },
      {
        $facet: {
          totalData: [
            { $match: {} },
            { $limit: limit + skip },
            { $skip: skip },
          ],
          totalCount: [
            {
              $group: {
                _id: null,
                count: { $sum: 1 },
              },
            },
          ],
        },
      },
    ]);

    const totalCount = list[0].totalCount[0].count;
    const projectList: any = {};
    projectList.data = list[0].totalData;
    projectList.totalCount = totalCount;
    projectList.limit = limit;
    projectList.totalPages = Math.ceil(totalCount / limit);
    projectList.page = page;
    projectList.pagingCounter = page;
    projectList.hasNextPage = page !== projectList.totalPages;
    projectList.hasPrevPage = !(page === 1 && page === projectList.totalPages);
    projectList.prevPage =
      page === 1 && page === projectList.totalPages ? null : page - 1;
    projectList.nextPage = page === projectList.totalPages ? null : page + 1;
    return projectList;
  }

  private projectParterner(filter: any, worldregionArray: any[]) {
    if (filter['partner_institutions.world_region']) {
      filter['partner_institutions.world_region'].forEach((element) => {
        const val = element.value ? element.value : element;
        worldregionArray.push(new mongoose.Types.ObjectId(val));
      });
    }
  }

  async get(projectId): Promise<ProjectInterface[]> {
    let _result: any = {};
    try {
      _result = await this.projectModel.findById(projectId).exec();
    } catch (error) {
      throw new NotFoundException(FindProject);
    }
    if (!_result) {
      throw new NotFoundException(FindProject);
    }
    return _result;
  }

  async update(
    projectId: any,
    updateProjectDto: UpdateProjectDto,
    user: object,
  ) {
    const getProjectById: any = await this.projectModel
      .findById(projectId)
      .exec();

    // Newly added institutions are added into institution model - starts
    const worldRegionAndCountry = {
      world_region: updateProjectDto['partner_institutions'][0].world_region,
      country: updateProjectDto['partner_institutions'][0].partner_country,
    };
    if (
      updateProjectDto['institution_invites'] &&
      updateProjectDto['institution_invites'].length > 0
    ) {
      const newInstList = [];
      const updateInstList = [];
      let fullInstIds = [];
      updateProjectDto['institution_invites'].forEach((inst) => {
        if (!inst._id) {
          if (inst.title) {
            newInstList.push({
              title: inst.title,
              contact_name: inst.contact_name,
              email: inst.email,
              status: 'Request Pending',
              user: user['userId'],
              address: {
                ...worldRegionAndCountry,
              },
            });
          }
        } else {
          updateInstList.push({
            title: inst.title,
            contact_name: inst.contact_name,
            email: inst.email,
            _id: inst._id,
          });
          fullInstIds.push(inst._id);
        }
      });

      fullInstIds = await this.projectIntlist(newInstList, fullInstIds);
      if (updateInstList.length > 0) {
        updateInstList.forEach(async (d) => {
          await this.institutionService.update(d._id, d);
        });
      }
      updateProjectDto['institution_invites'] = fullInstIds;
    }
    // Newly added institutions are added into institution model - ends
    const updatedProject = new this.projectModel(updateProjectDto);

    try {
      Object.keys(updateProjectDto).forEach((d) => {
        getProjectById[d] = updatedProject[d];
      });
      getProjectById.updated_at = new Date();
      getProjectById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Project.');
    }

    const languageCode = updateProjectDto['language']
      ? updateProjectDto['language']
      : 'en';

    await this._flagService.alertSubscribers(
      getProjectById.title,
      getProjectById._id,
      'project',
      languageCode,
    );
    return getProjectById;
  }

  private async projectIntlist(newInstList: any[], fullInstIds: any[]) {
    if (newInstList.length > 0) {
      const resp = await this.institutionService.bulkCreate(newInstList);
      fullInstIds = [...fullInstIds, ...resp.map((d) => d['_id'])];
    }
    return fullInstIds;
  }

  async delete(projectId: string) {
    const result = await this.projectModel.deleteOne({ _id: projectId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindProject);
    }
  }
}
