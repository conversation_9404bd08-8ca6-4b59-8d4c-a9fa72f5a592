//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { LandingPageInterface } from '../../interfaces/landing-page.interface';
import { CreateLandingPageDto } from './dto/create-landingPage.dto';
import { UpdateLandingPageDto } from './dto/update-landingPage.dto';
const FindLandingPage='Could not find landing page.'
@Injectable()
export class LandingPageService {
  constructor(
    @InjectModel('LandingPage') private landingPageModel: Model<LandingPageInterface>
  ) { }

  async create(createLandingPageDto: CreateLandingPageDto): Promise<LandingPageInterface> {
    const createdEvent = new this.landingPageModel(createLandingPageDto);
    return createdEvent.save();
  }

  async getTotalCount(filter: any) {
    let _filter = {};
    try {
      _filter = filter.query ? filter.query : {};
    } catch (e) {
    }
    return this.landingPageModel.count(_filter).exec();
  }

  async findAll(query): Promise<LandingPageInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};

    if (_filter.title) {
      _filter.title= _filter.title.replace('(','\\(').replace(')','\\)').replace('&','\\&');
      const regex = new RegExp(`^${_filter.title}`, 'gi');
      _filter['title'] = regex
    }

    return await this.landingPageModel.paginate(_filter, options);
  }

  async get(landinPageId): Promise<LandingPageInterface[]> {
    let _event;
    try {
      _event = await this.landingPageModel.findById(landinPageId).exec();
    } catch (error) {
      throw new NotFoundException(FindLandingPage);
    }
    if (!_event) {
      throw new NotFoundException(FindLandingPage);
    }
    return _event;
  }

  async update(landinPageId: any, updateLandingPageDto: UpdateLandingPageDto) {
    let errMessage;
    const getEventById: any = await this.landingPageModel.findById(landinPageId).exec();
    const updatedEvent = new this.landingPageModel(updateLandingPageDto);
    try {
      Object.keys(updateLandingPageDto).forEach((d) => {
        getEventById[d] = updatedEvent[d];
      });
      getEventById.updated_at = new Date();
      getEventById.save();
    } catch (e) {
      throw new NotFoundException('Could not update the landing page.');
    }
    return getEventById;
  }

  async delete(eventId: string) {
    const result = await this.landingPageModel.deleteOne({ _id: eventId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindLandingPage);
    }
  }
}
