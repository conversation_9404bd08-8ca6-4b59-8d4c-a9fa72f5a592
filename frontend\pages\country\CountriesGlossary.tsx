//Import services/components
import { ALPHABETIC_FILTERS_EN, ALPHABETIC_FILTERS_DE } from '../../data/alphabet';
import { useTranslation } from 'next-i18next';

interface CountriesGlossaryProps {
  selectedAlpha: string;
  setselectedAlpha: (alpha: string) => void;
}

const CountriesGlossary = (props: CountriesGlossaryProps) => {
  const { selectedAlpha, setselectedAlpha } = props;
  const { i18n } = useTranslation('common');
  const alphabets = i18n.language == 'en' ? ALPHABETIC_FILTERS_EN : ALPHABETIC_FILTERS_DE;
  return (
    <div className="alphabetContainer">
      <ul>
      {alphabets.map((item, i) => {
        return (
          <li key={i}>
            <a onClick={() => setselectedAlpha(item)} className={`${(selectedAlpha == item) ? 'active': null}`}>
              {item}
            </a>
          </li>
        )
      })}
      </ul>
    </div>
  )
}

export default CountriesGlossary;