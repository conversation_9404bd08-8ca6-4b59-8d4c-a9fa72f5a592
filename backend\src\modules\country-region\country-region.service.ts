//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { RegionInterface } from '../../interfaces/region.interface';

@Injectable()
export class CountryRegionService {
  constructor(
    @InjectModel('Region') private regionModel: Model<RegionInterface>,
  ) { }

  async get(countryId, query?: any): Promise<RegionInterface[]> {
    let _result;
    try {
      const myCustomLabels = {
        totalDocs: 'totalCount',
        docs: 'data'
      };

      const options = {
        sort: query.sort ? query.sort : {},
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
        customLabels: myCustomLabels,
        select: query.select ? query.select : ''
      };

      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }

      _result = await this.regionModel.paginate({ country: countryId }, options)//.exec();
    } catch (error) {
      throw new NotFoundException('Could not find Country Region.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Country Region.');
    }
    return _result;
  }
}
