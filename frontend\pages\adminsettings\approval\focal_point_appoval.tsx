//Import Library
import { Container } from "react-bootstrap";

//Import services/components
import AdminTable from "./AdminTable";
import PageHeading from "../../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddFocalPointApproval } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const FocalPointShow = (_props: any) => {
  const { t } = useTranslation('common');
  const ShowFocalPoint = () => {
    return (
      <Container fluid className="p-0">
        <PageHeading title={t("adminsetting.FocalPointsApproval")} />
        <AdminTable />
      </Container>
    )
  };

  const ShowAddFocalPointApproval = canAddFocalPointApproval(() => <ShowFocalPoint />);
  const state:any = useSelector((state: any) => state);
  if (!(state?.permissions?.institution_focal_point?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddFocalPointApproval />
  );
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default FocalPointShow;
