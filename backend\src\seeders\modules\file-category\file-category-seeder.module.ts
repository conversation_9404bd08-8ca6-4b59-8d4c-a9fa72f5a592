//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { FileCategorySeederService } from './file-category-seeder.services';
// SCHEMAS
import { FileCategorySchema } from 'src/schemas/file_category.schemas';

/**
 * Import and provide seeder classes for File category.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'FileCategory', schema: FileCategorySchema }
      ]
    )
  ],
  providers: [FileCategorySeederService],
  exports: [FileCategorySeederService],
})

export class FileCategorySeederModule { }
