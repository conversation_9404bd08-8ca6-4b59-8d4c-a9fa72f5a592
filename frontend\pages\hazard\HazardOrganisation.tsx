//Import Library
import { Card } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import { useTranslation } from 'next-i18next';

interface HazardOrganisationProps {
  t: (key: string) => string;
  hazardInstitutionData: any[];
}

const HazardOrganisation = (props: HazardOrganisationProps) => {
    let hazardInstitutionData = props.hazardInstitutionData;
    const { t } = useTranslation('common');
    return (
        <>
            <div className="rki-carousel-card">
                <Card className="infoCard">
                    <Card.Header className="text-center">
                    {t("hazardshow.organisations")}
                    </Card.Header>
                    <Card.Body className="hazardBody">
                    {hazardInstitutionData && hazardInstitutionData.length > 0 ? (
                        hazardInstitutionData.map((item, index) => (
                        <ul className="ulItems">
                            <li key={index} className="liItems">
                            <Link
                                href="/institution/[...routes]"
                                as={`/institution/show/${item._id}`}
                            >
                                {item && item.title ? `${item.title}` : ""}
                            </Link>
                            <span>
                                {" "}
                                (
                                {item && item.address && item.address.country
                                ? `${item.address.country.title}`
                                : ""}
                                )
                            </span>
                            </li>
                        </ul>
                        ))
                    ) : (
                        <span className="text-center">{t("noRecordFound")}</span>
                    )}
                    </Card.Body>
                </Card>
            </div>
        </>
    );
};
export default HazardOrganisation;