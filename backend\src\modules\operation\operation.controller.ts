//Import Library
import {
  <PERSON>,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Req,
  Patch,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ACGuard,
  InjectRolesBuilder,
  <PERSON>sBuilder,
  UseRoles,
} from 'nest-access-control';

//Import services/components
import { CreateOperationDto } from './dto/create-operation.dto';
import { UpdateOperationDto } from './dto/update-operation.dto';
import { OperationService } from './operation.service';
import { ImageService } from './../image/image.service';
import { InstitutionService } from './../institution/institution.service';
import { EmailService } from './../../email.service';
import { SessionGuard } from 'src/auth/session-guard';
import { ResponseError } from '../../common/dto/response.dto';
import { FilesService } from '../files/files.service';
import { OperationInterface } from 'src/interfaces/operation.interface';
import { FlagService } from '../flag/flag.service';
import { UpdateService } from '../updates/update.service';

@Controller('operation')
@UseGuards(SessionGuard)
export class OperationController {
  constructor(
    private readonly _operationService: OperationService,
    private readonly _imageService: ImageService,
    private readonly _emailService: EmailService,
    private readonly _institutionService: InstitutionService,
    private readonly _filesService: FilesService,
    private readonly _flagService: FlagService,
    private readonly _updateService: UpdateService,

    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) {}

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'operation',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(
    @Body() createOperationDto: CreateOperationDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    createOperationDto['user'] = user._id;
    const _operation = await this._operationService.create(createOperationDto);
    const imageIds = _operation['images']
      ? _operation['images'].map((d) => d._id)
      : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
    const documentIds = _operation['document']
      ? _operation['document'].map((d) => d['_id'])
      : [];
    if (documentIds.length > 0) {
      await this._filesService.bulkUpdate(documentIds);
    }
    await this.operationParterner(_operation, createOperationDto);
    return _operation;
  }
  private async operationParterner(
    _operation: OperationInterface,
    createOperationDto: CreateOperationDto,
  ) {
    if (_operation.partners && _operation.partners.length > 0) {
      const instIds = [];
      _operation.partners.forEach((d) => {
        if (d.institution) {
          instIds.push(d.institution);
        }
      });
      await this.foundInstutionlength(instIds, createOperationDto, _operation);
    }
  }
  private async foundInstutionlength(
    instIds: any[],
    createOperationDto: CreateOperationDto,
    _operation: OperationInterface,
  ) {
    if (instIds.length > 0) {
      const foundInstitutions: any = await this._institutionService.find({
        _id: { $in: instIds },
      });
      if (foundInstitutions && foundInstitutions.length > 0) {
        const languageCode = createOperationDto['language']
          ? createOperationDto['language']
          : 'en';
        foundInstitutions.forEach((d) => {
          if (d.focal_points && d.focal_points.length > 0) {
            d.focal_points.forEach(async (e) => {
              await this._emailService.operationInvite(
                e,
                _operation,
                languageCode,
              );
            });
          }
        });
      }
    }
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'operation',
    action: 'read',
    possession: 'any',
  })
  @Get()
  async findAll(@Query() query: any) {
    return this._operationService.findAll(query);
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'operation',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') operationId: string, @Req() request: Request) {
    if (request?.query?.Doctable) {
      return this._operationService.getSortedDocList(
        operationId,
        request.query,
      );
    } else if (request?.query?.DocUpdatetable) {
      return this._operationService.getSortedUpdateDocList(
        operationId,
        request.query,
      );
    } else {
      return this._operationService.get(operationId);
    }
  }

  @UseGuards(ACGuard)
  @Patch(':id')
  async update(
    @Param('id') operationId: string,
    @Body() updateOperationDto: UpdateOperationDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    const oldOperation: any = await this._operationService.get(operationId);
    const operationUserId = oldOperation.user ? oldOperation.user._id : null;
    const permission =
      user._id == operationUserId
        ? this.roleBuilder.can(user.roles).updateOwn('operation').granted
        : this.roleBuilder.can(user.roles).updateAny('operation').granted;
    if (permission) {
      const _operation = await this._operationService.update(
        operationId,
        updateOperationDto,
      );
      const imageIds = _operation['images']
        ? _operation['images'].map((d) => d._id)
        : [];
      if (imageIds.length > 0) {
        await this._imageService.bulkUpdate(imageIds);
      }
      const documentIds = _operation['document']
        ? _operation['document'].map((d) => d['_id'])
        : [];
      if (documentIds.length > 0) {
        await this._filesService.bulkUpdate(documentIds);
      }
      return _operation;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  @UseGuards(ACGuard)
  @Delete(':id')
  async remove(@Param('id') operationId: string, @Req() request: Request) {
    try {
      const operation: any = await this._operationService.get(operationId);
      const user: any = request.user;
      const operationUserId = operation.user ? operation.user._id : null;
      const permission =
        user._id == operationUserId
          ? this.roleBuilder.can(user.roles).deleteOwn('operation').granted
          : this.roleBuilder.can(user.roles).deleteAny('operation').granted;
      if (permission) {
        this._flagService.deleteFlagAssociatedWithEntity(operationId);
        this._updateService.deleteUpdateAssociatedWithEntity(operationId, "parent_operation");
        return this._operationService.delete(operationId);
      } else {
        return new ResponseError('OPERATION.ERROR.ACCESS_DENIED');
      }
    } catch (e) {
      return new ResponseError('OPERATION.ERROR.OPERATION_NOT_FOUND');
    }
  }
}
