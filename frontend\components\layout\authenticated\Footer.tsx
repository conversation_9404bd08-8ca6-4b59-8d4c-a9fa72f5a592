//Import Library
import { Container, <PERSON>, <PERSON> } from "react-bootstrap";
import Modal from "react-bootstrap/Modal";
import Link from "next/link";
import { useState } from "react";

//Import services/components
import FooterImprintContent from "./FooterImprintContent";
import { useTranslation } from 'next-i18next';

interface ImprintModalProps {
  show: boolean;
  onHide: () => void;
  dialogClassName?: string;
}

function ImprintModal(props: ImprintModalProps) {
  const { dialogClassName } = props;
  const { t } = useTranslation('common');
  return (
    <Modal
      {...props}
      size="lg"
      dialogClassName={dialogClassName}
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Modal.Header closeButton>
        <Modal.Title id="contained-modal-title-vcenter">
          {t("imprintHeader.tab")}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <FooterImprintContent />
      </Modal.Body>
    </Modal>
  );
}

interface FooterProps {
  [key: string]: any;
}

function Footer(props: FooterProps) {
  const [showImprint, setShowImprint] = useState(false);
  const { t } = useTranslation('common');
  return (
    <Container fluid className="footer">
      <Row>
        <Col md="5">
          <span style={{ fontFamily: "verdana" }}>&copy;</span>{" "}
          {new Date().getFullYear()} {t("RobertKochInstitute")}
        </Col>
        <Col md="7" style={{ textAlign: "right" }}>
          <a href="#" onClick={() => setShowImprint(true)}>
            {" "}
            {t("Imprint")}{" "}
          </a>{" "}
          |{" "}
          <Link href={"/data-privacy-policy"} as={"/data-privacy-policy"}>
            {t("DataPrivacyPolicy")}
          </Link>
          <span> | {t("Allrightsreservedunlessexplicitlygranted")}</span>
        </Col>
        <ImprintModal
          {...props}
          show={showImprint}
          onHide={() => setShowImprint(false)}
        />
      </Row>
    </Container>
  );
}

export default Footer;
