//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { PageCategoryInterface } from '../../interfaces/page-category.interface';
import { CreatePageCategoryDto } from './dto/create-page-category.dto';
import { UpdatePageCategoryDto } from './dto/update-page-category..dto';
const FindEventStatus='Could not find Event Status.'
@Injectable()
export class PageCategoryService {
  constructor(
    @InjectModel('PageCategory') private pageCategoryModel: Model<PageCategoryInterface>
  ) { }

  async create(createPageCategoryDto: CreatePageCategoryDto): Promise<PageCategoryInterface> {
    const createdPageCategory = new this.pageCategoryModel(createPageCategoryDto);
    return createdPageCategory.save();
  }

  async findAll(query): Promise<PageCategoryInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.pageCategoryModel.paginate(_filter, options);
  }

  async get(pageCategoryId): Promise<PageCategoryInterface[]> {
    let _result;
    try {
      _result = await this.pageCategoryModel.findById(pageCategoryId).exec();
    } catch (error) {
      throw new NotFoundException(FindEventStatus);
    }
    if (!_result) {
      throw new NotFoundException(FindEventStatus);
    }
    return _result;
  }

  async update(pageCategoryId: any, updatePageCategoryDto: UpdatePageCategoryDto) {
    const getById: any = await this.pageCategoryModel.findById(pageCategoryId).exec();
    const updatedData = new this.pageCategoryModel(updatePageCategoryDto);
    try {
      Object.keys(updatePageCategoryDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Event Status.');
    }
    return getById;
  }

  async delete(pageCategoryId: string) {
    const result = await this.pageCategoryModel.deleteOne({ _id: pageCategoryId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindEventStatus);
    }
  }
}
