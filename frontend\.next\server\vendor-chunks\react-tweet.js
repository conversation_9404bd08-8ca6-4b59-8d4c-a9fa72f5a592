/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-tweet";
exports.ids = ["vendor-chunks/react-tweet"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/fetch-tweet.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/fetch-tweet.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TwitterApiError: () => (/* binding */ TwitterApiError),\n/* harmony export */   fetchTweet: () => (/* binding */ fetchTweet)\n/* harmony export */ });\nconst SYNDICATION_URL = 'https://cdn.syndication.twimg.com';\nclass TwitterApiError extends Error {\n    constructor({ message, status, data }){\n        super(message);\n        this.name = 'TwitterApiError';\n        this.status = status;\n        this.data = data;\n    }\n}\nconst TWEET_ID = /^[0-9]+$/;\nfunction getToken(id) {\n    return (Number(id) / 1e15 * Math.PI).toString(6 ** 2).replace(/(0+|\\.)/g, '');\n}\n/**\n * Fetches a tweet from the Twitter syndication API.\n */ async function fetchTweet(id, fetchOptions) {\n    var _res_headers_get;\n    if (id.length > 40 || !TWEET_ID.test(id)) {\n        throw new Error(`Invalid tweet id: ${id}`);\n    }\n    const url = new URL(`${SYNDICATION_URL}/tweet-result`);\n    url.searchParams.set('id', id);\n    url.searchParams.set('lang', 'en');\n    url.searchParams.set('features', [\n        'tfw_timeline_list:',\n        'tfw_follower_count_sunset:true',\n        'tfw_tweet_edit_backend:on',\n        'tfw_refsrc_session:on',\n        'tfw_fosnr_soft_interventions_enabled:on',\n        'tfw_show_birdwatch_pivots_enabled:on',\n        'tfw_show_business_verified_badge:on',\n        'tfw_duplicate_scribes_to_settings:on',\n        'tfw_use_profile_image_shape_enabled:on',\n        'tfw_show_blue_verified_badge:on',\n        'tfw_legacy_timeline_sunset:true',\n        'tfw_show_gov_verified_badge:on',\n        'tfw_show_business_affiliate_badge:on',\n        'tfw_tweet_edit_frontend:on'\n    ].join(';'));\n    url.searchParams.set('token', getToken(id));\n    const res = await fetch(url.toString(), fetchOptions);\n    const isJson = (_res_headers_get = res.headers.get('content-type')) == null ? void 0 : _res_headers_get.includes('application/json');\n    const data = isJson ? await res.json() : undefined;\n    if (res.ok) {\n        if ((data == null ? void 0 : data.__typename) === 'TweetTombstone') {\n            return {\n                tombstone: true\n            };\n        }\n        return {\n            data\n        };\n    }\n    if (res.status === 404) {\n        return {\n            notFound: true\n        };\n    }\n    throw new TwitterApiError({\n        message: typeof data.error === 'string' ? data.error : `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        status: res.status,\n        data\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/fetch-tweet.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/get-oembed.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/get-oembed.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOEmbed: () => (/* binding */ getOEmbed)\n/* harmony export */ });\nasync function getOEmbed(url) {\n    const res = await fetch(`https://publish.twitter.com/oembed?url=${url}`);\n    if (res.ok) return res.json();\n    if (res.status === 404) return;\n    throw new Error(`Fetch for embedded tweet failed with code: ${res.status}`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS9nZXQtb2VtYmVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxlQUFlQSxVQUFVQyxHQUFHO0lBQy9CLE1BQU1DLE1BQU0sTUFBTUMsTUFBTSxDQUFDLHVDQUF1QyxFQUFFRixLQUFLO0lBQ3ZFLElBQUlDLElBQUlFLEVBQUUsRUFBRSxPQUFPRixJQUFJRyxJQUFJO0lBQzNCLElBQUlILElBQUlJLE1BQU0sS0FBSyxLQUFLO0lBQ3hCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLDJDQUEyQyxFQUFFTCxJQUFJSSxNQUFNLEVBQUU7QUFDOUUiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxhcGlcXGdldC1vZW1iZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldE9FbWJlZCh1cmwpIHtcbiAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaChgaHR0cHM6Ly9wdWJsaXNoLnR3aXR0ZXIuY29tL29lbWJlZD91cmw9JHt1cmx9YCk7XG4gICAgaWYgKHJlcy5vaykgcmV0dXJuIHJlcy5qc29uKCk7XG4gICAgaWYgKHJlcy5zdGF0dXMgPT09IDQwNCkgcmV0dXJuO1xuICAgIHRocm93IG5ldyBFcnJvcihgRmV0Y2ggZm9yIGVtYmVkZGVkIHR3ZWV0IGZhaWxlZCB3aXRoIGNvZGU6ICR7cmVzLnN0YXR1c31gKTtcbn1cbiJdLCJuYW1lcyI6WyJnZXRPRW1iZWQiLCJ1cmwiLCJyZXMiLCJmZXRjaCIsIm9rIiwianNvbiIsInN0YXR1cyIsIkVycm9yIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/get-oembed.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/get-tweet.js":
/*!********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/get-tweet.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTweet: () => (/* binding */ getTweet)\n/* harmony export */ });\n/* harmony import */ var _fetch_tweet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fetch-tweet.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/fetch-tweet.js\");\n\n/**\n * Returns a tweet from the Twitter syndication API.\n */ async function getTweet(id, fetchOptions) {\n    const { data, tombstone, notFound } = await (0,_fetch_tweet_js__WEBPACK_IMPORTED_MODULE_0__.fetchTweet)(id, fetchOptions);\n    if (notFound) {\n        console.error(`The tweet ${id} does not exist or has been deleted by the account owner. Update your code to remove this tweet when possible.`);\n    } else if (tombstone) {\n        console.error(`The tweet ${id} has been made private by the account owner. Update your code to remove this tweet when possible.`);\n    }\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS9nZXQtdHdlZXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDOUM7O0NBRUMsR0FBVSxlQUFlQyxTQUFTQyxFQUFFLEVBQUVDLFlBQVk7SUFDL0MsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsUUFBUSxFQUFFLEdBQUcsTUFBTU4sMkRBQVVBLENBQUNFLElBQUlDO0lBQzNELElBQUlHLFVBQVU7UUFDVkMsUUFBUUMsS0FBSyxDQUFDLENBQUMsVUFBVSxFQUFFTixHQUFHLDhHQUE4RyxDQUFDO0lBQ2pKLE9BQU8sSUFBSUcsV0FBVztRQUNsQkUsUUFBUUMsS0FBSyxDQUFDLENBQUMsVUFBVSxFQUFFTixHQUFHLGlHQUFpRyxDQUFDO0lBQ3BJO0lBQ0EsT0FBT0U7QUFDWCIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXGFwaVxcZ2V0LXR3ZWV0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZldGNoVHdlZXQgfSBmcm9tICcuL2ZldGNoLXR3ZWV0LmpzJztcbi8qKlxuICogUmV0dXJucyBhIHR3ZWV0IGZyb20gdGhlIFR3aXR0ZXIgc3luZGljYXRpb24gQVBJLlxuICovIGV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRUd2VldChpZCwgZmV0Y2hPcHRpb25zKSB7XG4gICAgY29uc3QgeyBkYXRhLCB0b21ic3RvbmUsIG5vdEZvdW5kIH0gPSBhd2FpdCBmZXRjaFR3ZWV0KGlkLCBmZXRjaE9wdGlvbnMpO1xuICAgIGlmIChub3RGb3VuZCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBUaGUgdHdlZXQgJHtpZH0gZG9lcyBub3QgZXhpc3Qgb3IgaGFzIGJlZW4gZGVsZXRlZCBieSB0aGUgYWNjb3VudCBvd25lci4gVXBkYXRlIHlvdXIgY29kZSB0byByZW1vdmUgdGhpcyB0d2VldCB3aGVuIHBvc3NpYmxlLmApO1xuICAgIH0gZWxzZSBpZiAodG9tYnN0b25lKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYFRoZSB0d2VldCAke2lkfSBoYXMgYmVlbiBtYWRlIHByaXZhdGUgYnkgdGhlIGFjY291bnQgb3duZXIuIFVwZGF0ZSB5b3VyIGNvZGUgdG8gcmVtb3ZlIHRoaXMgdHdlZXQgd2hlbiBwb3NzaWJsZS5gKTtcbiAgICB9XG4gICAgcmV0dXJuIGRhdGE7XG59XG4iXSwibmFtZXMiOlsiZmV0Y2hUd2VldCIsImdldFR3ZWV0IiwiaWQiLCJmZXRjaE9wdGlvbnMiLCJkYXRhIiwidG9tYnN0b25lIiwibm90Rm91bmQiLCJjb25zb2xlIiwiZXJyb3IiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/get-tweet.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/index.js":
/*!****************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types/index.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _fetch_tweet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fetch-tweet.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/fetch-tweet.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _fetch_tweet_js__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _fetch_tweet_js__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _get_tweet_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./get-tweet.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/get-tweet.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _get_tweet_js__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _get_tweet_js__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _get_oembed_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./get-oembed.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/get-oembed.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _get_oembed_js__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _get_oembed_js__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNBO0FBQ0Y7QUFDQyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXGFwaVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi90eXBlcy9pbmRleC5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2ZldGNoLXR3ZWV0LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZ2V0LXR3ZWV0LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZ2V0LW9lbWJlZC5qcyc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/edit.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/edit.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy9lZGl0LmpzIiwibWFwcGluZ3MiOiI7QUFBVyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXGFwaVxcdHlwZXNcXGVkaXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/edit.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/entities.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/entities.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy9lbnRpdGllcy5qcyIsIm1hcHBpbmdzIjoiO0FBQVciLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxhcGlcXHR5cGVzXFxlbnRpdGllcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/entities.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _edit_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/edit.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _edit_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _edit_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _entities_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./entities.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/entities.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _entities_js__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _entities_js__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _media_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./media.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/media.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _media_js__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _media_js__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _photo_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./photo.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/photo.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _photo_js__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _photo_js__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/tweet.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_js__WEBPACK_IMPORTED_MODULE_4__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_js__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _user_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./user.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/user.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _user_js__WEBPACK_IMPORTED_MODULE_5__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _user_js__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _video_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./video.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/types/video.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _video_js__WEBPACK_IMPORTED_MODULE_6__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _video_js__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUNJO0FBQ0g7QUFDQTtBQUNBO0FBQ0Q7QUFDQyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXGFwaVxcdHlwZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vZWRpdC5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2VudGl0aWVzLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vbWVkaWEuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9waG90by5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R3ZWV0LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdXNlci5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3ZpZGVvLmpzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/media.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/media.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy9tZWRpYS5qcyIsIm1hcHBpbmdzIjoiO0FBQVciLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxhcGlcXHR5cGVzXFxtZWRpYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/media.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/photo.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/photo.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy9waG90by5qcyIsIm1hcHBpbmdzIjoiO0FBQVciLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxhcGlcXHR5cGVzXFxwaG90by5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/photo.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/tweet.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/tweet.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/**\n * A tweet quoted by another tweet.\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy90d2VldC5qcyIsIm1hcHBpbmdzIjoiO0FBQUE7O0NBRUMsR0FBYyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXGFwaVxcdHlwZXNcXHR3ZWV0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQSB0d2VldCBxdW90ZWQgYnkgYW5vdGhlciB0d2VldC5cbiAqLyBleHBvcnQgeyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/tweet.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/user.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/user.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy91c2VyLmpzIiwibWFwcGluZ3MiOiI7QUFBVyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXGFwaVxcdHlwZXNcXHVzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/user.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/api/types/video.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/types/video.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2FwaS90eXBlcy92aWRlby5qcyIsIm1hcHBpbmdzIjoiO0FBQVciLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxhcGlcXHR5cGVzXFx2aWRlby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/api/types/video.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/date-utils.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-tweet/dist/date-utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDate: () => (/* binding */ formatDate)\n/* harmony export */ });\nconst options = {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n    weekday: 'short',\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric'\n};\nconst formatter = new Intl.DateTimeFormat('en-US', options);\nconst partsArrayToObject = (parts)=>{\n    const result = {};\n    for (const part of parts){\n        result[part.type] = part.value;\n    }\n    return result;\n};\nconst formatDate = (date)=>{\n    const parts = partsArrayToObject(formatter.formatToParts(date));\n    const formattedTime = `${parts.hour}:${parts.minute} ${parts.dayPeriod}`;\n    const formattedDate = `${parts.month} ${parts.day}, ${parts.year}`;\n    return `${formattedTime} · ${formattedDate}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/date-utils.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/hooks.js":
/*!************************************************!*\
  !*** ./node_modules/react-tweet/dist/hooks.js ***!
  \************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMounted: () => (/* binding */ useMounted),\n/* harmony export */   useTweet: () => (/* binding */ useTweet)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swr */ \"swr\");\n/* harmony import */ var _api_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api/index.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/api/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_1__]);\nswr__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ useTweet,useMounted auto */ \n\n\n// Avoids an error when used in the pages directory where useSWR might be in `default`.\nconst useSWR = swr__WEBPACK_IMPORTED_MODULE_1__[\"default\"][\"default\"] || swr__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\nconst host = 'https://react-tweet.vercel.app';\nasync function fetcher([url, fetchOptions]) {\n    const res = await fetch(url, fetchOptions);\n    const json = await res.json();\n    // We return null in case `json.data` is undefined, that way we can check for \"loading\" by\n    // checking if data is `undefined`. `null` means it was fetched.\n    if (res.ok) return json.data || null;\n    throw new _api_index_js__WEBPACK_IMPORTED_MODULE_2__.TwitterApiError({\n        message: `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        data: json,\n        status: res.status\n    });\n}\n/**\n * SWR hook for fetching a tweet in the browser.\n */ const useTweet = (id, apiUrl, fetchOptions)=>{\n    const { isLoading, data, error } = useSWR({\n        \"useTweet.useSWR\": ()=>apiUrl || id ? [\n                apiUrl || id && `${host}/api/tweet/${id}`,\n                fetchOptions\n            ] : null\n    }[\"useTweet.useSWR\"], fetcher, {\n        revalidateIfStale: false,\n        revalidateOnFocus: false,\n        shouldRetryOnError: false\n    });\n    return {\n        // If data is `undefined` then it might be the first render where SWR hasn't started doing\n        // any work, so we set `isLoading` to `true`.\n        isLoading: Boolean(isLoading || data === undefined && !error),\n        data,\n        error\n    };\n};\nconst useMounted = ()=>{\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMounted.useEffect\": ()=>setMounted(true)\n    }[\"useMounted.useEffect\"], []);\n    return mounted;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/hooks.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/index.client.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-tweet/dist/index.client.js ***!
  \*******************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/components.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _swr_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./swr.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/swr.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _utils_js__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _utils_js__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _hooks_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hooks.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/hooks.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_swr_js__WEBPACK_IMPORTED_MODULE_1__, _hooks_js__WEBPACK_IMPORTED_MODULE_3__]);\n([_swr_js__WEBPACK_IMPORTED_MODULE_1__, _hooks_js__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _swr_js__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _swr_js__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _hooks_js__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _hooks_js__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L2luZGV4LmNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNyQjtBQUNFO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxpbmRleC5jbGllbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi90d2l0dGVyLXRoZW1lL2NvbXBvbmVudHMuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9zd3IuanMnO1xuZXhwb3J0ICogZnJvbSAnLi91dGlscy5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2hvb2tzLmpzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/index.client.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/swr.js":
/*!**********************************************!*\
  !*** ./node_modules/react-tweet/dist/swr.js ***!
  \**********************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tweet: () => (/* binding */ Tweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/components.js\");\n/* harmony import */ var _hooks_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hooks.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/hooks.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_hooks_js__WEBPACK_IMPORTED_MODULE_2__]);\n_hooks_js__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ Tweet auto */ \n\n\nconst Tweet = ({ id, apiUrl, fallback = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__.TweetSkeleton, {}), components, fetchOptions, onError })=>{\n    const { data, error, isLoading } = (0,_hooks_js__WEBPACK_IMPORTED_MODULE_2__.useTweet)(id, apiUrl, fetchOptions);\n    if (isLoading) return fallback;\n    if (error || !data) {\n        const NotFound = (components == null ? void 0 : components.TweetNotFound) || _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__.TweetNotFound;\n        return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NotFound, {\n            error: onError ? onError(error) : error\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__.EmbeddedTweet, {\n        tweet: data,\n        components: components\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/swr.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/avatar-img.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AvatarImg: () => (/* binding */ AvatarImg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nconst AvatarImg = (props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", {\n        ...props\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvYXZhdGFyLWltZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDtBQUNoRCxtRkFBbUY7QUFDNUUsTUFBTUUsWUFBWSxDQUFDQyxRQUFRLFdBQVcsR0FBR0Ysc0RBQUlBLENBQUMsT0FBTztRQUNwRCxHQUFHRSxLQUFLO0lBQ1osR0FBRyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXGF2YXRhci1pbWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBqc3gtYTExeS9hbHQtdGV4dCAtLSBUaGUgYWx0IHRleHQgaXMgcGFydCBvZiBgLi4ucHJvcHNgXG5leHBvcnQgY29uc3QgQXZhdGFySW1nID0gKHByb3BzKT0+LyojX19QVVJFX18qLyBfanN4KFwiaW1nXCIsIHtcbiAgICAgICAgLi4ucHJvcHNcbiAgICB9KTtcbiJdLCJuYW1lcyI6WyJqc3giLCJfanN4IiwiQXZhdGFySW1nIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/components.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/components.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/types.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/index.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _icons_index_js__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _icons_index_js__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _embedded_tweet_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./embedded-tweet.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _embedded_tweet_js__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _embedded_tweet_js__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-actions-copy.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_actions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet-actions.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_actions_js__WEBPACK_IMPORTED_MODULE_4__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_actions_js__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_body_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tweet-body.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_body_js__WEBPACK_IMPORTED_MODULE_5__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_body_js__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tweet-container.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_container_js__WEBPACK_IMPORTED_MODULE_6__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_container_js__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_header_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tweet-header.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_header_js__WEBPACK_IMPORTED_MODULE_7__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_header_js__WEBPACK_IMPORTED_MODULE_7__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tweet-in-reply-to.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_8__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_8__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tweet-info-created-at.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_9__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_9__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_info_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./tweet-info.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_info_js__WEBPACK_IMPORTED_MODULE_10__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_info_js__WEBPACK_IMPORTED_MODULE_10__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_link_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./tweet-link.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_link_js__WEBPACK_IMPORTED_MODULE_11__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_link_js__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_media_video_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./tweet-media-video.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_media_video_js__WEBPACK_IMPORTED_MODULE_12__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_media_video_js__WEBPACK_IMPORTED_MODULE_12__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_media_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./tweet-media.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_media_js__WEBPACK_IMPORTED_MODULE_13__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_media_js__WEBPACK_IMPORTED_MODULE_13__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_not_found_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./tweet-not-found.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_not_found_js__WEBPACK_IMPORTED_MODULE_14__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_not_found_js__WEBPACK_IMPORTED_MODULE_14__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_replies_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./tweet-replies.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_replies_js__WEBPACK_IMPORTED_MODULE_15__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_replies_js__WEBPACK_IMPORTED_MODULE_15__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _tweet_skeleton_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./tweet-skeleton.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tweet_skeleton_js__WEBPACK_IMPORTED_MODULE_16__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tweet_skeleton_js__WEBPACK_IMPORTED_MODULE_16__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./quoted-tweet/index.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_17__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_17__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvY29tcG9uZW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJCO0FBQ007QUFDRztBQUNJO0FBQ0w7QUFDSDtBQUNLO0FBQ0g7QUFDSztBQUNJO0FBQ1g7QUFDQTtBQUNPO0FBQ047QUFDSTtBQUNGO0FBQ0M7QUFDSSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXGNvbXBvbmVudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi90eXBlcy5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2ljb25zL2luZGV4LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZW1iZWRkZWQtdHdlZXQuanMnO1xuZXhwb3J0ICogZnJvbSAnLi90d2VldC1hY3Rpb25zLWNvcHkuanMnO1xuZXhwb3J0ICogZnJvbSAnLi90d2VldC1hY3Rpb25zLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHdlZXQtYm9keS5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R3ZWV0LWNvbnRhaW5lci5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R3ZWV0LWhlYWRlci5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R3ZWV0LWluLXJlcGx5LXRvLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHdlZXQtaW5mby1jcmVhdGVkLWF0LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHdlZXQtaW5mby5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R3ZWV0LWxpbmsuanMnO1xuZXhwb3J0ICogZnJvbSAnLi90d2VldC1tZWRpYS12aWRlby5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R3ZWV0LW1lZGlhLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHdlZXQtbm90LWZvdW5kLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHdlZXQtcmVwbGllcy5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R3ZWV0LXNrZWxldG9uLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vcXVvdGVkLXR3ZWV0L2luZGV4LmpzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/components.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmbeddedTweet: () => (/* binding */ EmbeddedTweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _tweet_header_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet-header.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.js\");\n/* harmony import */ var _tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tweet-in-reply-to.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js\");\n/* harmony import */ var _tweet_body_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tweet-body.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.js\");\n/* harmony import */ var _tweet_media_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tweet-media.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js\");\n/* harmony import */ var _tweet_info_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tweet-info.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.js\");\n/* harmony import */ var _tweet_actions_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./tweet-actions.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js\");\n/* harmony import */ var _tweet_replies_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./tweet-replies.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js\");\n/* harmony import */ var _quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quoted-tweet/index.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/index.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst EmbeddedTweet = ({ tweet: t, components })=>{\n    var _tweet_mediaDetails;\n    // useMemo does nothing for RSC but it helps when the component is used in the client (e.g by SWR)\n    const tweet = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"EmbeddedTweet.useMemo[tweet]\": ()=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.enrichTweet)(t)\n    }[\"EmbeddedTweet.useMemo[tweet]\"], [\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_header_js__WEBPACK_IMPORTED_MODULE_4__.TweetHeader, {\n                tweet: tweet,\n                components: components\n            }),\n            tweet.in_reply_to_status_id_str && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_5__.TweetInReplyTo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_body_js__WEBPACK_IMPORTED_MODULE_6__.TweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_js__WEBPACK_IMPORTED_MODULE_7__.TweetMedia, {\n                tweet: tweet,\n                components: components\n            }) : null,\n            tweet.quoted_tweet && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_8__.QuotedTweet, {\n                tweet: tweet.quoted_tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_info_js__WEBPACK_IMPORTED_MODULE_9__.TweetInfo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_actions_js__WEBPACK_IMPORTED_MODULE_10__.TweetActions, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_replies_js__WEBPACK_IMPORTED_MODULE_11__.TweetReplies, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css":
/*!****************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css ***!
  \****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"verified\": \"icons_verified__1eJnA\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvaWNvbnMvaWNvbnMubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXGljb25zXFxpY29ucy5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInZlcmlmaWVkXCI6IFwiaWNvbnNfdmVyaWZpZWRfXzFlSm5BXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _verified_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./verified.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _verified_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _verified_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _verified_business_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./verified-business.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _verified_business_js__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _verified_business_js__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _verified_government_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./verified-government.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _verified_government_js__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _verified_government_js__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvaWNvbnMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUNTO0FBQ0UiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxpY29uc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi92ZXJpZmllZC5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3ZlcmlmaWVkLWJ1c2luZXNzLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdmVyaWZpZWQtZ292ZXJubWVudC5qcyc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js":
/*!********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedBusiness: () => (/* binding */ VerifiedBusiness)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst VerifiedBusiness = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"g\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-a\",\n                    x1: \"4.411\",\n                    x2: \"18.083\",\n                    y1: \"2.495\",\n                    y2: \"21.508\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f4e72a\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".539\",\n                            stopColor: \"#cd8105\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".68\",\n                            stopColor: \"#cb7b00\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4ec26\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4e72a\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-b\",\n                    x1: \"5.355\",\n                    x2: \"16.361\",\n                    y1: \"3.395\",\n                    y2: \"19.133\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f9e87f\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".406\",\n                            stopColor: \"#e2b719\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".989\",\n                            stopColor: \"#e2b719\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"g\", {\n                    clipRule: \"evenodd\",\n                    fillRule: \"evenodd\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.324 3.848L11 1.6 8.676 3.848l-3.201-.453-.559 3.184L2.06 8.095 3.48 11l-1.42 2.904 2.856 1.516.559 3.184 3.201-.452L11 20.4l2.324-2.248 3.201.452.559-3.184 2.856-1.516L18.52 11l1.42-2.905-2.856-1.516-.559-3.184zm-7.09 7.575l3.428 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-a)\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.101 4.533L11 2.5 8.899 4.533l-2.895-.41-.505 2.88-2.583 1.37L4.2 11l-1.284 2.627 2.583 1.37.505 2.88 2.895-.41L11 19.5l2.101-2.033 2.895.41.505-2.88 2.583-1.37L17.8 11l1.284-2.627-2.583-1.37-.505-2.88zm-6.868 6.89l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-b)\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M6.233 11.423l3.429 3.428 5.65-6.17.038-.033-.005 1.398-5.683 6.206-3.429-3.429-.003-1.405.005.003z\",\n                            fill: \"#d18800\"\n                        })\n                    ]\n                })\n            ]\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedGovernment: () => (/* binding */ VerifiedGovernment)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst VerifiedGovernment = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                clipRule: \"evenodd\",\n                d: \"M12.05 2.056c-.568-.608-1.532-.608-2.1 0l-1.393 1.49c-.284.303-.685.47-1.1.455L5.42 3.932c-.832-.028-1.514.654-1.486 1.486l.069 2.039c.014.415-.152.816-.456 1.1l-1.49 1.392c-.608.568-.608 1.533 0 2.101l1.49 1.393c.304.284.47.684.456 1.1l-.07 2.038c-.027.832.655 1.514 1.487 1.486l2.038-.069c.415-.014.816.152 1.1.455l1.392 1.49c.569.609 1.533.609 2.102 0l1.393-1.49c.283-.303.684-.47 1.099-.455l2.038.069c.832.028 1.515-.654 1.486-1.486L18 14.542c-.015-.415.152-.815.455-1.099l1.49-1.393c.608-.568.608-1.533 0-2.101l-1.49-1.393c-.303-.283-.47-.684-.455-1.1l.068-2.038c.029-.832-.654-1.514-1.486-1.486l-2.038.07c-.415.013-.816-.153-1.1-.456zm-5.817 9.367l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                fillRule: \"evenodd\"\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/verified.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Verified: () => (/* binding */ Verified)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst Verified = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 24 24\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: \"M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z\"\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvaWNvbnMvdmVyaWZpZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEO0FBQ2I7QUFDNUIsTUFBTUcsV0FBVyxJQUFJLFdBQVcsR0FBR0Ysc0RBQUlBLENBQUMsT0FBTztRQUM5Q0csU0FBUztRQUNULGNBQWM7UUFDZEMsTUFBTTtRQUNOQyxXQUFXSix1REFBVTtRQUNyQk0sVUFBVSxXQUFXLEdBQUdQLHNEQUFJQSxDQUFDLEtBQUs7WUFDOUJPLFVBQVUsV0FBVyxHQUFHUCxzREFBSUEsQ0FBQyxRQUFRO2dCQUNqQ1EsR0FBRztZQUNQO1FBQ0o7SUFDSixHQUFHIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcaWNvbnNcXHZlcmlmaWVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgcyBmcm9tICcuL2ljb25zLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFZlcmlmaWVkID0gKCk9Pi8qI19fUFVSRV9fKi8gX2pzeChcInN2Z1wiLCB7XG4gICAgICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgICAgIFwiYXJpYS1sYWJlbFwiOiBcIlZlcmlmaWVkIGFjY291bnRcIixcbiAgICAgICAgcm9sZTogXCJpbWdcIixcbiAgICAgICAgY2xhc3NOYW1lOiBzLnZlcmlmaWVkLFxuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwiZ1wiLCB7XG4gICAgICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwicGF0aFwiLCB7XG4gICAgICAgICAgICAgICAgZDogXCJNMjIuMjUgMTJjMC0xLjQzLS44OC0yLjY3LTIuMTktMy4zNC40Ni0xLjM5LjItMi45LS44MS0zLjkxcy0yLjUyLTEuMjctMy45MS0uODFjLS42Ni0xLjMxLTEuOTEtMi4xOS0zLjM0LTIuMTlzLTIuNjcuODgtMy4zMyAyLjE5Yy0xLjQtLjQ2LTIuOTEtLjItMy45Mi44MXMtMS4yNiAyLjUyLS44IDMuOTFjLTEuMzEuNjctMi4yIDEuOTEtMi4yIDMuMzRzLjg5IDIuNjcgMi4yIDMuMzRjLS40NiAxLjM5LS4yMSAyLjkuOCAzLjkxczIuNTIgMS4yNiAzLjkxLjgxYy42NyAxLjMxIDEuOTEgMi4xOSAzLjM0IDIuMTlzMi42OC0uODggMy4zNC0yLjE5YzEuMzkuNDUgMi45LjIgMy45MS0uODFzMS4yNy0yLjUyLjgxLTMuOTFjMS4zMS0uNjcgMi4xOS0xLjkxIDIuMTktMy4zNHptLTExLjcxIDQuMkw2LjggMTIuNDZsMS40MS0xLjQyIDIuMjYgMi4yNiA0LjgtNS4yMyAxLjQ3IDEuMzYtNi4yIDYuNzd6XCJcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0pXG4gICAgfSk7XG4iXSwibmFtZXMiOlsianN4IiwiX2pzeCIsInMiLCJWZXJpZmllZCIsInZpZXdCb3giLCJyb2xlIiwiY2xhc3NOYW1lIiwidmVyaWZpZWQiLCJjaGlsZHJlbiIsImQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/verified.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/media-img.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/media-img.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MediaImg: () => (/* binding */ MediaImg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nconst MediaImg = (props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", {\n        ...props\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvbWVkaWEtaW1nLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdEO0FBQ2hELG1GQUFtRjtBQUM1RSxNQUFNRSxXQUFXLENBQUNDLFFBQVEsV0FBVyxHQUFHRixzREFBSUEsQ0FBQyxPQUFPO1FBQ25ELEdBQUdFLEtBQUs7SUFDWixHQUFHIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcbWVkaWEtaW1nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUganN4LWExMXkvYWx0LXRleHQgLS0gVGhlIGFsdCB0ZXh0IGlzIHBhcnQgb2YgYC4uLnByb3BzYFxuZXhwb3J0IGNvbnN0IE1lZGlhSW1nID0gKHByb3BzKT0+LyojX19QVVJFX18qLyBfanN4KFwiaW1nXCIsIHtcbiAgICAgICAgLi4ucHJvcHNcbiAgICB9KTtcbiJdLCJuYW1lcyI6WyJqc3giLCJfanN4IiwiTWVkaWFJbWciLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/media-img.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _quoted_tweet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quoted-tweet.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _quoted_tweet_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _quoted_tweet_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-container.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quoted-tweet-header.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quoted-tweet-body.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvcXVvdGVkLXR3ZWV0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtDO0FBQ1U7QUFDSDtBQUNGIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxccXVvdGVkLXR3ZWV0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3F1b3RlZC10d2VldC5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3F1b3RlZC10d2VldC1jb250YWluZXIuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9xdW90ZWQtdHdlZXQtaGVhZGVyLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vcXVvdGVkLXR3ZWV0LWJvZHkuanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetBody: () => (/* binding */ QuotedTweetBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _quoted_tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-body.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css\");\n\n\nconst QuotedTweetBody = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n        className: _quoted_tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                dangerouslySetInnerHTML: {\n                    __html: item.text\n                }\n            }, i))\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvcXVvdGVkLXR3ZWV0L3F1b3RlZC10d2VldC1ib2R5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNEO0FBQ3hDLE1BQU1HLGtCQUFrQixDQUFDLEVBQUVDLEtBQUssRUFBRSxHQUFHLFdBQVcsR0FBR0gsc0RBQUlBLENBQUMsS0FBSztRQUM1REksV0FBV0gsK0RBQU07UUFDakJLLE1BQU1ILE1BQU1HLElBQUk7UUFDaEJDLEtBQUs7UUFDTEMsVUFBVUwsTUFBTU0sUUFBUSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsSUFBSSxXQUFXLEdBQUdaLHNEQUFJQSxDQUFDLFFBQVE7Z0JBQzNEYSx5QkFBeUI7b0JBQ3JCQyxRQUFRSCxLQUFLSSxJQUFJO2dCQUNyQjtZQUNKLEdBQUdIO0lBQ1gsR0FBRyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHF1b3RlZC10d2VldFxccXVvdGVkLXR3ZWV0LWJvZHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCBzIGZyb20gJy4vcXVvdGVkLXR3ZWV0LWJvZHkubW9kdWxlLmNzcyc7XG5leHBvcnQgY29uc3QgUXVvdGVkVHdlZXRCb2R5ID0gKHsgdHdlZXQgfSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcInBcIiwge1xuICAgICAgICBjbGFzc05hbWU6IHMucm9vdCxcbiAgICAgICAgbGFuZzogdHdlZXQubGFuZyxcbiAgICAgICAgZGlyOiBcImF1dG9cIixcbiAgICAgICAgY2hpbGRyZW46IHR3ZWV0LmVudGl0aWVzLm1hcCgoaXRlbSwgaSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcInNwYW5cIiwge1xuICAgICAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICAgICAgICAgICAgICAgIF9faHRtbDogaXRlbS50ZXh0XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgaSkpXG4gICAgfSk7XG4iXSwibmFtZXMiOlsianN4IiwiX2pzeCIsInMiLCJRdW90ZWRUd2VldEJvZHkiLCJ0d2VldCIsImNsYXNzTmFtZSIsInJvb3QiLCJsYW5nIiwiZGlyIiwiY2hpbGRyZW4iLCJlbnRpdGllcyIsIm1hcCIsIml0ZW0iLCJpIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJ0ZXh0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css":
/*!***********************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css ***!
  \***********************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"quoted-tweet-body_root__szSfI\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvcXVvdGVkLXR3ZWV0L3F1b3RlZC10d2VldC1ib2R5Lm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxxdW90ZWQtdHdlZXRcXHF1b3RlZC10d2VldC1ib2R5Lm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInF1b3RlZC10d2VldC1ib2R5X3Jvb3RfX3N6U2ZJXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetContainer: () => (/* binding */ QuotedTweetContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-container.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css\");\n/* __next_internal_client_entry_do_not_use__ QuotedTweetContainer auto */ \n\nconst QuotedTweetContainer = ({ tweet, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        onClick: (e)=>{\n            e.preventDefault();\n            window.open(tweet.url, '_blank');\n        },\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"article\", {\n            className: _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__.article,\n            children: children\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvcXVvdGVkLXR3ZWV0L3F1b3RlZC10d2VldC1jb250YWluZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzBFQUNnRDtBQUNJO0FBQzdDLE1BQU1HLHVCQUF1QixDQUFDLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFFLEdBQUcsV0FBVyxHQUFHSixzREFBSUEsQ0FBQyxPQUFPO1FBQzdFSyxXQUFXSixvRUFBTTtRQUNqQk0sU0FBUyxDQUFDQztZQUNOQSxFQUFFQyxjQUFjO1lBQ2hCQyxPQUFPQyxJQUFJLENBQUNSLE1BQU1TLEdBQUcsRUFBRTtRQUMzQjtRQUNBUixVQUFVLFdBQVcsR0FBR0osc0RBQUlBLENBQUMsV0FBVztZQUNwQ0ssV0FBV0osdUVBQVM7WUFDcEJHLFVBQVVBO1FBQ2Q7SUFDSixHQUFHIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxccXVvdGVkLXR3ZWV0XFxxdW90ZWQtdHdlZXQtY29udGFpbmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgcyBmcm9tICcuL3F1b3RlZC10d2VldC1jb250YWluZXIubW9kdWxlLmNzcyc7XG5leHBvcnQgY29uc3QgUXVvdGVkVHdlZXRDb250YWluZXIgPSAoeyB0d2VldCwgY2hpbGRyZW4gfSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcImRpdlwiLCB7XG4gICAgICAgIGNsYXNzTmFtZTogcy5yb290LFxuICAgICAgICBvbkNsaWNrOiAoZSk9PntcbiAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIHdpbmRvdy5vcGVuKHR3ZWV0LnVybCwgJ19ibGFuaycpO1xuICAgICAgICB9LFxuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwiYXJ0aWNsZVwiLCB7XG4gICAgICAgICAgICBjbGFzc05hbWU6IHMuYXJ0aWNsZSxcbiAgICAgICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlblxuICAgICAgICB9KVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbImpzeCIsIl9qc3giLCJzIiwiUXVvdGVkVHdlZXRDb250YWluZXIiLCJ0d2VldCIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwicm9vdCIsIm9uQ2xpY2siLCJlIiwicHJldmVudERlZmF1bHQiLCJ3aW5kb3ciLCJvcGVuIiwidXJsIiwiYXJ0aWNsZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css":
/*!****************************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css ***!
  \****************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"quoted-tweet-container_root__92393\",\n\t\"article\": \"quoted-tweet-container_article__FoJQN\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvcXVvdGVkLXR3ZWV0L3F1b3RlZC10d2VldC1jb250YWluZXIubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxccXVvdGVkLXR3ZWV0XFxxdW90ZWQtdHdlZXQtY29udGFpbmVyLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInF1b3RlZC10d2VldC1jb250YWluZXJfcm9vdF9fOTIzOTNcIixcblx0XCJhcnRpY2xlXCI6IFwicXVvdGVkLXR3ZWV0LWNvbnRhaW5lcl9hcnRpY2xlX19Gb0pRTlwiXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetHeader: () => (/* binding */ QuotedTweetHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../avatar-img.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js\");\n/* harmony import */ var _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quoted-tweet-header.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css\");\n/* harmony import */ var _verified_badge_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../verified-badge.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js\");\n\n\n\n\n\nconst QuotedTweetHeader = ({ tweet })=>{\n    const { user } = tweet;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.header,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow, user.profile_image_shape === 'Square' && _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarSquare),\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_avatar_img_js__WEBPACK_IMPORTED_MODULE_3__.AvatarImg, {\n                        src: user.profile_image_url_https,\n                        alt: user.name,\n                        width: 20,\n                        height: 20\n                    })\n                })\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.author,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorText,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                            title: user.name,\n                            children: user.name\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_verified_badge_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedBadge, {\n                        user: user\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.username,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                            title: `@${user.screen_name}`,\n                            children: [\n                                \"@\",\n                                user.screen_name\n                            ]\n                        })\n                    })\n                ]\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvcXVvdGVkLXR3ZWV0L3F1b3RlZC10d2VldC1oZWFkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQStEO0FBQ3ZDO0FBQ3FCO0FBQ0k7QUFDSTtBQUM5QyxNQUFNUSxvQkFBb0IsQ0FBQyxFQUFFQyxLQUFLLEVBQUU7SUFDdkMsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR0Q7SUFDakIsT0FBTyxXQUFXLEdBQUdOLHVEQUFLQSxDQUFDLE9BQU87UUFDOUJRLFdBQVdMLG1FQUFRO1FBQ25CTyxVQUFVO1lBQ04sV0FBVyxHQUFHWixzREFBSUEsQ0FBQyxLQUFLO2dCQUNwQmEsTUFBTUwsTUFBTU0sR0FBRztnQkFDZkosV0FBV0wsbUVBQVE7Z0JBQ25CVyxRQUFRO2dCQUNSQyxLQUFLO2dCQUNMTCxVQUFVLFdBQVcsR0FBR1osc0RBQUlBLENBQUMsT0FBTztvQkFDaENVLFdBQVdQLGdEQUFJQSxDQUFDRSwyRUFBZ0IsRUFBRUksS0FBS1UsbUJBQW1CLEtBQUssWUFBWWQseUVBQWM7b0JBQ3pGTyxVQUFVLFdBQVcsR0FBR1osc0RBQUlBLENBQUNJLHFEQUFTQSxFQUFFO3dCQUNwQ2lCLEtBQUtaLEtBQUthLHVCQUF1Qjt3QkFDakNDLEtBQUtkLEtBQUtlLElBQUk7d0JBQ2RDLE9BQU87d0JBQ1BDLFFBQVE7b0JBQ1o7Z0JBQ0o7WUFDSjtZQUNBLFdBQVcsR0FBR3hCLHVEQUFLQSxDQUFDLE9BQU87Z0JBQ3ZCUSxXQUFXTCxtRUFBUTtnQkFDbkJPLFVBQVU7b0JBQ04sV0FBVyxHQUFHWixzREFBSUEsQ0FBQyxPQUFPO3dCQUN0QlUsV0FBV0wsdUVBQVk7d0JBQ3ZCTyxVQUFVLFdBQVcsR0FBR1osc0RBQUlBLENBQUMsUUFBUTs0QkFDakM2QixPQUFPcEIsS0FBS2UsSUFBSTs0QkFDaEJaLFVBQVVILEtBQUtlLElBQUk7d0JBQ3ZCO29CQUNKO29CQUNBLFdBQVcsR0FBR3hCLHNEQUFJQSxDQUFDTSw2REFBYUEsRUFBRTt3QkFDOUJHLE1BQU1BO29CQUNWO29CQUNBLFdBQVcsR0FBR1Qsc0RBQUlBLENBQUMsT0FBTzt3QkFDdEJVLFdBQVdMLHFFQUFVO3dCQUNyQk8sVUFBVSxXQUFXLEdBQUdWLHVEQUFLQSxDQUFDLFFBQVE7NEJBQ2xDMkIsT0FBTyxDQUFDLENBQUMsRUFBRXBCLEtBQUtzQixXQUFXLEVBQUU7NEJBQzdCbkIsVUFBVTtnQ0FDTjtnQ0FDQUgsS0FBS3NCLFdBQVc7NkJBQ25CO3dCQUNMO29CQUNKO2lCQUNIO1lBQ0w7U0FDSDtJQUNMO0FBQ0osRUFBRSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHF1b3RlZC10d2VldFxccXVvdGVkLXR3ZWV0LWhlYWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCwganN4cyBhcyBfanN4cyB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IGNsc3ggZnJvbSAnY2xzeCc7XG5pbXBvcnQgeyBBdmF0YXJJbWcgfSBmcm9tICcuLi9hdmF0YXItaW1nLmpzJztcbmltcG9ydCBzIGZyb20gJy4vcXVvdGVkLXR3ZWV0LWhlYWRlci5tb2R1bGUuY3NzJztcbmltcG9ydCB7IFZlcmlmaWVkQmFkZ2UgfSBmcm9tICcuLi92ZXJpZmllZC1iYWRnZS5qcyc7XG5leHBvcnQgY29uc3QgUXVvdGVkVHdlZXRIZWFkZXIgPSAoeyB0d2VldCB9KT0+e1xuICAgIGNvbnN0IHsgdXNlciB9ID0gdHdlZXQ7XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX2pzeHMoXCJkaXZcIiwge1xuICAgICAgICBjbGFzc05hbWU6IHMuaGVhZGVyLFxuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAgLyojX19QVVJFX18qLyBfanN4KFwiYVwiLCB7XG4gICAgICAgICAgICAgICAgaHJlZjogdHdlZXQudXJsLFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogcy5hdmF0YXIsXG4gICAgICAgICAgICAgICAgdGFyZ2V0OiBcIl9ibGFua1wiLFxuICAgICAgICAgICAgICAgIHJlbDogXCJub29wZW5lciBub3JlZmVycmVyXCIsXG4gICAgICAgICAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeChcImRpdlwiLCB7XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogY2xzeChzLmF2YXRhck92ZXJmbG93LCB1c2VyLnByb2ZpbGVfaW1hZ2Vfc2hhcGUgPT09ICdTcXVhcmUnICYmIHMuYXZhdGFyU3F1YXJlKSxcbiAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeChBdmF0YXJJbWcsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNyYzogdXNlci5wcm9maWxlX2ltYWdlX3VybF9odHRwcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsdDogdXNlci5uYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDIwLFxuICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAyMFxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICAgIC8qI19fUFVSRV9fKi8gX2pzeHMoXCJkaXZcIiwge1xuICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogcy5hdXRob3IsXG4gICAgICAgICAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgICAgICAgICAgLyojX19QVVJFX18qLyBfanN4KFwiZGl2XCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogcy5hdXRob3JUZXh0LFxuICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeChcInNwYW5cIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiB1c2VyLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IHVzZXIubmFtZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICAgIC8qI19fUFVSRV9fKi8gX2pzeChWZXJpZmllZEJhZGdlLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB1c2VyOiB1c2VyXG4gICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgICAgICAvKiNfX1BVUkVfXyovIF9qc3goXCJkaXZcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOiBzLnVzZXJuYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeHMoXCJzcGFuXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogYEAke3VzZXIuc2NyZWVuX25hbWV9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIkBcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlci5zY3JlZW5fbmFtZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgXVxuICAgICAgICAgICAgfSlcbiAgICAgICAgXVxuICAgIH0pO1xufTtcbiJdLCJuYW1lcyI6WyJqc3giLCJfanN4IiwianN4cyIsIl9qc3hzIiwiY2xzeCIsIkF2YXRhckltZyIsInMiLCJWZXJpZmllZEJhZGdlIiwiUXVvdGVkVHdlZXRIZWFkZXIiLCJ0d2VldCIsInVzZXIiLCJjbGFzc05hbWUiLCJoZWFkZXIiLCJjaGlsZHJlbiIsImhyZWYiLCJ1cmwiLCJhdmF0YXIiLCJ0YXJnZXQiLCJyZWwiLCJhdmF0YXJPdmVyZmxvdyIsInByb2ZpbGVfaW1hZ2Vfc2hhcGUiLCJhdmF0YXJTcXVhcmUiLCJzcmMiLCJwcm9maWxlX2ltYWdlX3VybF9odHRwcyIsImFsdCIsIm5hbWUiLCJ3aWR0aCIsImhlaWdodCIsImF1dGhvciIsImF1dGhvclRleHQiLCJ0aXRsZSIsInVzZXJuYW1lIiwic2NyZWVuX25hbWUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css":
/*!*************************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css ***!
  \*************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"quoted-tweet-header_header___qrcQ\",\n\t\"avatar\": \"quoted-tweet-header_avatar__lGzrW\",\n\t\"avatarSquare\": \"quoted-tweet-header_avatarSquare__l_eYT\",\n\t\"author\": \"quoted-tweet-header_author__k48VI\",\n\t\"authorText\": \"quoted-tweet-header_authorText__FULly\",\n\t\"username\": \"quoted-tweet-header_username__YLPXR\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvcXVvdGVkLXR3ZWV0L3F1b3RlZC10d2VldC1oZWFkZXIubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxxdW90ZWQtdHdlZXRcXHF1b3RlZC10d2VldC1oZWFkZXIubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJoZWFkZXJcIjogXCJxdW90ZWQtdHdlZXQtaGVhZGVyX2hlYWRlcl9fX3FyY1FcIixcblx0XCJhdmF0YXJcIjogXCJxdW90ZWQtdHdlZXQtaGVhZGVyX2F2YXRhcl9fbEd6cldcIixcblx0XCJhdmF0YXJTcXVhcmVcIjogXCJxdW90ZWQtdHdlZXQtaGVhZGVyX2F2YXRhclNxdWFyZV9fbF9lWVRcIixcblx0XCJhdXRob3JcIjogXCJxdW90ZWQtdHdlZXQtaGVhZGVyX2F1dGhvcl9fazQ4VklcIixcblx0XCJhdXRob3JUZXh0XCI6IFwicXVvdGVkLXR3ZWV0LWhlYWRlcl9hdXRob3JUZXh0X19GVUxseVwiLFxuXHRcInVzZXJuYW1lXCI6IFwicXVvdGVkLXR3ZWV0LWhlYWRlcl91c2VybmFtZV9fWUxQWFJcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweet: () => (/* binding */ QuotedTweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-container.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js\");\n/* harmony import */ var _quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quoted-tweet-header.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js\");\n/* harmony import */ var _quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quoted-tweet-body.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js\");\n/* harmony import */ var _tweet_media_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tweet-media.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js\");\n\n\n\n\n\nconst QuotedTweet = ({ tweet })=>{\n    var _tweet_mediaDetails;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.QuotedTweetContainer, {\n        tweet: tweet,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__.QuotedTweetHeader, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__.QuotedTweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_js__WEBPACK_IMPORTED_MODULE_4__.TweetMedia, {\n                quoted: true,\n                tweet: tweet\n            }) : null\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/skeleton.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/skeleton.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _skeleton_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./skeleton.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css\");\n\n\nconst Skeleton = ({ style })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n        className: _skeleton_module_css__WEBPACK_IMPORTED_MODULE_1__.skeleton,\n        style: style\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvc2tlbGV0b24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEO0FBQ0w7QUFDcEMsTUFBTUcsV0FBVyxDQUFDLEVBQUVDLEtBQUssRUFBRSxHQUFHLFdBQVcsR0FBR0gsc0RBQUlBLENBQUMsUUFBUTtRQUN4REksV0FBV0gsMERBQWU7UUFDMUJFLE9BQU9BO0lBQ1gsR0FBRyIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHNrZWxldG9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgc3R5bGVzIGZyb20gJy4vc2tlbGV0b24ubW9kdWxlLmNzcyc7XG5leHBvcnQgY29uc3QgU2tlbGV0b24gPSAoeyBzdHlsZSB9KT0+LyojX19QVVJFX18qLyBfanN4KFwic3BhblwiLCB7XG4gICAgICAgIGNsYXNzTmFtZTogc3R5bGVzLnNrZWxldG9uLFxuICAgICAgICBzdHlsZTogc3R5bGVcbiAgICB9KTtcbiJdLCJuYW1lcyI6WyJqc3giLCJfanN4Iiwic3R5bGVzIiwiU2tlbGV0b24iLCJzdHlsZSIsImNsYXNzTmFtZSIsInNrZWxldG9uIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/skeleton.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css":
/*!*************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css ***!
  \*************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"skeleton\": \"skeleton_skeleton__gUMqh\",\n\t\"loading\": \"skeleton_loading__XZoZ6\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvc2tlbGV0b24ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcc2tlbGV0b24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJza2VsZXRvblwiOiBcInNrZWxldG9uX3NrZWxldG9uX19nVU1xaFwiLFxuXHRcImxvYWRpbmdcIjogXCJza2VsZXRvbl9sb2FkaW5nX19YWm9aNlwiXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/theme.css":
/*!***************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/theme.css ***!
  \***************************************************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetActionsCopy: () => (/* binding */ TweetActionsCopy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-actions.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\");\n/* __next_internal_client_entry_do_not_use__ TweetActionsCopy auto */ \n\n\nconst TweetActionsCopy = ({ tweet })=>{\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = ()=>{\n        navigator.clipboard.writeText(tweet.url);\n        setCopied(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TweetActionsCopy.useEffect\": ()=>{\n            if (copied) {\n                const timeout = setTimeout({\n                    \"TweetActionsCopy.useEffect.timeout\": ()=>{\n                        setCopied(false);\n                    }\n                }[\"TweetActionsCopy.useEffect.timeout\"], 6000);\n                return ({\n                    \"TweetActionsCopy.useEffect\": ()=>clearTimeout(timeout)\n                })[\"TweetActionsCopy.useEffect\"];\n            }\n        }\n    }[\"TweetActionsCopy.useEffect\"], [\n        copied\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"button\", {\n        type: \"button\",\n        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copy,\n        \"aria-label\": \"Copy link\",\n        onClick: handleCopy,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIconWrapper,\n                children: copied ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M9.64 18.952l-5.55-4.861 1.317-1.504 3.951 3.459 8.459-10.948L19.4 6.32 9.64 18.952z\"\n                        })\n                    })\n                }) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M18.36 5.64c-1.95-1.96-5.11-1.96-7.07 0L9.88 7.05 8.46 5.64l1.42-1.42c2.73-2.73 7.16-2.73 9.9 0 2.73 2.74 2.73 7.17 0 9.9l-1.42 1.42-1.41-1.42 1.41-1.41c1.96-1.96 1.96-5.12 0-7.07zm-2.12 3.53l-7.07 7.07-1.41-1.41 7.07-7.07 1.41 1.41zm-12.02.71l1.42-1.42 1.41 1.42-1.41 1.41c-1.96 1.96-1.96 5.12 0 7.07 1.95 1.96 5.11 1.96 7.07 0l1.41-1.41 1.42 1.41-1.42 1.42c-2.73 2.73-7.16 2.73-9.9 0-2.73-2.74-2.73-7.17 0-9.9z\"\n                        })\n                    })\n                })\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyText,\n                children: copied ? 'Copied!' : 'Copy link'\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetActions: () => (/* binding */ TweetActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-actions-copy.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js\");\n/* harmony import */ var _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-actions.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\");\n\n\n\n\nconst TweetActions = ({ tweet })=>{\n    const favoriteCount = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.favorite_count);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.actions,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.like,\n                href: tweet.like_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": `Like. This Tweet has ${favoriteCount} likes`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeIconWrapper,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                                    d: \"M20.884 13.19c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeCount,\n                        children: favoriteCount\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.reply,\n                href: tweet.reply_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Reply to this Tweet on Twitter\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyIconWrapper,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                                    d: \"M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyText,\n                        children: \"Reply\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__.TweetActionsCopy, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css":
/*!******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css ***!
  \******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"actions\": \"tweet-actions_actions__UDw7H\",\n\t\"like\": \"tweet-actions_like__H1xYv\",\n\t\"reply\": \"tweet-actions_reply__S4rFc\",\n\t\"copy\": \"tweet-actions_copy__Tbdg_\",\n\t\"likeIconWrapper\": \"tweet-actions_likeIconWrapper__JQkhp\",\n\t\"likeCount\": \"tweet-actions_likeCount__MyxBd\",\n\t\"replyIconWrapper\": \"tweet-actions_replyIconWrapper__NVdGa\",\n\t\"copyIconWrapper\": \"tweet-actions_copyIconWrapper__toM2y\",\n\t\"likeIcon\": \"tweet-actions_likeIcon__fhDng\",\n\t\"replyIcon\": \"tweet-actions_replyIcon__MI2tG\",\n\t\"copyIcon\": \"tweet-actions_copyIcon__SEaWw\",\n\t\"replyText\": \"tweet-actions_replyText__doQct\",\n\t\"copyText\": \"tweet-actions_copyText__fEqBx\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtYWN0aW9ucy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtYWN0aW9ucy5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImFjdGlvbnNcIjogXCJ0d2VldC1hY3Rpb25zX2FjdGlvbnNfX1VEdzdIXCIsXG5cdFwibGlrZVwiOiBcInR3ZWV0LWFjdGlvbnNfbGlrZV9fSDF4WXZcIixcblx0XCJyZXBseVwiOiBcInR3ZWV0LWFjdGlvbnNfcmVwbHlfX1M0ckZjXCIsXG5cdFwiY29weVwiOiBcInR3ZWV0LWFjdGlvbnNfY29weV9fVGJkZ19cIixcblx0XCJsaWtlSWNvbldyYXBwZXJcIjogXCJ0d2VldC1hY3Rpb25zX2xpa2VJY29uV3JhcHBlcl9fSlFraHBcIixcblx0XCJsaWtlQ291bnRcIjogXCJ0d2VldC1hY3Rpb25zX2xpa2VDb3VudF9fTXl4QmRcIixcblx0XCJyZXBseUljb25XcmFwcGVyXCI6IFwidHdlZXQtYWN0aW9uc19yZXBseUljb25XcmFwcGVyX19OVmRHYVwiLFxuXHRcImNvcHlJY29uV3JhcHBlclwiOiBcInR3ZWV0LWFjdGlvbnNfY29weUljb25XcmFwcGVyX190b00yeVwiLFxuXHRcImxpa2VJY29uXCI6IFwidHdlZXQtYWN0aW9uc19saWtlSWNvbl9fZmhEbmdcIixcblx0XCJyZXBseUljb25cIjogXCJ0d2VldC1hY3Rpb25zX3JlcGx5SWNvbl9fTUkydEdcIixcblx0XCJjb3B5SWNvblwiOiBcInR3ZWV0LWFjdGlvbnNfY29weUljb25fX1NFYVd3XCIsXG5cdFwicmVwbHlUZXh0XCI6IFwidHdlZXQtYWN0aW9uc19yZXBseVRleHRfX2RvUWN0XCIsXG5cdFwiY29weVRleHRcIjogXCJ0d2VldC1hY3Rpb25zX2NvcHlUZXh0X19mRXFCeFwiXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-body.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetBody: () => (/* binding */ TweetBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _tweet_link_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-link.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.js\");\n/* harmony import */ var _tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-body.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css\");\n\n\n\nconst TweetBody = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n        className: _tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>{\n            switch(item.type){\n                case 'hashtag':\n                case 'mention':\n                case 'url':\n                case 'symbol':\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_link_js__WEBPACK_IMPORTED_MODULE_2__.TweetLink, {\n                        href: item.href,\n                        children: item.text\n                    }, i);\n                case 'media':\n                    // Media text is currently never displayed, some tweets however might have indices\n                    // that do match `display_text_range` so for those cases we ignore the content.\n                    return;\n                default:\n                    // We use `dangerouslySetInnerHTML` to preserve the text encoding.\n                    // https://github.com/vercel-labs/react-tweet/issues/29\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        dangerouslySetInnerHTML: {\n                            __html: item.text\n                        }\n                    }, i);\n            }\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css ***!
  \***************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-body_root__ChzUj\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtYm9keS5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtYm9keS5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1ib2R5X3Jvb3RfX0NoelVqXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-container.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetContainer: () => (/* binding */ TweetContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-container.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css\");\n/* harmony import */ var _theme_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./theme.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/theme.css\");\n\n\n\n\nconst TweetContainer = ({ className, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('react-tweet-theme', _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__.root, className),\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"article\", {\n            className: _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__.article,\n            children: children\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtY29udGFpbmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWdEO0FBQ3hCO0FBQ3FCO0FBQ3hCO0FBQ2QsTUFBTUksaUJBQWlCLENBQUMsRUFBRUMsU0FBUyxFQUFFQyxRQUFRLEVBQUUsR0FBRyxXQUFXLEdBQUdMLHNEQUFJQSxDQUFDLE9BQU87UUFDM0VJLFdBQVdILGdEQUFJQSxDQUFDLHFCQUFxQkMsNkRBQU0sRUFBRUU7UUFDN0NDLFVBQVUsV0FBVyxHQUFHTCxzREFBSUEsQ0FBQyxXQUFXO1lBQ3BDSSxXQUFXRixnRUFBUztZQUNwQkcsVUFBVUE7UUFDZDtJQUNKLEdBQUciLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1jb250YWluZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCBjbHN4IGZyb20gJ2Nsc3gnO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1jb250YWluZXIubW9kdWxlLmNzcyc7XG5pbXBvcnQgJy4vdGhlbWUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldENvbnRhaW5lciA9ICh7IGNsYXNzTmFtZSwgY2hpbGRyZW4gfSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcImRpdlwiLCB7XG4gICAgICAgIGNsYXNzTmFtZTogY2xzeCgncmVhY3QtdHdlZXQtdGhlbWUnLCBzLnJvb3QsIGNsYXNzTmFtZSksXG4gICAgICAgIGNoaWxkcmVuOiAvKiNfX1BVUkVfXyovIF9qc3goXCJhcnRpY2xlXCIsIHtcbiAgICAgICAgICAgIGNsYXNzTmFtZTogcy5hcnRpY2xlLFxuICAgICAgICAgICAgY2hpbGRyZW46IGNoaWxkcmVuXG4gICAgICAgIH0pXG4gICAgfSk7XG4iXSwibmFtZXMiOlsianN4IiwiX2pzeCIsImNsc3giLCJzIiwiVHdlZXRDb250YWluZXIiLCJjbGFzc05hbWUiLCJjaGlsZHJlbiIsInJvb3QiLCJhcnRpY2xlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css":
/*!********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css ***!
  \********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-container_root__0rJLq\",\n\t\"article\": \"tweet-container_article__0ERPK\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtY29udGFpbmVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWNvbnRhaW5lci5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1jb250YWluZXJfcm9vdF9fMHJKTHFcIixcblx0XCJhcnRpY2xlXCI6IFwidHdlZXQtY29udGFpbmVyX2FydGljbGVfXzBFUlBLXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-header.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetHeader: () => (/* binding */ TweetHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./avatar-img.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js\");\n/* harmony import */ var _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-header.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css\");\n/* harmony import */ var _verified_badge_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./verified-badge.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js\");\n\n\n\n\n\nconst TweetHeader = ({ tweet, components })=>{\n    var _components_AvatarImg;\n    const Img = (_components_AvatarImg = components == null ? void 0 : components.AvatarImg) != null ? _components_AvatarImg : _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__.AvatarImg;\n    const { user } = tweet;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.header,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                href: tweet.url,\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow, user.profile_image_shape === 'Square' && _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarSquare),\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Img, {\n                            src: user.profile_image_url_https,\n                            alt: user.name,\n                            width: 48,\n                            height: 48\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                            className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarShadow\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.author,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                        href: tweet.url,\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorLink,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorLinkText,\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                                    title: user.name,\n                                    children: user.name\n                                })\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_verified_badge_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedBadge, {\n                                user: user,\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorVerified\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorMeta,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                                href: tweet.url,\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.username,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                                    title: `@${user.screen_name}`,\n                                    children: [\n                                        \"@\",\n                                        user.screen_name\n                                    ]\n                                })\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorFollow,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.separator,\n                                        children: \"·\"\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                                        href: user.follow_url,\n                                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.follow,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        children: \"Follow\"\n                                    })\n                                ]\n                            })\n                        ]\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.brand,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"View on Twitter\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.twitterIcon,\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css ***!
  \*****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"tweet-header_header__CXzdi\",\n\t\"avatar\": \"tweet-header_avatar__0Wi9G\",\n\t\"avatarOverflow\": \"tweet-header_avatarOverflow__E2gxj\",\n\t\"avatarSquare\": \"tweet-header_avatarSquare__uIUBO\",\n\t\"avatarShadow\": \"tweet-header_avatarShadow__CB9Zo\",\n\t\"author\": \"tweet-header_author___jWoR\",\n\t\"authorLink\": \"tweet-header_authorLink__qj5Sm\",\n\t\"authorVerified\": \"tweet-header_authorVerified__OFYo2\",\n\t\"authorLinkText\": \"tweet-header_authorLinkText__y6HdU\",\n\t\"authorMeta\": \"tweet-header_authorMeta__gIC3U\",\n\t\"authorFollow\": \"tweet-header_authorFollow__w_j4h\",\n\t\"username\": \"tweet-header_username__UebZb\",\n\t\"follow\": \"tweet-header_follow__Fi7bf\",\n\t\"separator\": \"tweet-header_separator__d4pqe\",\n\t\"brand\": \"tweet-header_brand__0FLQl\",\n\t\"twitterIcon\": \"tweet-header_twitterIcon__m0Rzu\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtaGVhZGVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1oZWFkZXIubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJoZWFkZXJcIjogXCJ0d2VldC1oZWFkZXJfaGVhZGVyX19DWHpkaVwiLFxuXHRcImF2YXRhclwiOiBcInR3ZWV0LWhlYWRlcl9hdmF0YXJfXzBXaTlHXCIsXG5cdFwiYXZhdGFyT3ZlcmZsb3dcIjogXCJ0d2VldC1oZWFkZXJfYXZhdGFyT3ZlcmZsb3dfX0UyZ3hqXCIsXG5cdFwiYXZhdGFyU3F1YXJlXCI6IFwidHdlZXQtaGVhZGVyX2F2YXRhclNxdWFyZV9fdUlVQk9cIixcblx0XCJhdmF0YXJTaGFkb3dcIjogXCJ0d2VldC1oZWFkZXJfYXZhdGFyU2hhZG93X19DQjlab1wiLFxuXHRcImF1dGhvclwiOiBcInR3ZWV0LWhlYWRlcl9hdXRob3JfX19qV29SXCIsXG5cdFwiYXV0aG9yTGlua1wiOiBcInR3ZWV0LWhlYWRlcl9hdXRob3JMaW5rX19xajVTbVwiLFxuXHRcImF1dGhvclZlcmlmaWVkXCI6IFwidHdlZXQtaGVhZGVyX2F1dGhvclZlcmlmaWVkX19PRllvMlwiLFxuXHRcImF1dGhvckxpbmtUZXh0XCI6IFwidHdlZXQtaGVhZGVyX2F1dGhvckxpbmtUZXh0X195NkhkVVwiLFxuXHRcImF1dGhvck1ldGFcIjogXCJ0d2VldC1oZWFkZXJfYXV0aG9yTWV0YV9fZ0lDM1VcIixcblx0XCJhdXRob3JGb2xsb3dcIjogXCJ0d2VldC1oZWFkZXJfYXV0aG9yRm9sbG93X193X2o0aFwiLFxuXHRcInVzZXJuYW1lXCI6IFwidHdlZXQtaGVhZGVyX3VzZXJuYW1lX19VZWJaYlwiLFxuXHRcImZvbGxvd1wiOiBcInR3ZWV0LWhlYWRlcl9mb2xsb3dfX0ZpN2JmXCIsXG5cdFwic2VwYXJhdG9yXCI6IFwidHdlZXQtaGVhZGVyX3NlcGFyYXRvcl9fZDRwcWVcIixcblx0XCJicmFuZFwiOiBcInR3ZWV0LWhlYWRlcl9icmFuZF9fMEZMUWxcIixcblx0XCJ0d2l0dGVySWNvblwiOiBcInR3ZWV0LWhlYWRlcl90d2l0dGVySWNvbl9fbTBSenVcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInReplyTo: () => (/* binding */ TweetInReplyTo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _tweet_in_reply_to_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-in-reply-to.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css\");\n\n\nconst TweetInReplyTo = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n        href: tweet.in_reply_to_url,\n        className: _tweet_in_reply_to_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        children: [\n            \"Replying to @\",\n            tweet.in_reply_to_screen_name\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtaW4tcmVwbHktdG8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtEO0FBQ0g7QUFDeEMsTUFBTUcsaUJBQWlCLENBQUMsRUFBRUMsS0FBSyxFQUFFLEdBQUcsV0FBVyxHQUFHSCx1REFBS0EsQ0FBQyxLQUFLO1FBQzVESSxNQUFNRCxNQUFNRSxlQUFlO1FBQzNCQyxXQUFXTCwrREFBTTtRQUNqQk8sUUFBUTtRQUNSQyxLQUFLO1FBQ0xDLFVBQVU7WUFDTjtZQUNBUCxNQUFNUSx1QkFBdUI7U0FDaEM7SUFDTCxHQUFHIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtaW4tcmVwbHktdG8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4cyBhcyBfanN4cyB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1pbi1yZXBseS10by5tb2R1bGUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldEluUmVwbHlUbyA9ICh7IHR3ZWV0IH0pPT4vKiNfX1BVUkVfXyovIF9qc3hzKFwiYVwiLCB7XG4gICAgICAgIGhyZWY6IHR3ZWV0LmluX3JlcGx5X3RvX3VybCxcbiAgICAgICAgY2xhc3NOYW1lOiBzLnJvb3QsXG4gICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgcmVsOiBcIm5vb3BlbmVyIG5vcmVmZXJyZXJcIixcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgIFwiUmVwbHlpbmcgdG8gQFwiLFxuICAgICAgICAgICAgdHdlZXQuaW5fcmVwbHlfdG9fc2NyZWVuX25hbWVcbiAgICAgICAgXVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbImpzeHMiLCJfanN4cyIsInMiLCJUd2VldEluUmVwbHlUbyIsInR3ZWV0IiwiaHJlZiIsImluX3JlcGx5X3RvX3VybCIsImNsYXNzTmFtZSIsInJvb3QiLCJ0YXJnZXQiLCJyZWwiLCJjaGlsZHJlbiIsImluX3JlcGx5X3RvX3NjcmVlbl9uYW1lIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-in-reply-to_root__o784R\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtaW4tcmVwbHktdG8ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWluLXJlcGx5LXRvLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LWluLXJlcGx5LXRvX3Jvb3RfX283ODRSXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInfoCreatedAt: () => (/* binding */ TweetInfoCreatedAt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _date_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../date-utils.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/date-utils.js\");\n/* harmony import */ var _tweet_info_created_at_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-info-created-at.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css\");\n\n\n\nconst TweetInfoCreatedAt = ({ tweet })=>{\n    const createdAt = new Date(tweet.created_at);\n    const formattedCreatedAtDate = (0,_date_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatDate)(createdAt);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n        className: _tweet_info_created_at_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        href: tweet.url,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        \"aria-label\": formattedCreatedAtDate,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"time\", {\n            dateTime: createdAt.toISOString(),\n            children: formattedCreatedAtDate\n        })\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css":
/*!**************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css ***!
  \**************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-info-created-at_root__KaxZi\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtaW5mby1jcmVhdGVkLWF0Lm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1pbmZvLWNyZWF0ZWQtYXQubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtaW5mby1jcmVhdGVkLWF0X3Jvb3RfX0theFppXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInfo: () => (/* binding */ TweetInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-info-created-at.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js\");\n/* harmony import */ var _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-info.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css\");\n\n\n\nconst TweetInfo = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.info,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_2__.TweetInfoCreatedAt, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.infoLink,\n                href: \"https://help.x.com/en/x-for-websites-ads-info-and-privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Twitter for Websites, Ads Information and Privacy\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.infoIcon,\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.5 8.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 7 12 7s1.5.67 1.5 1.5zM13 17v-5h-2v5h2zm-1 5.25c5.66 0 10.25-4.59 10.25-10.25S17.66 1.75 12 1.75 1.75 6.34 1.75 12 6.34 22.25 12 22.25zM20.25 12c0 4.56-3.69 8.25-8.25 8.25S3.75 16.56 3.75 12 7.44 3.75 12 3.75s8.25 3.69 8.25 8.25z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css ***!
  \***************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"info\": \"tweet-info_info__ll_kH\",\n\t\"infoLink\": \"tweet-info_infoLink__xdgYO\",\n\t\"infoIcon\": \"tweet-info_infoIcon__S8lzA\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtaW5mby5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWluZm8ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJpbmZvXCI6IFwidHdlZXQtaW5mb19pbmZvX19sbF9rSFwiLFxuXHRcImluZm9MaW5rXCI6IFwidHdlZXQtaW5mb19pbmZvTGlua19feGRnWU9cIixcblx0XCJpbmZvSWNvblwiOiBcInR3ZWV0LWluZm9faW5mb0ljb25fX1M4bHpBXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-link.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetLink: () => (/* binding */ TweetLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _tweet_link_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-link.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css\");\n\n\nconst TweetLink = ({ href, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n        href: href,\n        className: _tweet_link_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer nofollow\",\n        children: children\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtbGluay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Q7QUFDUjtBQUNqQyxNQUFNRyxZQUFZLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxRQUFRLEVBQUUsR0FBRyxXQUFXLEdBQUdKLHNEQUFJQSxDQUFDLEtBQUs7UUFDL0RHLE1BQU1BO1FBQ05FLFdBQVdKLHdEQUFNO1FBQ2pCTSxRQUFRO1FBQ1JDLEtBQUs7UUFDTEosVUFBVUE7SUFDZCxHQUFHIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtbGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1saW5rLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFR3ZWV0TGluayA9ICh7IGhyZWYsIGNoaWxkcmVuIH0pPT4vKiNfX1BVUkVfXyovIF9qc3goXCJhXCIsIHtcbiAgICAgICAgaHJlZjogaHJlZixcbiAgICAgICAgY2xhc3NOYW1lOiBzLnJvb3QsXG4gICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgcmVsOiBcIm5vb3BlbmVyIG5vcmVmZXJyZXIgbm9mb2xsb3dcIixcbiAgICAgICAgY2hpbGRyZW46IGNoaWxkcmVuXG4gICAgfSk7XG4iXSwibmFtZXMiOlsianN4IiwiX2pzeCIsInMiLCJUd2VldExpbmsiLCJocmVmIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJyb290IiwidGFyZ2V0IiwicmVsIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css ***!
  \***************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-link_root__4EzRS\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtbGluay5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtbGluay5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1saW5rX3Jvb3RfXzRFelJTXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetMediaVideo: () => (/* binding */ TweetMediaVideo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-media.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\");\n/* harmony import */ var _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet-media-video.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css\");\n/* __next_internal_client_entry_do_not_use__ TweetMediaVideo auto */ \n\n\n\n\n\nconst TweetMediaVideo = ({ tweet, media })=>{\n    const [playButton, setPlayButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ended, setEnded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mp4Video = (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMp4Video)(media);\n    let timeout = 0;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"video\", {\n                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.image,\n                poster: (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMediaUrl)(media, 'small'),\n                controls: !playButton,\n                playsInline: true,\n                preload: \"none\",\n                tabIndex: playButton ? -1 : 0,\n                onPlay: ()=>{\n                    if (timeout) window.clearTimeout(timeout);\n                    if (!isPlaying) setIsPlaying(true);\n                    if (ended) setEnded(false);\n                },\n                onPause: ()=>{\n                    // When the video is seeked (moved to a different timestamp), it will pause for a moment\n                    // before resuming. We don't want to show the message in that case so we wait a bit.\n                    if (timeout) window.clearTimeout(timeout);\n                    timeout = window.setTimeout(()=>{\n                        if (isPlaying) setIsPlaying(false);\n                        timeout = 0;\n                    }, 100);\n                },\n                onEnded: ()=>{\n                    setEnded(true);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"source\", {\n                    src: mp4Video.url,\n                    type: mp4Video.content_type\n                })\n            }),\n            playButton && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n                type: \"button\",\n                className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.videoButton,\n                \"aria-label\": \"View video on X\",\n                onClick: (e)=>{\n                    const video = e.currentTarget.previousSibling;\n                    e.preventDefault();\n                    setPlayButton(false);\n                    video.load();\n                    video.play().then(()=>{\n                        setIsPlaying(true);\n                        video.focus();\n                    }).catch((error)=>{\n                        console.error('Error playing video:', error);\n                        setPlayButton(true);\n                        setIsPlaying(false);\n                    });\n                },\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.videoButtonIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M21 12L4 2v20l17-10z\"\n                        })\n                    })\n                })\n            }),\n            !isPlaying && !ended && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.watchOnTwitter,\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                    href: tweet.url,\n                    className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.anchor,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    children: playButton ? 'Watch on X' : 'Continue watching on X'\n                })\n            }),\n            ended && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.anchor, _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.viewReplies),\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"View replies\"\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"anchor\": \"tweet-media-video_anchor__EMqq1\",\n\t\"videoButton\": \"tweet-media-video_videoButton__P9iF2\",\n\t\"videoButtonIcon\": \"tweet-media-video_videoButtonIcon__7gRo1\",\n\t\"watchOnTwitter\": \"tweet-media-video_watchOnTwitter__2ucCU\",\n\t\"viewReplies\": \"tweet-media-video_viewReplies__dp8G_\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtbWVkaWEtdmlkZW8ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtbWVkaWEtdmlkZW8ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJhbmNob3JcIjogXCJ0d2VldC1tZWRpYS12aWRlb19hbmNob3JfX0VNcXExXCIsXG5cdFwidmlkZW9CdXR0b25cIjogXCJ0d2VldC1tZWRpYS12aWRlb192aWRlb0J1dHRvbl9fUDlpRjJcIixcblx0XCJ2aWRlb0J1dHRvbkljb25cIjogXCJ0d2VldC1tZWRpYS12aWRlb192aWRlb0J1dHRvbkljb25fXzdnUm8xXCIsXG5cdFwid2F0Y2hPblR3aXR0ZXJcIjogXCJ0d2VldC1tZWRpYS12aWRlb193YXRjaE9uVHdpdHRlcl9fMnVjQ1VcIixcblx0XCJ2aWV3UmVwbGllc1wiOiBcInR3ZWV0LW1lZGlhLXZpZGVvX3ZpZXdSZXBsaWVzX19kcDhHX1wiXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetMedia: () => (/* binding */ TweetMedia)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_media_video_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tweet-media-video.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js\");\n/* harmony import */ var _media_img_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./media-img.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/media-img.js\");\n/* harmony import */ var _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-media.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\");\n\n\n\n\n\n\n\nconst getSkeletonStyle = (media, itemCount)=>{\n    let paddingBottom = 56.25 // default of 16x9\n    ;\n    // if we only have 1 item, show at original ratio\n    if (itemCount === 1) paddingBottom = 100 / media.original_info.width * media.original_info.height;\n    // if we have 2 items, double the default to be 16x9 total\n    if (itemCount === 2) paddingBottom = paddingBottom * 2;\n    return {\n        width: media.type === 'photo' ? undefined : 'unset',\n        paddingBottom: `${paddingBottom}%`\n    };\n};\nconst TweetMedia = ({ tweet, components, quoted })=>{\n    var _tweet_mediaDetails, _tweet_mediaDetails1;\n    var _tweet_mediaDetails_length;\n    const length = (_tweet_mediaDetails_length = (_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) != null ? _tweet_mediaDetails_length : 0;\n    var _components_MediaImg;\n    const Img = (_components_MediaImg = components == null ? void 0 : components.MediaImg) != null ? _components_MediaImg : _media_img_js__WEBPACK_IMPORTED_MODULE_4__.MediaImg;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.root, !quoted && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.rounded),\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaWrapper, length > 1 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid2Columns, length === 3 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid3, length > 4 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid2x2),\n            children: (_tweet_mediaDetails1 = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails1.map((media)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                    children: media.type === 'photo' ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                        href: tweet.url,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaContainer, _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaLink),\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Img, {\n                                src: (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMediaUrl)(media, 'small'),\n                                alt: media.ext_alt_text || 'Image',\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.image,\n                                draggable: true\n                            })\n                        ]\n                    }, media.media_url_https) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaContainer,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_video_js__WEBPACK_IMPORTED_MODULE_6__.TweetMediaVideo, {\n                                tweet: tweet,\n                                media: media\n                            })\n                        ]\n                    }, media.media_url_https)\n                }, media.media_url_https))\n        })\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css":
/*!****************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css ***!
  \****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-media_root__k6gQ2\",\n\t\"rounded\": \"tweet-media_rounded__LgwFx\",\n\t\"mediaWrapper\": \"tweet-media_mediaWrapper__6rfqr\",\n\t\"grid2Columns\": \"tweet-media_grid2Columns__tO2Ea\",\n\t\"grid3\": \"tweet-media_grid3__XbH4s\",\n\t\"grid2x2\": \"tweet-media_grid2x2__Wiunq\",\n\t\"mediaContainer\": \"tweet-media_mediaContainer__rjXGp\",\n\t\"mediaLink\": \"tweet-media_mediaLink__vFkZL\",\n\t\"skeleton\": \"tweet-media_skeleton__qZmSS\",\n\t\"image\": \"tweet-media_image__yoPJg\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtbWVkaWEubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LW1lZGlhLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LW1lZGlhX3Jvb3RfX2s2Z1EyXCIsXG5cdFwicm91bmRlZFwiOiBcInR3ZWV0LW1lZGlhX3JvdW5kZWRfX0xnd0Z4XCIsXG5cdFwibWVkaWFXcmFwcGVyXCI6IFwidHdlZXQtbWVkaWFfbWVkaWFXcmFwcGVyX182cmZxclwiLFxuXHRcImdyaWQyQ29sdW1uc1wiOiBcInR3ZWV0LW1lZGlhX2dyaWQyQ29sdW1uc19fdE8yRWFcIixcblx0XCJncmlkM1wiOiBcInR3ZWV0LW1lZGlhX2dyaWQzX19YYkg0c1wiLFxuXHRcImdyaWQyeDJcIjogXCJ0d2VldC1tZWRpYV9ncmlkMngyX19XaXVucVwiLFxuXHRcIm1lZGlhQ29udGFpbmVyXCI6IFwidHdlZXQtbWVkaWFfbWVkaWFDb250YWluZXJfX3JqWEdwXCIsXG5cdFwibWVkaWFMaW5rXCI6IFwidHdlZXQtbWVkaWFfbWVkaWFMaW5rX192RmtaTFwiLFxuXHRcInNrZWxldG9uXCI6IFwidHdlZXQtbWVkaWFfc2tlbGV0b25fX3FabVNTXCIsXG5cdFwiaW1hZ2VcIjogXCJ0d2VldC1tZWRpYV9pbWFnZV9feW9QSmdcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetNotFound: () => (/* binding */ TweetNotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _tweet_not_found_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-not-found.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css\");\n\n\n\nconst TweetNotFound = (_props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n            className: _tweet_not_found_module_css__WEBPACK_IMPORTED_MODULE_2__.root,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h3\", {\n                    children: \"Tweet not found\"\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n                    children: \"The embedded tweet could not be found…\"\n                })\n            ]\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtbm90LWZvdW5kLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0Q7QUFDVDtBQUNKO0FBQzNDLE1BQU1NLGdCQUFnQixDQUFDQyxTQUFTLFdBQVcsR0FBR04sc0RBQUlBLENBQUNHLCtEQUFjQSxFQUFFO1FBQ2xFSSxVQUFVLFdBQVcsR0FBR0wsdURBQUtBLENBQUMsT0FBTztZQUNqQ00sV0FBV0osNkRBQVc7WUFDdEJHLFVBQVU7Z0JBQ04sV0FBVyxHQUFHUCxzREFBSUEsQ0FBQyxNQUFNO29CQUNyQk8sVUFBVTtnQkFDZDtnQkFDQSxXQUFXLEdBQUdQLHNEQUFJQSxDQUFDLEtBQUs7b0JBQ3BCTyxVQUFVO2dCQUNkO2FBQ0g7UUFDTDtJQUNKLEdBQUciLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1ub3QtZm91bmQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3gsIGpzeHMgYXMgX2pzeHMgfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCB7IFR3ZWV0Q29udGFpbmVyIH0gZnJvbSAnLi90d2VldC1jb250YWluZXIuanMnO1xuaW1wb3J0IHN0eWxlcyBmcm9tICcuL3R3ZWV0LW5vdC1mb3VuZC5tb2R1bGUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldE5vdEZvdW5kID0gKF9wcm9wcyk9Pi8qI19fUFVSRV9fKi8gX2pzeChUd2VldENvbnRhaW5lciwge1xuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4cyhcImRpdlwiLCB7XG4gICAgICAgICAgICBjbGFzc05hbWU6IHN0eWxlcy5yb290LFxuICAgICAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgICAgICAvKiNfX1BVUkVfXyovIF9qc3goXCJoM1wiLCB7XG4gICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBcIlR3ZWV0IG5vdCBmb3VuZFwiXG4gICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgLyojX19QVVJFX18qLyBfanN4KFwicFwiLCB7XG4gICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBcIlRoZSBlbWJlZGRlZCB0d2VldCBjb3VsZCBub3QgYmUgZm91bmTigKZcIlxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBdXG4gICAgICAgIH0pXG4gICAgfSk7XG4iXSwibmFtZXMiOlsianN4IiwiX2pzeCIsImpzeHMiLCJfanN4cyIsIlR3ZWV0Q29udGFpbmVyIiwic3R5bGVzIiwiVHdlZXROb3RGb3VuZCIsIl9wcm9wcyIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwicm9vdCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css":
/*!********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css ***!
  \********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-not-found_root__KQedq\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtbm90LWZvdW5kLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1ub3QtZm91bmQubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtbm90LWZvdW5kX3Jvb3RfX0tRZWRxXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetReplies: () => (/* binding */ TweetReplies)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-replies.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css\");\n\n\n\nconst TweetReplies = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.replies,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n            className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.link,\n            href: tweet.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.text,\n                children: tweet.conversation_count === 0 ? 'Read more on X' : tweet.conversation_count === 1 ? `Read ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.conversation_count)} reply` : `Read ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.conversation_count)} replies`\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css":
/*!******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css ***!
  \******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"replies\": \"tweet-replies_replies__PUxl8\",\n\t\"link\": \"tweet-replies_link__roxYQ\",\n\t\"text\": \"tweet-replies_text__o0Naf\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtcmVwbGllcy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LXJlcGxpZXMubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyZXBsaWVzXCI6IFwidHdlZXQtcmVwbGllc19yZXBsaWVzX19QVXhsOFwiLFxuXHRcImxpbmtcIjogXCJ0d2VldC1yZXBsaWVzX2xpbmtfX3JveFlRXCIsXG5cdFwidGV4dFwiOiBcInR3ZWV0LXJlcGxpZXNfdGV4dF9fbzBOYWZcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetSkeleton: () => (/* binding */ TweetSkeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _skeleton_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./skeleton.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/skeleton.js\");\n/* harmony import */ var _tweet_skeleton_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-skeleton.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css\");\n\n\n\n\nconst TweetSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        className: _tweet_skeleton_module_css__WEBPACK_IMPORTED_MODULE_2__.root,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '3rem',\n                    marginBottom: '0.75rem'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '6rem',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: {\n                    borderTop: 'var(--tweet-border)',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '2rem'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '2rem',\n                    borderRadius: '9999px',\n                    marginTop: '0.5rem'\n                }\n            })\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css":
/*!*******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css ***!
  \*******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-skeleton_root__1sn43\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHdlZXQtc2tlbGV0b24ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHJraVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LXNrZWxldG9uLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LXNrZWxldG9uX3Jvb3RfXzFzbjQzXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/types.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/types.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/**\n * Custom components that the default Twitter theme allows.\n *\n * Note: We only use these components in Server Components, because the root `Tweet`\n * component that uses them is a Server Component and you can't pass down functions to a\n * client component unless they're Server Actions.\n */ /**\n * @deprecated Use `TwitterComponents` instead.\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdHlwZXMuanMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7Q0FNQyxHQUFHOztDQUVILEdBQWMiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEN1c3RvbSBjb21wb25lbnRzIHRoYXQgdGhlIGRlZmF1bHQgVHdpdHRlciB0aGVtZSBhbGxvd3MuXG4gKlxuICogTm90ZTogV2Ugb25seSB1c2UgdGhlc2UgY29tcG9uZW50cyBpbiBTZXJ2ZXIgQ29tcG9uZW50cywgYmVjYXVzZSB0aGUgcm9vdCBgVHdlZXRgXG4gKiBjb21wb25lbnQgdGhhdCB1c2VzIHRoZW0gaXMgYSBTZXJ2ZXIgQ29tcG9uZW50IGFuZCB5b3UgY2FuJ3QgcGFzcyBkb3duIGZ1bmN0aW9ucyB0byBhXG4gKiBjbGllbnQgY29tcG9uZW50IHVubGVzcyB0aGV5J3JlIFNlcnZlciBBY3Rpb25zLlxuICovIC8qKlxuICogQGRlcHJlY2F0ZWQgVXNlIGBUd2l0dGVyQ29tcG9uZW50c2AgaW5zdGVhZC5cbiAqLyBleHBvcnQgeyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/types.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/verified-badge.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedBadge: () => (/* binding */ VerifiedBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/index.js */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/icons/index.js\");\n/* harmony import */ var _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./verified-badge.module.css */ \"(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css\");\n\n\n\n\nconst VerifiedBadge = ({ user, className })=>{\n    const verified = user.verified || user.is_blue_verified || user.verified_type;\n    let icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_3__.Verified, {});\n    let iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedBlue;\n    if (verified) {\n        if (!user.is_blue_verified) {\n            iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedOld;\n        }\n        switch(user.verified_type){\n            case 'Government':\n                icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_3__.VerifiedGovernment, {});\n                iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedGovernment;\n                break;\n            case 'Business':\n                icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_3__.VerifiedBusiness, {});\n                iconClassName = null;\n                break;\n        }\n    }\n    return verified ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(className, iconClassName),\n        children: icon\n    }) : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css":
/*!*******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css ***!
  \*******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"verifiedOld\": \"verified-badge_verifiedOld__zcaba\",\n\t\"verifiedBlue\": \"verified-badge_verifiedBlue__s3_Vu\",\n\t\"verifiedGovernment\": \"verified-badge_verifiedGovernment__qRJxq\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9kaXN0L3R3aXR0ZXItdGhlbWUvdmVyaWZpZWQtYmFkZ2UubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxya2lcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx2ZXJpZmllZC1iYWRnZS5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInZlcmlmaWVkT2xkXCI6IFwidmVyaWZpZWQtYmFkZ2VfdmVyaWZpZWRPbGRfX3pjYWJhXCIsXG5cdFwidmVyaWZpZWRCbHVlXCI6IFwidmVyaWZpZWQtYmFkZ2VfdmVyaWZpZWRCbHVlX19zM19WdVwiLFxuXHRcInZlcmlmaWVkR292ZXJubWVudFwiOiBcInZlcmlmaWVkLWJhZGdlX3ZlcmlmaWVkR292ZXJubWVudF9fcVJKeHFcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/dist/utils.js":
/*!************************************************!*\
  !*** ./node_modules/react-tweet/dist/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enrichTweet: () => (/* binding */ enrichTweet),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getMediaUrl: () => (/* binding */ getMediaUrl),\n/* harmony export */   getMp4Video: () => (/* binding */ getMp4Video),\n/* harmony export */   getMp4Videos: () => (/* binding */ getMp4Videos)\n/* harmony export */ });\nconst getTweetUrl = (tweet)=>`https://x.com/${tweet.user.screen_name}/status/${tweet.id_str}`;\nconst getUserUrl = (usernameOrTweet)=>`https://x.com/${typeof usernameOrTweet === 'string' ? usernameOrTweet : usernameOrTweet.user.screen_name}`;\nconst getLikeUrl = (tweet)=>`https://x.com/intent/like?tweet_id=${tweet.id_str}`;\nconst getReplyUrl = (tweet)=>`https://x.com/intent/tweet?in_reply_to=${tweet.id_str}`;\nconst getFollowUrl = (tweet)=>`https://x.com/intent/follow?screen_name=${tweet.user.screen_name}`;\nconst getHashtagUrl = (hashtag)=>`https://x.com/hashtag/${hashtag.text}`;\nconst getSymbolUrl = (symbol)=>`https://x.com/search?q=%24${symbol.text}`;\nconst getInReplyToUrl = (tweet)=>`https://x.com/${tweet.in_reply_to_screen_name}/status/${tweet.in_reply_to_status_id_str}`;\nconst getMediaUrl = (media, size)=>{\n    const url = new URL(media.media_url_https);\n    const extension = url.pathname.split('.').pop();\n    if (!extension) return media.media_url_https;\n    url.pathname = url.pathname.replace(`.${extension}`, '');\n    url.searchParams.set('format', extension);\n    url.searchParams.set('name', size);\n    return url.toString();\n};\nconst getMp4Videos = (media)=>{\n    const { variants } = media.video_info;\n    const sortedMp4Videos = variants.filter((vid)=>vid.content_type === 'video/mp4').sort((a, b)=>{\n        var _b_bitrate, _a_bitrate;\n        return ((_b_bitrate = b.bitrate) != null ? _b_bitrate : 0) - ((_a_bitrate = a.bitrate) != null ? _a_bitrate : 0);\n    });\n    return sortedMp4Videos;\n};\nconst getMp4Video = (media)=>{\n    const mp4Videos = getMp4Videos(media);\n    // Skip the highest quality video and use the next quality\n    return mp4Videos.length > 1 ? mp4Videos[1] : mp4Videos[0];\n};\nconst formatNumber = (n)=>{\n    if (n > 999999) return `${(n / 1000000).toFixed(1)}M`;\n    if (n > 999) return `${(n / 1000).toFixed(1)}K`;\n    return n.toString();\n};\nfunction getEntities(tweet) {\n    const textMap = Array.from(tweet.text);\n    const result = [\n        {\n            indices: tweet.display_text_range,\n            type: 'text'\n        }\n    ];\n    addEntities(result, 'hashtag', tweet.entities.hashtags);\n    addEntities(result, 'mention', tweet.entities.user_mentions);\n    addEntities(result, 'url', tweet.entities.urls);\n    addEntities(result, 'symbol', tweet.entities.symbols);\n    if (tweet.entities.media) {\n        addEntities(result, 'media', tweet.entities.media);\n    }\n    fixRange(tweet, result);\n    return result.map((entity)=>{\n        const text = textMap.slice(entity.indices[0], entity.indices[1]).join('');\n        switch(entity.type){\n            case 'hashtag':\n                return Object.assign(entity, {\n                    href: getHashtagUrl(entity),\n                    text\n                });\n            case 'mention':\n                return Object.assign(entity, {\n                    href: getUserUrl(entity.screen_name),\n                    text\n                });\n            case 'url':\n            case 'media':\n                return Object.assign(entity, {\n                    href: entity.expanded_url,\n                    text: entity.display_url\n                });\n            case 'symbol':\n                return Object.assign(entity, {\n                    href: getSymbolUrl(entity),\n                    text\n                });\n            default:\n                return Object.assign(entity, {\n                    text\n                });\n        }\n    });\n}\nfunction addEntities(result, type, entities) {\n    for (const entity of entities){\n        for (const [i, item] of result.entries()){\n            if (item.indices[0] > entity.indices[0] || item.indices[1] < entity.indices[1]) {\n                continue;\n            }\n            const items = [\n                {\n                    ...entity,\n                    type\n                }\n            ];\n            if (item.indices[0] < entity.indices[0]) {\n                items.unshift({\n                    indices: [\n                        item.indices[0],\n                        entity.indices[0]\n                    ],\n                    type: 'text'\n                });\n            }\n            if (item.indices[1] > entity.indices[1]) {\n                items.push({\n                    indices: [\n                        entity.indices[1],\n                        item.indices[1]\n                    ],\n                    type: 'text'\n                });\n            }\n            result.splice(i, 1, ...items);\n            break; // Break out of the loop to avoid iterating over the new items\n        }\n    }\n}\n/**\n * Update display_text_range to work w/ Array.from\n * Array.from is unicode aware, unlike string.slice()\n */ function fixRange(tweet, entities) {\n    if (tweet.entities.media && tweet.entities.media[0].indices[0] < tweet.display_text_range[1]) {\n        tweet.display_text_range[1] = tweet.entities.media[0].indices[0];\n    }\n    const lastEntity = entities.at(-1);\n    if (lastEntity && lastEntity.indices[1] > tweet.display_text_range[1]) {\n        lastEntity.indices[1] = tweet.display_text_range[1];\n    }\n}\n/**\n * Enriches a tweet with additional data used to more easily use the tweet in a UI.\n */ const enrichTweet = (tweet)=>({\n        ...tweet,\n        url: getTweetUrl(tweet),\n        user: {\n            ...tweet.user,\n            url: getUserUrl(tweet),\n            follow_url: getFollowUrl(tweet)\n        },\n        like_url: getLikeUrl(tweet),\n        reply_url: getReplyUrl(tweet),\n        in_reply_to_url: tweet.in_reply_to_screen_name ? getInReplyToUrl(tweet) : undefined,\n        entities: getEntities(tweet),\n        quoted_tweet: tweet.quoted_tweet ? {\n            ...tweet.quoted_tweet,\n            url: getTweetUrl(tweet.quoted_tweet),\n            entities: getEntities(tweet.quoted_tweet)\n        } : undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/dist/utils.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e) {\n    var t, f, n = \"\";\n    if (\"string\" == typeof e || \"number\" == typeof e) n += e;\n    else if (\"object\" == typeof e) if (Array.isArray(e)) {\n        var o = e.length;\n        for(t = 0; t < o; t++)e[t] && (f = r(e[t])) && (n && (n += \" \"), n += f);\n    } else for(f in e)e[f] && (n && (n += \" \"), n += f);\n    return n;\n}\nfunction clsx() {\n    for(var e, t, f = 0, n = \"\", o = arguments.length; f < o; f++)(e = arguments[f]) && (t = r(e)) && (n && (n += \" \"), n += t);\n    return n;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10d2VldC9ub2RlX21vZHVsZXMvY2xzeC9kaXN0L2Nsc3gubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsU0FBU0EsRUFBRUMsQ0FBQztJQUFFLElBQUlDLEdBQUVDLEdBQUVDLElBQUU7SUFBRyxJQUFHLFlBQVUsT0FBT0gsS0FBRyxZQUFVLE9BQU9BLEdBQUVHLEtBQUdIO1NBQU8sSUFBRyxZQUFVLE9BQU9BLEdBQUUsSUFBR0ksTUFBTUMsT0FBTyxDQUFDTCxJQUFHO1FBQUMsSUFBSU0sSUFBRU4sRUFBRU8sTUFBTTtRQUFDLElBQUlOLElBQUUsR0FBRUEsSUFBRUssR0FBRUwsSUFBSUQsQ0FBQyxDQUFDQyxFQUFFLElBQUdDLENBQUFBLElBQUVILEVBQUVDLENBQUMsQ0FBQ0MsRUFBRSxNQUFLRSxDQUFBQSxLQUFJQSxDQUFBQSxLQUFHLEdBQUUsR0FBR0EsS0FBR0QsQ0FBQUE7SUFBRSxPQUFNLElBQUlBLEtBQUtGLEVBQUVBLENBQUMsQ0FBQ0UsRUFBRSxJQUFHQyxDQUFBQSxLQUFJQSxDQUFBQSxLQUFHLEdBQUUsR0FBR0EsS0FBR0QsQ0FBQUE7SUFBRyxPQUFPQztBQUFDO0FBQVEsU0FBU0s7SUFBTyxJQUFJLElBQUlSLEdBQUVDLEdBQUVDLElBQUUsR0FBRUMsSUFBRSxJQUFHRyxJQUFFRyxVQUFVRixNQUFNLEVBQUNMLElBQUVJLEdBQUVKLElBQUksQ0FBQ0YsSUFBRVMsU0FBUyxDQUFDUCxFQUFFLEtBQUlELENBQUFBLElBQUVGLEVBQUVDLEVBQUMsS0FBS0csQ0FBQUEsS0FBSUEsQ0FBQUEsS0FBRyxHQUFFLEdBQUdBLEtBQUdGLENBQUFBO0lBQUcsT0FBT0U7QUFBQztBQUFDLGlFQUFlSyxJQUFJQSxFQUFDIiwic291cmNlcyI6WyJEOlxccmtpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcbm9kZV9tb2R1bGVzXFxjbHN4XFxkaXN0XFxjbHN4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiByKGUpe3ZhciB0LGYsbj1cIlwiO2lmKFwic3RyaW5nXCI9PXR5cGVvZiBlfHxcIm51bWJlclwiPT10eXBlb2YgZSluKz1lO2Vsc2UgaWYoXCJvYmplY3RcIj09dHlwZW9mIGUpaWYoQXJyYXkuaXNBcnJheShlKSl7dmFyIG89ZS5sZW5ndGg7Zm9yKHQ9MDt0PG87dCsrKWVbdF0mJihmPXIoZVt0XSkpJiYobiYmKG4rPVwiIFwiKSxuKz1mKX1lbHNlIGZvcihmIGluIGUpZVtmXSYmKG4mJihuKz1cIiBcIiksbis9Zik7cmV0dXJuIG59ZXhwb3J0IGZ1bmN0aW9uIGNsc3goKXtmb3IodmFyIGUsdCxmPTAsbj1cIlwiLG89YXJndW1lbnRzLmxlbmd0aDtmPG87ZisrKShlPWFyZ3VtZW50c1tmXSkmJih0PXIoZSkpJiYobiYmKG4rPVwiIFwiKSxuKz10KTtyZXR1cm4gbn1leHBvcnQgZGVmYXVsdCBjbHN4OyJdLCJuYW1lcyI6WyJyIiwiZSIsInQiLCJmIiwibiIsIkFycmF5IiwiaXNBcnJheSIsIm8iLCJsZW5ndGgiLCJjbHN4IiwiYXJndW1lbnRzIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/react-tweet/node_modules/clsx/dist/clsx.mjs\n");

/***/ })

};
;