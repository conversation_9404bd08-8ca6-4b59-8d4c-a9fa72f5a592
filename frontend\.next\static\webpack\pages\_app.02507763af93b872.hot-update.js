"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/hoc/AuthSync.tsx":
/*!*************************************!*\
  !*** ./components/hoc/AuthSync.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/CustomLoader */ \"(pages-dir-browser)/./components/common/CustomLoader.tsx\");\n//Import Library\n\n\n\n\n\n// Public routes used to handle layouts\nconst publicRoutes = [\n    \"/home\",\n    \"/login\",\n    // \"/admin/login\",\n    \"/forgot-password\",\n    \"/reset-password/[passwordToken]\",\n    \"/declarationform/[...routes]\"\n];\n// Gets the display name of a JSX component for dev tools\nconst getDisplayName = (Component1)=>Component1.displayName || Component1.name || \"Component\";\nfunction withAuthSync(WrappedComponent) {\n    class MainComponent extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n        static async getInitialProps(ctx) {\n            const componentProps = WrappedComponent.getInitialProps && await WrappedComponent.getInitialProps(ctx);\n            if (ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies) {\n                const objCookies = ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies ? ctx.ctx.req.cookies : {};\n                componentProps.objCookies = objCookies;\n                return {\n                    ...componentProps\n                };\n            } else {\n                return {\n                    ...componentProps\n                };\n            }\n        }\n        componentDidMount() {\n            const { route } = this.props.router;\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.on(\"routeChangeComplete\", (url)=>{\n                if (url === \"/home\") {\n                    this.setState({\n                        isLoading: false\n                    });\n                }\n            });\n            setTimeout(()=>{\n                if (!this.state.cookie && publicRoutes.indexOf(route) === -1) {\n                    this.props.router.push(\"/home\");\n                    return;\n                }\n                this.setState({\n                    isLoading: false\n                });\n            }, 0);\n        }\n        componentWillUnmount() {\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.off(\"routeChangeComplete\", ()=>null);\n        }\n        componentDidUpdate(prevProps) {\n            if (!prevProps.objCookies && this.props.objCookies) {\n                this.setState({\n                    cookie: this.props && this.props.objCookies && this.props.objCookies[\"connect.sid\"] ? this.props.objCookies[\"connect.sid\"] : null,\n                    isLoading: true\n                });\n            }\n        }\n        render() {\n            const { router } = this.props;\n            const isPublicRoute = publicRoutes.indexOf(router.route) > -1;\n            return this.state.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 97,\n                columnNumber: 37\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n                isLoading: this.state.isLoading,\n                isPublicRoute: isPublicRoute,\n                ...this.props\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this);\n        }\n        constructor(props){\n            super(props);\n            this.state = {\n                isLoading: true,\n                cookie: this.props && this.props.objCookies && this.props.objCookies[\"connect.sid\"] ? this.props.objCookies[\"connect.sid\"] : null\n            };\n        }\n    }\n    MainComponent.displayName = \"withAuthSync(\".concat(getDisplayName(WrappedComponent), \")\");\n    return (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.connect)((state)=>state)(MainComponent);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withAuthSync);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/hoc/AuthSync.tsx\n"));

/***/ })

});