//Import Library
import React, { useEffect, useState } from 'react';
import _ from "lodash";
import Link from "next/link";
import ListGroup from "react-bootstrap/ListGroup";

//Import services/components
import RKICard from "../../components/common/RKICard";
import apiService from '../../services/apiService';
import CardPlaceholder from "../../components/common/placeholders/CardPlaceholder";


interface ListItemsProps {
  list: any[];
}

function ListItems(props: ListItemsProps) {
  const { list } = props;
  if (list.length > 0) {
    return (
      <ListGroup>
        {list.map((item: any, index: number) => {
          return (
            <ListGroup.Item
              key={index}>
              <Link href="/operation/[...routes]" as={`/operation/show/${item._id}`}>

                {item.title}

              </Link>
            </ListGroup.Item>
          );
        })}
      </ListGroup>
    );
  }
  return null;
}

interface CardDetailsProps {
  operation: {
    body: string;
    id: string;
  };
}

function CardDetails(props: CardDetailsProps) {
  const { operation } = props;
  return (
    <Link
      href='/operation/[...routes]'
      as={`/operation/show/${operation.id}`}
      className='active-op-project'>

      <span className="project-title link">{operation.body}</span>

    </Link>
  );
}

interface OngoingOperationsProps {
  t: (key: string) => string;
  fetchOngoingOperations: (operations: any[]) => void;
}

function OngoingOperations(props: OngoingOperationsProps) {
  const {t, fetchOngoingOperations} = props;
  const cardHeader = t("OngoingOperations");
  const [operation, setOperation] = useState<{ body: string; id: string; list: any[] }>({ body: "", id: "", list: [] });
  const [loading, setLoading] = useState(true);

  const setEmptyNotice = () => {
    setOperation({ body: t("Nooperationavailable"), id: "", list: [] })
  };

  const fetchOperation = async () => {
    const operationParams = {
      query: { status: [] },
      sort: { created_at: "desc" },
      limit: 10,
      select: "-description -status -start_date -end_date -timeline -world_region -region -hazard_type -hazard -syndrome -partners -vspace -vspace_visibility -images -user -created_at -updated_at"
    };
    const statusId = await fetchOperationStatus();

    if (statusId) {
      operationParams.query.status = statusId;
      try {
        setLoading(true);
        const operations = await apiService.get('/operation', operationParams);
        setLoading(false);
        if (Array.isArray(operations.data) && operations.data.length > 0) {
          setOperation({ body: operations.data[0].title, id: operations.data[0]._id, list: operations.data })
          fetchOngoingOperations(operations.data);
        } else {
          setEmptyNotice()
        }
      } catch (e) {
        setEmptyNotice()
      }
    } else {
      setEmptyNotice()
    }

  };

  const fetchOperationStatus = async () => {
    const response = await apiService.get('/operation_status');
    if (response && response.data && response.data.length > 0) {
      const statusId = []
      _.forEach(response.data, function (item: any) {
        if (item.title == 'Ongoing') {
          statusId.push(item._id);
        }
      });
      return statusId;
    }
    return false;
  };

  useEffect(() => {
    fetchOperation();
  }, []);

  const list = {
    heading: cardHeader,
    body: <ListItems list={operation.list} />
  };

  return (
    <RKICard
      dialogClassName={"ongoing-project-list"}
      list={list}
      header={cardHeader}
      body={loading ? <CardPlaceholder /> : <CardDetails operation={operation} />}
    />
  )
}

export default OngoingOperations;