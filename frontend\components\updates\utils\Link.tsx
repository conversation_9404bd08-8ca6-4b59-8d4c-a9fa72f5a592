//Import Library
import React from "react";
import { Row, Col } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faGlobe } from "@fortawesome/free-solid-svg-icons";

//TOTO refactor
interface LinksProps {
  link: Array<{
    url: string;
    title: string;
  }>;
}

const Links = (props: LinksProps): React.ReactElement => {
    const { link } = props;

    return (
        <div>
            {
                link && link.map((item: any, index: number) => {
                    return (
                        <Row key={index}>
                            <Col className="p-0" >
                                <div className="d-flex align-items-center mb-2" >
                                    <FontAwesomeIcon icon={faGlobe} color="#87CEFA" />&nbsp;&nbsp;
                                    <a href={item.link} target="_blank">
                                        <span>{item.title}</span>
                                    </a>
                                </div>
                            </Col>
                        </Row>
                    );
                })
            }
        </div >
    )

}

export default Links;