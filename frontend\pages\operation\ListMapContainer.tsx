//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";

//Import services/components
import RKIMAP1 from "../../components/common/RKIMap1";
import RKIMapMarker from "../../components/common/RKIMapMarker";
import { useTranslation } from 'next-i18next';

interface Operation {
  _id: string;
  title: string;
  country?: {
    _id: string;
    coordinates?: Array<{
      latitude: string;
      longitude: string;
    }>;
  };
}

interface ListMapContainerProps {
  operations: Operation[];
}

const ListMapContainer = (props: ListMapContainerProps) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language;
  const { operations } = props;
  const [points, setPoints] = useState([]);
  const [activeMarker, setactiveMarker]: any = useState({});
  const [markerInfo, setMarkerInfo]: any = useState({});
  const [groupedOperations, setGroupedOperations]: any = useState({});

  const MarkerInfo = (Markerprops: { info: { countryId?: string } }) => {
    const { info } = Markerprops;
    if (info && info.countryId && groupedOperations[info.countryId]) {
      return (
        <ul>
          {groupedOperations[info.countryId].map((item, index) => {
            return (
              <li key={index}>
                <a href={`${currentLang}/operation/show/${item._id}`}>{item.title}</a>
              </li>
            );
          })}
        </ul>
      );
    }
    return null;
  };

  const resetMarker = () => {
    setactiveMarker(null);
    setMarkerInfo(null);
  };

  const onMarkerClick = (propsinit: { name: string; id: string; countryId: string }, marker: any, e: any) => {
    resetMarker();
    setactiveMarker(marker);
    setMarkerInfo({
      name: propsinit.name,
      id: propsinit.id,
      countryId: propsinit.countryId,
    });
  };

  const setPointersFromOperations = () => {
    const filteroperationPoints = [];
    _.forEach(operations, (operation) => {
      filteroperationPoints.push({
        title: operation.title,
        id: operation._id,
        lat:
          operation.country &&
          operation.country.coordinates &&
          parseFloat(operation.country.coordinates[0].latitude),
        lng:
          operation.country &&
          operation.country.coordinates &&
          parseFloat(operation.country.coordinates[0].longitude),
        countryId: operation.country && operation.country._id,
      });
    });
    setPoints([...filteroperationPoints]);
  };

  useEffect(() => {
    setPointersFromOperations();
    if (operations && operations.length > 0) {
      const groupByCountriesOperation = _.groupBy(operations, "country._id");
      setGroupedOperations(groupByCountriesOperation);
    }
  }, [operations]);

  return (
    <RKIMAP1
      onClose={resetMarker}
      points={points}
      language={currentLang}
      activeMarker={activeMarker}
      markerInfo={<MarkerInfo info={markerInfo} />}
    >
      {points.length >= 1
        ? points.map((item, index) => {
            return (
              <RKIMapMarker
                key={index}
                name={item.title}
                id={item.id}
                countryId={item.countryId}
                icon={{
                  url: "/images/map-marker-white.svg",
                }}
                onClick={onMarkerClick}
                position={item}
              />
            );
          })
        : null}
    </RKIMAP1>
  );
};

export default ListMapContainer;
