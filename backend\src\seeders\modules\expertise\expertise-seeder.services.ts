//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { expertises } from "../../data/expertise";
import { ExpertiseInterface } from "src/interfaces/expertise.interface";

/**
 * Service dealing with Expertise.
 *
 * @class
 */
@Injectable()
export class ExpertiseSeederService {

  constructor(
    @InjectModel('Expertise') private expertiseModel: Model<ExpertiseInterface>
  ) {}

  /**
   * Seed all Expertise.
   *
   * @function
   */
  create(): Array<Promise<ExpertiseInterface>> {
    return expertises.map(async (classif: any) => {
      return await this.expertiseModel
        .findOne({ title: classif.title })
        .exec()
        .then(async dbExpertise => {
          // We check if a Expertise already exists.
          // If it does don't create a new one.
          if (dbExpertise) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.expertiseModel.create(classif),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}