//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const ExpertiseTable = (props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectExpertise, setSelectExpertise] = useState({});


    const expertiseParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("adminsetting.Expertise.Table.Title"),
            selector: "title",
        },
        {
            name: t("adminsetting.Expertise.Table.Action"),
            selector: "",
            cell: (d: any) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_expertise/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={(e) => userAction(d, e)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    const getExpertiseData = async () => {
        setLoading(true);
        const response = await apiService.get("/expertise", expertiseParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page: any) => {
        expertiseParams.limit = perPage;
        expertiseParams.page = page;
        getExpertiseData();
    };

    const handlePerRowsChange = async (newPerPage: any, page: any) => {
        expertiseParams.limit = newPerPage;
        expertiseParams.page = page;
        setLoading(true);
        const response = await apiService.get("/expertise", expertiseParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row: any, e: any) => {
        e.preventDefault();
        setSelectExpertise(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/expertise/${selectExpertise}`);
            getExpertiseData();
            setModal(false);
            toast.success(t("adminsetting.Expertise.Table.expertiseDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.Expertise.Table.errorDeletingExpertise"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        getExpertiseData();
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title> {t("adminsetting.Expertise.Table.DeleteExpertise")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.Expertise.Table.AreyousurewanttodeletethisExpertise?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.Expertise.Table.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.Expertise.Table.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default ExpertiseTable;
