//Import Library
import {useEffect, useState} from "react";
import {Col, Container, FormControl, FormGroup, FormLabel, Row} from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';
const types = [
  {
    _id: "operation",
    title: "Operations"
  },
  {
    _id: "institution",
    title: "Organisations"
  },
  {
    _id: "event",
    title: "Events"
  },
  {
    _id: "project",
    title: "Projects"
  },
  {
    _id: "updates",
    title: "Updates"
  },
  {
    _id: "vspace",
    title: "Virtual Spaces"
  }
];


const ContentTableFilter = ({filterText, onFilter, onFilterTypeChange, onClear, filterType }: any) => {
  const { t } = useTranslation('common');
  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={6} md={4} className="ps-0 align-self-end" >
          <FormGroup>
          <FormControl
            type="text"
            className="searchInput"
            placeholder={t("adminsetting.content.table.Search")}
            aria-label="Search"
            value={filterText}
            onChange={onFilter}
          />
          </FormGroup>
        </Col>
        <Col xs={6} md={4}>
          <FormGroup as={Row}>
            <FormLabel column sm={3} lg={2} className="me-2">{t('adminsetting.content.table.Type')}</FormLabel>
            <Col className="ps-md-0">
              <FormControl
                as="select"
                aria-label="Type"
                onChange={onFilterTypeChange}
                value={filterType}>
                {types.map((item, index) => {
                  return (
                    <option key={index} value={item._id}>{item.title}</option>
                  )
                })}
              </FormControl>
            </Col>
          </FormGroup>
        </Col>
      </Row>
    </Container>
  )
};

export default ContentTableFilter;
