declare module 'redux-auth-wrapper/connectedAuthWrapper' {
  import { ComponentType } from 'react';
  import { ConnectedComponent } from 'react-redux';

  interface AuthWrapperConfig {
    authenticatedSelector: (state: any, ownProps?: any) => boolean;
    redirectAction?: (location: any) => any;
    wrapperDisplayName?: string;
    FailureComponent?: ComponentType<any>;
    predicate?: (authData: any) => boolean;
    allowRedirectBack?: boolean;
    redirectPath?: string | ((state: any, ownProps: any) => string);
  }

  function connectedAuthWrapper(config: AuthWrapperConfig): <T>(component: ComponentType<T>) => ConnectedComponent<ComponentType<T>, any>;

  export default connectedAuthWrapper;
}
