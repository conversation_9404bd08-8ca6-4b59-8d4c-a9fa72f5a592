//Import Library
import { Col, Row } from "react-bootstrap";

//Import services/components
import ReadMoreContainer from "../../components/common/readMore/readMore";

interface HazardCoverSectionProps {
  hazardData: {
    title?: {
      [key: string]: string;
    };
    description?: {
      [key: string]: string;
    };
    picture?: string;
  };
  currentLang: string;
}

const HazardCoverSection = (props: HazardCoverSectionProps) => {
    const hazardData = props.hazardData;
    const currentLang = props.currentLang;
    return(
        <>
            <Row>
                <Col className="ps-4">
                    <h2>
                    {hazardData.title && hazardData.title[currentLang]
                        ? hazardData.title[currentLang]
                        : ""}
                    </h2>
                    <ReadMoreContainer
                    description={
                        hazardData.description && hazardData.description[currentLang]
                        ? hazardData.description[currentLang]
                        : ""
                    }
                    />
                </Col>
                <Col style={{ display: "flex" }}>
                    <img
                    src={hazardData.picture}
                    style={{ width: "100%", height: "400px", backgroundSize: "cover" }}
                    alt="banner"
                    />
                </Col>
            </Row>
        </>
    )
};

export default HazardCoverSection;