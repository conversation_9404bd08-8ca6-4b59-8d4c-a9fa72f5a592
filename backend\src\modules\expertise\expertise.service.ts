//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { ExpertiseInterface } from '../../interfaces/expertise.interface';
import { CreateExpertiseDto } from './dto/create-expertise.dto';
import { UpdateExpertiseDto } from './dto/update-expertise.dto';

const FindExpertise = 'Could not find Expertise.';
@Injectable()
export class ExpertiseService {
  constructor(
    @InjectModel('Expertise') private expertiseModel: Model<ExpertiseInterface>,
  ) {}

  async create(
    createExpertiseDto: CreateExpertiseDto,
  ): Promise<ExpertiseInterface> {
    const createdExpertise = new this.expertiseModel(createExpertiseDto);
    return createdExpertise.save();
  }

  async findAll(query): Promise<ExpertiseInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};

    if (_filter.title) {
      _filter.title = _filter.title
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(_filter.title, 'gi');
      _filter['title'] = regex;
    }

    return await this.expertiseModel.paginate(_filter, options);
  }

  async get(expertiseId): Promise<ExpertiseInterface[]> {
    let _result;
    try {
      _result = await this.expertiseModel.findById(expertiseId).exec();
    } catch (error) {
      throw new NotFoundException(FindExpertise);
    }
    if (!_result) {
      throw new NotFoundException(FindExpertise);
    }
    return _result;
  }

  async update(expertiseId: any, updateExpertiseDto: UpdateExpertiseDto) {
    const getById: any = await this.expertiseModel.findById(expertiseId).exec();
    const updatedData = new this.expertiseModel(updateExpertiseDto);
    Object.keys(updateExpertiseDto).forEach((d) => {
      getById[d] = updatedData[d];
    });
    getById.updated_at = new Date();
    return getById.save();
  }

  async delete(expertiseId: string) {
    const result = await this.expertiseModel
      .deleteOne({ _id: expertiseId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(FindExpertise);
    }
  }
}
