//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { TaxonomyIconInterface } from '../../interfaces/taxonomy-icon.interface';
import { CreateTaxonomyIconDto } from './dto/create-taxonomy-icon.dto';
import { UpdateTaxonomyIconDto } from './dto/update-taxonomy-icon.dto';
const TaxonomyIcon = "Could not find TaxonomyIcon."
@Injectable()
export class TaxonomyIconService {
  constructor(
    @InjectModel('TaxonomyIcon') private taxonomyIconModel: Model<TaxonomyIconInterface>
  ) { }

  async create(createTaxonomyIconDto: CreateTaxonomyIconDto): Promise<TaxonomyIconInterface> {
    const createdTaxonomyIcon = new this.taxonomyIconModel(createTaxonomyIconDto);
    return createdTaxonomyIcon.save();
  }

  async findAll(query): Promise<TaxonomyIconInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };
    

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.taxonomyIconModel.paginate(_filter, options);
  }

  async get(taxonomyIconId): Promise<TaxonomyIconInterface[]> {
    
    let _result;
    try {
      _result = await this.taxonomyIconModel.findById(taxonomyIconId).exec();
    } catch (error) {
      throw new NotFoundException(TaxonomyIcon);
    }
    if (!_result) {
      throw new NotFoundException(TaxonomyIcon);
    }
    return _result;
  }

  async update(taxonomyIconId: any, updateTaxonomyIconDto: UpdateTaxonomyIconDto) {
    const getById: any = await this.taxonomyIconModel.findById(taxonomyIconId).exec();
    const updatedData = new this.taxonomyIconModel(updateTaxonomyIconDto);
    try {
      Object.keys(updateTaxonomyIconDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException(TaxonomyIcon);
    }
    return getById;
  }

  async delete(taxonomyIconId: string) {
    const result = await this.taxonomyIconModel.deleteOne({ _id: taxonomyIconId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(TaxonomyIcon);
    }
  }
}
