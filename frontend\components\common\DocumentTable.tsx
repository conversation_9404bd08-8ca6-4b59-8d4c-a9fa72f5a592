//Import Library
import React from "react";
import moment from "moment";

//Import services/components
import RKITable from '../../components/common/RKITable';
import { useTranslation } from 'next-i18next';

interface DocumentTableProps {
  docs: any[];
  docsDescription: string;
  sortProps: (sortObj: { columnSelector: string; sortDirection: string }) => void;
  loading: boolean;
}

const DocumentTable: React.FC<DocumentTableProps> = ({ docs, docsDescription, sortProps, loading }) => {

  const handleSort = async (column: any, sortDirection: string) => {
    const objSlect = {
      columnSelector: column.selector,
      sortDirection: sortDirection
    }
    sortProps(objSlect);
  };

  const { t } = useTranslation('common');


  const columns = [
    {
      name: t("FileType"),
      width: "15%",
      selector: 'extension',
      cell: (d: any) => d && d.extension && d.extension,
    },
    {
      name: t("FileName"),
      width: "25%",
      selector: "document_title",
      cell: (d: any) => d && d.original_name && <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target="_blank">{d.original_name.split('.').slice(0, -1).join('.')}</a>,
      sortable: true
    },
    {
      name: t("Description"),
      selector: 'description',
      cell: (d: any) => d && d.description && d.description,
    },
    {
      name: t("UploadedDate"),
      width: "25%",
      selector: 'doc_created_at',
      cell: (d: any) => d && d.updated_at && moment(d.updated_at).format('MM/DD/YYYY'),
      sortable: true
    }
  ];

  return (
    <RKITable
      columns={columns}
      data={docs}
      pagServer={true}
      onSort={handleSort}
      persistTableHead
      loading={loading}
    />

  )
}

export default DocumentTable;
