// Core application types and interfaces

export interface User {
  _id: string;
  username: string;
  email: string;
  role: Role;
  institution?: Institution;
  region?: Region[];
  country?: Country;
  enabled: boolean;
  password?: string;
  roles?: string[];
}

export interface Role {
  _id: string;
  title: string;
  description?: string;
}

export interface Country {
  _id: string;
  title: string;
  code: string;
  dial_code: string;
  world_region?: WorldRegion;
}

export interface WorldRegion {
  _id: string;
  title: string;
  description?: string;
}

export interface Region {
  _id: string;
  title: string;
  description?: string;
  country?: Country;
}

export interface Institution {
  _id: string;
  title: string;
  description?: string;
  type?: InstitutionType;
  country?: Country;
  region?: Region;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  focal_points?: User[];
  images?: MediaFile[];
  images_src?: string[];
}

export interface InstitutionType {
  _id: string;
  title: string;
  description?: string;
}

export interface MediaFile {
  _id: string;
  name: string;
  original_name?: string;
  src?: string;
  caption?: string;
  alt?: string;
}

export interface Hazard {
  _id: string;
  title: LocalizedText;
  description: LocalizedText;
  monitored: boolean;
  region?: Region;
  images?: MediaFile[];
  images_src?: string[];
}

export interface LocalizedText {
  en: string;
  fr: string;
  de: string;
}

export interface Language {
  _id: string;
  title: string;
  abbr: string;
}

export interface Event {
  _id: string;
  title: string;
  description: string;
  start_date: string;
  end_date?: string;
  status?: EventStatus;
  hazard?: Hazard;
  country?: Country;
  region?: Region;
  images?: MediaFile[];
  images_src?: string[];
  documents?: MediaFile[];
  doc_src?: string[];
}

export interface EventStatus {
  _id: string;
  title: string;
  description?: string;
}

export interface Operation {
  _id: string;
  title: string;
  description: string;
  start_date: string;
  end_date?: string;
  status?: OperationStatus;
  country?: Country;
  region?: Region;
  partners?: Institution[];
  images?: MediaFile[];
  images_src?: string[];
  documents?: MediaFile[];
  doc_src?: string[];
}

export interface OperationStatus {
  _id: string;
  title: string;
  description?: string;
}

export interface Project {
  _id: string;
  title: string;
  description: string;
  start_date: string;
  end_date?: string;
  status?: ProjectStatus;
  country?: Country;
  region?: Region;
  images?: MediaFile[];
  images_src?: string[];
  documents?: MediaFile[];
  doc_src?: string[];
}

export interface ProjectStatus {
  _id: string;
  title: string;
  description?: string;
}

export interface VSpace {
  _id: string;
  title: string;
  description: string;
  start_date: string;
  end_date?: string;
  visibility: boolean;
  owner: User;
  members: User[];
  subscribers: User[];
  nonMembers: string[];
  images?: MediaFile[];
  images_src?: string[];
  documents?: MediaFile[];
  doc_src?: string[];
}

export interface Expertise {
  _id: string;
  title: string;
  description?: string;
}

export interface Syndrome {
  _id: string;
  title: string;
  description?: string;
}

export interface HazardType {
  _id: string;
  title: string;
  description?: string;
}

export interface RiskLevel {
  _id: string;
  title: string;
  description?: string;
  level: number;
}

export interface DeploymentStatus {
  _id: string;
  title: string;
  description?: string;
}

export interface UpdateType {
  _id: string;
  title: string;
  description?: string;
}

export interface InstitutionNetwork {
  _id: string;
  title: string;
  description?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
  total?: number;
  totalCount?: number;
  page?: number;
  limit?: number;
}

export interface PaginationParams {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface TableColumn {
  name: string;
  selector: (row: any) => any;
  sortable?: boolean;
  cell?: (row: any) => React.ReactNode;
  width?: string;
  wrap?: boolean;
}

// Form types
export interface FormState {
  [key: string]: any;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  validator?: (value: any) => boolean;
  message?: string;
}

// Event handler types
export interface ChangeEvent extends React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement> {}
export interface FormEvent extends React.FormEvent<HTMLFormElement> {}
export interface MouseEvent extends React.MouseEvent<HTMLElement> {}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface TableProps extends BaseComponentProps {
  data: any[];
  columns: TableColumn[];
  loading?: boolean;
  pagination?: boolean;
  onPageChange?: (page: number) => void;
  onPerRowsChange?: (newPerPage: number, page: number) => void;
  onSort?: (column: any, sortDirection: string) => void;
  totalRows?: number;
  paginationServer?: boolean;
}

// Redux state types
export interface RootState {
  auth: AuthState;
  ui: UIState;
  data: DataState;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
  loading: boolean;
}

export interface UIState {
  loading: boolean;
  error: string | null;
  notifications: Notification[];
}

export interface DataState {
  countries: Country[];
  regions: Region[];
  institutions: Institution[];
  users: User[];
  events: Event[];
  operations: Operation[];
  projects: Project[];
  vspaces: VSpace[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
}

// Content management types
export interface ContentItem {
  _id: string;
  title: string;
  type: string;
  user?: User;
  created_at: string;
  updated_at?: string;
  status?: string;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
