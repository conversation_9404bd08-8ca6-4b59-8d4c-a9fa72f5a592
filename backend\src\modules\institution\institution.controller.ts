//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
  Req,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ACGuard,
  InjectRolesBuilder,
  RolesBuilder,
  UseRoles,
} from 'nest-access-control';

//Import services/components
import { CreateInstitutionDto } from './dto/create-institution.dto';
import { UpdateInstitutionDto } from './dto/update-institution.dto';
import { InstitutionService } from './institution.service';
import { EmailService } from './../../email.service';
import { ImageService } from './../image/image.service';
import { SessionGuard } from 'src/auth/session-guard';
import { ResponseError } from '../../common/dto/response.dto';
import { FilesService } from '../files/files.service';
import { FlagService } from '../flag/flag.service';
import { UpdateService } from '../updates/update.service';

@Controller('institution')
@UseGuards(SessionGuard)
export class InstitutionController {
  constructor(
    private readonly _institutionService: InstitutionService,
    private readonly _imageService: ImageService,
    private readonly _emailService: EmailService,
    private readonly _filesService: FilesService,
    private readonly _flagService: FlagService,
    private readonly _updateService: UpdateService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) {}

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'institution',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(
    @Body() createInstitutionDto: CreateInstitutionDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    createInstitutionDto['user'] = user._id;
    const resp: any = await this._institutionService.create(
      createInstitutionDto,
    );
    const headerimageIds = [];
    if (resp?.header?._id) {
      headerimageIds.push(resp.header._id);
    }
    if (resp.images && resp.images.length > 0) {
      resp.images.forEach((d) => {
        if (d._id) {
          headerimageIds.push(d._id);
        }
      });
    }
    if (headerimageIds.length > 0) {
      await this._imageService.bulkUpdate(headerimageIds);
    }
    const imageIds = this._institutionService['images']
      ? this._institutionService['images'].map((d) => d._id)
      : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
    if (resp.partners && resp.partners.length > 0) {
      const partnerIds = resp.partners.map((d) => d._id);
      await this._institutionService.bulkUpdate(
        { _id: { $in: partnerIds } },
        { partners: resp._id },
      );
    }
    const documentIds = this._institutionService['document']
      ? this._institutionService['document'].map((d) => d['_id'])
      : [];
    if (documentIds.length > 0) {
      await this._filesService.bulkUpdate(documentIds);
    }
    return resp;
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'institution',
    action: 'read',
    possession: 'any',
  })
  @Get()
  async findAll(@Query() query: any) {
    return this._institutionService.findAll(query);
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'institution',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') institutionId: string) {
    return this._institutionService.get(institutionId);
  }

  @UseGuards(ACGuard)
  @Patch(':id')
  async update(
    @Param('id') institutionId: string,
    @Body() updateInstitutionDto: UpdateInstitutionDto,
    @Req() request: Request,
  ) {
    const permission = await this.updateInstution(request, institutionId);
    const existData = await this._institutionService.get(institutionId);
    //request.user['_id'] == existData['user'] Adding this condition as organisation user who created organsiation can update it 
    if (permission || request.user['_id'] == existData['user']) {
      const resp: any = await this._institutionService.update(
        institutionId,
        updateInstitutionDto,
      );
      await this.instutionHeader(resp);
      const documentIds = this._institutionService['document']
        ? this._institutionService['document'].map((d) => d['_id'])
        : [];
      const { addInstitutionIds, deleteIds } = await this.instutionControler(
        documentIds,
        updateInstitutionDto,
        institutionId,
      );

      if (addInstitutionIds && addInstitutionIds.length > 0) {
        await this._institutionService.bulkUpdate(
          { _id: { $in: addInstitutionIds } },
          { partners: resp._id },
        );
      }

      if (deleteIds && deleteIds.length > 0) {
        await this._institutionService.bulkUpdate(
          { _id: { $in: deleteIds } },
          { partners: [] },
        );
      }

      return resp;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  @UseGuards(ACGuard)
  @Patch(':id/updateFocalPoints')
  async updateFocalPoints(
    @Param('id') institutionId: string,
    @Body() updateInstitutionDto: UpdateInstitutionDto,
    @Req() request: Request,
  ) {
    const permission = await this.updateInstution(request, institutionId);
    if (permission) {
      const resp: any = await this._institutionService.updateFocalPoints(
        institutionId,
        updateInstitutionDto,
      );
      return resp;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  async instutionControler(
    documentIds: any,
    updateInstitutionDto: UpdateInstitutionDto,
    institutionId: string,
  ) {
    if (documentIds.length > 0) {
      await this._filesService.bulkUpdate(documentIds);
    }

    const deleteIds = [];
    let addInstitutionIds = [];
    if (
      updateInstitutionDto['partners'] &&
      updateInstitutionDto['partners'].length > -1
    ) {
      const getById: any = await this._institutionService.get(institutionId);
      const existingIds = getById.partners
        .filter((d) => d._id)
        .map((d) => d._id.toString());
      existingIds.forEach((element) => {
        const index = updateInstitutionDto['partners'].indexOf(element);
        if (index === -1) {
          deleteIds.push(element);
        } else {
          delete updateInstitutionDto['partners'][index];
        }
      });
      addInstitutionIds = updateInstitutionDto['partners'];
    }
    return { addInstitutionIds, deleteIds };
  }

  private async instutionHeader(resp: any) {
    const headerimageIds = [];
    if (resp?.header) {
      headerimageIds.push(resp.header);
    }
    if (resp?.images?.length > 0) {
      resp.images.forEach((d) => {
        if (d._id) {
          headerimageIds.push(d._id);
        }
      });
    }
    if (headerimageIds?.length > 0) {
      await this._imageService.bulkUpdate(headerimageIds);
    }
    const imageIds = this._institutionService['images']
      ? this._institutionService['images'].map((d) => d._id)
      : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
  }

  private instutionImagepush(d: any, headerimageIds: any[]) {
    if (d._id) {
      headerimageIds.push(d._id);
    }
    return headerimageIds;
  }

  private async updateInstution(request, institutionId: string) {
    delete request.body.__v;
    const user: any = request.user;
    const oldInstitution: any = await this._institutionService.get(
      institutionId,
    );
    const instituionUserId = oldInstitution.user
      ? oldInstitution.user._id
      : null;
    const permission =
      user._id === instituionUserId
        ? (this.roleBuilder.can(user.roles).updateOwn('institution').granted || this.roleBuilder.can(user.roles).updateOwn('institution_focal_point').granted)
        : (this.roleBuilder.can(user.roles).updateAny('institution').granted || this.roleBuilder.can(user.roles).updateAny('institution_focal_point').granted);
    return permission;
  }

  @UseGuards(ACGuard)
  @Delete(':id')
  async remove(@Param('id') institutionId: string, @Req() request: Request) {
    try {
      const user: any = request.user;
      const oldInstitution: any = await this._institutionService.get(
        institutionId,
      );
      const instituionUserId = oldInstitution.user
        ? oldInstitution.user._id
        : null;
      const permission =
        user._id === instituionUserId
          ? this.roleBuilder.can(user.roles).updateOwn('institution').granted
          : this.roleBuilder.can(user.roles).updateAny('institution').granted;
      if (permission) {
        this._updateService.deleteUpdateAssociatedWithEntity(institutionId, "parent_institution");
        const deletedData = await this._institutionService.delete(
          institutionId,
        );
        this._flagService.deleteFlagAssociatedWithEntity(institutionId);
        const headerimageIds = [];
        await this.headerDelete(deletedData, headerimageIds);

        return deletedData;
      } else {
        return new ResponseError('INSTITUTION.ERROR.ACCESS_DENIED');
      }
    } catch (e) {
      return new ResponseError('INSTITUTION.ERROR.NOT_FOUND');
    }
  }

  private async headerDelete(deletedData: any, headerimageIds: any[]) {
    let instutionHeader;
    if (deletedData?.header?._id) {
      headerimageIds.push(deletedData.header._id);
    }
    if (deletedData.images && deletedData.images.length > 0) {
      deletedData.images.forEach((d) => {
        instutionHeader = this.instutionImagepush(d, headerimageIds);
      });
    }
    if (instutionHeader && instutionHeader.length > 0) {
      await this._imageService.bulkDelete(instutionHeader);
    }
    const imageIds = this._institutionService['images']
      ? this._institutionService['images'].map((d) => d['_id'])
      : [];
    if (imageIds.length > 0) {
      await this._filesService.bulkDelete(imageIds);
    }
    const documentIds = this._institutionService['document']
      ? this._institutionService['document'].map((d) => d['_id'])
      : [];
    if (documentIds.length > 0) {
      await this._filesService.bulkDelete(documentIds);
    }
  }
}
