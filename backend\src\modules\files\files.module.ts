//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { FilesController } from './files.controller';
import { FilesService } from './files.service';
// SCHEMAS
import { FilesSchema } from '../../schemas/files.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Files', schema: FilesSchema }
    ])
  ],
  controllers: [FilesController],
  providers: [FilesService],
})

export class FilesModule { }