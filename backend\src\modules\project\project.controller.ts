//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Req,
  Patch,
  Delete,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ACGuard,
  InjectRolesBuilder,
  RolesBuilder,
  UseRoles,
} from 'nest-access-control';

//Import services/components
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ProjectService } from './project.service';
import { InstitutionService } from './../institution/institution.service';
import { EmailService } from './../../email.service';
import { SessionGuard } from 'src/auth/session-guard';
import { ProjectInterface } from 'src/interfaces/project.interface';
import { FlagService } from '../flag/flag.service';
import { UpdateService } from '../updates/update.service';

@Controller('project')
@UseGuards(SessionGuard)
export class ProjectController {
  constructor(
    private readonly _projectService: ProjectService,
    private readonly _emailService: EmailService,
    private readonly _institutionService: InstitutionService,
    private readonly _flagService: FlagService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
    private readonly _updateService: UpdateService
  ) {}

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'project',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(
    @Body() createProjectDto: CreateProjectDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    createProjectDto['user'] = user._id;
    const _project = await this._projectService.create(createProjectDto, user);
    if (
      _project['partner_institutions'] &&
      _project['partner_institutions'].length > 0
    ) {
      let instIds = [];
      _project['partner_institutions'].forEach((d) => {
        if (d.partner_institution && d.partner_institution.length > 0) {
          instIds = d.partner_institution;
        }
      });
      await this.projectInstution(instIds, createProjectDto, _project);
    }
    return _project;
  }

  private async projectInstution(
    instIds: any[],
    createProjectDto: CreateProjectDto,
    _project: ProjectInterface,
  ) {
    if (instIds.length > 0) {
      const foundInstitutions: any = await this._institutionService.find({
        _id: { $in: instIds },
      });
      if (foundInstitutions && foundInstitutions.length > 0) {
        const languageCode = createProjectDto['language']
          ? createProjectDto['language']
          : 'en';
        foundInstitutions.forEach((d) => {
          if (d.focal_points && d.focal_points.length > 0) {
            d.focal_points.forEach(async (e) => {
              await this._emailService.projectInvite(e, _project, languageCode);
            });
          }
        });
      }
    }
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'project',
    action: 'read',
    possession: 'any',
  })
  @Get()
  async findAll(@Query() query: any) {
    return this._projectService.findAll(query);
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'project',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') projectId: string) {
    return this._projectService.get(projectId);
  }

  @UseGuards(ACGuard)
  @Patch(':id')
  async update(
    @Param('id') projectId: string,
    @Body() updateProjectDto: UpdateProjectDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    const oldProject: any = await this._projectService.get(projectId);
    const projectUserId = oldProject.user ? oldProject.user._id : null;
    const permission =
      user._id == projectUserId
        ? this.roleBuilder.can(user.roles).updateOwn('project').granted
        : this.roleBuilder.can(user.roles).updateAny('project').granted;
    if (permission) {
      const _project = this._projectService.update(
        projectId,
        updateProjectDto,
        user,
      );
      return _project;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  @UseGuards(ACGuard)
  @Delete(':id')
  async remove(@Param('id') projectId: string, @Req() request: Request) {
    const project: any = await this._projectService.get(projectId);
    const user: any = request.user;
    const projectUserId = project.user ? project.user._id : null;
    const permission =
      user._id == projectUserId
        ? this.roleBuilder.can(user.roles).deleteOwn('project').granted
        : this.roleBuilder.can(user.roles).deleteAny('project').granted;
    if (permission) {
      this._flagService.deleteFlagAssociatedWithEntity(projectId);
      this._updateService.deleteUpdateAssociatedWithEntity(projectId, "parent_project");
      const _language = this._projectService.delete(projectId);
      return _language;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }
}
