name: <PERSON>ar<PERSON><PERSON> Scan

on:
  push:
    branches:
      - development

jobs:
  SonarQube-Backend:
    name: <PERSON><PERSON><PERSON><PERSON>an (backend)
    runs-on: ['self-hosted', 'linux', 'sonarscan']

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Setup NodeJS 16
        uses: actions/setup-node@v2
        with:
          node-version: '16'

      - name: Run npm install
        run: npm install --legacy-peer-deps
        working-directory: ./backend

      # - name: Run OWASP DependencyCheck
      #   run: dependency-check.sh -s ./backend -o . -f HTML -f JSON

      - name: Execute SonarQube Scan
        env:
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
          SONAR_PROJECT_KEY: ${{ secrets.RKI_BACKEND_PROJECT_KEY }}
          SONAR_LOGIN: ${{ secrets.RKI_BACKEND_TOKEN }}
        run: |
          sonar-scanner \
          -Dsonar.projectKey="$SONAR_PROJECT_KEY" \
          -Dsonar.login="$SONAR_LOGIN" \
          -Dsonar.projectBaseDir='.' \
          -Dsonar.host.url="$SONAR_HOST_URL" \
          -Dsonar.inclusions='backend/**/*' \
          -Dsonar.exclusions='**/node_modules/**/*'

  SonarQube-Frontend:
    name: SonarQube scan (frontend)
    runs-on: ['self-hosted', 'linux', 'sonarscan']

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Setup NodeJS 12
        uses: actions/setup-node@v2-beta
        with:
          node-version: '12'

      - name: Run npm install
        run: npm install
        working-directory: ./frontend

      # - name: Run OWASP DependencyCheck
      #   run: dependency-check.sh -s ./frontend -o . -f HTML -f JSON

      - name: Execute SonarQube Scan
        env:
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
          SONAR_PROJECT_KEY: ${{ secrets.RKI_FRONTEND_PROJECT_KEY }}
          SONAR_LOGIN: ${{ secrets.RKI_FRONTEND_TOKEN }}
        run: |
          sonar-scanner \
          -Dsonar.projectKey="$SONAR_PROJECT_KEY" \
          -Dsonar.login="$SONAR_LOGIN" \
          -Dsonar.projectBaseDir='.' \
          -Dsonar.host.url="$SONAR_HOST_URL" \
          -Dsonar.inclusions='frontend/**/*' \
          -Dsonar.exclusions='**/node_modules/**/*'
