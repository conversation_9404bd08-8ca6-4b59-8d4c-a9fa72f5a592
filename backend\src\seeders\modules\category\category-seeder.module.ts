//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { CategorySeederService } from './category-seeder.services';
// SCHEMAS
import { CategorySchema } from 'src/schemas/category.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Category', schema: CategorySchema }
      ]
    )
  ],
  providers: [CategorySeederService],
  exports: [CategorySeederService],
})

export class CategorySeederModule { }
