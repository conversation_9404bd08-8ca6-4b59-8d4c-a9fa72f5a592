//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import UserTable from "./userTable";
import { useTranslation } from 'next-i18next';
import { canAddUsers } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const UserIndex = (props) => {
  const { t } = useTranslation('common');
  const ShowUserIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title= {t("adminsetting.user.form.Users")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_user"
              >
              <Button variant="secondary" size="sm">
              {t("adminsetting.user.form.AddUser")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <UserTable />
          </Col>
        </Row>
      </Container>
    );
  }
  const ShowAddUsers = canAddUsers(() => <ShowUserIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.users?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddUsers />
  )
}
export default UserIndex;