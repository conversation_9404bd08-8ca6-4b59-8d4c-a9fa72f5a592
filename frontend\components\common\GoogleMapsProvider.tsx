// components/GoogleMapsProvider.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';

interface GoogleMapsContextValue {
  isLoaded: boolean;
  loadError: boolean;
}

const GoogleMapsContext = createContext<GoogleMapsContextValue>({
  isLoaded: false,
  loadError: false,
});

export const useGoogleMaps = () => useContext(GoogleMapsContext);

interface Props {
  language: string;
  children: React.ReactNode;
}

export const GoogleMapsProvider = ({ language, children }: Props) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState(false);

  useEffect(() => {
    const existingScript = document.getElementById('google-maps-script');
    if (existingScript) {
      existingScript.remove(); // Remove old one before reloading with new language
    }

    const script = document.createElement('script');
    const apiKey = process.env.NEXT_PUBLIC_MAP_KEY || process.env.MAP_KEY || ''

    script.id = 'google-maps-script';
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&language=${language}`;
    script.async = true;
    script.defer = true;

    script.onload = () => setIsLoaded(true);
    script.onerror = () => setLoadError(true);

    document.head.appendChild(script);

    return () => {
      setIsLoaded(false); // Reset on language change
    };
  }, [language]);

  return (
    <GoogleMapsContext.Provider value={{ isLoaded, loadError }}>
      {children}
    </GoogleMapsContext.Provider>
  );
};
