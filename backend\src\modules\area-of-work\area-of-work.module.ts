//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';


//Import services/components
import { AreaOfWorkController } from './area-of-work.controller';
import { AreaOfWorkService } from './area-of-work.service';
// SCHEMAS
import { AreaOfWorkSchema } from '../../schemas/area_of_work.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'AreaOfWork', schema: AreaOfWorkSchema }
    ])
  ],
  controllers: [AreaOfWorkController],
  providers: [AreaOfWorkService],
})

export class AreaOfWorkModule { }