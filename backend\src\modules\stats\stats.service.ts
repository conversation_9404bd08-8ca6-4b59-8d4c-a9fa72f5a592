//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { CountryInterface } from 'src/interfaces/country.interface';
import { ProjectInterface } from 'src/interfaces/project.interface';
import { ProjectStatusInterface } from 'src/interfaces/project-status.interface';
import { OperationInterface } from 'src/interfaces/operation.interface';
import { EventInterface } from 'src/interfaces/event.interface';
import { InstitutionInterface } from 'src/interfaces/institution.interface';
import { OperationStatusInterface } from 'src/interfaces/operation-status.interface';
import { EventStatusInterface } from 'src/interfaces/event-status.interface';
import { VspaceInterface } from 'src/interfaces/vspace.interface';
const InstitutionStats = 'Could not find Institution Stats.';
const FindVspace = 'Could not find vspace.';
@Injectable()
export class StatsService {
  constructor(
    @InjectModel('Project') private projectModel: Model<ProjectInterface>,
    @InjectModel('Operation') private operationModel: Model<OperationInterface>,
    @InjectModel('Event') private eventModel: Model<EventInterface>,
    @InjectModel('Institution')
    private institutionModel: Model<InstitutionInterface>,
    @InjectModel('Country') private countryModel: Model<CountryInterface>,
    @InjectModel('OperationStatus')
    private operationStatusModel: Model<OperationStatusInterface>,
    @InjectModel('ProjectStatus')
    private projectStatusModel: Model<ProjectStatusInterface>,
    @InjectModel('EventStatus')
    private eventStatusModel: Model<EventStatusInterface>,
    @InjectModel('Vspace') private vspaceModel: Model<VspaceInterface>,
  ) {}

  async getStatsForCountry(countryId): Promise<CountryInterface[]> {
    const _result: any = {};
    const _projectStatusIds = [];
    const _operationStatusIds = [];
    const _eventStatusIds = [];

    try {
      const _operationStatus = await this.operationStatusModel.find({}).exec();
      await _operationStatus.map((e) => {
        if (e.title === 'Ongoing') {
          _operationStatusIds.push(e._id);
        }
      });
      const _projectStatus = await this.projectStatusModel.find({}).exec();
      await _projectStatus.map((e) => {
        if (e.title === 'Ongoing') {
          _projectStatusIds.push(e._id);
        }
      });
      const _eventStatus = await this.eventStatusModel.find({}).exec();
      await _eventStatus.map((e) => {
        if (e.title === 'Current') {
          _eventStatusIds.push(e._id);
        }
      });
      _result.operation_count = await this.operationModel
        .count({ country: countryId, status: { $in: _operationStatusIds } })
        .exec();
      _result.project_count = await this.projectModel
        .count({
          'partner_institutions.partner_country': { $in: countryId },
          status: { $in: _projectStatusIds },
        })
        .exec();
      _result.event_count = await this.eventModel
        .count({ country: countryId, status: { $in: _eventStatusIds } })
        .exec();
    } catch (error) {
      throw new NotFoundException('Could not find Country Stats.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Country Stats.');
    }
    return _result;
  }

  async getStatsForInstitution(institutionId): Promise<InstitutionInterface> {
    const _result: any = {};
    try {
      _result.operations = await this.operationModel
        .count({ 'partners.institution': { $in: institutionId } })
        .exec();
      _result.operationData = await this.operationModel
        .find({ 'partners.institution': { $in: institutionId } })
        .lean()
        .populate({
          path: 'partners.institution',
          populate: { path: '_id' },
          select: 'title',
        })
        .exec();
      _result.projects = await this.projectModel
        .count({
          'partner_institutions.partner_institution': { $in: institutionId },
        })
        .exec();
      _result.projectData = await this.projectModel
        .find({
          'partner_institutions.partner_institution': { $in: institutionId },
        })
        .lean()
        .populate({
          path: 'partner_institutions.partner_institution',
          populate: { path: '_id' },
          select: 'title',
        })
        .exec();
      const institutionData = await this.institutionModel
        .find({ _id: institutionId })
        .exec();
      _result.partners = institutionData.length
        ? institutionData[0].partners.length
        : 0;
    } catch (error) {
      throw new NotFoundException(InstitutionStats);
    }
    if (!_result) {
      throw new NotFoundException(InstitutionStats);
    }
    return _result;
  }

  async getStatsForInstitutions(queryParams): Promise<InstitutionInterface> {
    const _result: any = {};
    try {
      let _filter = {};
      try {
        _filter = queryParams.query ? queryParams.query : {};
      } catch (e) {}
      _result.institutions = await this.institutionModel.count(_filter).exec();
      _result.projects = await this.projectModel
        .count({
          'partner_institutions.partner_institution': { $exists: true },
        })
        .exec();
      const _countryId = await this.countryModel
        .findOne({ title: 'Germany' })
        .exec();
      _result.german_countryId = _countryId._id;
      _result.german_institutions = await this.institutionModel
        .count({ 'address.country': { $in: _countryId } })
        .exec();
    } catch (error) {
      throw new NotFoundException(InstitutionStats, error);
    }
    if (!_result) {
      throw new NotFoundException(InstitutionStats);
    }
    return _result;
  }

  async getOperationLinkedVspace(operationId): Promise<VspaceInterface> {
    const _result: any = {};
    try {
      _result.operation = await this.vspaceModel
        .find({ operation: operationId })
        .exec();
    } catch (error) {
      throw new NotFoundException(FindVspace, error);
    }
    if (!_result) {
      throw new NotFoundException(FindVspace);
    }
    return _result;
  }

  async getProjectLinkedVspace(ProjectId): Promise<VspaceInterface> {
    const _result: any = {};
    try {
      _result.project = await this.vspaceModel
        .find({ project: ProjectId })
        .exec();
    } catch (error) {
      throw new NotFoundException(FindVspace, error);
    }
    if (!_result) {
      throw new NotFoundException(FindVspace);
    }
    return _result;
  }
}
