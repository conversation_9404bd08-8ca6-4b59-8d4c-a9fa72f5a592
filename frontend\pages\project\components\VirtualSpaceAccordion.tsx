//Import Library
import React, { useState } from "react";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Accordion, Card } from "react-bootstrap";

//Import services/components
import VspaceTable from "../../../components/common/VspaceTable";
import { useTranslation } from 'next-i18next';


interface VirtualSpaceAccordionProps {
  routeData: {
    routes: string[];
  };
}

const VirtualSpaceAccordion = (props: VirtualSpaceAccordionProps) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("LinkedVirtualSpace")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <VspaceTable
                      id={props.routeData?.routes?.[1] || ''}
                      type="Project"
                      vspaceData={[]}
                      vspaceDataLoading={false}
                      vspaceDataTotalRows={0}
                      vspaceDataPerPage={10}
                      vspaceDataCurrentPage={1}
                    />
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
}
export default VirtualSpaceAccordion;