//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { FileCategoryInterface } from '../../interfaces/file-category.interface';
import { CreateFileCategoryDto } from './dto/create-file-category.dto';
import { UpdateFileCategoryDto } from './dto/update-file-category.dto';
const EventStatus='Could not find Event Status.'
@Injectable()
export class FileCategoryService {
  constructor(
    @InjectModel('FileCategory') private fileCategoryModel: Model<FileCategoryInterface>
  ) { }

  async create(createFileCategoryDto: CreateFileCategoryDto): Promise<FileCategoryInterface> {
    const createdFileCategory = new this.fileCategoryModel(createFileCategoryDto);
    return createdFileCategory.save();
  }

  async findAll(query): Promise<FileCategoryInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.fileCategoryModel.paginate(_filter, options);
  }

  async get(fileCategoryId): Promise<FileCategoryInterface[]> {
    let _result;
    try {
      _result = await this.fileCategoryModel.findById(fileCategoryId).exec();
    } catch (error) {
      throw new NotFoundException(EventStatus);
    }
    if (!_result) {
      throw new NotFoundException(EventStatus);
    }
    return _result;
  }

  async update(fileCategoryId: any, updateFileCategoryDto: UpdateFileCategoryDto) {
    const getById: any = await this.fileCategoryModel.findById(fileCategoryId).exec();
    const updatedData = new this.fileCategoryModel(updateFileCategoryDto);
    try {
      Object.keys(updateFileCategoryDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Event Status.');
    }
    return getById;
  }

  async delete(fileCategoryId: string) {
    const result = await this.fileCategoryModel.deleteOne({ _id: fileCategoryId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(EventStatus);
    }
  }
}
