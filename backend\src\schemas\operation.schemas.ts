//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const OperationSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  status: { type: mongoose.Schema.Types.ObjectId, ref: 'OperationStatus', autopopulate: true },
  start_date: { type: Date },
  end_date: { type: Date },
  timeline: Array,
  country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
  world_region: {type: mongoose.Schema.Types.ObjectId, ref: 'WorldRegion'},
  region: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true }],
  hazard_type: { type: mongoose.Schema.Types.ObjectId, ref: 'HazardType', autopopulate: true },
  hazard: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Hazard', autopopulate: true }],
  syndrome: { type: mongoose.Schema.Types.ObjectId, ref: 'Syndrome', autopopulate: true },
  partners: [{
    institution: { type: mongoose.Schema.Types.ObjectId, ref: 'Institution', autopopulate: true },
    expertise: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Expertise', autopopulate: true }],
    status: { type: mongoose.Schema.Types.ObjectId, ref: 'DeploymentStatus', autopopulate: true },
    work_description: { type: String },
    contacts: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true },
    area_of_work: { type: mongoose.Schema.Types.ObjectId, ref: 'AreaOfWork', autopopulate: true },
    working_region: { type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true },
    start_date: { type: Date },
    end_date: { type: Date },
    other_contacts: {
      name: { type: String},
      email: { type: String},
      phone: { type: String },
      invite: { type: String }
    }
  }],
  vspace: { type: mongoose.Schema.Types.ObjectId, ref: 'Vspace', autopopulate: true },
  vspace_visibility: { type: String },
  document: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Files', autopopulate: true }],
  doc_src: [{ type: String}],
  images: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true }],
  images_src: [{ type: String}],
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true }, // operation created user
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

OperationSchema.plugin(mongoosePaginate);
