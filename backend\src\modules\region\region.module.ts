//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { RegionController } from './region.controller';
import { RegionService } from './region.service';
// SCHEMAS
import { RegionSchema } from '../../schemas/region.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Region', schema: RegionSchema }
    ])
  ],
  controllers: [RegionController],
  providers: [RegionService],
})

export class RegionModule { }