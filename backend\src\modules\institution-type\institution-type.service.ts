//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { InstitutionTypeInterface } from '../../interfaces/institution-type.interface';
import { CreateInstitutionTypeDto } from './dto/create-institution-type.dto';
import { UpdateInstitutionTypeDto } from './dto/update-institution-type.dto';
const InstitutionType = 'Could not find Institution Type.';
@Injectable()
export class InstitutionTypeService {
  constructor(
    @InjectModel('InstitutionType')
    private institutionTypeModel: Model<InstitutionTypeInterface>,
  ) {}

  async create(
    createInstitutionTypeDto: CreateInstitutionTypeDto,
  ): Promise<InstitutionTypeInterface> {
    const createdInstitutionType = new this.institutionTypeModel(
      createInstitutionTypeDto,
    );
    return createdInstitutionType.save();
  }

  async findAll(query): Promise<InstitutionTypeInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.institutionTypeModel.paginate(_filter, options);
  }

  async get(institutionTypeId): Promise<InstitutionTypeInterface[]> {
    let _result;
    try {
      _result = await this.institutionTypeModel
        .findById(institutionTypeId)
        .exec();
    } catch (error) {
      throw new NotFoundException(InstitutionType);
    }
    if (!_result) {
      throw new NotFoundException(InstitutionType);
    }
    return _result;
  }

  async update(
    institutionTypeId: any,
    updateInstitutionTypeDto: UpdateInstitutionTypeDto,
  ) {
    const getById: any = await this.institutionTypeModel
      .findById(institutionTypeId)
      .exec();
    const updatedData = new this.institutionTypeModel(updateInstitutionTypeDto);
    Object.keys(updateInstitutionTypeDto).forEach((d) => {
      getById[d] = updatedData[d];
    });
    getById.updated_at = new Date();
    return getById.save();
  }

  async delete(institutionTypeId: string) {
    const result = await this.institutionTypeModel
      .deleteOne({ _id: institutionTypeId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(InstitutionType);
    }
  }
}
