//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ExpertiseController } from './expertise.controller';
import { ExpertiseService } from './expertise.service';
// SCHEMAS
import { ExpertiseSchema } from '../../schemas/expertise.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Expertise', schema: ExpertiseSchema }
    ])
  ],
  controllers: [ExpertiseController],
  providers: [ExpertiseService],
})

export class ExpertiseModule { }