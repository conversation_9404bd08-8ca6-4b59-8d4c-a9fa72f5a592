//Import Library
import { faAngleLeft, faAngleRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import moment from "moment";
import React, { useState, useEffect } from "react";
import { Col, Row } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';


const OperationTimelineSection = (props) => {
    const { t } = useTranslation('common');
    const formatDateWithoutTime = "MM-D-YYYY";
    const [scrolled, setScrolled] = useState(40);

    useEffect(() => {
        document.getElementById("timeline-container")?.scroll(scrolled, 5000);
    }, [scrolled]);

    const onLeftArrow = () => {
        const temp = scrolled - 50;
        setScrolled(temp < 0 ? 0 : temp);
    };

    const onRigthArrow = () => {
        setScrolled(scrolled + 50);
    };

    const timelineIcon = {
        1: "/images/home/<USER>",
        2: "/images/home/<USER>",
        3: "/images/home/<USER>",
        4: "/images/home/<USER>",
        5: "/images/home/<USER>",
        6: "/images/home/<USER>",
        7: "/images/home/<USER>",
    };

    return (
        <>
            <Row>
                <Col className="operatinTimeline" xs={12}>
                    <div className="progress_main_sec" style={{ marginTop: "90px" }}>
                        {props.operation && props.operation.timeline.length > 2 && (
                            <div>
                                <div
                                    className="prev"
                                    onClick={onLeftArrow}
                                    style={{ cursor: "pointer" }}
                                >
                                    <span>
                                        <FontAwesomeIcon icon={faAngleLeft} />
                                    </span>
                                </div>
                                <div
                                    className="next"
                                    onClick={onRigthArrow}
                                    style={{ cursor: "pointer" }}
                                >
                                    <span>
                                        <FontAwesomeIcon icon={faAngleRight} />
                                    </span>
                                </div>
                            </div>
                        )}

                        <div className="progressbar-container" id="timeline-container">
                            <ul className="progressbar">
                                {props.operation &&
                                    props.operation.timeline &&
                                    props.operation.timeline.map((item, i) => {
                                        return (
                                            <li
                                                style={{
                                                    zIndex: props.operation.timeline.length - i,
                                                }}
                                                key={i}
                                            >
                                                <div className="timelineIcon">
                                                    <img
                                                        src={timelineIcon[item.iconclass]}
                                                        width="80px"
                                                        height="80px"
                                                    />
                                                </div>
                                                {item.timetitle ? (
                                                    <p className="step-label">{item.timetitle}</p>
                                                ) : (
                                                    <p className="step-label">{t("NoTitle")}</p>
                                                )}
                                                {item.date ? (
                                                    <p className="step-text">
                                                        {moment(item.date).format(
                                                            formatDateWithoutTime
                                                        )}
                                                    </p>
                                                ) : (
                                                    <p className="step-text">{t("NoDate")}</p>
                                                )}
                                            </li>
                                        );
                                    })}
                            </ul>
                        </div>
                    </div>
                </Col>
            </Row>
        </>
    )
}

export default OperationTimelineSection;