//Import Library
import { Injectable, Logger } from "@nestjs/common";

//Import services/components
import { WorldRegionSeederService } from "./modules/world-region/world-region-seeder.services";
import { LanguageSeederService } from "./modules/language/language-seeder.services";
import { AreaOfWorkSeederService } from "./modules/area-of-work/area-of-work-seeder.services";
import { CountrySeederService } from "./modules/country/country-seeder.services";
import { HazardSeederService } from "./modules/hazard/hazard-seeder.services";
import { HazardTypeSeederService } from "./modules/hazard-type/hazard-type-seeder.services";
import { CategorySeederService } from "./modules/category/category-seeder.services";
import { ClassificationSeederService } from "./modules/classification/classification-seeder.services";
import { SyndromeSeederService } from "./modules/syndrome/syndrome-seeder.services";
import { OperationStatusSeederService } from "./modules/operation-status/operation-status-seeder.services";
import { InstitutionTypeSeederService } from "./modules/institution-type/institution-type-seeder.services";
import { RolesSeederService } from "./modules/roles/roles-seeder.services";
import { UsersSeederService } from "./modules/users/users-seeder.services";
import { ExpertiseSeederService } from "./modules/expertise/expertise-seeder.services";
import { InstitutionNetworkSeederService } from "./modules/institution-network/institution-network-seeder.services";
import { ProjectStatusSeederService } from "./modules/project-status/project-status-seeder.services";
import { RegionSeederService } from "./modules/regions/region-seeder.services";
import { EventStatusSeederService } from "./modules/event-status/event-status-seeder.services";
import { RiskLevelSeederService } from "./modules/risk-level/risk-level-seeder.services";
import { UpdateTypeSeederService } from "./modules/update-type/update-type-seeder.services";
import { DeploymentStatusSeederService } from "./modules/deployment-status/deployment-status-seeder.services";
import { FileCategorySeederService } from "./modules/file-category/file-category-seeder.services";
import { PageCategorySeederService } from "./modules/page-category/page-category-seeder.services";
import { LandingpagesSeederService } from "./modules/landingpages/landingpages.services";

@Injectable()
export class SeederService {
  constructor(
    private readonly _logger: Logger,
    private readonly _rolesSeederService: RolesSeederService,
    private readonly _usersSeederService: UsersSeederService,
    private readonly _worldRegionSeederService: WorldRegionSeederService,
    private readonly _languageSeederService: LanguageSeederService,
    private readonly _areaOfWorkSeederService: AreaOfWorkSeederService,
    private readonly _countrySeederService: CountrySeederService,
    private readonly _hazardSeederService: HazardSeederService,
    private readonly _hazardTypeSeederService: HazardTypeSeederService,
    private readonly _syndromeSeederService: SyndromeSeederService,
    private readonly _operationStatusSeederService: OperationStatusSeederService,
    private readonly _categorySeederService: CategorySeederService,
    private readonly _classificationSeederService: ClassificationSeederService,
    private readonly _institutionTypeSeederService: InstitutionTypeSeederService,
    private readonly _expertiseSeederService: ExpertiseSeederService,
    private readonly _institutionNetworkSeederService: InstitutionNetworkSeederService,
    private readonly _projectStatusSeederService: ProjectStatusSeederService,
    private readonly _regionSeederService: RegionSeederService,
    private readonly _eventStatusSeederService: EventStatusSeederService,
    private readonly _riskLevelSeederService: RiskLevelSeederService,
    private readonly _updateTypeSeederService: UpdateTypeSeederService,
    private readonly _deploymentStatusSeederService: DeploymentStatusSeederService,
    private readonly _fileCategorySeederService: FileCategorySeederService,
    private readonly _pageCategorySeederService: PageCategorySeederService,
    private readonly _landingpagesSeederService: LandingpagesSeederService
  ) { }

  async seed() {

    // Roles
    await this.initiateSeeder('Role', this._rolesSeederService);

    // Skip Users seeding to avoid duplicate key errors
    // await this.initiateUsersSeeder(); // Need to fix

    // World Regions Seeder
    await this.initiateSeeder('World Region', this._worldRegionSeederService);

    // Language Seeder
    await this.initiateSeeder('Language', this._languageSeederService);

    // Area Of Work Seeder
    await this.initiateSeeder('Area of Work', this._areaOfWorkSeederService);

    // Country
    await this.initiateSeeder('Country', this._countrySeederService);

    // Hazard Type
    await this.initiateSeeder('Hazard Type', this._hazardTypeSeederService);

    // Hazard
    await this.initiateSeeder('Hazard', this._hazardSeederService);

    // Syndrome
    await this.initiateSeeder('Syndrome', this._syndromeSeederService);

    // Operation Status
    await this.initiateSeeder('Syndrome', this._operationStatusSeederService);

    // Category
    await this.initiateSeeder('Category', this._categorySeederService);

    // Classification
    await this.initiateSeeder('Classification', this._classificationSeederService);

    // Institution Type
    await this.initiateSeeder('Institution', this._institutionTypeSeederService);

    // Expertise
    await this.initiateSeeder('Expertise', this._expertiseSeederService);

    // Institution Network
    await this.initiateSeeder('Institution Network', this._institutionNetworkSeederService);

    // Project Status
    await this.initiateSeeder('Project Status', this._projectStatusSeederService);

    // Regions
    await this.initiateSeeder('Regions', this._regionSeederService);

    // Event Status
    await this.initiateSeeder('Event Status', this._eventStatusSeederService);

    // Risk Level
    await this.initiateSeeder('Risk Level', this._riskLevelSeederService);

    // Update Type
    await this.initiateSeeder('Update Type', this._updateTypeSeederService);

    // Deployment Status
    await this.initiateSeeder('Deployment Status', this._deploymentStatusSeederService);

    // File Category
    await this.initiateSeeder('File Category', this._fileCategorySeederService);

    //Page Category
    await this.initiateSeeder('Page Category', this._pageCategorySeederService);

    //Landing Pages
    await this.initiateSeeder('Landing Pages', this._landingpagesSeederService);
  }

  /**
   *
   *
   * @private
   * @param {*} modelName
   * @param {*} modelService
   * @memberof Seeder
   */
  private async initiateSeeder(modelName: any, modelService: any) {
    await this.createSeeder(modelName, modelService)
      .then(completed => {
        this._logger.debug(`Successfuly completed seeding ${modelName}...`);
        Promise.resolve(completed);
      })
      .catch(error => {
        this._logger.error(`Failed seeding ${modelName}...`);
        Promise.reject(error);
      });
  }

  /**
   *
   *
   * @private
   * @param {*} modelName
   * @param {*} modelService
   * @returns
   * @memberof Seeder
   */
  private async createSeeder(modelName: any, modelService: any) {
    return await Promise.all(modelService.create())
      .then(createdModelName => {
        // Can also use this._logger.verbose('...');
        this._logger.debug(
          `No. of ${modelName} created : ` +
          // Remove all null values and return only created Model.
          createdModelName.filter(
            nullValueOrCreatedModelName => nullValueOrCreatedModelName,
          ).length,
        );
        return Promise.resolve(true);
      })
      .catch(error => Promise.reject(error));
  }

  private async initiateUsersSeeder() {
    await this.createUsers()
      .then(completed => {
        this._logger.debug('Successfuly completed seeding Users...');
        Promise.resolve(completed);
      })
      .catch(error => {
        this._logger.error('Failed seeding Users...');
        Promise.reject(error);
      });
  }

  async createUsers() {
    return await this._usersSeederService.create()
      .then(createdUsers => {
        // Can also use this.logger.verbose('...');
        this._logger.debug(
          'No. of Users created : 1'
          // Remove all null values and return only created Operation Status.
        );
        return Promise.resolve(true);
      })
      .catch(error => Promise.reject(error));
  }
}