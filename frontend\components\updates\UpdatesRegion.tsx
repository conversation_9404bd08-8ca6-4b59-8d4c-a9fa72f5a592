//Import Library
import React, { useState, useEffect, useContext } from "react";
import { usePathname } from "next/navigation";
import { useRouter as useNextRouter } from "next/router";
import Link from "next/link";
import _ from "lodash";
import { Dropdown, OverlayTrigger, Tooltip } from "react-bootstrap";
import { Scrollbars } from "react-custom-scrollbars-2";

//Import services/components
import UpdateList from "./UpdatesList";
import apiService from "../../services/apiService";
import { UpdateContext } from "../../context/update";
import { useTranslation } from 'next-i18next';
import TwitterTimeline from "../common/widgets/TwitterTimeline";
import printContainer from "../common/printContent/printContainer";
import { canViewUpdateDropDown, canViewUpdateRegion } from "./permissions";
import { useIsMounted } from "../hooks/useIsMounted";

export default function UpdatesRegion() {
  const { t } = useTranslation('common');
  const router = useNextRouter();
  const pathname = usePathname();

  const isMounted = useIsMounted();
  const updatesLimit = 15;
  const pathArr = pathname.substring(1).split("/");
  const { updates, setUpdates } = useContext(UpdateContext);
  const [loadMoreButton, setLoadMoreButton] = useState(false);
  const [updateTypes, setUpdateTypes] = useState<any[]>([]);
  const [isDashboard, setDashboard] = useState(false);
  const [calendarUpdates, setCalendarUpdates] = useState("");

  const updateParams: any = {
    sort: { updated_at: "desc" },
    limit: updatesLimit,
    select:
      "-contact_details -document -end_date -images -link -media -reply -show_as_announcement -start_date",
  };

  const onRemoveUpdate = async (id: string) => {
    await apiService.remove(`/updates/${id}`);
    const items = updates.filter((e) => e._id !== id);
    setUpdates(items);
  };

  const onClickDropdown = (item: any) => {
    router.push(
      {
        pathname: "/updates/[...routes]",
        query: {
          parent_id: router.query.routes?.[1],
          parent_type: pathArr[0],
          update_type: item._id,
        },
      },
      `/updates/add?parent_id=${router.query.routes?.[1]}&parent_type=${pathArr[0]}&update_type=${item._id}`
    );
  };

  // fetch update_types on component mounts initially - used for the dropdown field
  useEffect(() => {
    const fetchUpdateTypes = async () => {
      const updateTypeParams = {
        sort: { title: "asc" },
        limit: "~",
      };
      const response = await apiService.get("/updatetype", updateTypeParams);
      //set calendar updates id
        response && response.data && response.data.length > 0 &&
        response.data.map(
          (item: any) =>
            item.title === "Calendar Event" && setCalendarUpdates(item._id)
        );
      if (response && response.data && response.data.length > 0 && isMounted) {
        _.remove(response.data, { title: "Conversation" });
        setUpdateTypes(response.data);
      }
    };

    fetchUpdateTypes();
    return;
  }, []);

  const fetchUpdates = async (newPage = false) => {
    try {
      const resp = await apiService.get("/updates", updateParams);
      let filteredData;

      if (resp && Array.isArray(resp.data)) {
        //Enable/Disable load more button when there is no next page data
        if (!resp.hasNextPage && resp.data.length <= updatesLimit) {
          setLoadMoreButton(false);
        } else {
          setLoadMoreButton(true);
        }

        filteredData = resp.data;
        if (updates && updates.length > 0) {
          update_func(updates, filteredData);
        }
        if (newPage) {
          setUpdates(filteredData);
        } else {
          setUpdates([...updates, ...filteredData]);
        }
      }
    } catch (err) {
      setUpdates([]);
    }
  };

  const loadMore = () => {
    // updateParams.skip = updates.length;
    calendarUpdates && (updateParams.query = { update_type: calendarUpdates });
    fetchUpdates();
  };

  // fetch updates whenever the route path changes
  useEffect(() => {
    //Load updates based routes and its params
    if (pathname === "/") {
      //Load all
    } else if (router.query && router.query.hasOwnProperty("routes")) {
      const id = router.query.routes?.[1];
      if (id && pathArr[0]) {
        const key = `parent_${pathArr[0]}`;
        updateParams.query = { [key]: id };
      }
    } else if (pathname === "/events-calendar") {
      calendarUpdates &&
        (updateParams.query = { update_type: calendarUpdates });
    } else {
      updateParams.query = { type: pathArr[0] };
    }

    fetchUpdates(true);
    setDashboard(pathname === "/");
  }, [pathname, router.query, calendarUpdates]);

  const ShowUpdateRegion = () =>{
  return (
    <div>
      <div
        className="updatesBlock"
        style={isDashboard ? { height: "calc(56vh - 70px)" } : undefined}
      >
        <Scrollbars
          className={isDashboard ? "dashboardUpdateScroll" : "updatesScrollbar"}
        >
          <div className="rightTitle">
            <h3>{t("updates")}</h3>
            <Link
              href="#"
              onClick={() => printContainer("update-content")}
              className="topiconLinks"
              style={(updates && updates.length === 0) ? {pointerEvents:"none"}: {pointerEvents:"auto"}}>

              <OverlayTrigger
                placement="bottom"
                delay={{ show: 250, hide: 400 }}
                overlay={<Tooltip id="print-update-tooltip">{t("Print")}</Tooltip>}
              >
                <i className="fas fa-print" />
              </OverlayTrigger>

            </Link>
            {router.query &&
              router.query.hasOwnProperty("routes") &&
              router.query.routes?.[0] === "show" && (
                <ShowDropDownByPermission
                  onClickDropdown={onClickDropdown}
                  updateTypes={updateTypes}
                  t={t}
                />
              )}
          </div>
          {updates &&
            updates.length > 0 &&
            updateTypes &&
            updateTypes.length > 0 && (
              <UpdateList
                loadMoreButton={loadMoreButton}
                loadMore={loadMore}
                updates={updates}
                updateTypes={updateTypes.map(({ _id, title }) => ({
                  _id,
                  title,
                }))}
                onRemoveUpdate={onRemoveUpdate}
              />
            )}
        </Scrollbars>
      </div>
      {isDashboard && <TwitterTimeline />}
    </div>
  );
  }
  const CanViewUpdateRegion = canViewUpdateRegion( ()=> <ShowUpdateRegion/> )
  return (
  <CanViewUpdateRegion/>
  );
}

// should be added in common components
interface UpdateDropdownProps {
  items: any[];
  onClick: (item: any, idx: number) => void;
  t: (key: string) => string;
}

const UpdateDropdown = ({ items, onClick, t }: UpdateDropdownProps): React.ReactElement => (
  <Dropdown>
    <Dropdown.Toggle
      style={{ backgroundColor: "#ccc", borderColor: "#ddd", color: "#000" }}
      id="dropdown-basic"
    >
      {t("addUpdate")}
    </Dropdown.Toggle>
    <Dropdown.Menu>
      {items.map((item: any, idx: number) => (
        <Dropdown.Item key={idx} onClick={() => onClick(item, idx)}>
          {item.title}
        </Dropdown.Item>
      ))}
    </Dropdown.Menu>
  </Dropdown>
);

const ShowDropDownByPermission = canViewUpdateDropDown((props: any) => <UpdateDropdown onClick={props.onClickDropdown} items={props.updateTypes} t={props.t} />)

function update_func(updates: any[], filteredData: any) {
  if (updates[0].parent === filteredData[0].parent) {
    // TODO document why this block is empty
  }
}
