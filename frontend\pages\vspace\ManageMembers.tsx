//Import Library
import React, { useState, useEffect } from "react";
import { <PERSON>, Container, <PERSON>, Spinner, Button, Modal } from "react-bootstrap";
import _ from "lodash";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPen, faTrashAlt } from "@fortawesome/free-solid-svg-icons";
import Select from "react-select";
import makeAnimated from "react-select/animated";
import CreatableSelect from "react-select/creatable";

import toast from 'react-hot-toast';
import Router, { useRouter } from "next/router";

//Import services/components
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const vspaceerror = "vspace.error";
const vspacesuccess = "vspace.success";
const ManageMembers = (props: any) => {
    const router = useRouter();
    const { t } = useTranslation('common');
        const animatedComponents = makeAnimated();
    const [vspaceInfo, setVspaceInfo] = useState<any>(null);
    const [usersList, setUsersList] = useState<any[]>([]);
    const [selectedUsers, setSelectUsers] = useState<any[]>([]);
    const [inputValue, setInputValue] = useState<string>("");
    const [value, setValue] = useState<any[]>([]);
    const [vspaceRequestedUser, setVspaceRequestedUser] = useState<any[]>([]);
    const [modal, setModal] = useState<boolean>(false);
    const [platformMemberId, setPlatFormMemberId] = useState<any>(null);
    const [modalType, setModalType] = useState<any>(null);
    const [refreshPage, setRefreshpage] = useState<boolean>(false);

    const routeId = router && router.query && router.query.id;

    //Params for Data Fecthing
    const userParms = {
        query: {},
        sort: { username: "asc" },
        limit: "~",
    };

    //Get Vspace details by ID
    const getVspaceInfo = async () => {
        const response = await apiService.get(`/vspace/${routeId}`);
        if (response) {
            setVspaceInfo(response);
        }
    };

    //Filter the choose members
    const filteredMembers = async () => {
        let userList = await apiService.get(`/users`, userParms);
        if (userList?.data?.length)
            userList.data = userList.data.filter((_userList: any) =>
                _userList.vspace_status === "Request Pending" || _userList.status === "Request Pending" ? false : true
            );
        const response = await apiService.get(`/vspace/${routeId}`);
        if (userList && response && response.members) {
            const ids = response.members && response.members.map((item: any) => item._id);
            const _users = userList.data.map((item: any, _i: any) => ({ label: item.username, value: item._id }));
            const filteredUsers = _users.filter((item: any) => ids.indexOf(item.value) === -1);
            setUsersList(filteredUsers);
        }
    };

    //Fecth Vpsace Request

    const requestUserParms = {
        query: { vspace: routeId },
        select: "-vspace -requested_to -created_at -updated_at",
        limit: "~",
    };
    const getVspaceRequest = async () => {
        const usrRequestedVspace = await apiService.get(
            `/vspace-request-subscribers/getRequestedToMe`,
            requestUserParms
        );
        if (usrRequestedVspace && usrRequestedVspace.data && usrRequestedVspace.data.length > 0) {
            const requestedUsers = usrRequestedVspace.data.map((d: any) => {
                d.requested_by._id = d._id;
                return d.requested_by;
            });
            setVspaceRequestedUser(requestedUsers);
        } else {
            setVspaceRequestedUser(usrRequestedVspace && usrRequestedVspace.data);
        }
    };

    //For Handle Api Calls
    useEffect(() => {
        getVspaceInfo();
        filteredMembers();
        getVspaceRequest();
    }, []);

    //For Refersh the page
    useEffect(() => {
        getVspaceInfo();
        filteredMembers();
        getVspaceRequest();
    }, [refreshPage]);

    //Hanlde User Change
    const userHandler = (selected: any) => {
        const allOption = props.allOption || { label: "All users", value: "*" };
        if (selected && selected.length > 0 && selected[selected.length - 1].value === allOption.value) {
            setSelectUsers(usersList);
        } else {
            setSelectUsers(selected);
        }
    };

    //Hanldle Invite By email
    const handleChange = () => {
        return setValue(value ? value : []);
    };

    const handleInputChange = (inputchanges: any) => setInputValue(inputchanges);

    const handleKeyDown = async (event: any) => {
        if (!inputValue) {
            return;
        }
        switch (event.key) {
            case "Enter":
            case "Tab":
                //Validate it is email or not
                const re = /\S+@\S+\.\S+/;
                if (re.test(inputValue)) {
                    const data = { email: inputValue };
                    const _users = await apiService.post("/vspace/filterNonmember", data);

                    if (_users.message === "") {
                        setValue([...value, { label: inputValue, value: inputValue }]);
                    } else {
                        toast.error(`${inputValue}  is already exist`)
                    }
                    setInputValue("");
                    event.preventDefault();
                } else {
                    event.preventDefault();
                    toast.error(t("vspace.Pleaseentervalidemailaddress"))
                    setInputValue("");
                }
        }
    };

    //Appoved or Reject Requested User
    const requestStatus = async () => {
        const postdata = {
            _id: platformMemberId._id,
            status: modalType,
        };
        const response = await apiService.post("/vspace/requestStatus", postdata);
        if (response && response.status === 200) {
            setRefreshpage(!refreshPage);
        }
    };

    //Hanlde modal
    const modalHandler = (d: any, type: any) => {
        setModal(true);
        setModalType(type);
        setPlatFormMemberId(d);
    };

    //Delete Vspace
    const deleteVspaceForm = async () => {
        await apiService.remove(`/vspace/${routeId}`);
        toast.success(t("vspace.DeletedtheVirtualSpacesuccessfully"))
        Router.push("/vspace");
    };

    //Columns for Monitoring memebrs Table
    const columns = [
        {
            name: t("vspace.UserName"),
            selector: "username",
            sortable: true,
        },
        {
            name: t("vspace.Email"),
            selector: "email",
            sortable: true,
        },
        {
            name: t("vspace.Action"),
            cell: (d: any) =>
                d ? (
                    <a onClick={() => modalHandler(d._id, "platformMember")} style={{ cursor: "pointer" }}>
                        <i className="icon fas fa-trash-alt" />
                    </a>
                ) : (
                    ""
                ),
        },
    ];

    //Columns for Requested user
    const requestedColumns = [
        {
            name: t("vspace.UserName"),
            selector: "username",
            sortable: true,
        },
        {
            name: t("vspace.Email"),
            selector: "email",
            sortable: true,
        },
        {
            name: t("vspace.Action"),
            cell: (d: any) => (
                <div>
                    <Button variant="primary" size="sm" onClick={() => modalHandler(d, "approved")}>
                        {t("vspace.Approve")}
                    </Button>
                    &nbsp;
                    <Button variant="secondary" size="sm" onClick={() => modalHandler(d, "declined")}>
                        {t("vspace.Reject")}
                    </Button>
                </div>
            ),
        },
    ];

    //Hanlde Popup Message
    let headerText;
    let bodyMessage;
    switch (modalType) {
        case "platformMember":
            headerText = t("vspace.Deleteuserfromvirtualspace");
            bodyMessage = t("vspace.AreyousurewanttoremovetheuserfromtheVirtualSpace?");
            break;
        case "approved":
            headerText = t("vspace.ApproveUser");
            bodyMessage = t("vspace.AreyousurewanttoaddtheusertoVirtualSpace?");
            break;
        case "declined":
            headerText = t("vspace.RejectUser");
            bodyMessage = t("vspace.AreyousurewanttorejecttheuserfromVirtualSpace?");
            break;
        case "deleteVspace":
            headerText = t("vspace.DeleteVirtualSpace");
            bodyMessage = t("vspace.AreyousurewanttodeletetheVirtualSpace?");
            break;
    }

    //sort Function
    const sortSubscribeRequestTousers = (rows: any, field: any, direction: any) => {
        const handleField = (row: any) => {
            if (row[field]) {
                return row[field].toLowerCase();
            }
            return row[field];
        };
        return _.orderBy(rows, handleField, direction);
    };

    //Api Request to Delete the users
    const membersDeleteHandler = async () => {
        const filteredMemberss =
            vspaceInfo && vspaceInfo.members.filter((item) => item._id !== platformMemberId).map((item) => item._id);
        const filteredSubscribers =
            vspaceInfo && vspaceInfo.subscribers.filter((item) => item._id !== platformMemberId);
        await apiService.patch(`/vspace/${routeId}`, {
            ...vspaceInfo,
            members: filteredMemberss,
            nonMembers: vspaceInfo.nonMembers[0] === "" ? "" : vspaceInfo.nonMembers,
            subscribers: filteredSubscribers.map((item) => item._id),
        });
        setRefreshpage(!refreshPage);
    };

    //Empty Data & modal Handler
    const modalHanlder = () => {
        setModal(false);
        setPlatFormMemberId(null);
        setModalType(null);
    };

    //Vspcae DeleteHandler
    const deleteVspaceHandler = () => {
        setModal(true);
        setModalType("deleteVspace");
        setPlatFormMemberId(routeId);
    };

    //Modal Confirm to delete or Approve
    const modalConfirmHandler = () => {
        switch (modalType) {
            case "platformMember":
                toast.success(t("vspace.Userremovedsuccessfully"))
                modalHanlder();
                membersDeleteHandler();
                break;

            case "approved":
                toast.success(t("vspace.Userapprovedsuccessfully"))
                modalHanlder();
                requestStatus();
                break;

            case "declined":
                toast.success(t("vspace.Userrejectedsuccessfully"));
                modalHanlder();
                requestStatus();
                break;

            case "deleteVspace":
                setModal(false);
                setModalType(null);
                deleteVspaceForm();
                break;
        }
    };

    const modalRejectHanldler = () => {
        switch (modalType) {
            case "platformMember":
                modalHanlder();
                break;

            case "declined":
                modalHanlder();
                break;

            case "approved":
                modalHanlder();
                break;

            case "deleteVspace":
                modalHanlder();
                break;
        }
    };

    //Submit Hanlder to send data to server
    const submitHandler = async (type: any) => {
        const choosenNonMembers = value && value.map((item: any) => item.value);
        const choosenMembers = selectedUsers && selectedUsers.map((item: any) => item.value);
        const oldMembers = vspaceInfo && vspaceInfo.members.map((item: any) => item._id);
        if (type === "platformMembers" && selectedUsers.length > 0) {
            await apiService.patch(`/vspace/${routeId}`, {
                ...vspaceInfo,
                members: [...oldMembers, ...choosenMembers],
                nonMembers: vspaceInfo.nonMembers[0] === "" ? "" : vspaceInfo.nonMembers,
            });
            toast.success(t("vspace.UserInvitedsuccessfully"));
            setSelectUsers([]);
            setRefreshpage(!refreshPage);
        } else if (type === "nonPlatformMembers" && value.length > 0) {
            await apiService.patch(`/vspace/${routeId}`, {
                ...vspaceInfo,
                nonMembers: choosenNonMembers,
                members: oldMembers,
            });
            toast.success(t("vspace.UserInvitedsuccessfully"));
            setValue([]);
            setRefreshpage(!refreshPage);
        } else {
            toast.error(t("vspace.Pleasechoosemembersoraddemailtoinvite"));
        }
    };

    return (
        <div className="pe-2">
            <Container>
                {vspaceInfo ? (
                    <>
                        <Row className="mt-3">
                            <Col className="p-0">
                                <div className="d-flex justify-content-between">
                                    <div>
                                        <h4>
                                            <Link
                                                href="/vspace/[...routes]"
                                                as={`/vspace/show/${routeId}`}
                                                className="h5 p-0 m-0">
                                                {vspaceInfo.title}
                                            </Link>
                                            <Link href="/vspace/[...routes]" as={`/vspace/edit/${routeId}`} >
                                                <Button variant="secondary" size="sm" className="ms-2">
                                                    <FontAwesomeIcon icon={faPen} />
                                                    &nbsp;{t("vspace.Edit")}
                                                </Button>
                                            </Link>
                                        </h4>
                                    </div>
                                    <Button variant="dark" onClick={deleteVspaceHandler}>
                                        {" "}
                                        <FontAwesomeIcon icon={faTrashAlt} color="#fff" className="me-2" />
                                        {t("vspace.DeleteVirtualSpace")}
                                    </Button>
                                </div>
                            </Col>
                        </Row>
                        <Row>
                            <Col className="header-block" lg={12}>
                                <h6>
                                    <span>{t("vspace.MonitoringandEvaluationMembers")}</span>
                                </h6>
                                <RKITable
                                    noHeader={true}
                                    columns={columns}
                                    data={vspaceInfo.members}
                                    dense={true}
                                    paginationServer
                                    pagServer={true}
                                    paginationTotalRows={0}
                                    subHeader
                                    subHeaderAlign="left"
                                    pagination={true}
                                    persistTableHead
                                    sortFunction={sortSubscribeRequestTousers}
                                />
                            </Col>
                        </Row>
                        <Row>
                            <Col className="header-block" lg={12}>
                                <h6>
                                    <span>{t("vspace.SubscribeRequestUsers")}</span>
                                </h6>
                                {vspaceRequestedUser && vspaceRequestedUser.length > 0 ? (
                                    <RKITable
                                        noHeader={true}
                                        columns={requestedColumns}
                                        data={vspaceRequestedUser}
                                        dense={true}
                                        paginationServer
                                        pagServer={true}
                                        paginationTotalRows={0}
                                        subHeader
                                        subHeaderAlign="left"
                                        pagination={true}
                                        persistTableHead
                                        sortFunction={sortSubscribeRequestTousers}
                                    />
                                ) : (
                                    <div className="nodataFound">{t("vspace.Nodataavailable")}</div>
                                )}
                            </Col>
                        </Row>
                        <Row>
                            <Col className="header-block">
                                <h6>
                                    <span>{t("vspace.PlatformMembersInvites")}</span>
                                </h6>
                                <div>
                                    <Select
                                        closeMenuOnSelect={false}
                                        components={animatedComponents}
                                        isMulti
                                        value={selectedUsers || []}
                                        placeholder={t("vspace.SelectUsers")}
                                        onChange={userHandler}
                                        options={[
                                            props.allOption || { label: "All users", value: "*" },
                                            ...(usersList || [])
                                        ]}
                                    />
                                    <Button
                                        className="mt-3"
                                        variant="primary"
                                        onClick={() => submitHandler("platformMembers")}
                                    >
                                        {t("vspace.AddMembers")}
                                    </Button>
                                </div>
                            </Col>
                        </Row>
                        <Row>
                            <Col className="header-block">
                                <h6>
                                    <span>{t("vspace.Non-PlatformMemberInvites(by email)")}</span>
                                </h6>
                                <small>{t("vspace.PressTabtoseparatemultipleemailid(s)")}</small>
                                <div>
                                    <CreatableSelect
                                        components={animatedComponents}
                                        inputValue={inputValue}
                                        isClearable
                                        isMulti
                                        menuIsOpen={false}
                                        onChange={handleChange}
                                        onInputChange={handleInputChange}
                                        onKeyDown={handleKeyDown}
                                        placeholder={t("vspace.Typeemailid(s),pressingenterbetweeneachone")}
                                        value={value || []}
                                    />
                                    <Button
                                        className="mt-3"
                                        variant="primary"
                                        onClick={() => submitHandler("nonPlatformMembers")}
                                    >
                                        {t("vspace.AddMembers")}
                                    </Button>
                                </div>
                            </Col>
                        </Row>
                    </>
                ) : (
                    <div className="d-flex justify-content-center align-items-center ">
                        <Spinner animation="border" variant="primary" />
                    </div>
                )}
                <Modal show={modal} onHide={() => setModal(false)}>
                    <Modal.Header closeButton>
                        <Modal.Title>{headerText}</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>{bodyMessage}</Modal.Body>
                    <div className="d-flex justify-content-end m-2">
                        <Button variant="secondary" onClick={modalRejectHanldler} className="me-2">
                            {t("vspace.No")}
                        </Button>
                        <Button variant="primary" onClick={modalConfirmHandler}>
                            {t("vspace.Yes")}
                        </Button>
                    </div>
                </Modal>
            </Container>
        </div>
    );
};

ManageMembers.defaultProps = {
    allOption: {
        label: "All users",
        value: "*",
    },
};

export default ManageMembers;
