//Import Library
import {Module} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ProjectController } from './project.controller';
import { ProjectService } from './project.service';
import { InstitutionService } from './../institution/institution.service';
import { UsersService } from './../../users/users.service';
import { EmailService } from './../../email.service';
import { RolesService } from './../roles/roles.service';
import { FlagService } from '../flag/flag.service';
import {InstitutionModule} from "../institution/institution.module";
import {CountryService} from "../country/country.service";
import { UpdateService } from '../updates/update.service';
// SCHEMAS
import { ProjectSchema } from '../../schemas/project.schemas';
import { InstitutionSchema } from '../../schemas/institution.schemas';
import { UsersSchema } from '../../schemas/users.schemas';
import { RolesSchema } from '../../schemas/roles.schemas';
import {CountrySchema} from "../../schemas/country.schemas";
import { FlagSchema } from "../../schemas/flag.schemas";
import { VspaceSchema } from "../../schemas/vspace.schemas";
import { UpdateSchema } from 'src/schemas/update.schemas';
import { OperationSchema } from 'src/schemas/operation.schemas';
import { HttpModule } from '@nestjs/axios';


@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Project', schema: ProjectSchema },
      { name: 'Institution', schema: InstitutionSchema },
      { name: 'Users', schema: UsersSchema },
      { name: 'Roles', schema: RolesSchema },
      { name: 'Country', schema: CountrySchema},
      { name: 'Flag', schema: FlagSchema },
      { name: 'Vspace', schema: VspaceSchema },
      { name: 'Operation', schema: OperationSchema },
      { name: 'Update', schema: UpdateSchema}
    ]),InstitutionModule, HttpModule
  ],
  controllers: [ProjectController],
  providers: [ProjectService, InstitutionService, UsersService, EmailService, RolesService, CountryService, FlagService, UpdateService],
})

export class ProjectModule { }