//Import Library
import {Container, <PERSON>, Col} from "react-bootstrap";
import { connect } from "react-redux";
import { useEffect, useState } from "react";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

//Import services/components
import AboutUs from "./AboutUs";
import OngoingOperations from "./OngoingOperations";
import OngoingProjects from "./OngoingProjects";
import Announcement from "./Announcement"
import ActiveProjectOperations from "./ActiveProjectOperations";
import CalendarEvents from "./CalendarEvents";
import apiService from "../../services/apiService";


const events = [
  {
    title: "Global RKI summit",
    start: new Date(),
    end: new Date(),
    allDay: true
  }
]

interface DashboardProps {
  t: (key: string) => string;
  user: any;
}

function Dashboard({ t, user }: DashboardProps) {
  const [getUserName, setUserName] = useState("");
  const [ongoingOperations, setOngoingOperations] = useState<any[]>([]);
  const [ongoingProjects, setOngoingProjects] = useState<any[]>([]);
  const [focalPointApprove,setFocalPointApprove] = useState ("Approved");
  const [isApproval, setIsApproval] = useState ("");

  const getLoggedUser = async () => {
    const data = await apiService.post("/users/getLoggedUser", {});
    if(data?.is_focal_point === false && data?.is_vspace === false) {
      setFocalPointApprove("Approved");
    } else {
      if (data?.status) {
        const status =  getStatusForFocalPoint(data.status);
        setIsApproval(status);
        } else if (data?.vspace_status) {
          const status =  getStatusForVspace(data.vspace_status);
          setIsApproval(status);
        } else {
          setIsApproval("RejectedForFocalPoint");
        }
    }

    function getStatusForFocalPoint(status: string) {
      switch (status) {
        case "Request Pending": return "WaitingForFocalPoint";
        case "Approved": return "SucessForFocalPoint";
      }
    }
  };

  function getStatusForVspace(status: string) {
    switch (status) {
      case "Request Pending": return "WaitingForVspace";
      case "Approved": return "SucessForVspace";

    }
  }
  useEffect(() => {
    if(user.is_focal_point){
    const is_Approved = user.status == "Approved" ? "Approved": "Pending";
    setFocalPointApprove(is_Approved);
  }
  if(user.is_vspace){
    const is_Approved = user.vspace_status == "Approved" ? "Approved": "Pending";
    setFocalPointApprove(is_Approved);
  }
    const _username = (user && user.username) ? user.username : "";
    setUserName(_username);
    getLoggedUser();
  }, [user]);

  return (
    <>
    {focalPointApprove == "Approved" ? (
      <Container fluid={true} className="p-0 dashboardScreen">
      <h2>{t('hello')} {getUserName}</h2>
      <Container fluid={true}>
        <Row>
          <Col className="ps-lg-0 dashboardLeft" lg="8">
            <Row>
              <Col xs="12">
                <AboutUs/>
              </Col>
            </Row>
            <Row>
              <Col xs={12}>
                <Row>
                <Col md="6" className="ongoingBlock">
                  <OngoingOperations t={t} fetchOngoingOperations={setOngoingOperations} />
                </Col>
                <Col md="6" className="ongoingBlock">
                  <OngoingProjects t={t} fetchOngoingProjects={setOngoingProjects} />
                </Col>
                </Row>
              </Col>
            </Row>
          </Col>
          <Col className="pe-lg-0 dashboard-calendar" lg="4">
            <CalendarEvents />
          </Col>
        </Row>
        <Row>
        <Col className="p-lg-0" xs="12">
            <Announcement t={t} />
          </Col>
        </Row>
        <Row>
        <Col className="p-lg-0" xs={12}>
          <ActiveProjectOperations t={t} ongoingProjects={ongoingProjects} ongoingOperations={ongoingOperations} />
          </Col>
        </Row>
      </Container>
    </Container>
    ) : <div className="Focalpoint"><h2>Welcome to Robert Koch Institut !</h2>
      <div>
      <h5 className="text-muted">{t(isApproval)}</h5>
      </div>
      </div>}
    </>

  )
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default connect((state: any) => state)(Dashboard);