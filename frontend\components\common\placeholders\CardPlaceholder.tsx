//Import Library
import ContentLoader from 'react-content-loader';

// No props needed - component uses hardcoded values
export default function CardPlaceholder() {
  return(
    <ContentLoader
      viewBox="0 0 380 70"
      height={50}
      width={317}
      speed={2}
      title={'Loading'}
      foregroundColor="#f7f7f7"
      backgroundColor="#ecebeb"
      uniqueKey={"operation"}
    >
      <rect x="10" y="0" rx="4" ry="4" width="320" height="25" />
      <rect x="40" y="40" rx="3" ry="3" width="250" height="20" />
    </ContentLoader>
  )
}
