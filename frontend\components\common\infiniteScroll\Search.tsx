//Import Library
import {Col, FormControl,InputGroup} from "react-bootstrap";

interface SearchFilterProps {
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  filter: string;
}

const SearchFilter = ({onChange, filter}: SearchFilterProps) => {
  return (
      <>
                        <Col lg sm md={1}>
                        <p className="lead">Select the expertise:</p>
                    </Col>

                    <Col lg sm md={10}>
                        <InputGroup>
                            <FormControl
                                className="border border-primary"
                                type="text"
                                value={filter}
                                onChange={onChange}
                                placeholder="Search..."
                                style={{ borderRadius: "50px" }}
                            />
                            <div style={{
                                position: "absolute",
                                right: "10px",
                                top: "5px",
                                zIndex: 9999
                            }}>
                                <i className="fas fa-search" />
                            </div>
                        </InputGroup>
                    </Col>
         </>
      )
};

export default SearchFilter;
