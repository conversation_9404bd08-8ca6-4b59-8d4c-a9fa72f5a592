//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import EventstatusTable from "./eventstatusTable";
import { useTranslation } from 'next-i18next';
import { canAddEventStatus } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const EventstatusIndex = (props) => {
  const { t } = useTranslation('common');
  const ShowEventstatusIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title={t("adminsetting.EventStatus.Forms.AddEventStatus")} />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_eventstatus"
                >
                <Button variant="secondary" size="sm">
                {t("adminsetting.EventStatus.Forms.AddEventStatus")}
              </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              <EventstatusTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  const ShowAddEventStatus = canAddEventStatus(() => <ShowEventstatusIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.event_status?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddEventStatus />
  )
}
export default EventstatusIndex;