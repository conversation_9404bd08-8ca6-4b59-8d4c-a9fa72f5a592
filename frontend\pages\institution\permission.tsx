//Import Library
import React from 'react';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

//Import services/components
import R403 from "../r403";

export const canAddInstitution = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddInstitution',
});

export const canAddInstitutionForm = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.institution && state.permissions.institution['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddInstitutionForm',
  FailureComponent: () => <R403/>
});

export const canEditInstitution = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.institution) {
      if (state.permissions.institution['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.institution['update:own']) {
          if (props.institution && props.institution.user && props.institution.user === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditInstitution',
});

export const canEditInstitutionForm = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.institution) {
      if (state.permissions.institution['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.institution['update:own']) {
          if (props.institution && props.institution.user && props.institution.user === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditInstitutionForm',
  FailureComponent: () => <R403/>
});

export const canViewDiscussionUpdate = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewDiscussionUpdate',
});

export const canManageFocalPoints = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.institution_focal_point) {
      if (state.permissions.institution_focal_point["update:any"]) {
        return true;
      } else {
        if (state.permissions.institution_focal_point["update:own"]) {
          if (
            props.institution &&
            props.institution.user &&
            props.institution.user === state.user._id
          ) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: "canManageFocalPoints",
});

export default canAddInstitution;