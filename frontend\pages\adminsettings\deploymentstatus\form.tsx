//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { useTranslation } from 'next-i18next';
import { DeploymentstatusInterface } from "../../../components/interfaces/deploymentstatus.interface";
import apiService from "../../../services/apiService";

const DeploymentstatusForm = (props: any) => {
    const _initialdeploymentstatus = {
        title: "",
    };
    const { t } = useTranslation('common');
    const [initialVal, setInitialVal] = useState<DeploymentstatusInterface>(_initialdeploymentstatus);

    const editform: boolean = props.routes && props.routes[0] === "edit_deploymentstatus" && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialdeploymentstatus);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.DeploymentStatus.Forms.DeploymentstatusisUpdatedsuccessfully";
            response = await apiService.patch(`/deploymentstatus/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.DeploymentStatus.Forms.Deploymentstatusisaddedsuccessfully";
            response = await apiService.post("/deploymentstatus", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/deploymentstatus");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const deploymentstatusParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getDeploymentstatusData = async () => {
                const response = await apiService.get(`/deploymentstatus/${props.routes[1]}`, deploymentstatusParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getDeploymentstatusData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.DeploymentStatus.Forms.DeploymentStatus")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.DeploymentStatus.Forms.DeploymentStatus")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => value.trim() !== ""}
                                            errorMessage={{
                                                validator: t(
                                                    "adminsetting.DeploymentStatus.Forms.PleaseAddtheDeploymentstatus"
                                                ),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.DeploymentStatus.Forms.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.DeploymentStatus.Forms.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/deploymentstatus`}
                                        >
                                        <Button variant="secondary">
                                            {t("adminsetting.DeploymentStatus.Forms.Cancel")}
                                        </Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default DeploymentstatusForm;
