//Import Library
import React from "react";
import {  Container, Row, Col, } from "react-bootstrap";

//Import services/components
import ReactDropZone from "../../components/common/ReactDropZone";
import { useTranslation } from 'next-i18next';

//TOTO refactor
interface DocumentFormProps {
  data: any[];
  getId: (id: any[]) => void;
  srcText: any[];
  getSourceCollection: (docSrcArr: any[]) => void;
}

const DocumentForm = (props: DocumentFormProps): React.ReactElement => {
  const { t } = useTranslation('common');
  const getID = (id: any[]) => {
    props.getId(id)
  }

  const getSourceText = (imgSrcArr: any[]) => {
    props.getSourceCollection(imgSrcArr);
  }

  return (
    <Container className="formCard mt-0 p-0" fluid>
      <Col>
        <Row className='header-block' lg={12}>
          <h6><span>{t("update.Documents")}</span></h6>
        </Row>
        <Row>
          <ReactDropZone datas={props.data} type="application" srcText={props.srcText} getImgID={(id) => getID(id)} getImageSource={(imgSrcArr) => getSourceText(imgSrcArr)} />
        </Row>
      </Col>
    </Container>
  );
}

export default DocumentForm;