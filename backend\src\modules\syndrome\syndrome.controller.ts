//Import Library
import { Controller, Get, Query, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateSyndromeDto } from './dto/create-syndrome.dto';
import { UpdateSyndromeDto } from './dto/update-syndrome.dto';
import { SyndromeService } from "./syndrome.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('syndrome')
@UseGuards(SessionGuard)
export class SyndromeController {

  constructor(
    private readonly _syndromeService: SyndromeService
  ) { }

  @Post()
  create(@Body() createSyndromeDto: CreateSyndromeDto) {
    const _syndrome = this._syndromeService.create(createSyndromeDto);
    return _syndrome;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._syndromeService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') syndromeId: string) {
    return this._syndromeService.get(syndromeId);
  }

  @Patch(':id')
  update(@Param('id') syndromeId: string, @Body() updateSyndromeDto: UpdateSyndromeDto) {
    const _syndrome = this._syndromeService.update(syndromeId, updateSyndromeDto);
    return _syndrome;
  }

  @Delete(':id')
  remove(@Param('id') syndromeId: string) {
    const _syndrome = this._syndromeService.delete(syndromeId);
    return _syndrome;
  }
}
