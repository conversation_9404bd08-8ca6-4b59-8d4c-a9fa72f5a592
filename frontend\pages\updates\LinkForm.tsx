//Import Library
import React from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col,} from "react-bootstrap";
import { ValidationForm } from "../../components/common/FormValidation";

//Import services/components
import { useTranslation } from 'next-i18next';
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";


//TOTO refactor
interface LinkFormProps {
  link: Array<{ title: string; link: string }>;
  handleChangeforTimeline: (e: React.ChangeEvent<any>, i: number) => void;
  removeForm: (e: React.MouseEvent, i: number) => void;
  addform: () => void;
}

const LinkForm = (props: LinkFormProps): React.ReactElement => {
  const { t } = useTranslation('common');
  const { link, handleChangeforTimeline, removeForm, addform } = props;
  return (
    <Container className="formCard" fluid>
      <Card>
        <ValidationFormWrapper onSubmit={() => {}}>
          <Card.Body>
            {link && link.map((item: any, i: number) => {
              return (
                <div>
                  <Row>
                    <Col>
                      <Form.Group>
                        <Form.Label className="required-field">{t("update.Title")}</Form.Label>
                        <Form.Control
                        name="title"
                        id="timetitle"
                        type="text"
                        value={item.title}
                        required
                        onChange={(e) => handleChangeforTimeline(e, i)} />
                        <Form.Control.Feedback type="invalid">
                        {t("update.TitleisRequired")}
                  </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col>
                      <Form.Group>
                        <Form.Label className="required-field">{t("update.Link")}</Form.Label>
                        <Form.Control
                          name="link"
                          id="link"
                          type="text"
                          required
                          value={item.link}
                          onChange={(e) => handleChangeforTimeline(e, i)}
                          pattern="http(s)?://??[\w.-]+[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}.+"
                        />
                        <Form.Control.Feedback type="invalid">
                        {t("update.Providevalidlink")}
                  </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col>
                      <Form.Group>
                        {i === 0 ? (
                          <div></div>
                        ) : (
                            <Button variant="secondary" style={{ marginTop: "30px" }} onClick={(e) => removeForm(e, i)} >{t("update.Remove")}
                            </Button>
                          )}
                      </Form.Group>
                    </Col>
                  </Row>
                </div>
              );
            })}
            <Row>
              <Col md lg="4">
                <Button variant="secondary" style={{ marginTop: "27px", marginBottom: "20px" }} onClick={addform}>{t("update.ADD")}</Button>
              </Col>
            </Row>
          </Card.Body>
        </ValidationFormWrapper>
      </Card>
    </Container>
  );
}

export default LinkForm;
