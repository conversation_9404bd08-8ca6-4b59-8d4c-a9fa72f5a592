//Import Library
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col } from "react-bootstrap";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput, SelectGroup, Radio } from "../../../components/common/FormValidation";
import Router, { useRouter } from "next/router";
import validator from "validator";
import { MultiSelect } from "react-multi-select-component";
import Link from "next/link";
import toast from 'react-hot-toast';

//Import services/components
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const initialState = {
    username: "",
    email: "",
    roles: "",
    password: "",
    confirm_password: "",
    institution: "",
    region: [],
    country: "",
    mobile_number: "",
    dial_code: "",
    enabled: "",
    firstname: "",
    lastname: "",
    position: "",
};

const UserForm = (_props: any) => {
    const { t, i18n } = useTranslation('common');
    const [formState, setFormState] = useState(initialState);
    const [roles, setRoles] = useState([]);
    const [institutions, setInstitutions] = useState([]);
    const [regions, setRegions] = useState([]);
    const [countryList, setcountryList]: any = useState([]);
    const [userDetails, setUserDetails] = useState({});
    const router = useRouter();

    const routes: any = router.query.routes || [];
    const titleSearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };
    const currentLang = i18n.language;
    const [loggedInUser, setloggedInUser] = useState({});

    const userParams = {
        query: {},
        sort: titleSearch,
        limit: "~",
        languageCode: currentLang,
    };

    const getRoles = async (currentUserData) => {
        let roless = [
            { label: "Authenticated", value: "AUTHENTICATED" },
            { label: "Super Admin", value: "SUPER_ADMIN" },
            { label: "Platform Admins at Institutions", value: "PLATFORM_ADMIN" },
            { label: "NGO’s", value: "NGOS" },
            { label: "EMT User", value: "EMT" },
            { label: "INIG Stakeholder", value: "INIG_STAKEHOLDER" },
            { label: "Health Professional", value: "HEALTH_PROFESSIONAL" },
            { label: "General User", value: "GENERAL_USER" },
            { label: "EMT National Focal Point", value: "EMT_NATIONAL_FOCALPOINT" },
        ];
        roless = roless.sort((a, b) => a.label.localeCompare(b.label));
        let filteredRole = !currentUserData['roles']?.includes("SUPER_ADMIN") ? roless.filter(x => x.value != "SUPER_ADMIN") : roless;
        setRoles(filteredRole);
    };

    const get_response = (response) => {
        setUserDetails(response);
        setFormState((prev) => ({
            ...prev,
            username: response.username,
            firstname: response.firstname,
            lastname: response.lastname,
            position: response.position,
            email: response.email,
            roles: response.roles,
            institution: response.institution ? response.institution._id : "",
            country: response.country ? response.country._id : "",
            region: response.region
                ? response.region.map((item, _i) => {
                      return { label: item.title, value: item._id };
                  })
                : [],
            mobile_number: response.mobile_number ? response.mobile_number : "",
            dial_code: response.dial_code ? response.dial_code : "",
            enabled: response.enabled ? "true" : "false",
        }));
    };

    const getUser = async () => {
        if (routes[1]) {
            const response = await apiService.get(`users/${routes[1]}`, userParams);
            if (response && response._id) {
                get_response(response);
            }
            getRegions(response.country && response.country._id);
        }
    };

    const getInsitutions = async () => {
        const insitutionsList = await apiService.get("institution", userParams);
        if (insitutionsList && insitutionsList.data && insitutionsList.data.length > 0) {
            setInstitutions(insitutionsList.data);
        }
    };

    const getCountries = async () => {
        const response = await apiService.get("/country", userParams);
        if (response && response.data && response.data.length > 0) {
            setcountryList(response.data);
        }
    };
    useEffect(() => {
        loggedInUserData();
        getUser();
        getInsitutions();
        getCountries();
    }, []);

    const loggedInUserData = async () => {
        const currentUser = await apiService.post("/users/getLoggedUser", {});
        if (currentUser && currentUser.username) {
            setloggedInUser(currentUser);
            getRoles(currentUser);
        }
    };

    const formRef = useRef(null);

    const clearValue = (obj) => {
        setFormState((prevState) => ({
            ...prevState,
            ...obj,
        }));
    };

    const bindCountryRegions = (e) => {
        setFormState((prevState) => ({
            ...prevState,
            region: e,
        }));
    };

    const getRegions = async (id) => {
        let _regions = [];
        if (id) {
            const response = await apiService.get(`/country_region/${id}`, userParams);
            if (response && response.data) {
                _regions = response.data.map((item, _i) => {
                    return { label: item.title, value: item._id };
                });
                _regions.sort((a, b) => a.label.localeCompare(b.label));
            }
        }
        setRegions(_regions);
    };

    const handleChange = (e) => {
        const { name, value } = e.currentTarget;
        setFormState((prevState) => ({
            ...prevState,
            [name]: value,
        }));
        if (name === "country") {
            getRegions(value);
            clearValue({ region: [] });
        }
    };

    const resetHandler = () => {
        setFormState(initialState);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const getresponse = async (response, toastMsg) => {
        if ((response && response === "username or email already exists") || (response && response.status === 403)) {
            toast.error(t("adminsetting.user.form.usernameoremailalreadyexists"));
            window.scrollTo(0, 0);
        } else {
toast.success(t(toastMsg))
            Router.push("/adminsettings/users");
        }
    };

    const handleSubmit = async (e, values) => {
        e.preventDefault();
        // Use Formik values if available, otherwise fall back to formState
        const formValues = values || formState;
        const data = {
            username: formValues.username?.toLowerCase().trim(),
            firstname: formValues.firstname?.trim(),
            lastname: formValues.lastname,
            position: formValues.position,
            email: formValues.email?.toLowerCase(),
            roles: formValues.roles,
            institution: formValues.institution ? formValues.institution : null,
            region: formValues.region
                ? formValues.region.map((item, _i) => {
                      return item.value;
                  })
                : [],
            country: formValues.country ? formValues.country : null,
            mobile_number: formValues.mobile_number ? formValues.mobile_number : null,
            dial_code: formValues.dial_code ? formValues.dial_code : null,
            enabled: formValues.enabled === "true" ? true : false,
        };
        let response;
        let toastMsg;
        if (userDetails && userDetails["_id"]) {
            if (formValues.password !== "") {
                data["password"] = formValues.password;
            }
            toastMsg = "adminsetting.user.form.Updatedausersuccessfully";
            response = await apiService.patch(`/users/${userDetails["_id"]}`, data);
        } else {
            toastMsg = "adminsetting.user.form.Addedausersuccessfully";
            data["password"] = formValues.password;
            response = await apiService.post("/users", data);
        }
        getresponse(response, toastMsg);
    };

    const matchPassword = (value) => {
        return value === formState.password;
    };

    return (
        <Container className="formCard" fluid>
            <Card>
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={formState} enableReinitialize={true} autoComplete="off">
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>
                                    {userDetails && userDetails["_id"]
                                        ? t("adminsetting.user.form.EditUser")
                                        : t("adminsetting.user.form.CreateUser")}
                                </Card.Title>
                            </Col>
                        </Row>
                        <hr />

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">
                                        {t("adminsetting.user.form.Username")}
                                    </Form.Label>
                                    <TextInput
                                        name="username"
                                        id="username"
                                        required
                                        value={formState.username}
                                        validator={(value) => String(value || '').trim() !== ""}
                                        errorMessage={{
                                            validator: t("adminsetting.user.form.Youdon'thaveaUsername?"),
                                        }}
                                        onChange={handleChange}
                                        autoComplete="off"
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Form.Group as={Col}>
                                <Form.Label className="required-field">
                                    {t("adminsetting.user.form.Firstname")}
                                </Form.Label>
                                <TextInput
                                    name="firstname"
                                    required
                                    validator={(value) => String(value || '').trim() !== ""}
                                    errorMessage={{
                                        validator: t("adminsetting.user.form.Pleaseenterafirstname"),
                                    }}
                                    id="firstname"
                                    value={formState.firstname}
                                    onChange={handleChange}
                                />
                            </Form.Group>
                            <Form.Group as={Col}>
                                <Form.Label>{t("adminsetting.user.form.Lastname")}</Form.Label>
                                <TextInput
                                    name="lastname"
                                    id="lastname"
                                    value={formState.lastname}
                                    onChange={handleChange}
                                />
                            </Form.Group>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.user.form.Position")}</Form.Label>
                                    <TextInput
                                        name="position"
                                        id="position"
                                        value={formState.position}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">
                                        {t("adminsetting.user.form.Email")}
                                    </Form.Label>
                                    <TextInput
                                        name="email"
                                        id="email"
                                        type="email"
                                        validator={validator.isEmail}
                                        required
                                        errorMessage={{ validator: t("adminsetting.user.form.Pleaseenteravalidemail") }}
                                        value={formState.email}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">
                                        {t("adminsetting.user.form.Role")}
                                    </Form.Label>
                                    <SelectGroup
                                        required
                                        name="roles"
                                        value={formState.roles}
                                        errorMessage={t("adminsetting.user.form.PleaseselectaRole")}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("adminsetting.user.form.SelectRole")}</option>
                                        {roles.map((item, index) => {
                                            return (
                                                <option key={index} value={item.value}>
                                                    {item.label}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.user.form.Organisation")}</Form.Label>
                                    <SelectGroup
                                        name="institution"
                                        value={formState.institution}
                                        errorMessage={t("adminsetting.user.form.PleaseselectaOrganisation.")}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("adminsetting.user.form.SelectOrganisation")}</option>
                                        {institutions.map((item, index) => {
                                            return (
                                                <option key={index} value={item._id}>
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.user.form.Country")}</Form.Label>
                                    <SelectGroup
                                        name="country"
                                        id="country"
                                        value={formState.country}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("adminsetting.user.form.SelectCountry")}</option>
                                        {countryList.map((item, _i) => {
                                            return (
                                                <option key={item._id} value={item._id}>
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.user.form.Region")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectRegions"),
                                            allItemsAreSelected: t("adminsetting.user.form.AllRegionsareSelected"),
                                        }}
                                        options={regions}
                                        value={formState.region}
                                        onChange={bindCountryRegions}
                                        className={"region"}
                                        labelledBy={t("SelectRegions")}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col xs={6}>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.user.form.PhoneNumber")}</Form.Label>
                                    <SelectGroup
                                        name="dial_code"
                                        id="dial_code"
                                        type="number"
                                        value={formState.dial_code}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("adminsetting.user.form.DialCode")}</option>
                                        {countryList.map((item, _i) => {
                                            return (
                                                <option key={item._id} value={item.dial_code}>
                                                    {`(${item.dial_code}) ${item.title}`}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col>
                                <Form.Group>
                                    <Form.Label>&nbsp;</Form.Label>
                                    <TextInput
                                        type="number"
                                        name="mobile_number"
                                        id="mobile_number"
                                        value={formState.mobile_number}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    {userDetails && userDetails["_id"] ? (
                                        <>
                                            <Form.Label>{t("adminsetting.user.form.Password")}</Form.Label>
                                            <TextInput
                                                name="password"
                                                id="password"
                                                type="password"
                                                pattern="^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$"
                                                errorMessage={{
                                                    pattern: t(
                                                        "adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter"
                                                    ),
                                                }}
                                                value={formState.password}
                                                onChange={handleChange}
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <Form.Label className="required-field">
                                                {t("adminsetting.user.form.Password")}
                                            </Form.Label>
                                            <TextInput
                                                name="password"
                                                id="password"
                                                type="password"
                                                required
                                                pattern="^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$"
                                                errorMessage={{
                                                    required: t("adminsetting.user.form.Passwordisrequired"),
                                                    pattern: t(
                                                        "adminsetting.user.form.Passwordshouldcontainatleast8characters,withatleastonedigit,oneletterinuppercase&onespecialcharacter"
                                                    ),
                                                }}
                                                value={formState.password}
                                                onChange={handleChange}
                                            />
                                        </>
                                    )}
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    {userDetails && userDetails["_id"] ? (
                                        <>
                                            <Form.Label>{t("adminsetting.user.form.ConfirmPassword")}</Form.Label>
                                            <TextInput
                                                name="confirm_password"
                                                id="confirm_password"
                                                type="password"
                                                validator={matchPassword}
                                                errorMessage={{
                                                    required: t("adminsetting.user.form.Confirmpasswordisrequired"),
                                                    validator: t("adminsetting.user.form.Passworddoesnotmatch"),
                                                }}
                                                value={formState.confirm_password}
                                                onChange={handleChange}
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <Form.Label className="required-field">
                                                {t("adminsetting.user.form.ConfirmPassword")}
                                            </Form.Label>
                                            <TextInput
                                                name="confirm_password"
                                                id="confirm_password"
                                                type="password"
                                                required
                                                validator={matchPassword}
                                                errorMessage={{
                                                    required: t("adminsetting.user.form.Confirmpasswordisrequired"),
                                                    validator: t("adminsetting.user.form.Passworddoesnotmatch"),
                                                }}
                                                value={formState.confirm_password}
                                                onChange={handleChange}
                                            />
                                        </>
                                    )}
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <label>{t("adminsetting.user.form.Enabled")}</label>
                                    <Radio.RadioGroup
                                        name="enabled"
                                        errorMessage={t("adminsetting.user.form.Itisrequired")}
                                        valueSelected={formState.enabled}
                                        onChange={handleChange}
                                    >
                                        <Radio.RadioItem id="yes" label="Yes" value="true" />
                                        <Radio.RadioItem id="no" label="No" value="false" />
                                    </Radio.RadioGroup>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary">
                                    {t("adminsetting.user.form.Submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("adminsetting.user.form.Reset")}
                                </Button>
                                <Link
                                    href="/adminsettings/[...routes]"
                                    as={`/adminsettings/users`}
                                    >
                                    <Button variant="secondary">{t("adminsetting.user.form.Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
        </Container>
    );
};

export default UserForm;
