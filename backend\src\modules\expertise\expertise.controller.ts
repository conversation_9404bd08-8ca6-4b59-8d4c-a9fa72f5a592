//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateExpertiseDto } from './dto/create-expertise.dto';
import { UpdateExpertiseDto } from './dto/update-expertise.dto';
import { ExpertiseService } from './expertise.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('expertise')
@UseGuards(SessionGuard)
export class ExpertiseController {
  constructor(private readonly _expertiseService: ExpertiseService) {}

  @Post()
  async create(@Body() createExpertiseDto: CreateExpertiseDto) {
    try {
      const resp = await this._expertiseService.create(createExpertiseDto);
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._expertiseService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') expertiseId: string) {
    return this._expertiseService.get(expertiseId);
  }

  @Patch(':id')
  async update(
    @Param('id') expertiseId: string,
    @Body() updateExpertiseDto: UpdateExpertiseDto,
  ) {
    try {
      const resp = await this._expertiseService.update(
        expertiseId,
        updateExpertiseDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') expertiseId: string) {
    const deletedData = this._expertiseService.delete(expertiseId);
    return deletedData;
  }
}
