//Import Library
import axios from 'axios';

//Import services/components
import authService from "./authService";

class ProjectService {
  findAll = async (params = {}) => {
    try {
      const response = await axios.get(`${process.env.API_SERVER}/project`,{ params: params, headers: await authService.getAuthHeader() });
      return response;
    } catch (error: any) {
      return error.response ? error.response : {};
    }
  };
}

export default new ProjectService();