//Import Library
import { <PERSON>, Con<PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import Select from "react-select";
import { useEffect, useState } from "react";

//Import services/components
import { useTranslation } from 'next-i18next';
import apiService from "../../../services/apiService";

const UserTableFilter = ({ filterText, onFilter, onHandleSearch, onClear, onKeyPress }: any) => {
    const { t } = useTranslation('common');
    const [user, setUser] = useState([]);
    const [, setLoading] = useState(false);

    const userParams = {
        sort: { created_at: "desc" },
        limit: "~",
        page: 1,
        query: {},
        select: "-country -dial_code -firstname -region -institution.address -institution.contact_name -institution.description -institution.dial_code -institution.document -institution.doc_src -institution.images -institution.images_src -institution.expertise -institution.hazards -institution.hazard_types -institution.header -institution.networks -institution.partners -institution.telephone -institution.twitter -institution.type -institution.unit -institution.user -mobile_number -position",
    };

    const getUserData = async () => {
        setLoading(true);
        const response = await apiService.get("/users", userParams);
        if (response && Array.isArray(response.data)) {
            const _users = response.data.map((item, _i) => {
                return { label: item.username, value: item._id };
            });
            setUser(_users);

            setLoading(false);
        }
    };

    useEffect(() => {
        getUserData();
    }, []);

    return (
        <Container fluid className="p-0">
            <Row>
                <Col xs={6} md={4} className="p-0">

                    <Select
                        autoFocus={true}
                        isClearable={true}
                        isSearchable={true}
                        onKeyDown={onKeyPress}
                        onChange={onFilter}
                        placeholder={t("adminsetting.user.table.Usernameoremail")}
                        options={user}
                    />
                </Col>

            </Row>
        </Container>
    );
};

export default UserTableFilter;
