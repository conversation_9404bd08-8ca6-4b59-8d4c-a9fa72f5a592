//Import Library
import { all, call, delay, put, take, takeEvery } from 'redux-saga/effects';

//Import services/components
import { loadUserPermissions, actionTypes } from './userActions';
import apiService from "../services/apiService";

function* loadPermissionSaga(): Generator<any, void, any> {
  try {
    const data = yield apiService.get('/permissions');
    yield put(loadUserPermissions(data))
  } catch (err) {
    console.log(err);
  }
}

const loadata = takeEvery(actionTypes.LOAD_DATA, loadPermissionSaga);

export default loadata;