//Import Library
import React, { useState, useEffect } from "react";
import { Container } from "react-bootstrap";

//Import services/components
import apiService from '../../services/apiService';
import UpdatePopup from "../../components/updates/UpdatePopup";
import ProjectCoverSection from "./components/ProjectCoverSection";
import ProjectInfoSection from "./components/ProjectInfoSection";
import ProjectAccordianSection from "./components/ProjectAccordianSection";


interface ProjectShowProps {
  routes: string[];
}

const ProjectShow = (props: ProjectShowProps) => {

  const [projectData, setProjectData] = useState<any>({ title: '', website: '', area_of_work: [], status: {}, funded_by: '', country: {}, description: '', end_date: '', start_date: '', partner_institutions: [], partner_institution: {}, created_at: '', updated_at: '' });
  const [, setName] = useState('');
  const [, setPosition] = useState('');
  const [editAccess, setEditAccess] = useState(false);
  useEffect(() => {
    if (props?.routes[1]) {
      getLoggedInUser();
    }
  }, []);

  const getLoggedInUser = async () => {
    const data = await apiService.post("/users/getLoggedUser", {});
    if (data && data.roles && data.roles.length) {
      if (props.routes && props.routes[1]) {
        const getProjectData = async (projectParams) => {
          const response = await apiService.get(`/project/${props.routes[1]}`, projectParams);
          response_request_func(response, setProjectData, setName, setPosition);
          //Checking for Edit access
          getProjectEditAccess(response, data);
        }
        getProjectData({});
      }
    }
  };

  function getProjectEditAccess(projectData, loginUser) {
    setEditAccess(false);
    if (loginUser && loginUser['roles']) {
      if (loginUser['roles'].includes("SUPER_ADMIN")) {
        //SUPER_ADMIN can Edit all organisations
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "EMT_NATIONAL_FOCALPOINT").length > 0 && projectData.user['_id'] == loginUser['_id']) {
          setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "NGOS").length > 0 && projectData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "GENERAL_USER").length > 0 && projectData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "PLATFORM_ADMIN").length > 0 && projectData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "INIG_STAKEHOLDER").length > 0 && projectData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      } else if (loginUser['roles'].filter(x => x == "EMT").length > 0 && projectData.user['_id'] == loginUser['_id']) {
        setEditAccess(true);
      }
    }
  }

  let propData = {
    projectData : projectData,
    routeData : props,
    editAccess: editAccess
  }

  return (
    <Container className="projectDetail" fluid>
      <UpdatePopup routes={props.routes} />
      <ProjectCoverSection {...propData} />
      <ProjectInfoSection project = {projectData} />
      <ProjectAccordianSection {...propData} />
    </Container>
  );
};

export default ProjectShow;

function response_request_func(response: any, setProjectData: React.Dispatch<React.SetStateAction<{ title: string; website: string; area_of_work: any[]; status: {}; funded_by: string; country: {}; description: string; end_date: string; start_date: string; partner_institutions: any[]; partner_institution: {}; created_at: string; updated_at: string; }>>, setName: React.Dispatch<React.SetStateAction<string>>, setPosition: React.Dispatch<React.SetStateAction<string>>) {
  if (response) { setProjectData(response); }
  if (response?.user?.firstname && response?.user?.lastname) {
    setName(`${response.user.firstname} ${response.user.lastname}`);
  }
  if (response?.user?.position) {
    setPosition(response.user.position);
  }
}

