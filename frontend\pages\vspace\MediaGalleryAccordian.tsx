//Import Library
import { faPlus, faMinus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import { Card, Accordion } from "react-bootstrap";

//Import services/components
import ReactImages from "../../components/common/ReactImages";
import { useTranslation } from 'next-i18next';

interface MediaGalleryAccordianProps {
  calenderEvents: Array<{
    images: any[];
    images_src: any[];
  }>;
}

const MediaGalleryAccordian = (props: MediaGalleryAccordianProps) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);

    return (
        <>
            <Accordion.Item eventKey="2">
              <Accordion.Header onClick={() => setSection(!section)}>
                <div className="cardTitle">{t("vspace.MediaGallery")}</div>
                <div className="cardArrow">
                  {section ? <FontAwesomeIcon icon={faMinus} color="#fff" /> :
                    <FontAwesomeIcon icon={faPlus} color="#fff" />}
                </div>
              </Accordion.Header>
              <Accordion.Body>
                <ReactImages gallery={props.calenderEvents.length > 0 && props.calenderEvents.map(item => item.images).flat(1)}
                  imageSource={props.calenderEvents.length > 0 && props.calenderEvents.map(item => item.images_src).flat(1)} />
              </Accordion.Body>
            </Accordion.Item>
        </>
    )
};

export default MediaGalleryAccordian;