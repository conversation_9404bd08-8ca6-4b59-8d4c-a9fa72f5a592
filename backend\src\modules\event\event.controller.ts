//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  Req,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ACGuard,
  InjectRolesBuilder,
  RolesBuilder,
  UseRoles,
} from 'nest-access-control';

//Import services/components
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { EventService } from './event.service';
import { ImageService } from './../image/image.service';
import { SessionGuard } from 'src/auth/session-guard';
import { ResponseError } from '../../common/dto/response.dto';
import { FilesService } from '../files/files.service';
import { FlagService } from '../flag/flag.service';
import { UpdateService } from '../updates/update.service';
@Controller('event')
@UseGuards(SessionGuard)
export class EventController {
  constructor(
    private readonly _eventService: EventService,
    private readonly _imageService: ImageService,
    private readonly _filesService: FilesService,
    private readonly _flagService: FlagService,
    private readonly _updateService: UpdateService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) {}

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'event',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(
    @Body() createEventDto: CreateEventDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    createEventDto['user'] = user._id;
    const _event = await this._eventService.create(createEventDto);
    const imageIds = _event['images'] ? _event['images'].map((d) => d._id) : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
    const documentIds = this._eventService['document']
      ? this._eventService['document'].map((d) => d['_id'])
      : [];
    if (documentIds.length > 0) {
      await this._filesService.bulkUpdate(documentIds);
    }

    return _event;
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'event',
    action: 'read',
    possession: 'any',
  })
  @Get()
  findAll(@Query() query: any) {
    return this._eventService.findAll(query);
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'event',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') eventId: string) {
    return this._eventService.get(eventId);
  }

  @UseGuards(ACGuard)
  @Patch(':id')
  async update(
    @Param('id') eventId: string,
    @Body() updateEventDto: UpdateEventDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    const oldEvent: any = await this._eventService.get(eventId);
    const eventUserId = oldEvent.user ? oldEvent.user._id : null;
    const permission =
      user._id === eventUserId
        ? this.roleBuilder.can(user.roles).updateOwn('event').granted
        : this.roleBuilder.can(user.roles).updateAny('event').granted;
    const existData = await this._eventService.get(eventId);
    //request.user['_id'] == existData['user'] Adding this condition as organisation user who created organsiation can update it 
    if (permission || request.user['_id'] == existData['user']._id) {
      const _event = await this._eventService.update(eventId, updateEventDto);
      const imageIds = _event['images']
        ? _event['images'].map((d) => d._id)
        : [];
      if (imageIds.length > 0) {
        await this._imageService.bulkUpdate(imageIds);
      }
      const documentIds = this._eventService['document']
        ? this._eventService['document'].map((d) => d['_id'])
        : [];
      if (documentIds.length > 0) {
        await this._filesService.bulkUpdate(documentIds);
      }
      return _event;
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  @UseGuards(ACGuard)
  @Delete(':id')
  async remove(@Param('id') eventId: string, @Req() request: Request) {
    try {
      const user: any = request.user;
      const oldEvent: any = await this._eventService.get(eventId);
      const eventUserId = oldEvent.user ? oldEvent.user._id : null;
      const permission =
        user._id === eventUserId
          ? this.roleBuilder.can(user.roles).updateOwn('event').granted
          : this.roleBuilder.can(user.roles).updateAny('event').granted;
      if (permission) {
        this._flagService.deleteFlagAssociatedWithEntity(eventId);
        this._updateService.deleteUpdateAssociatedWithEntity(eventId, "parent_event");
        const _event = this._eventService.delete(eventId);
        return _event;
      } else {
        return new ResponseError('EVENT.ERROR.ACCESS_DENIED');
      }
    } catch (e) {
      return new ResponseError('EVENT.ERROR.NOT_FOUND');
    }
  }
}
