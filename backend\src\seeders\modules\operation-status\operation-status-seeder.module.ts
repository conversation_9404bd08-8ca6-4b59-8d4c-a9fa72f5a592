//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { OperationStatusSeederService } from './operation-status-seeder.services';
// SCHEMAS
import { OperationStatusSchema } from 'src/schemas/operation_status.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'OperationStatus', schema: OperationStatusSchema }
      ]
    )
  ],
  providers: [OperationStatusSeederService],
  exports: [OperationStatusSeederService],
})
export class OperationStatusSeederModule { }