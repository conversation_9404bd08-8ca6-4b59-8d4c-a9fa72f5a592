//Import Library
import { Calendar, momentLocalizer, View } from "react-big-calendar";
import moment from "moment";
import 'moment/locale/fr';
import Router from "next/router";
import { Col, Container, Row } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faStar } from "@fortawesome/free-solid-svg-icons";
const localizer = momentLocalizer(moment);
import _ from "lodash";

//Import services/components
import { useTranslation } from 'next-i18next';

interface CalendarEvent {
  _id: string;
  start_date: string | Date;
  end_date: string | Date;
  [key: string]: any;
}

interface ToolbarProps {
  label: string;
  onNavigate: (action: string) => void;
}

interface RKICalendarProps {
  eventsList: CalendarEvent[];
  style?: React.CSSProperties;
  minicalendar?: boolean;
  views?: View[];
  showEventCounts?: boolean;
}

function RKICalendar(props: RKICalendarProps) {
  const { t, i18n } = useTranslation('common');
  const currentLang = i18n.language
  const { eventsList, style, minicalendar, views } = props;
  let calendarComponent: any = {};
  if (props.showEventCounts) {
    calendarComponent = {
      eventWrapper: EventWrapper,
      month: {
        dateHeader: (headerProps: any) =>
           <DateHeader eventsList={eventsList} {...headerProps} />
      },
    };
  }
  if (minicalendar) {
    calendarComponent.toolbar = MinimalToolbar;
  }

  return (
    <Calendar
      culture={currentLang}
      localizer={localizer}
      events={eventsList}
      views={views}
      startAccessor="start_date"
      endAccessor="end_date"
      style={style}
      components={calendarComponent}
      messages={{today:t("today"),previous:t("back"),next:t("Next"),month:t("Month"),week:t("Week"),day:t("Day")}}
      onSelectEvent={(event: CalendarEvent) => {
        const findOption = Object.keys(event)
          .filter((item) => item.includes("parent"))
          .toString();
        const urlAppend = findOption.split("_")[1];
        Router.push(
          `/${urlAppend}/show/${event[findOption]}/update/${event._id}`
        );
      }}
    />
  );
}

RKICalendar.defaultProps = {
  minicalendar: false,
  views: ["month"],
};

function MinimalToolbar(props: ToolbarProps) {
  return (
    <Container className="mb-1">
      <Row>
        <Col className="p-0" md={1}>
          <i
            style={{ cursor: "pointer" }}
            onClick={() => props.onNavigate("PREV")}
            className={`fas fa-chevron-left`}
          />
        </Col>
        <Col className="text-center" md={10}>
          <span className="rbc-toolbar-label">{props.label}</span>
        </Col>
        <Col className="p-0 text-end" md={1}>
          <i
            style={{ cursor: "pointer" }}
            onClick={() => props.onNavigate("NEXT")}
            className={`fas fa-chevron-right`}
          />
        </Col>
      </Row>
    </Container>
  );
}

// Generates event counts based on the given date
const getEventsCount = (eventsList: CalendarEvent[], date: Date): number => {
  let eventsCount = 0;
  _.forEach(eventsList, (e) => {
    const startDate = moment(e.start_date).set({
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
    });
    const endDate = moment(e.end_date).set({
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
    });

    const isEvent = moment(date).isBetween(startDate, endDate, null, "[]");
    if (isEvent) {
      eventsCount += 1;
    }
  });
  return eventsCount;
};

const DateHeader = ({ date, label, eventsList }: { date: Date; label: string; eventsList: CalendarEvent[] }) => {
  const eventsCount = getEventsCount(eventsList, date);
  const isEventCompleted = moment(date).isBefore(new Date(), "day");

  return (
    <div
      className="rbc-date-cell"
      onClick={() => Router.push("/events-calendar")}
    >
      <a href="#">{label}</a>
      {eventsCount > 0 && (
        <span className="d-flex justify-content-start align-items-center fa-stack">
          <FontAwesomeIcon
            icon={faStar}
            color={isEventCompleted ? "grey" : "#04A6FB"}
            size="lg"
          />
          <span className="eventCount">{eventsCount}</span>
        </span>
      )}
    </div>
  );
};

const EventWrapper = (props: { onSelect?: () => void }) => {
  return <div onSelect={props.onSelect} />;
};

export default RKICalendar;
