//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { HazardInterface } from '../../interfaces/hazard.interface';

@Injectable()
export class HazardHazardTypeService {
  constructor(
    @InjectModel('Hazard') private hazardModel: Model<HazardInterface>,
  ) { }

  async get(hazardId, query?: any): Promise<HazardInterface[]> {
    let _result;
    try {
      const myCustomLabels = {
        totalDocs: 'totalCount',
        docs: 'data'
      };

      const options = {
        lean: query.lean ? query.lean : false,
        sort: query.sort ? query.sort : {},
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
        customLabels: myCustomLabels,
        select: query.select ? query.select : ''
      };

      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }

      const _filter = query.query ? query.query : {};

      if (hazardId) {
        _filter.hazard_type = hazardId
      }
      _result = await this.hazardModel.paginate(_filter, options);
    } catch (error) {
      throw new NotFoundException('Could not find Hazard.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Hazard.');
    }
    return _result;
  }
}
