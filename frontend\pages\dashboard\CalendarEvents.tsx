//Import Library
import {useEffect, useState} from "react";
import _ from "lodash";

//Import services/components
import RKICalendar from "../../components/common/RKICalendar";
import apiService from "../../services/apiService";

interface CalendarEventsProps {
  [key: string]: any;
}

function CalendarEvents(_props: CalendarEventsProps) {

  const [events, setEvents] = useState<any[]>([]);

  const fetchUpdateType = async () => {
    const response = await apiService.get('/updateType', {"query": {"title": "Calendar Event"}});
    if (response && response.data && response.data.length > 0 ) {
      fetchCalendarEvents(response.data[0]._id);
    }
  };

  const fetchCalendarEvents = async (updateTypeId) => {
    const calendarEventsParams = {
      query: {update_type: updateTypeId},
      sort: {created_at: "desc" },
      limit: 20,
      select: "-contact_details -description -document -images -link -media -parent_operation -reply -show_as_announcement -type -user"
    }

    const response = await apiService.get('/updates', calendarEventsParams);
    if (response && response.data) {
      _.forEach(response.data, function(val, i) {
        response.data[i].allDay = false;
      });
      setEvents(response.data)
    }
  };

  useEffect(() => {
    fetchUpdateType();
  },[])

  return (
    <RKICalendar eventsList={events} minicalendar showEventCounts />
  )
}

export default CalendarEvents;