//Import Library
import * as bcrypt from 'bcrypt';
import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import * as mongoose from 'mongoose';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { ForgottenPassword } from '../interfaces/forgottenpassword.interface';
import { UsersService } from '../users/users.service';
import { UsersSchema } from '../schemas/users.schemas';
import { EmailService } from '../email.service';
const Message = mongoose.model('Message', UsersSchema);

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private readonly _emailService: EmailService,
    @InjectModel('ForgottenPassword')
    private readonly forgottenPasswordModel: Model<ForgottenPassword>,
  ) {}

  async validateUser(username: string, pass: string): Promise<any> {
    const messageToSearchWith: any = new Message({ username });
    messageToSearchWith.encryptFieldsSync();
    const user: any = await this.usersService.findusernameoremail(
      { email: messageToSearchWith['username'] },
      { username: messageToSearchWith['username'] },
    );

    if (user?.length > 0) {
      const isValidPassword = await this.usersService.validPassword(
        user[0].password,
        pass,
      );
      if (isValidPassword) {
        const { password, ...result } = user[0];
        return result;
      }
    }
    return null;
  }

  async login(user: any) {
    return {
      username: user._doc.username,
      sub: user._doc._id,
      roles: user._doc.roles,
      isEnabled: user._doc.enabled,
    };
  }

  // async adminLogin(user: any) {
  //   if (user._doc.roles.includes('SUPER_ADMIN')) {
  //     return this.getUserInfo(user);
  //   } else {
  //     throw new ForbiddenException();
  //   }
  // }

  async checkPassword(email: string, password: string) {
    const User = mongoose.model('Users', UsersSchema);
    const emailToSearch: any = new User({ email });
    emailToSearch.encryptFieldsSync();
    const userFromDb = await this.usersService.findOne({
      email: emailToSearch['email'],
    });
    if (!userFromDb) {
      throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);
    }
    const bcrypassword = await bcrypt.compareSync(
      password,
      userFromDb.password,
    );
    return bcrypassword;
  }

  async createForgottenPasswordToken(
    email: string,
  ): Promise<ForgottenPassword> {
    const forgottenPasswordModel =
      await this.forgottenPasswordModel.findOneAndUpdate(
        { email: email },
        {
          email: email,
          newPasswordToken: uuidv4(),
          timestamp: new Date(),
        },
        { upsert: true, new: true },
      );
    if (forgottenPasswordModel) {
      return forgottenPasswordModel;
    } else {
      throw new HttpException(
        'LOGIN.ERROR.GENERIC_ERROR',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async forgotpassword(email: string): Promise<boolean> {
    const User = mongoose.model('Users', UsersSchema);
    const emailToSearch: any = new User({ email });
    emailToSearch.encryptFieldsSync();
    const userFromDb: any = await this.usersService.findOne({
      email: emailToSearch['email'],
    });
    if (!userFromDb) {
      throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);
    }
    const tokenModel = await this.createForgottenPasswordToken(email);
    if (tokenModel?.newPasswordToken) {
      return this._emailService.resetPasswordEmail(
        email,
        userFromDb.username,
        tokenModel.newPasswordToken,
      );
    } else {
      throw new HttpException(
        'REGISTER.USER_NOT_REGISTERED',
        HttpStatus.FORBIDDEN,
      );
    }
  }

  async getForgottenPasswordModel(
    newPasswordToken: string,
  ): Promise<ForgottenPassword> {
    const tokenData = await this.forgottenPasswordModel.findOne({
      newPasswordToken: newPasswordToken,
    });
    if (tokenData == null) {
      throw new HttpException('RESET_PASSWORD.NO_TOKEN', HttpStatus.NOT_FOUND);
    } else {
      const tokenDate: any = new Date(tokenData.timestamp);
      const currentDate: any = new Date();
      const diff = currentDate - tokenDate;
      const hoursDifference = Math.floor(diff / 1000 / 60 / 60);
      if (process.env.RESET_PASSWORD_EXPIRY) {
        if (hoursDifference <= parseInt(process.env.RESET_PASSWORD_EXPIRY)) {
          return tokenData;
        } else {
          throw new HttpException(
            'RESET_PASSWORD.EXPIRED_TOKEN',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      } else {
        throw new HttpException(
          'RESET_PASSWORD.EXPIRY_RESET_HOURS_NOT_SET',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }
}
