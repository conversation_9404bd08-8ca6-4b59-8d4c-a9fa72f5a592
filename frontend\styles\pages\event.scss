.eventDetail {
  padding-top: 15px;
}
.eventAccordion {
  padding-top: 25px;
  padding-bottom: 25px;
  .card {
    border: none;
    margin: 25px 0;
  }
  .card-body {
    padding: 30px 10px;
  }
  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;
    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }
    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }
    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;
      svg {
        font-size: 16px;
      }
    }
  }
}

.eventDetails {
  color: #0b1627;
  p {
    margin: 0 0 13px;
    display: flex;
    b {
      min-width: 170px;
      display: inline-block;
    }
    span {
      display: inline-block;
      margin: 0 0 0 5px;
    }
  }
}

.eventButton{
  width:150px
}


.riskIcon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 5px solid #ddd;
  border-radius: 50%;
  &.risk0 {
    // Low
    background: #2e9a09;
    background: -moz-radial-gradient(center, ellipse cover, #2e9a09 0%, #134c00 60%, #134c00 99%);
    background: -webkit-radial-gradient(center, ellipse cover, #2e9a09 0%, #134c00 60%, #134c00 99%);
    background: radial-gradient(ellipse at center, #2e9a09 0%, #134c00 60%, #134c00 99%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2e9a09', endColorstr='#134c00',GradientType=1 );
  }
  &.risk1 {
    // Medium
    background: #ffff00;
    background: -moz-radial-gradient(center, ellipse cover, #ffff00 0%, #e3ac00 55%, #b7b100 99%);
    background: -webkit-radial-gradient(center, ellipse cover, #ffff00 0%, #e3ac00 55%, #b7b100 99%);
    background: radial-gradient(ellipse at center, #ffff00 0%, #e3ac00 55%, #b7b100 99%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffff00', endColorstr='#b7b100',GradientType=1 );
  }
  &.risk2 {
    // High
    background: #ffb192;
    background: -moz-radial-gradient(center, ellipse cover, #ffb192 0%, #dd5409 60%, #c64c09 99%);
    background: -webkit-radial-gradient(center, ellipse cover, #ffb192 0%, #dd5409 60%, #c64c09 99%);
    background: radial-gradient(ellipse at center, #ffb192 0%, #dd5409 60%, #c64c09 99%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffb192', endColorstr='#c64c09',GradientType=1 );
  }
  &.risk3 {
    // VeryHigh
    background: #cb2121;
    background: -moz-radial-gradient(center, ellipse cover, #cb2121 0%, #720000 76%, #5e0e00 99%);
    background: -webkit-radial-gradient(center, ellipse cover, #cb2121 0%, #720000 76%, #5e0e00 99%);
    background: radial-gradient(ellipse at center, #cb2121 0%, #720000 76%, #5e0e00 99%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#cb2121', endColorstr='#5e0e00',GradientType=1 );
  }
}

.riskDetails {
  display: flex;
  justify-content: space-between;
  padding: 0 50px 0 0;
  .riskItems {
    display: flex;
  }
  .riskInfo {
    margin: 0 0 0 15px;
    h5 {
      text-transform: uppercase;
      font-size: 25px;
      font-weight: 400;
    }
  }
}


// Responsive 
@media screen and (max-width: 991px) {
  .main-container {
    .eventDetails {
      margin: 20px 0 0;
    }
    .riskDetails {
      display: block;
      padding: 0;
      .riskItems {
        display: flex;
        margin: 0 0 25px;
      }
    }
  }
}