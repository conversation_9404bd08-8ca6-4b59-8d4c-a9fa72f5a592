//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { RiskLevelInterface } from '../../interfaces/risklevel.interface';
import { CreateRiskLevelDto } from './dto/create-risk-level.dto';
import { UpdateRiskLevelDto } from './dto/update-risk-level.dto';
const FindRiskLevel = 'Could not find RiskLevel.';
@Injectable()
export class RiskLevelService {
  constructor(
    @InjectModel('RiskLevel') private risklevelModel: Model<RiskLevelInterface>,
  ) {}

  async create(
    createRiskLevelDto: CreateRiskLevelDto,
  ): Promise<RiskLevelInterface> {
    const createdRiskLevel = new this.risklevelModel(createRiskLevelDto);
    return createdRiskLevel.save();
  }

  async findAll(query): Promise<RiskLevelInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.risklevelModel.paginate(_filter, options);
  }

  async get(risklevelId): Promise<RiskLevelInterface[]> {
    let _risklevelModel;
    try {
      _risklevelModel = await this.risklevelModel.findById(risklevelId).exec();
    } catch (error) {
      throw new NotFoundException(FindRiskLevel);
    }
    if (!_risklevelModel) {
      throw new NotFoundException(FindRiskLevel);
    }
    return _risklevelModel;
  }

  async update(risklevelId: any, updateRiskLevelDto: UpdateRiskLevelDto) {
    const getRiskLevelById: any = await this.risklevelModel
      .findById(risklevelId)
      .exec();
    const updatedRiskLevel = new this.risklevelModel(updateRiskLevelDto);
    Object.keys(updateRiskLevelDto).forEach((d) => {
      getRiskLevelById[d] = updatedRiskLevel[d];
    });
    getRiskLevelById.updated_at = new Date();
    return getRiskLevelById.save();
  }

  async delete(risklevelId: string) {
    const result = await this.risklevelModel
      .deleteOne({ _id: risklevelId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(FindRiskLevel);
    }
  }
}
