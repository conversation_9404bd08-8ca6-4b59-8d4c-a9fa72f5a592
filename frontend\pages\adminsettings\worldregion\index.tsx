//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import WorldregionTable from "./worldregionTable";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddWorldRegion } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";
const WorldregionIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowWorldregionIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title={t("adminsetting.worldregion.form.WorldRegion")}/>
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_worldregion"
                >
                <Button variant="secondary" size="sm">
                {t("adminsetting.worldregion.form.Addworldregion")}
              </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              < WorldregionTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }
  const ShowAddWorldRegion = canAddWorldRegion(() => <ShowWorldregionIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.worl_region?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddWorldRegion />
  );
}

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default WorldregionIndex;