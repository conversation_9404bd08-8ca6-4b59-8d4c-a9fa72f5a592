//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { RiskLevelInterface } from '../../../interfaces/risklevel.interface';
import { riskLevels } from "../../data/risk-level";

/**
 * Service dealing with syndrome based operations.
 *
 * @class
 */
@Injectable()
export class RiskLevelSeederService {

  constructor(
    @InjectModel('RiskLevel') private riskLevelModel: Model<RiskLevelInterface>
  ) {}

  /**
   * Seed all syndrome.
   *
   * @function
   */
  create(): Array<Promise<RiskLevelInterface>> {
    return riskLevels.map(async (rl: RiskLevelInterface) => {
      return await this.riskLevelModel
        .findOne({ title: rl.title })
        .exec()
        .then(async dbRiskLevel => {
          // We check if a syndrome already exists.
          // If it does don't create a new one.
          if (dbRiskLevel) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.riskLevelModel.create(rl),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}