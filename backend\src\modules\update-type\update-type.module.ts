//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { UpdateTypeController } from './update-type.controller';
import { UpdateTypeService } from './update-type.service';
// SCHEMAS
import { UpdateTypeSchema } from '../../schemas/update_type.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'UpdateType', schema: UpdateTypeSchema }
    ])
  ],
  controllers: [UpdateTypeController],
  providers: [UpdateTypeService],
})

export class UpdateTypeModule { }