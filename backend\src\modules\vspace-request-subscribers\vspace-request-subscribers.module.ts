//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { VspaceRequestSubscribersController } from './vspace-request-subscribers.controller';
import { VspaceRequestSubscribersService } from './vspace-request-subscribers.service';
// SCHEMAS
import { VspaceRequestSubscribersSchema } from '../../schemas/vspace-request-subscribers.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'VspaceRequestSubscribers', schema: VspaceRequestSubscribersSchema }
    ])
  ],
  controllers: [VspaceRequestSubscribersController],
  providers: [VspaceRequestSubscribersService],
})

export class VspaceRequestSubscribersModule { }