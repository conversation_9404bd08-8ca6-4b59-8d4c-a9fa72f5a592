// userReducer.ts

// Import action types
import { actionTypes } from './userActions'

// Define the shape of user data
interface UserData {
  _id: string;
  username: string;
  email: string;
  status: string;
  enabled: boolean;
  vspace_status: string;
  is_vspace: boolean;
  is_focal_point: boolean;
  dataConsentPolicy: boolean;
  restrictedUsePolicy: boolean;
  acceptCookiesPolicy: boolean;
  withdrawConsentPolicy: boolean;
  medicalConsentPolicy: boolean;
  fullDataProtectionConsentPolicy: boolean;
  image: string;
}

// Define the shape of the action
interface LoadDataSuccessAction {
  type: typeof actionTypes.LOAD_DATA_SUCCESS;
  data: UserData;
}

type UserAction = LoadDataSuccessAction; // Add more actions to this union if needed

// Initial state with proper type
export const userInitialState: UserData = {
  _id: "",
  username: "",
  email: "",
  status: "",
  enabled: false,
  vspace_status: "",
  is_vspace: false,
  is_focal_point: false,
  dataConsentPolicy: false,
  restrictedUsePolicy: false,
  acceptCookiesPolicy: false,
  withdrawConsentPolicy: false,
  medicalConsentPolicy: false,
  fullDataProtectionConsentPolicy: false,
  image: ""
}

// Reducer with typed state and action
function reducer(
  state: UserData = userInitialState,
  action: UserAction
): UserData {
  switch (action.type) {
    case actionTypes.LOAD_DATA_SUCCESS:
      return {
        ...state,
        ...action.data
      };
    default:
      return state;
  }
}

export default reducer;
