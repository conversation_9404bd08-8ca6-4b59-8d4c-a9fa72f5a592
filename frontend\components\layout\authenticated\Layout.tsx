//Import Library
import { Container, <PERSON>, Col } from "react-bootstrap";
import { useState } from "react";

//Import services/components
import Header from "./Header";
import SideBar from "./SideBar";
import UpdatesRegion from "../../updates/UpdatesRegion";
import { UpdateContext, useUpdates } from "../../../context/update";
// TODO: Need to refactor the code and split into many components
const isUpdateOrCreate = (props: any) => {
  const routes: any = props.router.query.routes || [];
  const subPages = routes && routes[0] ? routes[0] : "";
  const routeName = props.router.route;
  if (
    subPages === "create" ||
    subPages === "edit" ||
    routeName === "/vspace" ||
    routeName === "/people" ||
    routeName === "/profile" ||
    (routeName.includes("/updates") && subPages === "add") ||
    routeName.indexOf("adminsettings") > -1 ||
    routeName.indexOf("users") > -1 ||
    routeName.indexOf("search") > -1 ||
    routeName.indexOf("data-privacy-policy") > -1
  ) {
    return true;
  }
  return false;
};

interface LayoutProps {
  children: React.ReactNode;
  router: any;
  [key: string]: any;
}

export default function Layout(props: LayoutProps) {
  const contextValue = useUpdates();
  const [isleftActive, setleftIsActive] = useState(false);
  const [isrightActive, setrightIsActive] = useState(false);

  const rightupdateButton = () => {
    setrightIsActive(!isrightActive);
    setleftIsActive(false);
  };

  const leftMenuButton = () => {
    setrightIsActive(false);
    setleftIsActive(!isleftActive);
  };

  const update = () => {
    if (!isUpdateOrCreate(props)) {
      return (
        <div
          className={`sidebar-rightregion ${isrightActive && "show-rightmenu"}`}
        >
          <div
            className={"mobile-rightmenu"}
            onClick={() => rightupdateButton()}
          >
            &lt;&lt;
          </div>
          <div className="sidebar-right">
            <UpdatesRegion />
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Container fluid={true} className="main-container">
      <Row>
        <Col className="p-0">
          <Header />
          <Container fluid={true} className="px-2">
            <Row>
              <div
                className={`p-0 sidebar-region ${
                  isleftActive && "show-leftmenu"
                }`}
              >
                <div
                  className={"mobile-leftmenu"}
                  onClick={() => leftMenuButton()}
                >
                  &gt;&gt;
                </div>
                <SideBar />
              </div>
              <Col className="content-region" id="main-content">
                <UpdateContext.Provider value={contextValue}>
                  <Row>
                    <Col
                      xs={isUpdateOrCreate(props) ? "12" : "auto"}
                      className={isUpdateOrCreate(props) ? "position-relative px-3 py-2 ":"px-3 py-2 content-block"}                    >
                      <div id="main">{props.children}</div>
                    </Col>
                    {update()}
                  </Row>
                </UpdateContext.Provider>
              </Col>
            </Row>
          </Container>
        </Col>
      </Row>
    </Container>
  );
}
