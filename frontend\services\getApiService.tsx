//Import services/components
import apiService from "./apiService";

class GetApiDataService {
  getApiData = async (url: string, withData: any, params: Record<string, any> = {}) => {
    try {
      const response = await apiService.get(url, params);
      return withData ? response.data : response;
    } catch (error: any) {
      return error.response ? error.response : {};
    }
  };
}

export default new GetApiDataService();