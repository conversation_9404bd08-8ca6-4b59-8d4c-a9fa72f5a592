//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { UsersSeederService } from './users-seeder.services';
// SCHEMAS
import { UsersSchema } from 'src/schemas/users.schemas';
import { RolesSchema } from 'src/schemas/roles.schemas';

/**
 * Import and provide seeder classes for Syndrome.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Users', schema: UsersSchema },
        { name: 'Roles', schema: RolesSchema }
      ]
    )
  ],
  providers: [UsersSeederService],
  exports: [UsersSeederService],
})
export class UsersSeederModule { }
