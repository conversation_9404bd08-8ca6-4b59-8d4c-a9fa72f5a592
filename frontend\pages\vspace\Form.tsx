//Import Library
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col } from "react-bootstrap";
import Router, { useRouter } from "next/router";
import Link from "next/link";
import moment from "moment";
import toast from 'react-hot-toast';
import { ValidationForm } from "../../components/common/FormValidation";

//Import services/components
import apiService from "../../services/apiService";
import RKIDatePicker from "../../components/common/RKIDatePicker";
import { useTranslation } from 'next-i18next';
import ReactDropZone from "../../components/common/ReactDropZone";
import GroupVisibility from "../../components/common/GroupVisibility";
import { EditorComponent } from "../../shared/quill-editor/quill-editor.component";

const initialState: any = {
    title: "",
    description: "",
    startDate: null,
    endDate: null,
    searchData: "",
    visibility: true,
    images: [],
    checked: false,
    file_category: "",
    nonMembers: [],
    images_src: [],
    members: [],
    doc_src: [],
    document: [],
};

const VSpaceForm = (props: any) => {
    const buttonRef = useRef<any>(null);

    const router = useRouter();
    const { t } = useTranslation('common');
    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);
    const [srcCollection, setSrcCollection] = useState<any[]>([]);
    const [docSrcCollection, setDocSrcCollection] = useState<any[]>([]);
    const [docCollection, setDocCollection] = useState<any[]>([]);
    const [formState, setFormState] = useState<any>(initialState);
    const [usersList, setUsersList] = useState<any[]>([]);
    const [groupRegion, setGroupRegion] = useState<any[]>([]);
    const [groupOrganisation, setOrganisation] = useState<any[]>([]);
    const [groupNetwork, setNetworkList] = useState<any[]>([]);
    const [expertiseList, setExpertiseList] = useState<any[]>([]);
    const [, setValidated] = useState<boolean>(false);
    const [vSpace, setVspace] = useState<any>(null);
    const [routeSource, setRouteSource] = useState<any>(null);
    const [groupVisibility, setGroupVisibility] = useState<any>({
        invitesCountry: [],
        invitesRegion: [],
        invitesOrganisationType: [],
        invitesOrganisation: [],
        invitesExpertise: [],
        invitesNetWork: [],
        userList: [],
    });

    const formRef = useRef<any>(null);

    const handleDescription = (value: any) => {
        setFormState((prevState: any) => ({
            ...prevState,
            description: value,
        }));
    };

    const onChangeDate = (date: any, key: any) => {
        setFormState((prevState: any) => ({
            ...prevState,
            [key]: date,
        }));
    };

    const handleEndDateCheckBox = () => {
        setFormState((prevState: any) => ({
            ...prevState,
            checked: !prevState.checked,
        }));
    };

    const resetHandler = () => {
        setFormState(initialState);
        setDropZoneCollection([]);
        setSrcCollection([]);
        setDocCollection([]);
        setDocSrcCollection([]);
        setGroupVisibility({
            invitesCountry: [],
            invitesRegion: [],
            invitesOrganisationType: [],
            invitesOrganisation: [],
            invitesExpertise: [],
            invitesNetWork: [],
            userList: [],
        });
        // Reset validation state (Formik handles this automatically)
        setValidated(false);
        window.scrollTo(0, 0);
    };

    const onSubmitForm = async (event: any) => {
        if (buttonRef.current) {
            buttonRef.current.setAttribute("disabled", "disabled");
        }

        event.preventDefault();

        // Basic validation for required fields
        if (!formState.title || formState.title.length < 5) {
            toast.error(t("minimum5CharsReq"));
            if (buttonRef.current) {
                buttonRef.current.removeAttribute("disabled");
            }
            return;
        }

        setValidated(true);
        let response;
        let toastMsg;
        //send user id to members array
        const _users = groupVisibility.userList.length > 0 ? groupVisibility.userList.map((item: any) => item.value) : [];
        const data: any = {
            title: formState.title,
            description: formState.description,
            start_date: formState.startDate,
            end_date: formState.endDate,
            visibility: formState.visibility === true,
            images: formState.images,
            images_src: formState.images_src,
            members: _users,
            nonMembers:
                formState.nonMembers && formState.nonMembers.length > 0
                    ? formState.nonMembers.map((item: any) => item.value)
                    : "",
            document: formState.document,
            doc_src: formState.doc_src,
        };

        if (routeSource === "Operation") {
            data["operation"] = vSpace;
        } else {
            data["project"] = vSpace;
        }

        try {
            if (props.routes && props.routes[0] === "edit" && props.routes[1]) {
                toastMsg = "vspace.virtualspaceupdatedsuccessfully";
                response = await apiService.patch(`/vspace/${props.routes[1]}`, data);
            } else {
                toastMsg = "vspace.virtualspaceaddedsuccessfully";
                response = await apiService.post("/vspace", data);
            }
            if (response && response._id) {
                toast.success(t(toastMsg));
                Router.push("/vspace/[...routes]", `/vspace/show/${response._id}`);
            } else {
                toast.error(t("An error occurred while saving the virtual space"));
                if (buttonRef.current) {
                    buttonRef.current.removeAttribute("disabled");
                }
            }
        } catch (error: any) {
            console.error("Error saving virtual space:", error);
            toast.error(t("An error occurred while saving the virtual space"));
            if (buttonRef.current) {
                buttonRef.current.removeAttribute("disabled");
            }
        }
    };

    const getrespdata = (respData: any) => {
        respData && respData.images ? setDropZoneCollection(respData.images) : setDropZoneCollection([]);
        respData && respData.images_src ? setSrcCollection(respData.images_src) : setSrcCollection([]);
        respData && respData.document ? setDocCollection(respData.document) : setDocCollection([]);
        respData && respData.doc_src ? setDocSrcCollection(respData.doc_src) : setDocSrcCollection([]);
    };
    const fetchFormData = async () => {
        const logUser = await apiService.post("/users/getLoggedUser", {});
        const respData = await apiService.get(`/vspace/${props.routes[1]}`);
        const data: any = {
            title: respData.title,
            description: respData.description,
            startDate: respData.start_date ? moment(respData.start_date).toDate() : null,
            endDate: respData.end_date ? moment(respData.end_date).toDate() : null,
            file_category: respData.file_category ? respData.file_category : null,
            visibility: respData.visibility ? true : false,
            user: respData.user ? respData.user._id : "",
            images: respData.images,
            images_src: respData.images_src,
            nonMembers:
                respData.nonMembers[0] !== "" ? respData.nonMembers.map((item: any) => ({ label: item, value: item })) : [],
        };
        if (respData.members.length > 0) {
            let selectedUsers: any[] = [];
            respData.members.forEach((member: any) => {
                selectedUsers.push({ label: member.username, value: member._id });
            });
            setGroupVisibility({ ...groupVisibility, ...{ userList: selectedUsers } });
        }
        getrespdata(respData);

        if (respData.user && respData.user._id !== logUser["_id"]) {
            Router.push("/vspace");
        }
        setFormState(data);
        return respData.end_date ? setFormState((prevState) => ({ ...prevState, checked: true })) : null;
    };

    useEffect(() => {
        if (props.routes && props.routes[0] === "edit" && props.routes[1]) {
            fetchFormData();
        }

        setVspace(router && router.query && router.query.id ? router.query.id : null);
        setRouteSource(router && router.query && router.query.source ? router.query.source : null);
    }, []);

    /**Fetching All Users**/
    useEffect(() => {
        //Parms
        const userParams = {
            query: {},
            sort: { username: "asc" },
            limit: "~",
            select: "-acceptCookiesPolicy -country -created_at -dataConsentPolicy -dial_code -enabled -firstname -image -institution -is_focal_point -password -position -region -restrictedUsePolicy -withdrawConsentPolicy -medicalConsentPolicy -fullDataProtectionConsentPolicy -email -roles -updated_at -emailActivateToken -lastname -mobile_number ",
        };

        const fetchAllUser = async () => {
            const userList = await apiService.get(`/users`, userParams);
            if (userList?.data?.length)
                userList.data = userList.data.filter((_userList) =>
                    _userList.vspace_status === "Request Pending" || _userList.status === "Request Pending"
                        ? false
                        : true
                );
            if (userList) {
                const _users = userList.data.map((item, _i) => {
                    return { label: item.username, value: item._id };
                });
                setUsersList(_users);
            }
        };

        fetchAllUser();
    }, [groupVisibility]);

    /**End**/

    /**Set Non Member to state***/
    const nonMemberHandler = (email: any) => {
        setFormState((prevState) => ({ ...prevState, nonMembers: email }));
    };
    /****End****/

    React.useEffect(() => {
        if (groupVisibility) {
            const normalizeGroup: any = {};
            Object.keys(groupVisibility).forEach((item: any, _i: any) => {
                const _data: any[] =
                    (groupVisibility as any)[item].length > 0 &&
                    (groupVisibility as any)[item].map((d: any) => {
                        return d.value;
                    });
                normalizeGroup[item] = _data ? _data : [];
            });
            getUsers(normalizeGroup);
        } else {
            console.log("No threshold reached.");
        }
    }, [groupVisibility]);

    const getinstitution = (institution: any) => {
        if (institution && Array.isArray(institution.data)) {
            const _institution = institution.data.map((item: any) => {
                return { label: item.title, value: item._id };
            });
            setOrganisation(_institution);
        }
    };

    const getexpertise = (expertise: any) => {
        if (expertise && Array.isArray(expertise.data)) {
            const _expertise = expertise.data.map((item: any, _i: any) => {
                return { label: item.title, value: item._id };
            });
            setExpertiseList(_expertise);
        }
    };

    const getinstitutionNetwork = (institutionNetwork: any) => {
        if (institutionNetwork && Array.isArray(institutionNetwork.data)) {
            const _institutionNetwork = institutionNetwork.data.map((item: any, _i: any) => {
                return { label: item.title, value: item._id };
            });
            setNetworkList(_institutionNetwork);
        }
    };
    const getUsers = async (normalizeGroup: any) => {
        const {
            invitesCountry,
            invitesRegion,
            invitesOrganisationType,
            invitesOrganisation,
            invitesExpertise,
            invitesNetWork,
        } = normalizeGroup;
        const groupParams = {
            query: {
                country: invitesCountry,
                country_region: invitesRegion,
                institution_type: invitesOrganisationType,
                institution: invitesOrganisation,
                expertises: invitesExpertise,
                networks: invitesNetWork,
                type: "public",
            },
        };
        let _regions: any[] = [];
        let _organisation: any[] = [];
        let _experts: any[] = [];
        let _network: any[] = [];
        const userInvites = await apiService.post("vspace/filterUser", groupParams);
        if (userInvites && Array.isArray(userInvites)) {
            if (userInvites[0].regions && userInvites[0].regions.length > 0) {
                _regions = userInvites[0].regions.map((item: any, _i: any) => {
                    return { label: item.title, value: item._id };
                });
                setGroupRegion(_regions);
            }
            if (userInvites[1].organisation && userInvites[1].organisation.length > 0) {
                _organisation = userInvites[1].organisation.map((org: any, i: any) => {
                    _network = org.networks.map((item: any) => {
                        return { label: item.title, value: item._id };
                    });
                    _experts = org.expertise.map((item: any) => {
                        return { label: item.title, value: item._id };
                    });
                    return { label: org.title, value: org._id };
                });
                setOrganisation(_organisation);
                setNetworkList(_network);
                setExpertiseList(_experts);
            } else if (userInvites[1].organisation.length === 0) {
                const institutionParams = {
                    query: {},
                    sort: { title: "asc" },
                    limit: "~",
                };
                const institution = await apiService.get("/institution", institutionParams);
                getinstitution(institution);

                const expertise = await apiService.get("/expertise", institutionParams);
                getexpertise(expertise);

                const institutionNetwork = await apiService.get("/institutionnetwork", institutionParams);
                getinstitutionNetwork(institutionNetwork);
            }
            if (userInvites[2].usersList && userInvites[2].usersList.length > 0) {
                const _users = userInvites[2].usersList.map((item: any, _i: any) => {
                    return { label: item.username, value: item._id };
                });
                setUsersList(_users);
            }
        }
    };

    //******To Handle Group Visibility******//
    const handleInviteChange = (e: any, name: any) => {
        setGroupVisibility((prevState) => ({
            ...prevState,
            [name]: e == null ? [] : e,
        }));
    };

    const radiohandler = () => {
        setFormState((prevState) => ({
            ...prevState,
            visibility: !prevState.visibility,
        }));
    };

    const getID = (id: any) => {
        const imageIds: any[] = [];
        const docIds: any[] = [];
        if (id.length > 0) {
            id.map((item: any) => {
                if (
                    item.type &&
                    (item.type.includes("pdf") ||
                        item.type.includes("docx") ||
                        item.type.includes("xlsx") ||
                        item.type.includes("xls"))
                ) {
                    docIds.push(item.serverID);
                } else {
                    imageIds.push(item.serverID);
                }
            });
        }
        setFormState((prevState) => ({ ...prevState, images: imageIds }));
        setFormState((prevState) => ({ ...prevState, document: docIds }));
    };

    const getSource = (imgSrcArr: any) => {
        setFormState((prevState) => ({ ...prevState, images_src: imgSrcArr }));
    };

    const getDocSource = (docSrcArr: any) => {
        setFormState((prevState) => ({ ...prevState, doc_src: docSrcArr }));
    };

    const onHandleChange = (e: any) => {
        const { name, value } = e.target;
        setFormState((prevState) => ({
            ...prevState,
            [name]: value,
        }));
    };

    return (
        <Container className="formCard" fluid>
            <Card>
                <ValidationForm
                    onSubmit={onSubmitForm}
                    ref={formRef}
                    onKeyPress={(e) => {
                        e.key === "Enter" && e.preventDefault();
                    }}
                >
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>
                                    {props.routes[0] === "edit"
                                        ? t("vspace.editVirtualSpace")
                                        : t("vspace.addVirtualSpace")}
                                </Card.Title>
                            </Col>
                        </Row>
                        <hr />

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("vspace.title")}</Form.Label>
                                    <Form.Control
                                        minLength={5}
                                        required
                                        type="text"
                                        name="title"
                                        value={formState.title}
                                        onChange={onHandleChange}
                                    />
                                    <Form.Control.Feedback type="invalid">
                                        {formState.title.length === 0
                                            ? t("Pleaseprovideatitle")
                                            : t("minimum5CharsReq")}
                                    </Form.Control.Feedback>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("vspace.Body")}</Form.Label>
                                    <EditorComponent initContent={formState.description} onChange={(evt) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col lg={12}>
                                <Form.Group>
                                    <Form.Label>{t("vspace.Image")}</Form.Label>
                                    <ReactDropZone
                                        datas={dropZoneCollection}
                                        srcText={srcCollection}
                                        getImgID={(id) => getID(id)}
                                        getImageSource={(imgSrcArr) => getSource(imgSrcArr)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col lg={12}>
                                <Form.Group>
                                    <Form.Label>{t("vspace.Documents")}</Form.Label>
                                    <ReactDropZone
                                        type="application"
                                        datas={docCollection}
                                        srcText={docSrcCollection}
                                        getImgID={(id) => getID(id)}
                                        getImageSource={(docSrcArr) => getDocSource(docSrcArr)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md lg={3} sm={12}>
                                <Form.Group>
                                    <Form.Label className="d-block">{t("vspace.StartDate")}</Form.Label>
                                    <RKIDatePicker
                                        selected={formState.startDate}
                                        onChange={(date) => onChangeDate(date, "startDate")}
                                        dateFormat="MMMM d, yyyy"
                                        placeholderText={t("vspace.Selectadate")}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md lg={2} sm={12} className="col-md">
                                <Form.Check
                                    type="checkbox"
                                    checked={formState.checked}
                                    onChange={handleEndDateCheckBox}
                                    label={t("vspace.ShowEndDate")}
                                />
                            </Col>
                            {formState.checked && (
                                <Col md lg={3} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="d-block">{t("vspace.EndDate")}</Form.Label>
                                        <RKIDatePicker
                                            selected={formState.endDate}
                                            minDate={formState.startDate}
                                            onChange={(date) => onChangeDate(date, "endDate")}
                                            dateFormat="MMMM d, yyyy"
                                            placeholderText={t("vspace.Selectadate")}
                                        />
                                    </Form.Group>
                                </Col>
                            )}
                        </Row>
                        <GroupVisibility
                            {...groupVisibility}
                            {...formState}
                            allOption={{ label: "All users", value: "*" }}
                            multiUserOptions={usersList}
                            multiRegionOptions={groupRegion}
                            multiOrganisationOptions={groupOrganisation}
                            multiExpertsOptions={expertiseList}
                            multiNetworkOptions={groupNetwork}
                            onChange={handleInviteChange}
                            handleVisibility={radiohandler}
                            onHandleChange={onHandleChange}
                            nonMember={nonMemberHandler}
                        />
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary" ref={buttonRef}>
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("reset")}
                                </Button>
                                <Link href="/vspace" as="/vspace" >
                                    <Button variant="secondary">{t("Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationForm>
            </Card>
        </Container>
    );
};

export default VSpaceForm;
