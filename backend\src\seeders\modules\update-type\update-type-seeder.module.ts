//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { UpdateTypeSeederService } from './update-type-seeder.services';
// SCHEMAS
import { UpdateTypeSchema } from 'src/schemas/update_type.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'UpdateType', schema: UpdateTypeSchema }
      ]
    )
  ],
  providers: [UpdateTypeSeederService],
  exports: [UpdateTypeSeederService],
})

export class UpdateTypeSeederModule { }
