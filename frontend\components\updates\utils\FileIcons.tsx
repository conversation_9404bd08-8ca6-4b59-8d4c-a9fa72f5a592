
//Import Library
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFilePdf, faFile, faFileExcel } from "@fortawesome/free-solid-svg-icons";

interface FileIconsProps {
  _id: string;
  extension: string;
}

const FileIcons = (props: FileIconsProps) => {

  const updateTypeByIcon: { [key: string]: { icon: any; color: string } } = {
    'pdf': { icon: faFilePdf, color: '#FF0000' },
    'docx': { icon: faFile, color: '#87CEFA' },
    'doc': { icon: faFile, color: '#87CEFA' },
    'xlsx': { icon: faFileExcel, color: '#1B4D3E' },
    'xls': { icon: faFileExcel, color: '#1B4D3E' },
    'csv': { icon: faFileExcel, color: '#1B4D3E' }
  }
  const rep = props.extension.replace(".", " ")
  const item = updateTypeByIcon[rep.trim()];

  return (
    <a href={`${process.env.API_SERVER}/files/download/${props._id}`} target="_blank">
      <FontAwesomeIcon icon={item.icon} color={item.color} />
    </a>
  );
}
export default FileIcons;