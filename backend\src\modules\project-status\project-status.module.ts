//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ProjectStatusController } from './project-status.controller';
import { ProjectStatusService } from './project-status.service';
// SCHEMAS
import { ProjectStatusSchema } from '../../schemas/project_status.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'ProjectStatus', schema: ProjectStatusSchema }
    ])
  ],
  controllers: [ProjectStatusController],
  providers: [ProjectStatusService],
})

export class ProjectStatusModule { }