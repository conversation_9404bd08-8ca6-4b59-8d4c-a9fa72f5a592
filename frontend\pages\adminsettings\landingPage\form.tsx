//Import Library
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col, DropdownButton, Dropdown } from "react-bootstrap";
import Router from "next/router";
import { SelectGroup } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import apiService from "../../../services/apiService";
import ReactDropZone from "../../../components/common/ReactDropZone";
import { useTranslation } from 'next-i18next';
import { EditorComponent } from "../../../shared/quill-editor/quill-editor.component";

interface LandingPageFormProps {
    [key: string]: any;
}

const LandingPageForm = (props: LandingPageFormProps) => {
    const { t, i18n } = useTranslation("common");

    const editform = props.routes && props.routes[0] === "edit_landing" && props.routes[1];
    const _initialLanding = {
        title: "",
        description: "",
        pageCategory: "",
        isEnabled: true,
        images: [],
        images_src: [],
        language: i18n.language,
    };
    const [initialVal, setinitialVal] = useState<any>(_initialLanding);
    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);
    const [srcCollection, setSrcCollection] = useState<any[]>([]);
    const [lang, setLang] = useState<any[]>([]);
    const [pageCategory, setPageCategory] = useState<any[]>([]);

    const resetHandler = () => {
        setinitialVal(_initialLanding);
        setDropZoneCollection([]);
        setSrcCollection([]);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const onChangeLocale = (item: any): void => {
        setinitialVal((prevState) => ({
            ...prevState,
            ["language"]: item.abbr,
        }));
    };

    const langParams = {
        query: {},
        sort: { title: "asc" },
        limit: "~",
        select: "-_id -created_at -updated_at",
    };

    const getLang = async () => {
        const response = await apiService.get("/language", langParams);
        if (response) {
            setLang(response.data);
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setinitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleDescription = (value: string) => {
        setinitialVal((prevState) => ({
            ...prevState,
            description: value,
        }));
    };

    const radiohandler = () => {
        setinitialVal((prevState) => ({
            ...prevState,
            isEnabled: !prevState.isEnabled,
        }));
    };

    const getID = (id: any[]) => {
        const _id = id.map((item) => item.serverID);
        setinitialVal((prevState) => ({ ...prevState, images: _id }));
    };

    const getSource = (imgSrcArr: any[]) => {
        setinitialVal((prevState) => ({ ...prevState, images_src: imgSrcArr }));
    };

    const handleSubmit = async (event: any, values?: any) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
            description: initialVal.description,
            isEnabled: initialVal.isEnabled,
            images: initialVal.images,
            images_src: initialVal.images_src,
            language: initialVal.language,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.landing.form.EditableContentisupdatedsuccessfully";
            response = await apiService.patch(`/landingPage/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.landing.form.EditableContentisaddedsuccessfully";
            response = await apiService.post("/landingPage", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/landing");
        } else {
            toast.error(response);
        }
    };

    const getPageCategory = async () => {
        const response = await apiService.get("/pagecategory");
        const select = { _id: "", title: "Select" };
        if (response && Array.isArray(response.data)) {
            const data = response.data.filter((item) => item.title === "AboutUs");
            data.unshift(select);
            setPageCategory(data);
        }
    };

    const getLandingPageData = async () => {
        const response = await apiService.get(`/landingPage/${props.routes[1]}`);
        if (response) {
            const { pageCategory } = response;
            response.pageCategory = pageCategory && pageCategory._id ? pageCategory._id : "";
            setDropZoneCollection(response.images ? response.images : []);
            setSrcCollection(response.images_src ? response.images_src : []);
            setinitialVal((prevState) => ({ ...prevState, ...response }));
        }
    };
    useEffect(() => {
        getLang();
        if (editform) {
            getLandingPageData();
        }
        getPageCategory();
    }, []);

    const formRef = useRef(null);

    return (
        <Container className="formCard" fluid>
            <Card>
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>{t("adminsetting.landing.form.EditableContent")}</Card.Title>
                            </Col>
                        </Row>
                        <hr />
                        <Row className="mb-3">
                            <Col md lg={12} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.landing.form.Title")}</Form.Label>
                                    <SelectGroup
                                        name="title"
                                        id="title"
                                        value={initialVal.title}
                                        validator={(value) => value.trim() !== ""}
                                        required
                                        errorMessage={{
                                            validator: t("adminsetting.landing.form.PleaseAddtheTitle"),
                                        }}
                                        onChange={handleChange}
                                    >
                                        <option key="" value="">Select</option>
                                        <option key="Header" value="Header">Header</option>
                                        <option key="About Us" value="About Us">About Us</option>
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={5}>
                                <Form.Group>
                                    <Form.Label className="pe-3">
                                        {t("adminsetting.landing.form.chooseLanguage")}
                                    </Form.Label>
                                    <DropdownButton
                                        title={initialVal.language.toUpperCase()}
                                        variant="outline-secondary"
                                        id="basic-dropdown"
                                        className="d-inline"
                                    >
                                        {lang &&
                                            lang.map((item, i) => (
                                                <div key={i}>
                                                    <Dropdown.Item
                                                        active={item.abbr === initialVal.language}
                                                        eventKey={item._id}
                                                        onClick={() => onChangeLocale(item)}
                                                    >
                                                        {item.abbr.toUpperCase()}-{item.title.toUpperCase()}
                                                    </Dropdown.Item>
                                                </div>
                                            ))}
                                    </DropdownButton>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.landing.form.Description")}</Form.Label>
                                    <EditorComponent initContent={initialVal.description} onChange={(evt) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("adminsetting.landing.form.Images")}</Form.Label>
                                    <ReactDropZone
                                        datas={dropZoneCollection}
                                        srcText={srcCollection}
                                        getImgID={(id) => getID(id)}
                                        getImageSource={(imgSrcArr) => getSource(imgSrcArr)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Check
                                        checked={initialVal.isEnabled}
                                        name="isEnabled"
                                        onClick={radiohandler}
                                        label={t("adminsetting.landing.form.Published")}
                                        type="checkbox"
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary">
                                    {t("adminsetting.landing.form.Submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("adminsetting.landing.form.Reset")}
                                </Button>
                                <Link
                                    href="/adminsettings/[...routes]"
                                    as={`/adminsettings/landing`}
                                    >
                                    <Button variant="secondary">{t("adminsetting.landing.form.Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
        </Container>
    );
};

export default LandingPageForm;
