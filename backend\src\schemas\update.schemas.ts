//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const UpdateSchema = new mongoose.Schema({
  title: { type: String, required: true },
  type: String,
  description: String,
  link: [],
  location: {},
  document: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Files', autopopulate: true }], // see https://medium.com/@alvenw/how-to-store-images-to-mongodb-with-node-js-fb3905c37e6d
  doc_src: [{ type: String}],
  images: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true }],
  images_src: [{ type: String}],
  media: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Files', autopopulate: true }],
  field_report: { type: Boolean },
  use_in_media_gallery: { type: Boolean },
  technical_guidance: { type: Boolean },
  show_as_announcement: { type: Boolean },
  reply: Array,
  contact_details: {}, //String,
  category: String, //{ type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  status: { type: mongoose.Schema.Types.ObjectId, ref: 'OperationStatus', autopopulate: true },
  start_date: Date,
  end_date: Date,
  country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
  region: { type: mongoose.Schema.Types.ObjectId, ref: 'WorldRegion', autopopulate: true },
  parent_operation: { type: mongoose.Schema.Types.ObjectId, ref: 'Operation' },
  parent_event: { type: mongoose.Schema.Types.ObjectId, ref: 'Event' },
  parent_project: { type: mongoose.Schema.Types.ObjectId, ref: 'Project' },
  parent_vspace: { type: mongoose.Schema.Types.ObjectId, ref: 'Vspace' },
  parent_country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country' },
  parent_hazard: { type: mongoose.Schema.Types.ObjectId, ref: 'Hazard' },
  parent_institution: { type: mongoose.Schema.Types.ObjectId, ref: 'Institution' },
  update_type: { type: mongoose.Schema.Types.ObjectId, ref: 'UpdateType' },
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

UpdateSchema.plugin(mongoosePaginate);
