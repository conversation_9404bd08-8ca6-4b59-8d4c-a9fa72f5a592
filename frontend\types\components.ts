// Component-specific types and interfaces
import { ReactNode } from 'react';
import { User, Country, Region, Institution, MediaFile, Language } from './index';

// Form component types
export interface FormProps {
  onSubmit: (event: React.FormEvent, values: any) => void;
  initialValues?: any;
  validationRules?: ValidationRules;
  loading?: boolean;
}

export interface ValidationRules {
  [fieldName: string]: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    validator?: (value: any) => boolean | string;
  };
}

export interface ValidationFormProps {
  onSubmit: (event: React.FormEvent, values: any) => void;
  children: ReactNode;
  className?: string;
}

export interface ValidationInputProps {
  name: string;
  type?: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  validator?: (value: any) => boolean | string;
  errorMessage?: string;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

// Table component types
export interface DataTableProps {
  data: any[];
  columns: TableColumnDefinition[];
  loading?: boolean;
  pagination?: boolean;
  paginationServer?: boolean;
  totalRows?: number;
  onPageChange?: (page: number) => void;
  onPerRowsChange?: (newPerPage: number, page: number) => void;
  onSort?: (column: any, sortDirection: 'asc' | 'desc') => void;
  onRowClicked?: (row: any, event: React.MouseEvent) => void;
  selectableRows?: boolean;
  onSelectedRowsChange?: (selectedRows: any) => void;
}

export interface TableColumnDefinition {
  name: string;
  selector?: (row: any) => any;
  cell?: (row: any) => ReactNode;
  sortable?: boolean;
  width?: string;
  wrap?: boolean;
  center?: boolean;
  right?: boolean;
}

// Filter component types
export interface FilterProps {
  onFilterChange: (filters: FilterState) => void;
  initialFilters?: FilterState;
  countries?: Country[];
  regions?: Region[];
  institutions?: Institution[];
}

export interface FilterState {
  search?: string;
  country?: string;
  region?: string;
  institution?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  [key: string]: any;
}

// Map component types
export interface MapProps {
  markers?: MapMarker[];
  center?: MapCoordinates;
  zoom?: number;
  onMarkerClick?: (marker: MapMarker) => void;
  onMapClick?: (coordinates: MapCoordinates) => void;
  height?: string;
  width?: string;
}

export interface MapMarker {
  id: string;
  position: MapCoordinates;
  title?: string;
  description?: string;
  icon?: string;
  data?: any;
}

export interface MapCoordinates {
  lat: number;
  lng: number;
}

// Calendar component types
export interface CalendarProps {
  events: CalendarEvent[];
  onEventClick?: (event: CalendarEvent) => void;
  onDateClick?: (date: Date) => void;
  view?: 'month' | 'week' | 'day';
  date?: Date;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end?: Date;
  allDay?: boolean;
  resource?: any;
}

// Image gallery component types
export interface ImageGalleryProps {
  images: GalleryImage[];
  showThumbnails?: boolean;
  showPlayButton?: boolean;
  showFullscreenButton?: boolean;
  autoPlay?: boolean;
  slideInterval?: number;
  onImageClick?: (index: number) => void;
}

export interface GalleryImage {
  original: string;
  thumbnail?: string;
  description?: string;
  alt?: string;
}

// File upload component types
export interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  acceptedFileTypes?: string[];
  maxFileSize?: number;
  multiple?: boolean;
  disabled?: boolean;
}

export interface DropZoneProps {
  onDrop: (files: File[]) => void;
  accept?: string[];
  maxSize?: number;
  multiple?: boolean;
  disabled?: boolean;
  children?: ReactNode;
}

// Rich text editor component types
export interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  modules?: any;
  formats?: string[];
}

// Pagination component types
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
}

// Modal component types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  backdrop?: boolean | 'static';
}

// Accordion component types
export interface AccordionProps {
  items: AccordionItem[];
  defaultActiveKey?: string;
  onSelect?: (eventKey: string) => void;
}

export interface AccordionItem {
  eventKey: string;
  header: ReactNode;
  body: ReactNode;
  disabled?: boolean;
}

// Select component types
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

export interface SelectProps {
  options: SelectOption[];
  value: string | number | string[] | number[];
  onChange: (value: any) => void;
  placeholder?: string;
  isMulti?: boolean;
  isSearchable?: boolean;
  isDisabled?: boolean;
  isClearable?: boolean;
  loading?: boolean;
}

// Language selector types
export interface LanguageSelectorProps {
  languages: Language[];
  currentLanguage: string;
  onLanguageChange: (language: string) => void;
}

// User management types
export interface UserFormData {
  username: string;
  email: string;
  password?: string;
  confirmPassword?: string;
  role: string;
  institution?: string;
  country?: string;
  region?: string[];
  enabled: boolean;
}

export interface UserTableRow extends User {
  actions?: ReactNode;
}

// Institution management types
export interface InstitutionFormData {
  title: string;
  description?: string;
  type?: string;
  country?: string;
  region?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  focal_points?: string[];
  images?: File[];
}

// Event management types
export interface EventFormData {
  title: string;
  description: string;
  start_date: string;
  end_date?: string;
  status?: string;
  hazard?: string;
  country?: string;
  region?: string;
  images?: File[];
  documents?: File[];
}

// VSpace management types
export interface VSpaceFormData {
  title: string;
  description: string;
  start_date: string;
  end_date?: string;
  visibility: boolean;
  members: string[];
  nonMembers: string[];
  images?: File[];
  documents?: File[];
}

// Permission component types
export interface PermissionWrapperProps {
  children: ReactNode;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  fallback?: ReactNode;
}

// Navigation types
export interface NavigationItem {
  key: string;
  label: string;
  href?: string;
  icon?: string;
  children?: NavigationItem[];
  roles?: string[];
  permissions?: string[];
}

// Search types
export interface SearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  initialValue?: string;
  debounceMs?: number;
}

export interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: string;
  url?: string;
  highlight?: string;
}

// Notification types
export interface NotificationProps {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  onClose?: () => void;
}

// Loading component types
export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  overlay?: boolean;
}

// Error boundary types
export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}
