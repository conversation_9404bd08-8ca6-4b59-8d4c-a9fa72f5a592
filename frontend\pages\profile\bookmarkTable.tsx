//Import Library
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import _ from "lodash";
import Link from "next/link";
import { Modal, Button } from "react-bootstrap";

//Import services/components
import BookmarkTableFilter from "./bookmarkTableFilter";
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';


const BookmarkTable = (props) => {
    const GROUP = [
        {
            value: "institution",
            label: "Organisations",
        },
        {
            value: "operation",
            label: "Operations",
        },
        {
            value: "project",
            label: "Projects",
        },
        {
            value: "event",
            label: "Events",
        },
        {
            value: "vspace",
            label: "Virtual Spaces",
        },
    ];
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [isModalShow, setModal] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [filterText, setFilterText] = useState("");
    const [resetPaginationToggle, setResetPaginationToggle] = useState(false);
    const [groupType, setGroupType] = useState(GROUP);
    const [selectedGroup, setSelectedGroup] = useState({});
    const { t } = useTranslation('common');

    const userAction = async (group) => {
        if (group && group._id) {
            setSelectedGroup(group._id);
        }
        setModal(true);
    };

    const flagParams: any = {
        sort: { created_at: "asc" },
        populate: { path: "entity_id", select: "title" },
        lean: true,
        limit: perPage,
        page: 1,
        query: {
            user: props.user && props.user._id ? props.user._id : "",
        },
    };

    const columns = [
        {
            name: t("Title"),
            selector: "",
            cell: (d) =>
                d.entity_id && d.entity_id.title ? (
                    <Link href={`/${d.entity_type}/[...routes]`} as={`/${d.entity_type}/show/${d.entity_id._id}`}>
                        {d.entity_id.title}
                    </Link>
                ) : (
                    ""
                ),
        },
        {
            name: t("Group"),
            selector: "group",
            cell: (d) => {
                return d.onModel && d.onModel === "Institution" ? "Organisation" : d.onModel;
            },
        },
        {
            name: t("Remove"),
            selector: "",
            cell: (d) => (
                <div onClick={() => userAction(d)} style={{ cursor: "pointer" }}>
                    <i className="icon fas fa-trash-alt" />
                </div>
            ),
        },
    ];

    const getUserData = async () => {
        setLoading(true);
        const response = await apiService.get("/flag", flagParams);
        if (response && response.data) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        flagParams.limit = perPage;
        flagParams.page = page;
        if (filterText !== "") {
            flagParams.query = { title: filterText };
        }
        const groupType1 = _.map(groupType, "value");
        if (groupType1 && groupType1.length > 0) {
            flagParams.query = {
                ...flagParams.query,
                entity_type: groupType1,
            };
        }
        getUserData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        flagParams.limit = newPerPage;
        flagParams.page = page;
        setLoading(true);
        const groupType1 = _.map(groupType, "value");
        if (groupType1 && groupType1.length > 0) {
            flagParams.query = {
                ...flagParams.query,
                entity_type: groupType1,
            };
        }
        const response = await apiService.get("/flag", flagParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    useEffect(() => {
        flagParams.page = 1;
        getUserData();
    }, []);

    const subHeaderComponentMemo = React.useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText("");
            }
        };

        const handleGroupHandler = (selectedOptions) => {
            setGroupType(selectedOptions);  // Update groupType with the selected values

            const groupArray = _.map(selectedOptions, "value");

            // Check if no groups are selected
            if (groupArray.length === 0) {
                // Clear data if no options are selected
                setDataToTable([]);  // Clear the table data
            } else {
                // Proceed with fetching data if there are selected groups
                flagParams.query = {
                    ...flagParams.query,
                    entity_type: groupArray,
                };
                getUserData(); // Fetch the data based on the selected groups
            }
        };

        const sendQuery = (q) => {
            if (q) {
                flagParams.populate.match = { title: { $regex: q } };
                getUserData();
            }
        };

        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            setFilterText(e.target.value);
            handleSearchTitle(e.target.value);
        };

        return (
            <BookmarkTableFilter
                onFilter={handleChange}
                onClear={handleClear}
                filterText={filterText}
                handleGroupHandler={handleGroupHandler}
                groupType={groupType}
                options={GROUP}
            />
        );
    }, [filterText, groupType, resetPaginationToggle]);

    const modalHide = () => setModal(false);

    const modalConfirm = async () => {
        setModal(false);

        await apiService.remove(`/flag/${selectedGroup}`);
        //set Filter Text
        if (groupType && Array.isArray(groupType)) {
            const groupArray = _.map(groupType, "value");
            if (groupArray && groupArray.length > 0) {
                flagParams.query = {
                    ...flagParams.query,
                    entity_type: groupArray,
                };
            }
        }
        getUserData();
    };

    return (
        <div className="my-bookmark-table">
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("Removebookmark")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("Areyousurewanttoremovefromyourbookmark")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("bookmarkDeleteYes")}
                    </Button>
                </Modal.Footer>
            </Modal>
            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                subheader
                pagServer={true}
                resetPaginationToggle={resetPaginationToggle}
                subHeaderComponent={subHeaderComponentMemo}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default connect((state) => state)(BookmarkTable);
