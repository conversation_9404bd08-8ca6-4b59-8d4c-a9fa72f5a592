//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import DeploymentstatusTable from "./deploymentstatusTable";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import PageHeading from "../../../components/common/PageHeading";
import { canAddDeploymentStatus } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const DeploymentstatusIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowDeploymentstatusIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title={t("adminsetting.DeploymentStatus.Forms.DeploymentStatus")} />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_deploymentstatus"
                >
                <Button variant="secondary" size="sm">
                {t("adminsetting.DeploymentStatus.Forms.AddDeploymentStatus")}
              </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              <DeploymentstatusTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }
  const ShowAddDeploymentStatus = canAddDeploymentStatus(() => <ShowDeploymentstatusIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.deployment_status?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
      <ShowAddDeploymentStatus />
  )
}

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default DeploymentstatusIndex;