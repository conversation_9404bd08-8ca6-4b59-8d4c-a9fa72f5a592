//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import ProjectstatusTable from "./projectstatusTable";
import { useTranslation } from 'next-i18next';
import { canAddProjectStatus } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const ProjectstatusIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowProjectstatusIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title={t("adminsetting.ProjectStatus.ProjectStatus")} />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_projectstatus"
                >
                <Button variant="secondary" size="sm">
                  {t("adminsetting.ProjectStatus.AddProjectStatus")}
              </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              <ProjectstatusTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  const ShowAddProjectStatus = canAddProjectStatus(() => <ShowProjectstatusIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.project_status?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddProjectStatus />
  )  
}
export default ProjectstatusIndex;