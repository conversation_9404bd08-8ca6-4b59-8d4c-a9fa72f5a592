//Import Library
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';
import RKITable from "../../../components/common/RKITable";


function InstitutionTable(_props: any) {
  const [tabledata, setDataToTable] = useState<any[]>([]);
  const [, setLoading] = useState<boolean>(false);
  const [totalRows, setTotalRows] = useState<number>(0);
  const [perPage, setPerPage] = useState<number>(10);
  const [isModalShow, setModal] = useState<boolean>(false);
  const [newStatus, setNewStatus] = useState<string>("");
  const [selectUserDetails, setSelectUserDetails] = useState<any>({});
  const { t } = useTranslation('common');



  const instParams = {
    sort: { created_at: "desc" },
    limit: perPage,
    page: 1,
    query: { status: "Request Pending" },
  };

  const columns = [
    {
      name: t("Title"),
      selector: "title",
      cell: (d: any) => d.title,
    },
    {
      name: t("Email"),
      selector: "email",
      cell: (d: any) => d.email,
    },
    {
      name: ("ContactName"),
      selector: "contact_name",
      cell: (d: any) => d.contact_name,
    },
    {
      name: t("Action"),
      selector: "",
      cell: (d: any) => (
        <div>
          <Button
            variant="primary"
            size="sm"
            onClick={() => instAction(d, "approve")}
          >
            {t("instu.Approves")}
          </Button>
          &nbsp;
          <Button
            variant="secondary"
            size="sm"
            onClick={() => instAction(d, "reject")}
          >
            {t("instu.Reject")}
          </Button>
        </div>
      ),
    },
  ];

  const getInstData = async () => {
    setLoading(true);
    const response = await apiService.get("/institution", instParams);
    if (response && response.data) {
      setDataToTable(response.data);
      setTotalRows(response.totalCount);
      setLoading(false);
    }
  };
  const handlePageChange = (page: number) => {
    instParams.limit = perPage;
    instParams.page = page;
    getInstData();
  };

  const handlePerRowsChange = async (newPerPage: number, page: number) => {
    instParams.limit = newPerPage;
    instParams.page = page;
    setLoading(true);
    const response = await apiService.get("/institution", instParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }
  };

  useEffect(() => {
    getInstData();
  }, []);

  const instAction = async (d: any, status: string) => {
    setModal(true);
    setNewStatus(status);
    if (d && d._id) {
      const setStatus = status === "approve" ? "Approved" : "Rejected";
      setSelectUserDetails({ ...d, status: setStatus });
    }
  };

  const modalConfirm = async () => {

    if( selectUserDetails['status'] === "Rejected"){
      await apiService.remove(`/institution/${selectUserDetails["_id"]}`);
      getInstData();
      toast.error(t("instu.Rejected"));
      setSelectUserDetails({});
      setModal(false);

    }else {
      const updatedData = await apiService.patch(
        `/institution/${selectUserDetails["_id"]}`,
        selectUserDetails
      );
      if (updatedData && updatedData.status === 403) {
        toast.error(
          updatedData.response && updatedData.response.message
            ? updatedData.response.message
            : t("adminsetting.FocalPointsApprovalTable.Somethingwentswrong")
        );
        return;
      } else {
        getInstData();
        toast.success(t("instu.Approve"));
        setSelectUserDetails({});
        setModal(false);
      }
    }
    getInstData();
    setSelectUserDetails({});
    setModal(false);
  };

  const modalHide = () => setModal(false);

  return (
    <div>
      <Modal show={isModalShow} onHide={modalHide}>
        <Modal.Header closeButton>
          <Modal.Title>
            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t("Organisation")}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
        {t("adminsetting.FocalPointsApprovalTable.Areyousurewantto")}  {newStatus} {t("thisOrganisation?")}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={modalHide}>
          {t("cancel")}
          </Button>
          <Button variant="primary" onClick={modalConfirm}>
          {t("yes")}
          </Button>
        </Modal.Footer>
      </Modal>

      <RKITable
        columns={columns}
        data={tabledata}
        totalRows={totalRows}
        pagServer={true}
        handlePerRowsChange={handlePerRowsChange}
        handlePageChange={handlePageChange}
      />
    </div>
  );
}

export default InstitutionTable;
