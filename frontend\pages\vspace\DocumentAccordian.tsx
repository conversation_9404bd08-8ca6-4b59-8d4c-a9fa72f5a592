//Import Library
import { faPlus, faMinus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Card, Accordion } from "react-bootstrap";
import { useState } from "react";

//Import services/components
import DocumentTable from "../../components/common/DocumentTable";
import { useTranslation } from 'next-i18next';

const DocumentAccordian = (props: any) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("vspace.Documents")}</div>
                    <div className="cardArrow">
                    {section ? <FontAwesomeIcon icon={faMinus} color="#fff" /> :
                        <FontAwesomeIcon icon={faPlus} color="#fff" />}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <DocumentTable loading={props.vSpaceLoading} sortProps={props.vSpaceSort} docs={props.documentAccoirdianProps.vSpaceDocs || []} docsDescription={props.calenderEvents.length > 0 && props.calenderEvents.map(item => item.doc_src).flat(1)} />
                    <h6 className="mt-3">{t("vspace.DocumentsfromUpdates")}</h6>
                    <DocumentTable loading={props.documentAccoirdianProps.updateLoading} sortProps={props.documentAccoirdianProps.updateSort} docs={props.document || []} docsDescription={props.documentAccoirdianProps.docSrc} />
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
};

export default DocumentAccordian;