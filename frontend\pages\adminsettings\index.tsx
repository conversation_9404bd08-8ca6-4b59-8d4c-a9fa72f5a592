//Import Library
import { <PERSON>, Row, Col, Container } from "react-bootstrap";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import PageHeading from "../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddAreaOfWork } from "../adminsettings/permissions";
import { canAddCountry } from "../adminsettings/permissions";
import { canAddDeploymentStatus } from "../adminsettings/permissions";
import { canAddEventStatus } from "../adminsettings/permissions";
import { canAddExpertise } from "../adminsettings/permissions";
import { canAddFocalPointApproval } from "../adminsettings/permissions";
import {canAddVspaceApproval} from "../adminsettings/permissions"

import { canAddHazards } from "../adminsettings/permissions";
import { canAddHazardTypes } from "../adminsettings/permissions";
import { canAddOrganisationApproval } from "../adminsettings/permissions";
import { canAddOrganisationNetworks } from "../adminsettings/permissions";
import { canAddOrganisationTypes } from "../adminsettings/permissions";
import { canAddOperationStatus } from "../adminsettings/permissions";
import { canAddProjectStatus } from "../adminsettings/permissions";
import { canAddRegions } from "../adminsettings/permissions";
import { canAddRiskLevels } from "../adminsettings/permissions";
import { canAddSyndromes } from "../adminsettings/permissions";
import { canAddUpdateTypes } from "../adminsettings/permissions";
import { canAddUsers } from "../adminsettings/permissions";
import { canAddWorldRegion } from "../adminsettings/permissions";
import { canAddLandingPage } from "../adminsettings/permissions";
import {canAddContent} from "../adminsettings/permissions";

const admin = () => {
  const { t } = useTranslation('common');

  const AreaOfWork = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/area_of_work"
            >
            <Card className="infoCard ">
              <Card.Body>
                <Card.Text>
                  {t("adminsetting.adminindex.Areaofwork")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const Countries = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/country"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  {t("adminsetting.adminindex.Countries")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const DeploymentStatus = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className=" infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/deploymentstatus"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  {t("adminsetting.adminindex.DeploymentStatus")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const EventStatus = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/eventstatus"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  {t("adminsetting.adminindex.EventStatus")}
                      <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const Expertise = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/expertise"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.Expertise")}
                      <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const FocalPointApproval = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/focal_point"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.FocalPointApproval")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const VspaceApproval = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/Vspace_point"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.VspaceApproval")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const Hazards = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/hazard"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.Hazards")}
                      <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const HazardTypes = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/hazardTypes"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.HazardTypes")}
                      <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const OrganisationApproval = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/institution_approval"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.OrganisationApproval")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const OrganisationNetworks = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/institution_network"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.OrganisationNetworks")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const OrganisationTypes = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/institution_type"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.OrganisationTypes")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const OperationStatus = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/operationstatus"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.OperationStatus")}
                <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const ProjectStatus = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/projectstatus"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.ProjectStatus")}
                <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const Regions = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/region"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.Regions")}
                <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const RiskLevels = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/risklevel"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.RiskLevels")}
                <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const Syndromes = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/syndrome"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.Syndromes")}
                <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const UpdateTypes = () => {
    return (
      <Col md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/update_type"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.UpdatesTypes")}
                <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const Users = () => {
    return (
      <Col  md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/users"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.Users")}
                      <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const WorldRegions = () => {
    return (
      <Col  md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/worldregion"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.WorldRegions")}
                      <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const LandingPage = () => {
    return (
      <Col  md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/landing"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                  {t("adminsetting.adminindex.EditableContent")}
                  <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }

  const Content = () => {
    return (
      <Col  md={4} sm={12} className ="mb-2 mt-2">
        <div className="infoCard_admin_card text-center cursor-pointer">
          <Link
            href="/adminsettings/[...routes]"
            as="/adminsettings/content"
            >
            <Card className="infoCard">
              <Card.Body>
                <Card.Text>
                  
                    {t("adminsetting.adminindex.Content")}
                      <span className="arrowStyle">
                      <FontAwesomeIcon icon={faArrowRight} />
                    </span>
                  
                </Card.Text>
              </Card.Body>
            </Card>
          </Link>
        </div>
      </Col>
    );
  }


  const CanAddAreaOfWork = canAddAreaOfWork(() => <AreaOfWork />);
  const CanAddCountry = canAddCountry(() => <Countries />);
  const CanAddDeploymentStatus = canAddDeploymentStatus(() => <DeploymentStatus />);
  const CanAddEventStatus = canAddEventStatus(() => <EventStatus />);
  const CanAddExpertise = canAddExpertise(() => <Expertise />);
  const CanAddFocalPointApproval = canAddFocalPointApproval(() => <FocalPointApproval />);
  const CanAddVspaceApproval = canAddVspaceApproval(() => <VspaceApproval />);

  const CanAddHazards = canAddHazards(() => <Hazards />);
  const CanAddHazardTypes = canAddHazardTypes(() => <HazardTypes />);
  const CanAddOrganisationApproval = canAddOrganisationApproval(() => <OrganisationApproval />);
  const CanAddOrganisationNetworks = canAddOrganisationNetworks(() => <OrganisationNetworks />);
  const CanAddOrganisationTypes = canAddOrganisationTypes(() => <OrganisationTypes />);
  const CanAddOperationStatus = canAddOperationStatus(() => <OperationStatus />);
  const CanAddProjectStatus = canAddProjectStatus(() => <ProjectStatus />);
  const CanAddRegions = canAddRegions(() => <Regions />);
  const CanAddRiskLevels = canAddRiskLevels(() => <RiskLevels />);
  const CanAddSyndromes = canAddSyndromes(() => <Syndromes />);
  const CanAddUpdateTypes = canAddUpdateTypes(() => <UpdateTypes />);
  const CanAddUsers = canAddUsers(() => <Users />);
  const CanAddWorldRegion = canAddWorldRegion(() => <WorldRegions />);
  const CanAddLandingPage = canAddLandingPage(() => <LandingPage />);
  const CanAddContent = canAddContent(() => <Content/>)


  return (
    <Container fluid style={{ overflowX: "hidden" }} className="p-0">
      <PageHeading title={t("menu.adminSettings")} />
      <Row>
        <CanAddAreaOfWork />
        <CanAddCountry />
        <CanAddDeploymentStatus />
        <CanAddEventStatus />
        <CanAddExpertise />
        <CanAddFocalPointApproval />
        <CanAddVspaceApproval />
        <CanAddHazards />
        <CanAddHazardTypes />
        <CanAddOrganisationApproval />
        <CanAddOrganisationNetworks />
        <CanAddOrganisationTypes />
        <CanAddOperationStatus />
        <CanAddProjectStatus />
        <CanAddRegions />
        <CanAddRiskLevels />
        <CanAddSyndromes />
        <CanAddUpdateTypes />
        <CanAddUsers />
        <CanAddWorldRegion />
        <CanAddLandingPage />
        <CanAddContent/>
      </Row>
    </Container>
  );
};

export async function getStaticProps({ locale }: { locale: string}) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default admin;
