//Import Library
import React, { useState, useRef } from "react";
import ReactPaginate from 'react-paginate';
import Link from 'next/link';
import Spinner from 'react-bootstrap/Spinner';
import { Col, Container, Row, } from "react-bootstrap";
import HazardSearch from "./HazardSearch";
import _ from "lodash";

//Import services/components
import HazardMultiCheckboxes from "../../components/common/hazardMultiCheckboxes";
import apiService from '../../services/apiService';
import { ALPHABETIC_FILTERS_EN, ALPHABETIC_FILTERS_DE } from "../../data/alphabet";
import PageHeading from "../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Hazard = () => {
  const { t, i18n } = useTranslation('common');
  const currentLang = i18n.language === 'fr' ? 'en' : i18n.language;
  const titleSearch = currentLang ? `title.${currentLang}` : "title.en";
  const alphabets = currentLang === 'en' ? ALPHABETIC_FILTERS_EN : ALPHABETIC_FILTERS_DE
  const [intialarr,] = useState(alphabets);
  const [hazardList, sethazardList]: any = useState([]);
  const [splitHazard, setsplitHazard]: any = useState([]);
  const [FilterHazard, setFilterHazard]: any = useState([]);
  const [totalcount, setTotalCount]: any = useState(0);
  const [currentPage, setCurrentPage]: any = useState(1);
  const [postsPerPage,]: any = useState(50);
  const [activeClass, setActiveClass]: any = useState('All');
  const [loader, setLoader]: any = useState(true);
  const [hazPage, sethazPage]: any = useState([]);
  const [filterText, setFilterText] = useState('');
  const [currLang,] = useState(titleSearch);
  const [search, showSearch] = useState(true);
  const [hazardResponse, setHazardResponse] = useState<any>({});



  const splitHazardies = async (hazardParams) => {
    await setLoader(true);
    const response = await apiService.get('/hazard', hazardParams);
    if (response && response.data) {
      await setLoader(false);
      await setsplitHazard(response.data);
      await setTotalCount(response.totalCount);
    }
  }

  const alphabeticFilter = async (_e, index) => {
    await setActiveClass(intialarr[index]);
    if (intialarr[index] === t("All") || intialarr[index] === t("Alle")) {

      showSearch(true);
      await setFilterHazard([])
      if (Array.isArray(hazPage) && hazPage.length !== 0) {
        const filtRegion = async () => {
          regionParams.query = { "hazard_type": hazPage };
          regionParams.page=1;
          const response = await apiService.get('/hazard', regionParams);
          await sethazardList(response.data);
          await setTotalCount(response.totalCount);
        }
        const regionParams = {
          "query": {},
          "limit": postsPerPage,
          "page": currentPage,
          "sort": {[currLang] : "asc" }
        };

        filtRegion();
      }
    }
    else {
      const FilteringByLetter = [];
      setFilterText("")
      const letter = intialarr[index].toLowerCase();
      showSearch(false);
      splitHazard.map(async (item, _i) => {
        if (item.title[currentLang].toLowerCase().split("")[0] === letter && item.enabled) {
          const z = { title: item.title, _id: item._id, coordinates: item.coordinates };
          FilteringByLetter.push(z);
        }
      })

      if (FilteringByLetter.length >= 1) {
        await setFilterHazard(FilteringByLetter);
        setTotalCount(FilteringByLetter.length)
      } else {
        await setFilterHazard([]);
        await sethazardList([]);
        await setTotalCount([]);
        await setHazardResponse({});
      }
    }
  }

  const paginate = async (selectedItem) => {
    const pageNumber = selectedItem.selected + 1; // react-paginate uses 0-based indexing

    await setCurrentPage(pageNumber);

    if (Array.isArray(hazPage) && hazPage.length !== 0) {
      const filtRegion = async () => {
        regionParams.query = { "hazard_type": hazPage }
        const response = await apiService.get('/hazard', regionParams);
        await sethazardList(response.data);
        await setTotalCount(response.totalCount);
        await setHazardResponse(response);
      }

      const regionParams = {
        "query": {},
        "limit": postsPerPage,
        "page": pageNumber,
        "sort": {[currLang] : "asc"  }
      };

      filtRegion();
    }
    else {
      const nextHazards = async () => {
        const response = await apiService.get('/hazard', operationParamss);
        sethazardList(response.data);
      }

      const operationParamss = {
        "query": {},
        "limit": postsPerPage,
        "page": pageNumber,
        "sort": { [currLang] : "asc" }
      };
      await nextHazards();
    }
  };

  const filterhazard = async (getHazard) => {
    showSearch(true);

    // If no hazards are selected (empty array), show no results
    if (Array.isArray(getHazard) && getHazard.length === 0) {
      await sethazardList([]);
      await setTotalCount(0);
      await setActiveClass("All");
      await setHazardResponse({});
      await setsplitHazard([]);
      setFilterHazard([]);
      sethazPage([]);
      return;
    }

    const operationParams1 = {
      "query": { "hazard_type": getHazard },
      "limit": "~",
      "sort": { [currLang] : "asc" }
    };
    splitHazardies(operationParams1);

    sethazPage(getHazard)
    let filterReghazard = [];
    const regionParams = {
      "query": {},
      "limit": postsPerPage,
      "page": currentPage,
      "sort": { [currLang] : "asc" }
    };

    const filtHazard = async () => {
      regionParams.sort = { [currLang] : "asc" };
      regionParams.query = { "hazard_type": getHazard };
      regionParams.page = 1;
      const response = await apiService.get('/hazard', regionParams);
      filterReghazard = response.data;
      await sethazardList(filterReghazard);
      await setTotalCount(response.totalCount);
      await setActiveClass("All");
      await setHazardResponse(response);
      setFilterHazard([])
    }
    filtHazard();

  }

  //Search Hazard
  const searchParams = {
    "query": {},
    "sort": {[currLang] : "asc" }
  }
  const searchHazard = async () => {
    const response = await apiService.get('/hazard', searchParams);
    sethazardList(response.data);
  }


  const sendQuery = (q,hazPagevalue) => {
    q ? searchParams.query = { [currLang] : q && q[0].toUpperCase() + q.slice(1).toLowerCase() } : searchParams.query = {};
    activeClass !== "ACTIVE" && (searchParams.query = { ...searchParams.query, hazard_type: hazPagevalue });
    searchHazard();
  }

  const handleSearchTitle = useRef(
    _.debounce((q,hazPageinit) => sendQuery(q,hazPageinit), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)
  ).current;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value);
    handleSearchTitle(e.target.value, hazPage)
  }
  //end
  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={12}>
          <PageHeading title={t('menu.hazards')} />
        </Col>
      </Row>
      <div className="hazard-image-block">
        <img className="hazard-image-cover" src="/images/hazard.838eccb4.jpg" alt="Logo" />
        <HazardMultiCheckboxes  currentLang ={currentLang}filterByletter={activeClass} filthaz={filterhazard} />
      </div>
      <div className="alphabetBlock">
        <div className="alphabetContainer">
          {intialarr.map((item, index) => (
            <span key={index} className={`alphabetItems ${activeClass === item ? 'active' : ''}`} onClick={(e) => alphabeticFilter(e, index)}>{item}</span>
          ))}
        </div>
        {search && (<div>
          <Row>
            <Col className="mt-3 mx-3">
              <HazardSearch onFilter={handleChange} filterText={filterText} />
            </Col>
          </Row>
        </div>)
        }
        <div className="alphabetLists">
          {
             loader ? <Spinner animation="border" variant="primary" /> :
             FilterHazard.length !== 0 ? FilterHazard.map((item, index) => (
               <li key={index} className="alphaListItems clearfix">
                 <Link href='/hazard/[...routes]' as={`/hazard/show/${item._id}`}>
                   {item.title && item.title[currentLang] ? item.title[currentLang] : ''}
                 </Link>
               </li>
             )) :
               (hazardList.length >= 1) ? <ul>
                 {hazardList.map((item, index) => (
                   item.enabled ?
                     <li key={index} className="alphaListItems clearfix">
                       <Link href='/hazard/[...routes]' as={`/hazard/show/${item._id}`}>
                         {item.title && item.title[currentLang] ? item.title[currentLang] : ''}
                       </Link>
                     </li> : null
                 ))}
               </ul> : <div className="noresultFound"> {t("Noresultsfound")} </div>
         }
        </div>
        {
          totalcount > postsPerPage && !filterText ?
            <div className="hazards-pagination">
              <ReactPaginate
                pageCount={Math.ceil(hazardResponse.totalCount / hazardResponse.limit)}
                pageRangeDisplayed={5}
                marginPagesDisplayed={2}
                onPageChange={paginate}
                forcePage={hazardResponse.page - 1} // react-paginate uses 0-based indexing
                containerClassName="pagination"
                pageClassName="page-item"
                pageLinkClassName="page-link"
                previousClassName="page-item"
                previousLinkClassName="page-link"
                nextClassName="page-item"
                nextLinkClassName="page-link"
                activeClassName="active"
                disabledClassName="disabled"
                previousLabel="‹"
                nextLabel="›"
              />
            </div>
            : null
        }

      </div>
    </Container>
  );

}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Hazard;