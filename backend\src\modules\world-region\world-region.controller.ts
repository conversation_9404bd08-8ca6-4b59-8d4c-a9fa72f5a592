//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateWorldRegionDto } from './dto/create-world-region.dto';
import { UpdateWorldRegionDto } from './dto/update-world-region.dto';
import { WorldRegionService } from './world-region.service';
import { SessionGuard } from 'src/auth/session-guard';
@Controller('worldregion')
@UseGuards(SessionGuard)
export class WorldRegionController {
  constructor(private readonly _worldRegionService: WorldRegionService) {}

  @Post()
  async create(@Body() createWorldRegionDto: CreateWorldRegionDto) {
    try {
      const _world_region = await this._worldRegionService.create(
        createWorldRegionDto,
      );
      return _world_region;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._worldRegionService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') worldRegionId: string) {
    return this._worldRegionService.get(worldRegionId);
  }

  @Patch(':id')
  async update(
    @Param('id') worldRegionId: string,
    @Body() updateWorldRegionDto: UpdateWorldRegionDto,
  ) {
    try {
      const _world_region = await this._worldRegionService.update(
        worldRegionId,
        updateWorldRegionDto,
      );
      return _world_region;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') worldRegionId: string) {
    const _world_region = this._worldRegionService.delete(worldRegionId);
    return _world_region;
  }
}
