MONGODB=*******************************************************************************************
APP_PORT=3001
API_VERSION=api/v1
DEPLOY=dev
GOOGLE_API_KEY=<<Google API Key>>
RESET_PASSWORD_EXPIRY=24

SEED_USERNAME=
SEED_EMAIL=
SEED_PASSWORD=
SEED_ROLE=

BASE_URL=http://localhost:3000
USER_SECRET_KEY=<<Any 60 characters alphanumeric key>>
SALT_KEY=<<Any 16 characters alphanumeric key>>
CRYPTO_SECRETKEY=<<Any 50 charactes Alphanumeric Key>>

FROM_EMAIL=<<Your from email id>>
SES_USER = <<SMTP User key>>
SES_PASS = <<SMTP Pass Key>>
SES_HOST = <<SMTP host>>
SES_PORT = <<SMTP PORT>>

JWTCONSTANTS_SECRET_KEY=  <<JWT_CONSTANTS_KEY>> 

TWITTER_BEARER_TOKEN= <<TWITTER_BEARER_TOKEN>>