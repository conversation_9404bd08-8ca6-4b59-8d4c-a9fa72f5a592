//Import Library
import { Accordion, Card } from "react-bootstrap";
import DocumentAccordian from "./DocumentAccordian";
import { faPlus, faMinus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";

//Import services/components
import Discussion from "../../components/common/disussion";
import { useTranslation } from 'next-i18next';
import canViewDiscussionUpdate from "./permission";
import VirtualSpaceAccordian from "./VirtualSpaceAccordian";
import MediaGalleryAccordion from "./MediaGalleryAccordion";

interface HazardAccordianSectionProps {
  t: (key: string) => string;
  images: any[];
  imgSrc: string[];
  routeData: any;
  documentAccoirdianProps: {
    loading: boolean;
    Document: any[];
    updateDocument: any[];
    hazardDocSort: (data: { columnSelector: string; sortDirection: string }) => void;
    hazardDocUpdateSort: (data: { columnSelector: string; sortDirection: string }) => void;
    docSrc: string[];
  };
}

const HazardAccordianSection = (props: HazardAccordianSectionProps) => {
  const { t } = useTranslation('common');
  const [section, setSection] = useState(false);
  const DiscussionComponent = () => {
    return (
      <Accordion.Item eventKey="1">
        <Accordion.Header onClick={() => setSection(!section)}>
          <div className="cardTitle">{t("discussions")}</div>
          <div className="cardArrow">
            {section ? (
              <FontAwesomeIcon icon={faMinus} color="#fff" />
            ) : (
              <FontAwesomeIcon icon={faPlus} color="#fff" />
            )}
          </div>
        </Accordion.Header>
        <Accordion.Body>
          <Discussion
            type="hazard"
            id={props?.routeData && props?.routeData?.routes ? props.routeData.routes[1] : null}
          />
        </Accordion.Body>
      </Accordion.Item>
    );
  };

  const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (
    <DiscussionComponent />
  ));

    return (
        <>
          <Accordion className="countryAccordionNew">
            <MediaGalleryAccordion {...props} />
          </Accordion>

          <Accordion className="countryAccordionNew">
            <DocumentAccordian {...props} />
          </Accordion>

          <Accordion className="countryAccordionNew">
            <CanViewDiscussionUpdate />
          </Accordion>

          <Accordion className="countryAccordion">
            <VirtualSpaceAccordian
              loading={props.documentAccoirdianProps.loading}
              Document={props.documentAccoirdianProps.Document}
              updateDocument={props.documentAccoirdianProps.updateDocument}
              hazardDocSort={props.documentAccoirdianProps.hazardDocSort}
              hazardDocUpdateSort={props.documentAccoirdianProps.hazardDocUpdateSort}
              docSrc={props.documentAccoirdianProps.docSrc}
              routeData={props.routeData}
            />
          </Accordion>
        </>
    )
}

export default HazardAccordianSection;