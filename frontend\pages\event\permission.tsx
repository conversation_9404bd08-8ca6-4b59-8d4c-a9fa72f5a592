//Import Library
import React from 'react';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

//Import services/components
import R403 from "../r403";

export const canAddEvent = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddEvent',
});

export const canAddEventForm = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.event && state.permissions.event['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddEventForm',
  FailureComponent: () => <R403/>
});

export const canEditEvent = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.event) {
      if (state.permissions.event['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.event['update:own']) {
          if (props.event && props.event.user && props.event.user._id === state.user._id) {
            return true;
          }  
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditEvent',
});

export const canEditEventForm = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.event) {
      if (state.permissions.event['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.event['update:own']) {
          if (props.event && props.event.user && props.event.user._id === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditEventForm',
  FailureComponent: () => <R403/>
});

export const canViewDiscussionUpdate = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewDiscussionUpdate',
});

export default canAddEvent;