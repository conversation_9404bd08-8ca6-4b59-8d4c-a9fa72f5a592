{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "ts-node --project tsconfig.server.json server.ts", "memfix": "increase-memory-limit", "build": "next build && tsc --project tsconfig.server.json", "export": "next build && next export", "start": "next start", "postinstall": "next telemetry disable", "preinstall": "npx force-resolutions"}, "dependencies": {"@babel/core": "^7.17.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@react-google-maps/api": "^2.20.6", "@redux-devtools/extension": "^3.3.0", "@tinymce/tinymce-react": "^6.1.0", "@types/googlemaps": "^3.43.3", "@types/webpack-env": "^1.16.3", "async": "^3.2.4", "axios": "^1.9.0", "bootstrap": "^5.3.6", "core-js-compat": "^3.25.1", "cross-fetch": "^3.1.6", "decode-uri-component": "^0.2.1", "es6-promise": "^4.2.8", "express": "^4.17.3", "formik": "^2.4.6", "glob-parent": "^6.0.2", "helmet": "^5.0.2", "http-cache-semantics": "^4.1.1", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "json5": "^2.2.3", "jwt-decode": "^3.1.2", "loader-utils": "^2.0.4", "lodash": "^4.17.21", "minimatch": "^3.0.5", "minimist": "^1.2.6", "moment": "^2.30.1", "next": "^15.3.2", "next-fonts": "^1.5.1", "next-i18next": "^15.4.2", "next-redux-saga": "^4.1.2", "next-redux-wrapper": "^8.1.0", "node-fetch": "^3.2.3", "nprogress": "^0.2.0", "react": "^19.1.0", "react-alice-carousel": "^2.5.1", "react-avatar-editor": "13.0.2", "react-big-calendar": "^1.15.0", "react-bootstrap": "^2.10.10", "react-bootstrap-icons": "^1.1.0", "react-bootstrap-range-slider": "^3.0.3", "react-confirm-alert": "^2.7.0", "react-content-loader": "^6.2.0", "react-custom-scrollbars-2": "^4.5.0", "react-data-table-component": "^7.5.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-dropzone": "^12.0.4", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-infinite-scroll-hook": "^4.0.2", "react-multi-select-component": "^4.2.3", "react-overlays": "^5.2.1", "react-paginate": "^8.3.0", "react-redux": "^9.2.0", "react-responsive-carousel": "^3.2.23", "react-select": "^5.2.2", "react-truncate": "^2.4.0", "react-tweet": "^3.2.2", "react-twitter-embed": "^4.0.4", "redux": "^5.0.1", "redux-auth-wrapper": "^3.0.0", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "sass": "^1.49.9", "sass-graph": "^4.0.1", "scss-tokenizer": "^0.4.3", "semver": "^7.5.2", "styled-components": "^6.1.18", "tar": "^7.0.0", "tough-cookie": "^4.1.3", "ua-parser-js": "^0.7.33", "validator": "^13.15.0", "yup": "^1.6.1"}, "devDependencies": {"@types/express": "^5.0.2", "@types/lodash": "^4.17.17", "@types/node": "^17.0.21", "@types/react": "^19.1.5", "@types/react-avatar-editor": "^13.0.4", "@types/react-dom": "^19.1.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "browser": {"child_process": false}, "resolutions": {"react": "^19.1.0", "react-dom": "^19.1.0", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "tar": "^7.0.0"}, "overrides": {"react-avatar-editor": {"react": ">=19.0.0", "react-dom": ">=19.0.0"}, "react-custom-scrollbars-2": {"react": ">=19.0.0", "react-dom": ">=19.0.0"}, "react-multi-select-component": {"react": ">=19.0.0", "react-dom": ">=19.0.0"}, "react-truncate": {"react": ">=19.0.0", "react-dom": ">=19.0.0"}, "react-twitter-embed": {"react": ">=19.0.0", "react-dom": ">=19.0.0"}}}