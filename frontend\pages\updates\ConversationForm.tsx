//Import Library
import React from "react";
import { Form, Container, Row, Col } from "react-bootstrap";
import { TextInput } from "../../components/common/FormValidation";

//Import services/components
import { useTranslation } from 'next-i18next';

//TOTO refactor
interface ConversationFormProps {
  onHandleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  title: string;
}

const ConversationForm = (props: ConversationFormProps): React.ReactElement => {
    const { t } = useTranslation('common');
    const { title, onHandleChange } = props;
    return (
        <Container className="formCard" fluid>
            <Row>
                <Col>
                    <Form.Group>
                        <Form.Label className="required-field">
                            {t("update.Conversation")} {t("update.Title")}
                        </Form.Label>
                        <TextInput
                            name="title"
                            id="title"
                            required
                            value={title}
                            onChange={onHandleChange}
                            validator={(value) => value.trim() != ""}
                            errorMessage={{
                                validator: t("Pleaseprovideconversationatitle"),
                            }}
                        />
                    </Form.Group>
                </Col>
            </Row>
        </Container>
    );
};

export default ConversationForm;
