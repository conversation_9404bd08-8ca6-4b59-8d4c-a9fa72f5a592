//Import Library
import { Card } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import { useTranslation } from 'next-i18next';

interface HazardPastEventProps {
  t: (key: string) => string;
  hazardPastEventData: any[];
}

const HazardPastEvent = (props: HazardPastEventProps) => {
    const hazardPastEventData = props.hazardPastEventData;
    const { t } = useTranslation('common');
    return (
        <>
            <div className="rki-carousel-card">
                <Card className="infoCard">
                    <Card.Header className="text-center">
                    {t("hazardshow.pastevents")}
                    </Card.Header>
                    <Card.Body className="hazardBody">
                    {hazardPastEventData && hazardPastEventData.length > 0 ? (
                        hazardPastEventData.map((item, index) => (
                        <ul className="ulItems">
                            <li key="index" className="liItems">
                            <Link
                                key={item._id}
                                href="/event/[...routes]"
                                as={`/event/show/${item._id}`}
                            >
                                {item && item.title ? `${item.title}` : ""}
                            </Link>
                            <span>
                                {" "}
                                ({item && item.country ? `${item.country.title}` : ""})
                            </span>
                            </li>
                        </ul>
                        ))
                    ) : (
                        <span className="text-center">{t("noRecordFound")}</span>
                    )}
                    </Card.Body>
                </Card>
            </div>
        </>
    );
};

export default HazardPastEvent;