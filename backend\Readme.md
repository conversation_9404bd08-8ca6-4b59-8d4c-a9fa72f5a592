<p align="center">
  <img src="backend/src/assets/img/rki-logo.jpg" alt="RKI" />
</p>

The Robert Koch Institut is a German federal government agency and research institute responsible for disease control and prevention. It is located in Berlin and Wernigerode. As an upper federal agency, it is subordinate to the Federal Ministry of Health.

## BACKEND

### Initial Setup

```
$ git clone <CLONE URL>
$ cd backend
```
Rename .env file to .env.default and change the settings as per your requirement.

### Installation

```bash
$ npm install
```

### Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

### Running the app on docker
```
git clone <CLONE URL>
cd rki
docker-compose build .
docker-compose up -d
```
BACKEND PORT: 3001
FRONTEND PORT: 3002
MONGO PORT: 27017

### Test

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## FRONTEND


## Adding Github Actions to auto deploy to AWS ECS
