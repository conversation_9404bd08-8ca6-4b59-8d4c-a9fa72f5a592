//Import Library
import UpdatesItem from "./UpdatesItem";
import {useEffect, useState} from "react";

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

interface UpdatesListProps {
  updates: any[];
  onRemoveUpdate: (id: string) => void;
  loadMore: () => void;
  loadMoreButton: boolean;
  [key: string]: any;
}

export default function UpdatesList({ updates, onRemoveUpdate, loadMore, loadMoreButton, ...props }: UpdatesListProps) {
  const { t } = useTranslation('common');
  const [updateTypes, setUpdateTypes] = useState<any[]>([]);
  const fetUpdateTypes = async () => {
    const updateTypeParams = {
      sort: {title: "asc"},
      limit: "~"
    }
    const response = await apiService.get('/updatetype', updateTypeParams);
    if (response && response.data && response.data.length > 0) {
      setUpdateTypes(response.data);
    }
  }
  useEffect(() => {
    fetUpdateTypes();
  },[])
  return (
    <div className='updates-block' id="update-content">
      <ul className='updatesList'>
        {updates && Array.isArray(updates) && updateTypes && (updateTypes.length > 0) && updates.map((item, index) => {
          return (
            <UpdatesItem updateTypes={updateTypes} item={item} key={index} onRemoveUpdate={onRemoveUpdate}/>
          )
        })}
        {loadMoreButton && updates.length > 0 ? (
          <li className="updates-load-more">
            <a onClick={loadMore}>{t('LoadMore')}</a>
          </li>
        ):null}
      </ul>
    </div>
  )
}



