//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { InstitutionNetwork } from "../../../types";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

interface InstitutionNetworkFormProps {
    [key: string]: any;
}

const InstitutionNetworkForm = (props: InstitutionNetworkFormProps) => {
    const _initialinstitutionNetowrk = {
        title: "",
    };

    const [initialVal, setInitialVal] = useState<InstitutionNetwork>(_initialinstitutionNetowrk);

    const editform = props.routes && props.routes[0] === "edit_institution_network" && props.routes[1];
    const { t } = useTranslation('common');

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialinstitutionNetowrk);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.Organisationnetworks.updatesuccess";
            response = await apiService.patch(`/institutionnetwork/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.Organisationnetworks.success";
            response = await apiService.post("/institutionnetwork", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/institution_network");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const institutionNetworkParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getInstitutionNetworkData = async () => {
                const response: InstitutionNetwork = await apiService.get(
                    `/institutionnetwork/${props.routes[1]}`,
                    institutionNetworkParams
                );
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getInstitutionNetworkData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>
                                        {t("adminsetting.Organisationnetworks.OrganisationNetworks")}
                                    </Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.Organisationnetworks.OrganisationNetworks")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => value.trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.Organisationnetworks.Add"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/institution_network`}
                                        >
                                        <Button variant="secondary">{t("Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default InstitutionNetworkForm;
