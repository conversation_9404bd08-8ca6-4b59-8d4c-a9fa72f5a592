//Import Library
import React, { useState } from "react";
import { Pagination as Page } from "react-bootstrap";

const Pagination = ({ postsPerPage, totalPosts, paginate }) => {
  const pageNumbers = [];
  const [activePage, setActivePage] = useState(1);

  for (let i = 1; i <= Math.ceil(totalPosts / postsPerPage); i++) {
    pageNumbers.push(
      <Page.Item key={i} active={i === activePage}>
        {i}
      </Page.Item>
    );
  }

  const paginationHandler = async (e) => {
    setActivePage(e.target.text);
    e.target && (await paginate(e.target.text));
  };

  return <Page onClick={paginationHandler}>{pageNumbers}</Page>;
};

export default Pagination;
