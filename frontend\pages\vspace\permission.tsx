//Import Library
import React from 'react';
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

//Import services/components
import R403 from "../r403";

export const canAddVspace = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddVspace',
});

export const canAddVspaceForm = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.vspace && state.permissions.vspace['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddVspaceForm',
  FailureComponent: () => <R403/>
});

export const canEditVspace = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.vspace) {
      if (state.permissions.vspace['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.vspace['update:own']) {
          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {
            return true;
          }  
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditVspace',
});

export const canEditVspaceForm = connectedAuthWrapper({
  authenticatedSelector: (state, props) => {
    if (state.permissions && state.permissions.vspace) {
      if (state.permissions.vspace['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.vspace['update:own']) {
          if (props.vspace && props.vspace.user && props.vspace.user._id === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditVspaceForm',
  FailureComponent: () => <R403/>
});

export const canViewDiscussionUpdate = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewDiscussionUpdate',
});

export default canAddVspace;