//Import Library
import Link from "next/link";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';
const SyndromeTable = (_props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectSyndrome, setSelectSyndrome] = useState({});
    
    const columns = [
        {
            name: t("adminsetting.syndrome.Syndromes"),
            selector: "title",
        },
        {
            name: t("adminsetting.syndrome.Code"),
            selector: "code",
            cell: (d) => d.code,
        },
        {
            name: t("adminsetting.syndrome.Description"),
            selector: "description",
            cell: (d) => d.description.replace(/<[^>]+>/g, ""),
        },
        {
            name: t("adminsetting.syndrome.Action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_syndrome/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    useEffect(() => {
        getSyndromeData();
    }, []);

    const syndromeParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const getSyndromeData = async () => {
        setLoading(true);
        const response = await apiService.get("/syndrome", syndromeParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        syndromeParams.limit = perPage;
        syndromeParams.page = page;
        getSyndromeData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        syndromeParams.limit = newPerPage;
        syndromeParams.page = page;
        setLoading(true);
        const response = await apiService.get("/syndrome", syndromeParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectSyndrome(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/syndrome/${selectSyndrome}`);
            getSyndromeData();
            setModal(false);
            toast.success(t("adminsetting.syndrome.Table.syndromeDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.syndrome.Table.errorDeletingSyndrome"));
        }
    };

    const modalHide = () => setModal(false);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.syndrome.Deletesyndrome")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.syndrome.Areyousurewanttodeletethissyndrome?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.syndrome.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.syndrome.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default SyndromeTable;
