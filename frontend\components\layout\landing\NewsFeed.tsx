//Import Library
import React from "react";
import { Col } from "react-bootstrap";

//Import services/components
import { newsData, newsImages } from "../../../data/landing";

const NewsFeed: React.FunctionComponent<{}> = (): React.ReactElement => (
  <Col sm={4}>
    <div className="block-title news-feeds">News Feeds</div>
    {newsData.map((item, idx) => (
      <div className="feed-news" key={idx}>
        <img src={newsImages[idx]} />
        <div className="newsContent">
          <div className="newsTitle">{item.title} <span>{item.tag}</span></div>
          <div className="newsDesc">{item.content}</div>
        </div>
      </div>
    ))}
  </Col>
);

export default NewsFeed;
