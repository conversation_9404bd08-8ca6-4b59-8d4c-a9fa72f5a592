//Import Library
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';

//Import services/components
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix(process.env.API_VERSION);
  app.useGlobalPipes(new ValidationPipe());

  console.log("senthil and jon ready to bootstrap data");


}

bootstrap();