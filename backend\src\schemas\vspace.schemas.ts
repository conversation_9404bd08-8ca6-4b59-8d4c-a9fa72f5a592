//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const VspaceSchema = new mongoose.Schema({
  title: String,
  description: String,
  start_date: Date,
  end_date: Date,
  topic: { type: mongoose.Schema.Types.ObjectId, ref: 'VspaceTopic', autopopulate: true },
  visibility: Boolean,
  members: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true }],
  images: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Image', autopopulate: true }],
  images_src: [{ type: String }],
  subscribers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true }],
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'Users', autopopulate: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
  file_category: { type: mongoose.Schema.Types.ObjectId, ref: 'FileCategory', autopopulate: true },
  vspace_email_invite: String,
  private_user_invite: [{
    country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', autopopulate: true },
    region: { type: mongoose.Schema.Types.ObjectId, ref: 'Region', autopopulate: true },
    institution_type: { type: mongoose.Schema.Types.ObjectId, ref: 'InstitutionType', autopopulate: true },
    institution: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Institution', autopopulate: true }],
    expertise: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Expertise', autopopulate: true }],
    networks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'InstitutionNetwork', autopopulate: true }],
  }],
  nonMembers: [{ type: String }],
  document: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Files', autopopulate: true }],
  doc_src: [{ type: String }],
  operation: { type: mongoose.Schema.Types.ObjectId, ref: 'Operation' },
  project: { type: mongoose.Schema.Types.ObjectId, ref: 'Project' },

});

VspaceSchema.plugin(mongoosePaginate);
