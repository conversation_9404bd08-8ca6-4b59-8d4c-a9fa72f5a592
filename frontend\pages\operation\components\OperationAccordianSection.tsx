//Import Library
import React, { useState } from "react";
import { Accordion, Card, Col, Row } from "react-bootstrap";
import PartnersAccordian from "./PartnersAccordian";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import { useTranslation } from 'next-i18next';
import Discussion from "../../../components/common/disussion";
import { canViewDiscussionUpdate } from "../permission";
import VirtualSpaceAccordian from "./VirtualSpaceAccordian";
import OperationDetailsAccordian from "./OperationDetailsAccordian";
import MediaGalleryAccordian from "./MediaGalleryAccordian";
import DocumentsAccordian from "./DocumentsAccordian";

const OperationAccordianSection = (props) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    console.log(props, "propsdata")

    const DiscussionComponent = () => {
        return (
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("Discussions")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <Discussion
                        type="operation"
                        id={props?.routeData?.routes ? props.routeData.routes[1] : null}
                    />
                </Accordion.Body>
            </Accordion.Item>
        );
    };

    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (
        <DiscussionComponent />
    ));
    return (
        <>
            <Row>
                <Col className="operationAccordion" xs={12}>
                    <Accordion>
                        <OperationDetailsAccordian operation={props.operationData} />
                    </Accordion>

                    <Accordion>
                        <PartnersAccordian operation={props.operationData} />
                    </Accordion>

                    <Accordion>
                        <MediaGalleryAccordian operation={props.operationData} />
                    </Accordion>

                    <Accordion>
                        <DocumentsAccordian documentAccoirdianData={props} />
                    </Accordion>

                    <Accordion>
                        <CanViewDiscussionUpdate />
                    </Accordion>
                    <Accordion>
                        <VirtualSpaceAccordian routeData={props.routeData} />
                    </Accordion>
                </Col>
            </Row>
        </>
    )
}

export default OperationAccordianSection;