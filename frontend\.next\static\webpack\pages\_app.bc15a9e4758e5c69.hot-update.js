"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/hoc/AuthSync.tsx":
/*!*************************************!*\
  !*** ./components/hoc/AuthSync.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/CustomLoader */ \"(pages-dir-browser)/./components/common/CustomLoader.tsx\");\n//Import Library\n\n\n\n\n\n// Public routes used to handle layouts\nconst publicRoutes = [\n    \"/home\",\n    \"/login\",\n    // \"/admin/login\",\n    \"/forgot-password\",\n    \"/reset-password/[passwordToken]\",\n    \"/declarationform/[...routes]\"\n];\n// Gets the display name of a JSX component for dev tools\nconst getDisplayName = (Component1)=>Component1.displayName || Component1.name || \"Component\";\nfunction withAuthSync(WrappedComponent) {\n    class MainComponent extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n        static async getInitialProps(ctx) {\n            const componentProps = WrappedComponent.getInitialProps && await WrappedComponent.getInitialProps(ctx);\n            if (ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies) {\n                const objCookies = ctx.ctx && ctx.ctx.req && ctx.ctx.req.cookies ? ctx.ctx.req.cookies : {};\n                componentProps.objCookies = objCookies;\n                return {\n                    ...componentProps\n                };\n            } else {\n                return {\n                    ...componentProps\n                };\n            }\n        }\n        componentDidMount() {\n            const { route } = this.props.router;\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.on(\"routeChangeComplete\", (url)=>{\n                if (url === \"/home\") {\n                    this.setState({\n                        isLoading: false\n                    });\n                }\n            });\n            setTimeout(()=>{\n                if (!this.state.cookie && publicRoutes.indexOf(route) === -1) {\n                    this.props.router.push(\"/home\");\n                    return;\n                }\n                this.setState({\n                    isLoading: false\n                });\n            }, 0);\n        }\n        componentWillUnmount() {\n            next_router__WEBPACK_IMPORTED_MODULE_2___default().events.off(\"routeChangeComplete\", ()=>null);\n        }\n        componentDidUpdate(prevProps) {\n            if (!prevProps.objCookies && this.props.objCookies) {\n                this.setState({\n                    cookie: this.props && this.props.objCookies && this.props.objCookies[\"connect.sid\"] ? this.props.objCookies[\"connect.sid\"] : null,\n                    isLoading: true\n                });\n            }\n        }\n        render() {\n            const { router } = this.props;\n            const isPublicRoute = publicRoutes.indexOf(router.route) > -1;\n            return this.state.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_CustomLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 97,\n                columnNumber: 37\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n                isLoading: this.state.isLoading,\n                isPublicRoute: isPublicRoute,\n                ...this.props\n            }, void 0, false, {\n                fileName: \"D:\\\\rki\\\\frontend\\\\components\\\\hoc\\\\AuthSync.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this);\n        }\n        constructor(props){\n            super(props);\n            this.state = {\n                isLoading: true,\n                cookie: this.props && this.props.objCookies && this.props.objCookies[\"connect.sid\"] ? this.props.objCookies[\"connect.sid\"] : null\n            };\n        }\n    }\n    MainComponent.displayName = \"withAuthSync(\".concat(getDisplayName(WrappedComponent), \")\");\n    return (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.connect)((state)=>state)(MainComponent);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withAuthSync);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/hoc/AuthSync.tsx\n"));

/***/ })

});