//Import Library
import React, { useRef, useMemo, useEffect, useState } from "react";
import Link from "next/link";
import _ from "lodash";
import { useRouter } from "next/router";
import moment from "moment";

//Import services/components
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import OperationsTableFilter from "./OperationsTableFilter";
import { useTranslation } from 'next-i18next';

const PartnersLink = ({ partners }) => {
  if (partners && partners.length > 0) {
    return (
      <ul>
        {partners.map((item, index) => {
          if (item.institution) {
            return (
              <li key={index}>
                <Link
                  href="/institution/[...routes]"
                  as={`/institution/show/${item.institution._id}`}
                >
                  {item.institution.title}
                </Link>
              </li>
            );
          }
        })}
      </ul>
    );
  }
  return null;
};

function OperationsTable(props) {
  const { t } = useTranslation('common');
  const router = useRouter();
  const { setOperations, selectedRegions } = props;
  const [filterText, setFilterText] = useState("");
  const [filterStatus, setFilterStatus] = useState("");
  const [resetPaginationToggle, setResetPaginationToggle] = useState(false);
  const [tabledata, setDataToTable] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [pageSort, setPageSort] = useState(null);


  const operationParams: any = {
    sort: { created_at: "desc" },
    lean: true,
    populate: [
      { path: "partners.status", select: "title" },
      { path: "partners.institution", select: "title" },
      { path: "status", select: "title" },
      { path: "country", select: "coordinates" },
    ],
    limit: perPage,
    page: 1,
    query: {},
    select:
      "-timeline -region -hazard -description -end_date -syndrome -hazard_type -created_at -updated_at",
  };
  const [opParams, setOpParams] = useState(operationParams);

  const columns = [
    {
      name: t("Operations"),
      selector: "title",
      sortable: true,
      cell: (d) => (
        <Link href="/operation/[...routes]" as={`/operation/show/${d._id}`}>
          {d.title}
        </Link>
      ),
    },
    {
      name: t("Status"),
      selector: "status",
      sortable: true,
      cell: (d) => d.status && d.status.title ? d.status.title : ""
    },
    {
      name: t("StartDate"),
      selector: "start_date",
      sortable: true,
      cell: (d) =>
        d && d.start_date ? moment(d.start_date).format("M/D/Y") : "",
    },
    {
      name: t("Partners"),
      selector: "partners",
      cell: (d) => <PartnersLink partners={d.partners} />,
    },
  ];



  const getOperationsData = async (operationParamsinit) => {
    setLoading(true);

    if (router.query && router.query.country) {
      operationParamsinit.query["country"] = router.query.country;
    }

    // Handle selectedRegions with proper condition
    if (selectedRegions === null) {
      // First load: don't apply region filter
      delete operationParamsinit.query["world_region"];
    } else if (selectedRegions.length === 0) {
      // No regions selected: force zero results
      operationParamsinit.query["world_region"] = ["__NO_MATCH__"]; // dummy value
    } else {
      // Normal filtering
      operationParamsinit.query["world_region"] = selectedRegions;
    }

    const response = await apiService.get("/operation", operationParamsinit);
    if (response && Array.isArray(response.data)) {
      setDataToTable(response.data);
      setOperations(response.data);
      setTotalRows(response.totalCount);
      setLoading(false);
    }
  };


  const handlePageChange = (page) => {
    operationParams.limit = perPage;
    operationParams.page = page;

    // Apply the filter for title if present
    if (filterText !== "") {
      operationParams.query = { title: filterText };
    }

    // Apply filter for status if present
    filterStatus &&
      (operationParams.query = {
        ...operationParams.query,
        status: filterStatus,
      });

    // Handle sorting
    pageSort && (operationParams.sort = pageSort.sort);

    // Get the data
    getOperationsData(operationParams);
    setPageNum(page);
  };


  const handlePerRowsChange = async (newPerPage, page) => {
    operationParams.limit = newPerPage;
    operationParams.page = page;
    setLoading(true);

    if (router.query && router.query.country) {
      operationParams.query["country"] = router.query.country;
    }

    // Handle selected regions similarly as in `getOperationsData()`
    if (selectedRegions === null) {
      delete operationParams.query["world_region"];
    } else if (selectedRegions.length === 0) {
      operationParams.query["world_region"] = ["__NO_MATCH__"];
    } else {
      operationParams.query["world_region"] = selectedRegions;
    }

    filterStatus &&
      (operationParams.query = {
        ...operationParams.query,
        status: filterStatus,
      });

    pageSort && (operationParams.sort = pageSort.sort);

    const response = await apiService.get("/operation", operationParams);
    if (response && Array.isArray(response.data)) {
      setDataToTable(response.data);
      setOperations(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }
    setPageNum(page);
  };


  useEffect(() => {
    opParams.page = 1;
    getOperationsData(opParams);
  }, [selectedRegions, router]);

  useEffect(() => {
    getOperationsData(opParams);
  }, [opParams]);  // Make sure to watch `opParams` for changes


  const handleSort = async (column, sortDirection) => {
    setLoading(true);
    operationParams.sort = {
      [column.selector]: sortDirection,
    };
    filterStatus && (operationParams.query = { ...operationParams.query, status: filterStatus });
    filterText !== "" && (operationParams.query = { ...operationParams.query, title: filterText });

    await getOperationsData(operationParams);
    setPageSort(operationParams);
    setLoading(false);
  };

  const sendQuery = (q, page) => {
    if (q) {
      opParams.query["title"] = q;
      opParams.page = page;
      setOpParams({ ...opParams });
    } else {
      delete opParams.query.title;
      setOpParams({ ...opParams });
    }
  };

  const handleSearchTitle = useRef(
    _.debounce((q, page) => sendQuery(q, page), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)
  ).current;

  const subHeaderComponentMemo = useMemo(() => {
    const handleClear = () => {
      if (filterText) {
        setResetPaginationToggle(!resetPaginationToggle);
        setFilterText("");
      }
    };

    const handleFilterStatusChange = (status) => {
      setFilterStatus(status);
      if (status) {
        opParams.query["status"] = status;
        opParams.page = pageNum;
        setOpParams({ ...opParams });
      } else {
        delete opParams.query.status;
        setOpParams({ ...opParams });
      }
    };

    const handleChange = (e) => {
      setFilterText(e.target.value);
      handleSearchTitle(e.target.value, pageNum);
    };

    return (
      <OperationsTableFilter
        onFilter={handleChange}
        onFilterStatusChange={(e) => handleFilterStatusChange(e.target.value)}
        onClear={handleClear}
        filterText={filterText}
        filterStatus={filterStatus}
      />
    );
  }, [filterText, filterStatus, resetPaginationToggle, selectedRegions]);
  return (
    <RKITable
      columns={columns}
      data={tabledata}
      totalRows={totalRows}
      loading={loading}
      subheader
      persistTableHead
      onSort={handleSort}
      sortServer
      pagServer={true}
      resetPaginationToggle={resetPaginationToggle}
      subHeaderComponent={subHeaderComponentMemo}
      handlePerRowsChange={handlePerRowsChange}
      handlePageChange={handlePageChange}
    />
  );
}

export default OperationsTable;
