//Import Library
import {Col} from "react-bootstrap";
import <PERSON> from "next/link";

const truncateLength = 260;

//TODO: Remove the maths random number for image after updates completed with image upload
interface AnnouncementItemProps {
  item: {
    _id: string;
    title: string;
    description: string;
    type: string;
    [key: string]: any;
  };
}

export default function AnnouncementItem(props: AnnouncementItemProps) {

  const {item} = props;

  const getTrimmedString = (html: string) => {
    const div = document.createElement("div");
    div.innerHTML = html;
    const string = div.textContent || div.innerText || "";
    return (string.length > truncateLength ? `${string.substring(0, truncateLength - 3)}...` : string);
  }

  const indexNew =  item.images.length -1;
  const parent = `parent_${item.type}`;

  return (
    <Col className="p-0" xs={12}>
      <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>

        {(item.images && item.images[indexNew]) ?
          <img src={`${process.env.API_SERVER}/image/show/${item.images[indexNew]._id}`} alt="announcement"
               className="announceImg"/>
          : <i className="fa fa-bullhorn announceImg"/>}

      </Link>
      <div className="announceDesc">
        <Link href={`/${item.type}/[...routes]`} as={`/${item.type}/show/${item[parent]}/update/${item._id}`}>
          {item && item.title ? item.title : ''}
        </Link>
        <p>
          {item && item.description ? getTrimmedString(item.description) : null}
        </p>
      </div>
    </Col>
  );
}