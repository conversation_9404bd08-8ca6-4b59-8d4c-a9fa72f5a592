//Import Library
import React, { useState, useRef, useEffect } from "react";
import Router from "next/router";
import { <PERSON><PERSON>, Card, Form, Container, Row, Col } from "react-bootstrap";
import Link from "next/link";
import { TextInput, SelectGroup } from "../../components/common/FormValidation";
import ValidationFormWrapper from "../../components/common/ValidationFormWrapper";
import { MultiSelect } from "react-multi-select-component";
import moment from "moment";
import toast from 'react-hot-toast';

//Import services/components
import ReactDropZone from "../../components/common/ReactDropZone";
import RKIDatePicker from "../../components/common/RKIDatePicker";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';
import { EditorComponent } from "../../shared/quill-editor/quill-editor.component";

const EventForm = (props: any) => {
    const initialState = {
        title: "",
        operation: null,
        date: null,
        syndrome: "",
        description: "",
        hazard_type: [],
        country_regions: [],
        hazard: [],
        status: [],
        rki_monitored: false,
        country: [],
        world_region: null,
        more_info: "",
        laboratory_confirmed: false,
        officially_validated: false,
        images: [],
        images_src: [],
    };

    const buttonRef = useRef<any>(null);
    const dateRef = useRef<any>(null);
    const { t, i18n } = useTranslation('common');
    const eventSearch = i18n.language === "de" ? { title_de: "asc" } : { title: "asc" };
    const currentLang = i18n.language === "fr" ? "en" : i18n.language;
    const currentLangs = i18n.language;

    const titleSearch = currentLang ? `title.${currentLang}` : "title.en";
    const [dropZoneCollection, setDropZoneCollection] = useState([]);
    const [srcCollection, setSrcCollection] = useState([]);
    const [initialVal, setInitialVal] = useState<any>(initialState);
    const [regions, setRegion] = useState([]);
    const [hazards, setHazards] = useState([]);
    const [countryList, setcountryList] = useState([]);
    const [hazardType, setHazardType] = useState([]);
    const [syndromes, setSyndromes] = useState([]);
    const [operation, setOperation] = useState([]);
    const [eventStatus, setEventStatus] = useState([]);
    const [countryRiskLevel, setCountryRiskLevel] = useState([]);
    const [regionRiskLevel, setRegionRiskLevel] = useState([]);
    const [currLang] = useState(titleSearch);
    const [internationalRiskLevel, setInternationalRiskLevel] = useState([]);

    const eventParams = {
        query: {},
        sort: eventSearch,
        limit: "~",
        languageCode: currentLangs,
    };

    const getCountries = async (eventParamsinit: any) => {
        const response = await apiService.get("/country", eventParamsinit);
        if (response && Array.isArray(response.data)) {
            setcountryList(response.data);
        }
    };

    const getHazardType = async (eventParamsinit: any) => {
        const response = await apiService.get("/hazardtype", eventParamsinit);
        if (response && Array.isArray(response.data)) {
            setHazardType(response.data);
        }
    };

    const getSyndromes = async (eventParamsinit: any) => {
        const response = await apiService.get("/syndrome", eventParamsinit);

        if (response && Array.isArray(response.data)) {
            setSyndromes(response.data);
        }
    };

    const getOperationName = async (eventParamsinit: any) => {
        const response = await apiService.get("/operation", eventParamsinit);

        if (response && Array.isArray(response.data)) {
            setOperation(response.data);
        }
    };

    const getEventStatus = async (eventParamsvalue: any) => {
        const response = await apiService.get("/eventstatus", eventParamsvalue);
        if (response && Array.isArray(response.data)) {
            setEventStatus(response.data);
        }
    };
    const getRiskLevel = async (eventParamsinit: any) => {
        const response = await apiService.get("/risklevel", eventParamsinit);

        if (response && Array.isArray(response.data)) {
            setCountryRiskLevel(response.data);
            setRegionRiskLevel(response.data);
            setInternationalRiskLevel(response.data);
        }
    };

    const getresponse = (risk_assessment: any, response: any) => {
        getRegion(response.country);
        getHazard(response.hazard_type);
        setDropZoneCollection(response.images ? response.images : []);
        setSrcCollection(response.images_src ? response.images_src : []);

        setInitialVal((prevState) => ({ ...prevState, ...response }));
        if (risk_assessment) {
            setRiskAssessment((prevState) => ({
                ...prevState,
                riskcountry: risk_assessment.country ? risk_assessment.country._id : "",
                region: risk_assessment.region ? risk_assessment.region._id : "",
                international: risk_assessment.international ? risk_assessment.international._id : "",
                description: risk_assessment.description ? risk_assessment.description : "",
            }));
        }
    };

    const get_response = (country_regions: any, hazard: any, date: any, response: any) => {
        response.country_regions =
            country_regions && country_regions.length > 0
                ? country_regions.map((item: any, _i: any) => {
                      return { label: item.title, value: item._id };
                  })
                : [];
        response.date = date ? moment(date).toDate() : null;
        response.hazard =
            hazard && hazard.length > 0
                ? hazard.map((item: any, _i: any) => {
                      return {
                          label: item.title[currentLang],
                          value: item._id,
                      };
                  })
                : [];
    };

    const get_responseevent = (response: any, syndrome: any, country: any, hazard_type: any, operations: any, status: any) => {
        response.syndrome = syndrome && syndrome._id ? syndrome._id : "";
        response.status = status && status._id ? status._id : "";
        response.country = country && country._id ? country._id : "";
        response.hazard_type = hazard_type && hazard_type._id ? hazard_type._id : "";
        response.operation = operations && operations._id ? operations._id : "";
    };
    useEffect(() => {
        if (props.routes && props.routes[0] === "edit" && props.routes[1]) {
            const getEventsData = async () => {
                const response = await apiService.get(`/event/${props.routes[1]}`, eventParams);
                if (response) {
                    const {
                        status,
                        syndrome,
                        country,
                        hazard_type,
                        risk_assessment,
                        country_regions,
                        hazard,
                        operations,
                        date,
                    } = response;
                    get_responseevent(response, syndrome, country, hazard_type, operations, status);
                    get_response(country_regions, hazard, date, response);
                    getresponse(risk_assessment, response);
                }
            };
            getEventsData();
        }
        getCountries(eventParams);
        getHazardType(eventParams);
        getSyndromes(eventParams);
        getOperationName(eventParams);
        getEventStatus(eventParams);
        getRiskLevel(eventParams);
    }, []);

    const _initialRiskAssessment = {
        riskcountry: null,
        region: null,
        international: null,
        description: null,
        country: null,
    };
    const [riskAssessment, setRiskAssessment] = useState(_initialRiskAssessment);
    const handleRiskAssessment = (e: any) => {
        const { name, value } = e.target;
        setRiskAssessment((prevState) => ({ ...prevState, [name]: value }));
    };
    const handleRiskDescription = (value: any) => {
        setRiskAssessment((prevState) => ({
            ...prevState,
            description: value,
        }));
    };
    const handleSubmit = async (e: any) => {
        //Since the bootstrap validation dont support datepicker, we used this workaround
        //We check for date as date is required
        if (initialVal.date == null) {
            dateRef.current.focus();
            return;
        } else {
            if (buttonRef.current) {
                buttonRef.current.setAttribute("disabled", "disabled");
            }
        }

        // Prevent default form submission
        if (e && e.preventDefault) {
            e.preventDefault();
        }

        // Basic validation for required fields
        if (!initialVal.title || !initialVal.country) {
            if (buttonRef.current) {
                buttonRef.current.removeAttribute("disabled");
            }
            return;
        }
        try {
            initialVal.country_regions = initialVal.country_regions
                ? initialVal.country_regions.map((item: any, _i: any) => item.value)
                : [];
            initialVal.hazard = initialVal.hazard ? initialVal.hazard.map((item: any, _i: any) => item.value) : [];
            initialVal.operation = initialVal.operation || null;
            initialVal.syndrome = initialVal.syndrome || null;
            let response;
            let toastMsg;
            riskAssessment.country = riskAssessment.riskcountry;

            if (props.routes && props.routes[0] === "edit" && props.routes[1]) {
                toastMsg = "toast.Eventupdatedsuccessfully";
                response = await apiService.patch(`/event/${props.routes[1]}`, {
                    ...initialVal,
                    risk_assessment: riskAssessment,
                });
            } else {
                toastMsg = "toast.Eventaddedsuccessfully";
                response = await apiService.post("/event", {
                    ...initialVal,
                    risk_assessment: riskAssessment,
                });
            }
            if (response && response._id) {
                toast.success(t(toastMsg));
                Router.push("/event/[...routes]", `/event/show/${response._id}`);
            } else {
                // Re-enable the button in case of error
                if (buttonRef.current) {
                    buttonRef.current.removeAttribute("disabled");
                }

                if (response === "date should not be empty") {
                    response = t("toast.dateShouldNotBeEmpty");
                }
                toast.error(response || t("toast.errorOccurred"));
            }
        } catch (error) {
            // Re-enable the button in case of error
            if (buttonRef.current) {
                buttonRef.current.removeAttribute("disabled");
            }

            console.error("Error submitting event:", error);
            toast.error(t("toast.errorOccurred"));
        }
    };

    const clearValue = (obj: any) => {
        setInitialVal((prevState) => ({
            ...prevState,
            ...obj,
        }));
    };

    const getRegion = async (id: any) => {
        let _regions = [];
        if (id) {
            const response = await apiService.get(`/country_region/${id}`, eventParams);
            if (response && response.data) {
                _regions = response.data.map((item: any, _i: any) => {
                    return { label: item.title, value: item._id };
                });
                _regions.sort((a: any, b: any) => a.label.localeCompare(b.label));
            }
        }
        setRegion(_regions);
    };

    const bindCountryRegions = (e: any) => {
        setInitialVal((prevState) => ({
            ...prevState,
            country_regions: e,
        }));
    };

    const regionsParams = {
        query: { enabled: true },
        sort: { [currLang]: "asc" },
        limit: "~",
        select: "-description -first_letter -hazard_type -picture -picture_source  -created_at -updated_at",
    };
    const getHazard = async (id: any) => {
        let _hazard = [];
        if (id) {
            const response = await apiService.get(`/hazard_hazard_type/${id}`, regionsParams);
            if (response && response.data) {
                _hazard = response.data.map((item) => ({
                    label: item.title[currentLang],
                    value: item._id,
                }));
            }
        }
        setHazards(_hazard);
    };

    const bindHazard = (e) => {
        setInitialVal((prevState) => ({
            ...prevState,
            hazard: e,
        }));
    };

    const onChangeDate = (date, key) => {
        setInitialVal((prevState) => ({
            ...prevState,
            [key]: date,
        }));
    };
    /*******HANDLE CHANGES & SET VALUE TO STATE*****/

    const handleChange = (e) => {
        const { name, value } = e.target;

        setInitialVal((prevState) => ({
            ...prevState,
            [name]: value,
        }));

        if (name === "country") {
            const selectedIndex = e.target.selectedIndex;
            if (e.target && selectedIndex && selectedIndex != null) {
                const worldRegion = e.target[selectedIndex].getAttribute("data-worldregion");
                setInitialVal((prevState) => ({
                    ...prevState,
                    world_region: worldRegion,
                }));
            }
        }

        if (name === "country") {
            getRegion(value);
            clearValue({ country_regions: [] });
        }
        if (name === "hazard_type") {
            getHazard(value);
            clearValue({ hazard: [] });
        }
    };

    const handleValidateOffical = () => {
        setInitialVal((prevState) => ({
            ...prevState,
            officially_validated: !prevState.officially_validated,
        }));
    };

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(initialState);
        setDropZoneCollection([]);
        setSrcCollection([]);
        setCountryRiskLevel([]);
        setRegionRiskLevel([]);
        setInternationalRiskLevel([]);
        setRiskAssessment(_initialRiskAssessment);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleLabConfirm = () => {
        setInitialVal((prevState) => ({
            ...prevState,
            laboratory_confirmed: !prevState.laboratory_confirmed,
        }));
    };

    const handleDescription = (value) => {
        setInitialVal((prevState) => ({ ...prevState, description: value }));
    };

    const handleMoreInfoDescription = (value) => {
        setInitialVal((prevState) => ({ ...prevState, more_info: value }));
    };

    const handleMonitored = (e) => {
        setInitialVal((prevState) => ({
            ...prevState,
            rki_monitored: !prevState.rki_monitored,
        }));
    };

    const getID = (id) => {
        const _id = id.map((item) => item.serverID);
        setInitialVal((prevState) => ({ ...prevState, images: _id }));
    };

    const getSource = (imgSrcArr) => {
        setInitialVal((prevState) => ({ ...prevState, images_src: imgSrcArr }));
    };

    return (
        <Container fluid>
            <Card
                style={{
                    marginTop: "5px",
                    boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                }}
            >
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>{props.routes[0] === "edit" ? t("editEvent") : t("addEvent")}</Card.Title>
                                <hr />
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("Events.forms.EventID")}</Form.Label>
                                    <TextInput
                                        name="title"
                                        id="eventId"
                                        value={initialVal.title}
                                        validator={(value) => value.trim() !== ""}
                                        errorMessage={{
                                            validator: t("PleaseAddtheEventTitle"),
                                        }}
                                        onChange={handleChange}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col>
                                <Form.Group>
                                    <div className="d-flex">
                                        <Form.Label className="d-flex me-3">
                                            {t("Events.forms.OperationName")}
                                        </Form.Label>
                                        <Form.Check
                                            className="ms-4"
                                            type="switch"
                                            name="rki_monitored"
                                            id="custom-switch"
                                            onChange={handleMonitored}
                                            label={t("Events.forms.MonitoredbyRKI")}
                                            checked={initialVal.rki_monitored}
                                        />
                                    </div>
                                    <SelectGroup
                                        name="operation"
                                        id="operation"
                                        value={initialVal.operation === null ? "" : initialVal.operation}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("Events.forms.SelectOperationName")}</option>
                                        {operation.map((item, i) => (
                                            <option key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label className="required-field">
                                        {t("Events.forms.CountryorTerritory")}
                                    </Form.Label>
                                    <SelectGroup
                                        name="country"
                                        id="country"
                                        value={
                                            Array.isArray(initialVal.country) && initialVal.country.length === 0
                                                ? ""
                                                : initialVal.country
                                        }
                                        onChange={handleChange}
                                        required
                                        errorMessage={t("thisfieldisrequired")}
                                    >
                                        <option value="">{t("Events.forms.SelectCountry")}</option>
                                        {countryList.map((item, i) => (
                                            <option data-worldregion={item.world_region._id} key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col sm={6} md={6} lg={6}>
                                <Form.Group style={{ maxWidth: "600px" }}>
                                    <Form.Label>{t("CountryRegions")}</Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("SelectRegions"),
                                            allItemsAreSelected: t("Events.forms.AllRegionsareSelected"),
                                        }}
                                        options={regions}
                                        value={initialVal.country_regions}
                                        onChange={bindCountryRegions}
                                        className={"region"}
                                        labelledBy={t("SelectRegions")}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col sm={6} md={6} lg={4}>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("Events.forms.Status")}</Form.Label>
                                    <SelectGroup
                                        name="status"
                                        id="status"
                                        value={
                                            Array.isArray(initialVal.status) && initialVal.status.length === 0
                                                ? ""
                                                : initialVal.status
                                        }
                                        onChange={handleChange}
                                        required
                                        errorMessage={t("thisfieldisrequired")}
                                    >
                                        <option value="">{t("Events.forms.SelectStatus")}</option>

                                        {eventStatus.map((item, i) => (
                                            <option key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col sm={6} md={6} lg={4}>
                                <Form.Group>
                                    <Form.Label className="required-field">{t("Events.forms.HazardType")}</Form.Label>
                                    <SelectGroup
                                        name="hazard_type"
                                        id="hazardType"
                                        value={
                                            Array.isArray(initialVal.hazard_type) && initialVal.hazard_type.length === 0
                                                ? ""
                                                : initialVal.hazard_type
                                        }
                                        onChange={handleChange}
                                        required
                                        errorMessage={t("thisfieldisrequired")}
                                    >
                                        <option value="">{t("Events.forms.SelectHazardType")}</option>
                                        {hazardType.map((item, i) => {
                                            return (
                                                <option key={i} value={item._id}>
                                                    {item.title}
                                                </option>
                                            );
                                        })}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col sm={6} md={6} lg={4}>
                                <Form.Group style={{ maxWidth: "600px" }}>
                                    <Form.Label>{t("Events.forms.Hazard")} </Form.Label>
                                    <MultiSelect
                                        overrideStrings={{
                                            selectSomeItems: t("Events.forms.SelectHazard"),
                                            allItemsAreSelected: t("Events.forms.AllHazardsareSelected"),
                                        }}
                                        options={hazards}
                                        value={initialVal.hazard}
                                        onChange={bindHazard}
                                        className={"region"}
                                        labelledBy={t("Events.forms.SelectHazard")}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col md lg={3} sm={12}>
                                <Form.Group>
                                    <Form.Label>{t("Events.forms.Syndrome")}</Form.Label>
                                    <SelectGroup
                                        name="syndrome"
                                        id="syndrome"
                                        value={initialVal.syndrome}
                                        onChange={handleChange}
                                    >
                                        <option value="">{t("Events.forms.SelectSyndrome")}</option>
                                        {syndromes.map((item, i) => (
                                            <option key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col>
                                <Form.Group>
                                    <Row>
                                        <Col>
                                            <Form.Label className="required-field">{t("Events.forms.Date")}</Form.Label>
                                        </Col>
                                    </Row>
                                    <label className="date-validation w-100" ref={dateRef}>
                                        <RKIDatePicker
                                            selected={initialVal.date}
                                            maxDate={new Date()}
                                            errorMessage={t("PleaseselecttheDate")}
                                            onChange={(date) => onChangeDate(date, "date")}
                                            dateFormat="MMMM d, yyyy"
                                            placeholderText={t("Events.forms.SelectDate")}
                                        />
                                    </label>
                                </Form.Group>
                            </Col>
                            <Col sm={6} lg={3} className="align-self-center">
                                <Form.Group>
                                    <Form.Check
                                        style={{ all: "unset" }}
                                        type="checkbox"
                                        name={t("Events.forms.LaboratoryConfirmed")}
                                        label={t("Events.forms.LaboratoryConfirmed")}
                                        checked={initialVal.laboratory_confirmed}
                                        onChange={handleLabConfirm}
                                        inline
                                    />
                                </Form.Group>
                            </Col>
                            <Col sm={6} lg={3} className="align-self-center">
                                <Form.Group controlId="validated_by_official">
                                    <Form.Check
                                        name={t("Events.forms.ValidatedbyOfficial")}
                                        label={t("Events.forms.ValidatedbyOfficial")}
                                        checked={initialVal.officially_validated}
                                        onChange={handleValidateOffical}
                                        inline
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("Events.forms.Description")}</Form.Label>
                                    <EditorComponent initContent={initialVal.description} onChange={(evt) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Card.Text>
                                    <b>{t("Events.forms.RiskAssessment")}</b>
                                </Card.Text>
                                <hr />
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col sm={6} md={6} lg={4}>
                                <Form.Group>
                                    <Form.Label>{t("Events.forms.SelectCountryrisklevel")}</Form.Label>
                                    <SelectGroup
                                        name="riskcountry"
                                        id="riskcountry"
                                        value={riskAssessment.riskcountry === null ? "" : riskAssessment.riskcountry}
                                        onChange={handleRiskAssessment}
                                    >
                                        <option value="">{t("Events.forms.SelectCountryrisklevel")}</option>
                                        {countryRiskLevel.map((item, i) => (
                                            <option key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col sm={6} md={6} lg={4}>
                                <Form.Group>
                                    <Form.Label>{t("Events.forms.SelectRegionrisklevel")}</Form.Label>

                                    <SelectGroup
                                        name="region"
                                        id="region"
                                        value={riskAssessment.region === null ? "" : riskAssessment.region}
                                        onChange={handleRiskAssessment}
                                    >
                                        <option value="">{t("Events.forms.SelectRegionrisklevel")}</option>
                                        {regionRiskLevel.map((item, i) => (
                                            <option key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                            <Col sm={6} md={6} lg={4}>
                                <Form.Group>
                                    <Form.Label>{t("Events.forms.SelectInternationalrisklevel")}</Form.Label>
                                    <SelectGroup
                                        name="international"
                                        id="international"
                                        value={
                                            riskAssessment.international === null ? "" : riskAssessment.international
                                        }
                                        onChange={handleRiskAssessment}
                                    >
                                        <option value="">{t("Events.forms.SelectInternationalrisklevel")}</option>
                                        {internationalRiskLevel.map((item, i) => (
                                            <option key={i} value={item._id}>
                                                {item.title}
                                            </option>
                                        ))}
                                    </SelectGroup>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <EditorComponent initContent={riskAssessment.description} onChange={(evt) => handleRiskDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Card.Text>
                                    <b>{t("Events.forms.MoreInformation")}</b>
                                </Card.Text>
                                <hr />
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Form.Group>
                                    <EditorComponent initContent={initialVal.more_info} onChange={(evt) => handleMoreInfoDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <Card.Text>
                                    <b>{t("Events.forms.MediaGallery")}</b>
                                </Card.Text>
                                <hr />
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col>
                                <ReactDropZone
                                    datas={dropZoneCollection}
                                    srcText={srcCollection}
                                    getImgID={(id) => getID(id)}
                                    getImageSource={(imgSrcArr) => getSource(imgSrcArr)}
                                />
                            </Col>
                        </Row>
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary" ref={buttonRef}>
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" variant="info" onClick={resetHandler}>
                                    {t("reset")}
                                </Button>
                                <Link href="/event" as="/event" >
                                    <Button variant="secondary">{t("Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
        </Container>
    );
};

export default EventForm;
