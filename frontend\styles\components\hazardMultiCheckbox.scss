.hazards-multi-checkboxes {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  width: 100%;
  bottom: 0;
  position: absolute;
  background: rgba(20, 20, 20, 0.85);
  color: #FFF;
  padding-left: 30px;
  padding-top: 15px;
  .form-check {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
    margin: 0 0 8px;
    .form-check-label {
      margin-left: 10px;
      color: #FFF;
    }
  }
  button.btn-plain {
    background: transparent;
    border: none;
    padding: 0;
    height: 20px;
    font-weight: 500;
    color: #fff;
    &:hover, &:focus, &:active {
      color: #f1f1f1 !important;
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  }
}

@media screen and (max-width: 575px){
  .hazards-multi-checkboxes{
    .form-check-label{
      font-size: 14px;
    }
  }
}