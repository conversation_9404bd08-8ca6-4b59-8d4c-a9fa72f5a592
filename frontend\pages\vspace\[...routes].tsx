//Import Library
import { useRouter } from 'next/router';

//Import services/components
import Form from './Form';
import ViewVSpace from './View';
import { canAddVspaceForm } from "./permission";
import ManageMembers from './ManageMembers';
import AcceptRequestVspace from './AcceptRequestVspace';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';


const Router = () => {
  const router = useRouter()
  const routes: any = router.query.routes || []
  const CanAccessCreateForm = canAddVspaceForm(() => <Form routes={routes} />)

  switch (routes[0]) {
    case 'create':
      return <CanAccessCreateForm />

    case 'edit':
      return <Form routes={routes} />
    case 'show':
      return <ViewVSpace routes={routes} />
    case 'manage':
      return <ManageMembers routes={routes} />
    case "acceptvspace" :
      return <AcceptRequestVspace routes={routes} />
    default:
      return null;
  }
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Router
