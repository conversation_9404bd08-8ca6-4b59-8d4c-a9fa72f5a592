//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { OperationController } from './operation.controller';
import { OperationService } from './operation.service';
import { ImageService } from './../image/image.service';
import { InstitutionService } from './../institution/institution.service';
import { EmailService } from './../../email.service';
import { UsersService } from './../../users/users.service';
import { FlagService } from '../flag/flag.service';
import { FilesService } from './../files/files.service';
import {InstitutionModule} from "../institution/institution.module";
import {CountryService} from "../country/country.service";
import { UpdateService } from '../updates/update.service';
// SCHEMAS
import { OperationSchema } from '../../schemas/operation.schemas';
import { ImageSchema } from '../../schemas/image.schemas';
import { InstitutionSchema } from '../../schemas/institution.schemas';
import { UsersSchema } from '../../schemas/users.schemas';
import {CountrySchema} from "../../schemas/country.schemas";
import { ProjectSchema } from '../../schemas/project.schemas';
import { FlagSchema } from "../../schemas/flag.schemas";
import { FilesSchema } from '../../schemas/files.schemas';
import { VspaceSchema } from '../../schemas/vspace.schemas';
import { UpdateSchema } from 'src/schemas/update.schemas';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Operation', schema: OperationSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Institution', schema: InstitutionSchema },
      { name: 'Users', schema: UsersSchema },
      { name: 'Country', schema: CountrySchema },
      { name: 'Project', schema: ProjectSchema },
      { name: 'Flag', schema: FlagSchema},
      { name: 'Files', schema: FilesSchema },
      { name: 'Vspace', schema: VspaceSchema },
      { name: 'Update', schema: UpdateSchema}

    ]),InstitutionModule, HttpModule
  ],
  controllers: [OperationController],
  providers: [OperationService, ImageService, EmailService, InstitutionService, UsersService, CountryService, FlagService, FilesService, UpdateService],
})

export class OperationModule { }