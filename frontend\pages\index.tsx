//Import Library
import React from 'react';

//Import services/components
import Dashboard from './dashboard/Dashboard';
import { withTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

interface IndexProps {
  t: (key: string) => string;
  user: any;
  [key: string]: any;
}

const Index = (props: IndexProps) => {
  return <Dashboard t={props.t} user={props.user} {...props} />
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default withTranslation('common')(Index);
