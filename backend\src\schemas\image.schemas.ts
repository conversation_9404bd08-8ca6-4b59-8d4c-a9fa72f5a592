//Import Library
import * as mongoose from 'mongoose';
import * as mongoosePaginate from 'mongoose-paginate-v2';

export const ImageSchema = new mongoose.Schema({
  image: Buffer,
  name: { type: String, required: true },
  original_name: String,
  source: { type: String},
  is_temp: { type: Boolean, default: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

ImageSchema.plugin(mongoosePaginate);
