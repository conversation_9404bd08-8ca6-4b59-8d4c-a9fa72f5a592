.alphabetLists {
  column-count: 3;
  margin: 10px 0;
  margin-left: 10px;
  ul {
    padding: 0;
    margin: 0;
    list-style: none;
    li {
      padding: 0;
      margin: 0;
      a {
        color: #5d5d5d;
        &:hover {
          color: #0d8aed;
        }
      }
    }
  }
}
.countries-pagination {
  float: right;
  margin: 20px 0;
  ul {
    margin-bottom: 0;
    .page-item {
      .page-link {
        border: none;
        color: #5d5d5d;
        padding: 6px 0.75rem;
      }
      &.active {
        .page-link {
          background-color: #5d5d5d;
          color: #FFFFFF;
        }
      }
    }
  }
}

.countryMap {
  width: calc(100% - 350px);
}

.countryInfo {
  width: 350px;
  padding: 0 20px;

  h4 {
    margin: 10px 0 5px;
  }
}

.countryInfoDetails {
  margin: 25px 0 0;
}

.countryInfo-Item {
  display: flex;
  margin: 0 0 25px;

  span {
    padding: 0 0 0 10px;
    font-size: 18px;
    ;

    b {
      font-weight: 400;
      font-size: 32px;
      line-height: 22px;
      color: #2e3545;
    }
  }
}

.countryInfo-img {
  width: 53px;
  height: 53px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #fff;
  border-radius: 50%;
  background: #212e3d;
  background: radial-gradient(ellipse at center, #212e3d 0%, #131e2b 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#212e3d', endColorstr='#131e2b', GradientType=1);
  position: relative;
  margin: 0 8px 0 0;

  &::after {
    content: "";
    width: 59px;
    height: 59px;
    background: #ddd;
    position: absolute;
    z-index: -99;
    border-radius: 50%;
    top: -4px;
    left: -4px;
  }
}

.countryBtn {
  background: #3f51b5;
  text-transform: uppercase;
  font-size: 17px;
  font-weight: 500;
}

.countryAccordion {
  padding-top: 25px;
  padding-bottom: 25px;

  .card {
    border: none;
    margin: 25px 0;
  }

  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;

    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }

    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }

    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;

      svg {
        font-size: 16px;
      }
    }
  }
}

.map-badge-count {
  z-index: 99;
  position: relative;
  width: 60px;
  height: 60px;
  background: #FFFFFF;
  box-shadow: 2px 2px 6px #999999;
  border-radius: 50%;
  text-align: center;
  float: right;
  margin-right: 60px;
  margin-top: -90px;
}

.map-badge-count span {
  font-family: 'FontAwesome', sans-serif;
  color: #555555;
  font-size: 32px;
  font-weight: 600;
  line-height: 60px;
}