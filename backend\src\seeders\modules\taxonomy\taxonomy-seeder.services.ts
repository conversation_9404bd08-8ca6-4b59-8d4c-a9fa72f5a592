
// /**
//  * Service dealing with syndrome based operations.
//  *
//  * @class
//  */
// @Injectable()
// export class SyndromeSeederService {

//   constructor(
//     @InjectModel('taxonomy') private taxonomyModel: Model<TaxonomyInterface>
//   ) {}

//   /**
//    * Seed all syndrome.
//    *
//    * @function
//    */
//   create(): Array<Promise<TaxonomyInterface>> {
//     return Object.keys(taxonomies).map(async (d) => {
//       const syndrome: TaxonomyInterface = taxonomies[d];
//       return await this.taxonomyModel
//         .findOne({ title: syndrome.title })
//         .exec()
//         .then(async dbSyndrome => {
//           // We check if a syndrome already exists.
//           // If it does don't create a new one.
//           if (dbSyndrome) {
//             return Promise.resolve(null);
//           }
//           return Promise.resolve(
//             await this.taxonomyModel.create(syndrome),
//           );
//         })
//         .catch(error => Promise.reject(error));
//     });
//   }
// }