//Import Library
import React from "react";
import { Form, Container, Row, Col } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

//TOTO refactor
interface ContactFormProps {
  onHandleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  telephoneNo: string;
  mobileNo: string;
}

const ContactForm = (props: ContactFormProps): React.ReactElement => {
    const { t } = useTranslation('common');
    const { onHandleChange, telephoneNo, mobileNo } = props;

    return (
        <Container className="formCard" fluid>
            <Row>
                <Col>
                    <Form.Group>
                        <Form.Label className="required-field">{t("Updates.TelephoneNo")}</Form.Label>
                        <Form.Control
                            type="number"
                            name="telephoneNo"
                            placeholder={t("Updates.TelephoneNumber")}
                            required
                            value={telephoneNo}
                            onChange={onHandleChange}
                        />
                        <Form.Control.Feedback type="invalid">
                            {t("Updates.PleaseTelephoneNumber")}
                        </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group>
                        <Form.Label className="required-field">{t("Updates.MobileNo")}</Form.Label>
                        <Form.Control
                            type="number"
                            name="mobileNo"
                            placeholder={t("Updates.MobileNumber")}
                            required
                            value={mobileNo}
                            onChange={onHandleChange}
                        />
                        <Form.Control.Feedback type="invalid">{t("Updates.PleaseProvideMobile")}</Form.Control.Feedback>
                    </Form.Group>
                </Col>
            </Row>
        </Container>
    );
};

export default ContactForm;
