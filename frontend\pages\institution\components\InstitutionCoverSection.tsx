//Import Library
import React from "react";

//Import services/components
import InstitutionCoverSectionContent from "./InstitutionCoverSectionContent";

interface InstitutionCoverSectionProps {
  prop: any;
  imageLoading: boolean;
  institutionData: {
    header?: {
      _id: string;
    };
    title: string;
    description: string;
    website?: string;
    telephone?: string;
    dial_code?: string;
    address?: {
      line_1?: string;
      line_2?: string;
      city?: string;
      postal_code?: string;
      country?: {
        title: string;
      };
    };
  };
  editAccess: boolean;
  focalPoints: any[];
}

const InstitutionCoverSection = (props: InstitutionCoverSectionProps) => {
    return (
        <>
            <div className="institution-image-block">
                {!props.imageLoading &&
                    props.institutionData &&
                    props.institutionData.header &&
                    props.institutionData.header._id ? (
                    <img
                        className="institution-image-cover"
                        src={`${process.env.API_SERVER}/image/show/${props.institutionData.header._id}`}
                    />
                ) : (
                    ""
                )}
                {props.imageLoading ? (
                    <div className="institution-imageLoader">
                        <div className="spinner-border text-primary" />
                    </div>
                ) : (
                    ""
                )}
                {!props.imageLoading && props.institutionData && !props.institutionData.header ? (
                    <img
                        className="institution-image-cover"
                        src="/images/rki_institute.7cb751d6.jpg"
                    />
                ) : (
                    ""
                )}
                <div className="institution-image-inner-content">
                    <InstitutionCoverSectionContent
                        institutionData={props.institutionData}
                        routeData={props.prop}
                        prop={props.prop}
                        editAccess={props.editAccess}
                        focalPoints={props.focalPoints}
                    />
                </div>
            </div>
        </>
    )
}

export default InstitutionCoverSection;