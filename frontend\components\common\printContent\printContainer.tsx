const printContainer = (divToPrint: string) => {
  if (divToPrint) {
    const loadmore = document.getElementsByClassName("updates-load-more");
    if (loadmore.length > 0 && loadmore[0]) {
      loadmore[0].remove();
    }
    const printElement = document.getElementById(divToPrint);
    if (printElement) {
      const printContents = printElement.innerHTML;
      const originalContents = document.body.innerHTML;
      window.print();
    }
  }
}

export default printContainer;
