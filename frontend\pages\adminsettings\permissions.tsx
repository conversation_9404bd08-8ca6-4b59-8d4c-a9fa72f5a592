//Import services/components
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

const create = "create:any";
export const canAddAreaOfWork = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.area_of_work && state.permissions.area_of_work[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddAreaOfWork',
});

export const canAddCountry = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.country && state.permissions.country[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddCountry',
});

export const canAddDeploymentStatus = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.deployment_status && state.permissions.deployment_status[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddDeploymentStatus',
});

export const canAddEventStatus = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.event_status && state.permissions.event_status[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddEventStatus',
});

export const canAddExpertise = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.expertise && state.permissions.expertise[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddExpertise',
});

export const canAddFocalPointApproval = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddFocalPointApproval',
});

export const canAddVspaceApproval = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.institution_focal_point && state.permissions.institution_focal_point[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddVspaceApproval',
});
export const canAddHazards = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.hazard && state.permissions.hazard[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddHazards',
});

export const canAddHazardTypes = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.hazard_type && state.permissions.hazard_type[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddHazardTypes',
}); 

export const canAddOrganisationApproval = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.institution && state.permissions.institution[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddOrganisationApproval',
}); 

export const canAddOrganisationNetworks = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.institution_network && state.permissions.institution_network[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddOrganisationNetworks',
});

export const canAddOrganisationTypes = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.institution_type && state.permissions.institution_type[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddOrganisationTypes',
});

export const canAddOperationStatus = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.operation_status && state.permissions.operation_status[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddOperationStatus',
});

export const canAddProjectStatus = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.project_status && state.permissions.project_status[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddProjectStatus',
});

export const canAddRegions = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.region && state.permissions.region[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddRegions',
});

export const canAddRiskLevels = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.risk_level && state.permissions.risk_level[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddRiskLevels',
});

export const canAddSyndromes = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.syndrome && state.permissions.syndrome[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddSyndromes',
});

export const canAddUpdateTypes = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.update_type && state.permissions.update_type[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddUpdateTypes',
});

export const canAddUsers = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.users && state.permissions.users[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddUsers',
});

export const canAddWorldRegion = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.worl_region && state.permissions.worl_region[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddWorldRegion',
});

export const canAddLandingPage = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.landing_page && state.permissions.landing_page[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddLandingPage',
});

export const canAddContent = connectedAuthWrapper({
  authenticatedSelector: (state) => {
    if (state.permissions && state.permissions.operation && state.permissions.operation[create] && state.permissions.project && state.permissions.project[create]&& state.permissions.event && state.permissions.event[create]&& state.permissions.vspace && state.permissions.vspace[create]&& state.permissions.institution && state.permissions.institution[create]&& state.permissions.update && state.permissions.update[create]) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddContent',
});

export default canAddAreaOfWork;