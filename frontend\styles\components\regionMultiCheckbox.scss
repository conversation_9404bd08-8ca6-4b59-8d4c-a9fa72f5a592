.regions-multi-checkboxes {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  margin: 20px 0 0;
  padding-left: 30px;
  .form-check {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
    margin: 0 0 15px;
  }
  .form-check-label{
    margin-left: 10px;
  }
}

button.btn-plain {
  background: transparent;
  border: none;
  padding: 0;
  height: 20px;
  font-weight: 500;
  color: #202020;
  &:hover, &:focus, &:active {
    color: #595959 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
}

// Responsive
@media screen and (max-width: 767px) {
  .main-container {
    .regions-multi-checkboxes .form-check {
      max-width: 50%;
      flex-basis: 50%;
    }
  }
}
@media screen and (max-width: 575px){
  .regions-multi-checkboxes{
    .form-check-label{
      font-size: 14px;
    }
  }
}