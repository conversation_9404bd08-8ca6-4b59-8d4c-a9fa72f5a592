//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { TextInput } from "../../../components/common/FormValidation";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const RisklevelForm = (props: any) => {
    const _initialrisklevel = {
        title: "",
        level: "",
    };

    const [initialVal, setInitialVal] = useState(_initialrisklevel);

    const editform = props.routes && props.routes[0] === "edit_risklevel" && props.routes[1];
    const { t } = useTranslation('common');

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialrisklevel);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const toUppercase = (_str) => {
        if (!_str) _str = "";
        return _str.charAt(0).toUpperCase() + _str.slice(1);
    };

    const handleSubmit = async (event, values) => {
        event.preventDefault();
        // Use Formik values if available, otherwise fall back to initialVal
        const formValues = values || initialVal;
        const obj = {
            title: formValues.title.trim(),
            level: parseInt(formValues.level),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.RiskLevel.updatesuccess";
            response = await apiService.patch(`/risklevel/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.RiskLevel.success";
            response = await apiService.post("/risklevel", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg))
            Router.push("/adminsettings/risklevel");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"))
            } else {
                toast.error(toUppercase(response));
            }
        }
    };

    useEffect(() => {
        const risklevelParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getRisklevelData = async () => {
                const response = await apiService.get(`/risklevel/${props.routes[1]}`, risklevelParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getRisklevelData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.RiskLevel.Risklevel")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.RiskLevel.Risklevelname")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => value.trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.RiskLevel.add"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.RiskLevel.Risklevelvalue")}
                                        </Form.Label>
                                        <TextInput
                                            min="0"
                                            type="number"
                                            name="level"
                                            id="level"
                                            required
                                            value={initialVal.level}
                                            errorMessage={{
                                                validator: t("adminsetting.RiskLevel.value"),
                                                min: t("adminsetting.RiskLevel.minValue"),
                                                required: t("adminsetting.RiskLevel.value"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/risklevel`}
                                        >
                                        <Button variant="secondary">{t("Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default RisklevelForm;
