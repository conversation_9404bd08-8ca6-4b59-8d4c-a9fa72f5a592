//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { LanguageController } from './language.controller';
import { LanguageService } from './language.service';
// SCHEMAS
import { LanguageSchema } from '../../schemas/language.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'language', schema: LanguageSchema }
    ])
  ],
  controllers: [LanguageController],
  providers: [LanguageService],
})

export class LanguageModule { }