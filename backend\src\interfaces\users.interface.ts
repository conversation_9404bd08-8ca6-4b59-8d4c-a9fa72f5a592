export interface UsersInterface {
  username: string,
  email: string,
  password: string,
  role: object,
  roles: string[],
  institution: object,
  region: object,
  country: object,
  status: string,
  enabled: boolean,
  dataConsentPolicy: boolean,
  restrictedUsePolicy: boolean,
  acceptCookiesPolicy: boolean,
  withdrawConsentPolicy: boolean,
  medicalConsentPolicy: boolean,
  fullDataProtectionConsentPolicy: boolean,
  emailActivateToken: string,
  image: object
}
