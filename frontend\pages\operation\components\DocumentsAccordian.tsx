//Import services/components
import React, { useState } from "react";
import { Accordion, Card } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import DocumentTable from "../../../components/common/DocumentTable";
import { useTranslation } from 'next-i18next';

const DocumentsAccordian = (props) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);

    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("Documents")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                        <DocumentTable
                            loading={props.documentAccoirdianData.documentAccoirdianProps.loading}
                            sortProps={props.documentAccoirdianData.documentAccoirdianProps.sortProps}
                            docs={props.documentAccoirdianData.documentAccoirdianProps.Document || []}
                            docsDescription={props.documentAccoirdianData.operationData.doc_src}
                        />
                        <h6 className="mt-3">{t("DocumentsfromUpdates")}</h6>
                        <DocumentTable
                            loading={props.documentAccoirdianData.documentAccoirdianProps.loading}
                            sortProps={props.documentAccoirdianData.documentAccoirdianProps.sortUpdateProps}
                            docs={props.documentAccoirdianData.documentAccoirdianProps.updateDocument || []}
                            docsDescription={props.documentAccoirdianData.operationData.doc_src}
                        />
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
}

export default DocumentsAccordian;