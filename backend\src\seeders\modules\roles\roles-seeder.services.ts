//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { RolesInterface } from '../../../interfaces/roles.interface';
import { Roles } from "../../data/roles";

/**
 * Service dealing with syndrome based operations.
 *
 * @class
 */
@Injectable()
export class RolesSeederService {

  constructor(
    @InjectModel('Roles') private rolesModel: Model<RolesInterface>
  ) {}

  /**
   * Seed all syndrome.
   *
   * @function
   */
  create(): Array<Promise<RolesInterface>> {
    return Roles.map(async (role: RolesInterface) => {
      return await this.rolesModel
        .findOne({ title: role.title })
        .exec()
        .then(async dbRole => {
          // We check if a syndrome already exists.
          // If it does don't create a new one.
          if (dbRole) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.rolesModel.create(role),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}