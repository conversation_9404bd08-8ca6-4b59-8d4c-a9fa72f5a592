//Import Library
import React from "react";
import { Accordion } from "react-bootstrap";

//Import services/components
import CountryOrganisationAccordion from "./CountryOrganisationAccordion";
import CountryMediaGalleryAccordion from "./CountryMediaGalleryAccordion";
import CountryDocumentAccordion from "./CountryDocumentAccordion";
import DiscussionAccordion from "./DiscussionAccordion";
import canViewDiscussionUpdate from "../permission";

interface CountryAccordionSectionProps {
  prop: any;
  images: any[];
  imgSrc: string[];
  loading: boolean;
  updateSort: any;
  document: any[];
  docSrc: string[];
  updateDocumentSort: any;
  updateDocument: any[];
}

const CountryAccordionSection = (props: CountryAccordionSectionProps) => {

    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (
        <DiscussionAccordion {...props.prop} />
      ));

    return (
        <>
            <Accordion defaultActiveKey="0" className="countryAccordionNew">
                <CountryOrganisationAccordion {...props} />
            </Accordion>
            <Accordion className="countryAccordionNew">
                <CountryMediaGalleryAccordion {...props} />
            </Accordion>
            <Accordion className="countryAccordionNew">
                <CountryDocumentAccordion {...props} />
            </Accordion>
            <Accordion className="countryAccordionNew">
                <CanViewDiscussionUpdate {...props} />
            </Accordion>
        </>
    )
}

export default CountryAccordionSection;