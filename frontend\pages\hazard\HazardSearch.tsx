//Import Library
import { Container, Row, InputGroup, Form } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch
} from "@fortawesome/free-solid-svg-icons";

//Import services/components
import { useTranslation } from 'next-i18next';

interface HazardSearchProps {
  filterText: string;
  onFilter: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const HazardSearch = ({ filterText, onFilter }: HazardSearchProps) => {
  const { t } = useTranslation('common');

  return (
    <Container fluid className="p-0">
      <Row>
        <InputGroup >
          <Form.Control
            className="rounded"
            type="text"
            placeholder= {t("SearchHazards")}
            value={filterText}
            onChange={onFilter}
          />
          <div className="search-icon">
            <FontAwesomeIcon icon={faSearch} />
          </div>
        </InputGroup>

      </Row>
    </Container>
  );
};

export default HazardSearch;
