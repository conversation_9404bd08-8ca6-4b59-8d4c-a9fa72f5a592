//Import Library
import React from "react";
import Link from 'next/link';
import { Container, Row, Col, Button } from 'react-bootstrap';

//Import services/components
import UsersTable from "./UsersTable";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Users = (): React.ReactElement => {

  return (
    <Container className='users-page'>
      <Row className='page-header'>
        <h4>User List</h4>
      </Row>
      <Row>
        <Col xs={12}>
          <Link href='/users/[...routes]' as='/users/create' ><Button variant="secondary" size="sm">
            Add Users
          </Button></Link></Col>
      </Row>
      <Row className="mt-3">
        <Col xs={12}>
          <UsersTable />
        </Col>
      </Row>
    </Container>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Users;
