//Import Library
import Link from "next/link";
import moment from "moment";
import _ from "lodash";
import React, { useEffect, useState, useRef } from "react";
import { useRouter } from "next/router";

//Import services/components
import RKITable from "../../components/common/RKITable";
import apiService from "../../services/apiService";
import EventsFilter from "./EventsTableFilter";
import { useTranslation } from 'next-i18next';

const Hazards = (props) => {
  const { i18n } = useTranslation('common');
  const currentLang = i18n.language === "fr" ? "en" : i18n.language;
  const { hazards } = props;
  return (
    <ul>
      {hazards.map((item, index) => {
        {
          return item && item._id && item.title && item.title[currentLang] ? (
            <li key={index}>
              <Link href="/hazard/[...routes]" as={`/hazard/show/${item._id}`} >
                {item.title[currentLang].toString()}
              </Link>
            </li>
          ) : (
            ""
          );
        }
      })}
    </ul>
  );
};

function EventsTable(props) {
  const router = useRouter();
  const { t } = useTranslation('common');
  const { setEvents, selectedRegions } = props;
  const [filterText, setFilterText] = React.useState("");
  const [filterHazard, setFilterHazard] = React.useState("");
  const [resetPaginationToggle, setResetPaginationToggle] =
    React.useState(false);
  const [tabledata, setDataToTable] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [pageSort, setPageSort] = useState(null);

  const eventParams: any = {
    sort: { created_at: "desc" },
    lean: true,
    populate: [
      { path: "country", select: "coordinates title" },
      { path: "hazard_type", select: "title" },
      { path: "hazard", select: "title" },
    ],
    limit: perPage,
    page: 1,
    query: {},
    select:
      "-description -operation -world_region -country_regions -syndrome -status -laboratory_confirmed -officially_validated -rki_monitored -risk_assessment -date -more_info -images -user -created_at -updated_at",
  };

  const [evParams, setEvParams] = useState(eventParams);

  const columns = [
    {
      name: t("Events.table.EventId"),
      selector: "title",
      sortable: true,
      width: "20%",
      cell: (d) => (
        <Link href="/event/[...routes]" as={`/event/show/${d._id}`}>
          {d.title}
        </Link>
      ),
    },
    {
      name: t("Events.table.Country"),
      selector: "country",
      sortable: true,
      cell: (d) =>
        d.country && d.country.title ? (
          <Link
            href="/country/[...routes]"
            as={`/country/show/${d.country._id}`}
          >
            {d.country.title}
          </Link>
        ) : (
          ""
        ),
    },
    {
      name: t("Events.table.HazardType"),
      selector: "hazard_type",
      sortable: true,
      cell: (d) =>
        d.hazard_type && d.hazard_type.title ? d.hazard_type.title : "",
    },
    {
      name: t("Events.table.Hazard"),
      selector: "hazard",
      cell: (d) => <Hazards hazards={d.hazard} />,
    },
    {
      name: t("Events.table.InfoReceivedon"),
      selector: "created_at",
      sortable: true,
      cell: (d) => moment(d.start_date).format("M/D/Y"),
    },
    {
      name: t("Events.table.Lastupdated"),
      selector: "updated_at",
      sortable: true,
      cell: (d) => moment(d.updated_at).format("M/D/Y"),
    },
  ];

  const getEventsData = async (eventParamsinit) => {
    setLoading(true);

    // Check if there's a country query
    if (router.query && router.query.country) {
      eventParamsinit.query["country"] = router.query.country;
    }

    // Handle selectedRegions correctly
    if (selectedRegions === null) {
      // No regions selected: don't filter by region
      delete eventParamsinit.query["world_region"];
    } else if (selectedRegions.length === 0) {
      // Empty region selection: force zero results
      eventParamsinit.query["world_region"] = ["__NO_MATCH__"];
    } else {
      // Filter by selected regions
      eventParamsinit.query["world_region"] = selectedRegions;
    }

    // Fetch data from the API
    const response = await apiService.get("/event", eventParamsinit);
    if (response && Array.isArray(response.data)) {
      setDataToTable(response.data);
      setEvents(response.data);
      setTotalRows(response.totalCount);
    }

    setLoading(false);
  };


  const handlePageChange = (page) => {
    eventParams.limit = perPage;
    eventParams.page = page;

    // Add hazard type filter
    filterHazard &&
      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });

    // Add sorting
    pageSort && (eventParams.sort = pageSort.sort);

    // Get data with updated filters
    getEventsData(eventParams);
    setPageNum(page);
  };


  const handlePerRowsChange = async (newPerPage, page) => {
    eventParams.limit = newPerPage;
    eventParams.page = page;
    setLoading(true);

    // Handle selected region in pagination
    if (router.query && router.query.country) {
      eventParams.query["country"] = router.query.country;
    }

    // Handle selected region filter
    if (selectedRegions === null) {
      delete eventParams.query["world_region"];
    } else if (selectedRegions.length === 0) {
      eventParams.query["world_region"] = ["__NO_MATCH__"];
    } else {
      eventParams.query["world_region"] = selectedRegions;
    }

    // Apply hazard filter
    filterHazard &&
      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });

    // Apply sorting
    pageSort && (eventParams.sort = pageSort.sort);

    // Fetch data from the API
    const response = await apiService.get("/event", eventParams);
    if (response && Array.isArray(response.data)) {
      setDataToTable(response.data);
      setEvents(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }

    setPageNum(page);
  };


  useEffect(() => {
    evParams.page = 1;
    getEventsData(evParams);
  }, [selectedRegions, router]);

  useEffect(() => {
    getEventsData(evParams);
  }, [evParams]);


  const handleSort = async (column, sortDirection) => {
    setLoading(true);
    eventParams.sort = {
      [column.selector]: sortDirection,
    };
    filterHazard &&
      (eventParams.query = { ...eventParams.query, hazard_type: filterHazard });
    filterText !== "" &&
      (eventParams.query = { ...eventParams.query, title: filterText });

    await getEventsData(eventParams);
    setPageSort(eventParams);
    setLoading(false);
  };

  const sendQuery = (q, page) => {
    if (q) {
      evParams.query["title"] = q;
      evParams.page = page;
      setEvParams({ ...evParams });
    } else {
      delete evParams.query.title;
      setEvParams({ ...evParams });
    }
  };

  const handleSearchTitle = useRef(
    _.debounce(
      (q, page) => sendQuery(q, page),
      Number(process.env.SEARCH_DEBOUNCE_TIME) || 300
    )
  ).current;

  const subHeaderComponentMemo = React.useMemo(() => {
    const handleClear = () => {
      if (filterText) {
        setResetPaginationToggle(!resetPaginationToggle);
        setFilterText("");
      }
    };

    const handleFilterHazardType = (hazardType) => {
      setFilterHazard(hazardType);
      if (hazardType) {
        evParams.query["hazard_type"] = hazardType;
        setEvParams({ ...evParams });
      } else {
        delete evParams.query.hazard_type;
        setEvParams({ ...evParams });
      }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setFilterText(e.target.value);
      handleSearchTitle(e.target.value, pageNum);
    };

    return (
      <EventsFilter
        onFilter={handleChange}
        onFilterHazardChange={(e) => handleFilterHazardType(e.target.value)}
        onClear={handleClear}
        filterText={filterText}
        filterHazard={filterHazard}
      />
    );
  }, [filterText, resetPaginationToggle, filterHazard, selectedRegions]);

  return (
    <RKITable
      columns={columns}
      data={tabledata}
      totalRows={totalRows}
      loading={loading}
      subheader
      persistTableHead
      onSort={handleSort}
      sortServer
      pagServer={true}
      subHeaderComponent={subHeaderComponentMemo}
      handlePerRowsChange={handlePerRowsChange}
      handlePageChange={handlePageChange}
    />
  );
}

export default EventsTable;
