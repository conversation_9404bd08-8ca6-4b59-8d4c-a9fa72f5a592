//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PassportModule } from '@nestjs/passport';

//Import services/components
import { AuthService } from './auth.service';
import { LocalStrategy } from './local.strategy';
import { SessionGuard } from './session-guard';
import { SessionAuthGuard } from './session-auth-guard';
import { UsersModule } from '../users/users.module';
import { EmailService } from '../email.service';
import { ForgottenPasswordSchema } from '../schemas/forgottenpassword.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'ForgottenPassword', schema: ForgottenPasswordSchema }
    ]),
    PassportModule.register({defaultStrategy: 'local', session: true}),
    UsersModule,
  ],
  providers: [AuthService, LocalStrategy, EmailService, SessionAuthGuard, SessionGuard],
  exports: [AuthService],
})
export class AuthModule {}
