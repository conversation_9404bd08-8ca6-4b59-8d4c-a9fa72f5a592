//Import Library
import React, { useEffect, useState } from "react";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Accordion, Card } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';
import ReadMoreContainer from "../../../components/common/readMore/readMore";
const icons = {
    Low: "risk0",
    Medium: "risk1",
    High: "risk2",
    "Very High": "risk3",
};

interface RiskAssessmentAccordionProps {
  risk_assessment: {
    country?: {
      title: string;
    };
    description?: string;
  };
}

const RiskAssessmentAccordion = (props: RiskAssessmentAccordionProps) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);

    const { risk_assessment } = props;

    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("Events.show.RiskAssessment")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    {risk_assessment.country ? (
                        <div>
                            {risk_assessment_Func(risk_assessment, t)}

                            {risk_assessment_func(props)}
                        </div>
                    ) : null}
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
};

export default RiskAssessmentAccordion;

function risk_assessment_func(eventData: any) {
    return (
        <div className="mt-4">
            <ReadMoreContainer
                description={
                    eventData && eventData.risk_assessment.description ? eventData.risk_assessment.description : ""
                }
            />
        </div>
    );
}

function risk_assessment_Func(risk_assessment: any, t: (key: string) => string) {
    return (
        <div className="riskDetails">
            {risk_assessment.country ? (
                <div className="riskItems">
                    <div className={`riskIcon ${icons[risk_assessment.country.title]}`}>
                        <img src="/images/event_country.png" width="30" height="30" alt="Risk Assessment Info" />
                    </div>
                    <div className="riskInfo">
                        <h5>{t("Events.show.Country")}</h5>
                        <h4>{risk_assessment.country.title}</h4>
                    </div>
                </div>
            ) : (
                <></>
            )}
            {risk_assessment.region ? (
                <div className="riskItems">
                    <div
                        className={`riskIcon ${
                            risk_assessment && risk_assessment.region ? icons[risk_assessment.region.title] : ""
                        }`}
                    >
                        <img src="/images/event_region.png" width="35" height="26" alt="Risk Assessment Info" />
                    </div>
                    <div className="riskInfo">
                        <h5>{t("Events.show.Region")}</h5>
                        <h4>{risk_assessment && risk_assessment.region ? risk_assessment.region.title : ""}</h4>
                    </div>
                </div>
            ) : (
                <></>
            )}
            {risk_assessment.international ? (
                <div className="riskItems">
                    <div
                        className={`riskIcon ${
                            risk_assessment && risk_assessment.international
                                ? icons[risk_assessment.international.title]
                                : ""
                        }`}
                    >
                        <img src="/images/event_international.png" width="38" height="38" alt="Risk Assessment Info" />
                    </div>
                    <div className="riskInfo">
                        <h5>{t("Events.show.International")}</h5>
                        <h4>
                            {risk_assessment && risk_assessment.international
                                ? risk_assessment.international.title
                                : ""}
                        </h4>
                    </div>
                </div>
            ) : (
                <></>
            )}
        </div>
    );
}
