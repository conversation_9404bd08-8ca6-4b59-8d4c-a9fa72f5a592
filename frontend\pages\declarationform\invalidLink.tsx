//Import Library
import React from 'react';
import { <PERSON>, Button, Container } from "react-bootstrap";
import {
    faArrowCircleLeft,
    faExclamationTriangle
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Router from "next/router"

const InvaidLink = () => {

    const clickHandler = () => {
        Router.push("/home")
    }

    return (
        <div className="d-flex justify-content-center align-content-center">
            <Card className="text-center m-4 p-5" style={{ boxShadow: "0 10px 20px #777", borderRadius: "10px", width: '50vw' }}>
                <div className="text-center pt-2">
                    <FontAwesomeIcon
                        icon={faExclamationTriangle}

                        color="indianRed"
                        style={{
                            background: "#d6deec",
                            padding: "60px",
                            borderRadius: "50%",
                            width: "300px",
                            height: "300px",

                            fontSize: "100px"
                        }}
                    />
                </div>
                <Card.Body>
                    <h4 ><b>Huh! Looks like invalid link.</b></h4>
                    <Button className="mt-3" variant="danger" onClick={clickHandler}>
                        <FontAwesomeIcon
                            icon={faArrowCircleLeft}
                            color="white"
                        />
                    &nbsp;&nbsp;Back to RKI Home</Button>
                </Card.Body>


            </Card>
        </div>
    )
}



export default InvaidLink;