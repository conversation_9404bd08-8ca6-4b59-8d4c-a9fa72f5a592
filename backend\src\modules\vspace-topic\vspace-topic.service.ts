//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { VspaceTopicInterface } from '../../interfaces/vspace-topic.interface';
import { CreateVspaceTopicDto } from './dto/create-vspace-topic.dto';
import { UpdateVspaceTopicDto } from './dto/update-vspace-topic.dto';

const CouldnotfindVspaceTopic = 'Could not find VspaceTopic.'
@Injectable()
export class VspaceTopicService {
  constructor(
    @InjectModel('VspaceTopic') private vspaceTopicModel: Model<VspaceTopicInterface>
  ) { }

  async create(createVspaceTopicDto: CreateVspaceTopicDto): Promise<VspaceTopicInterface> {
    const createdVspaceTopic = new this.vspaceTopicModel(createVspaceTopicDto);
    return createdVspaceTopic.save();
  }

  async findAll(query): Promise<VspaceTopicInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.vspaceTopicModel.paginate(_filter, options);
  }

  async get(vspaceTopicId): Promise<VspaceTopicInterface[]> {
    let _result;
    try {
      _result = await this.vspaceTopicModel.findById(vspaceTopicId).exec();
    } catch (error) {
      throw new NotFoundException(CouldnotfindVspaceTopic);
    }
    if (!_result) {
      throw new NotFoundException(CouldnotfindVspaceTopic);
    }
    return _result;
  }

  async update(vspaceTopicId: any, updateVspaceTopicDto: UpdateVspaceTopicDto) {
    const getById: any = await this.vspaceTopicModel.findById(vspaceTopicId).exec();
    const updatedData = new this.vspaceTopicModel(updateVspaceTopicDto);
    try {
      Object.keys(updateVspaceTopicDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update VspaceTopic.');
    }
    return getById;
  }

  async delete(vspaceTopicId: string) {
    const result = await this.vspaceTopicModel.deleteOne({ _id: vspaceTopicId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(CouldnotfindVspaceTopic);
    }
  }
}
