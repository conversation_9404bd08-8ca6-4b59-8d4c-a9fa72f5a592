//Import Library
import { Controller, Get, Query, Param, Delete, UseGuards, Req } from '@nestjs/common';
import { Request } from 'express';

//Import services/components
import { VspaceRequestSubscribersService } from "./vspace-request-subscribers.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('vspace-request-subscribers')
@UseGuards(SessionGuard)
export class VspaceRequestSubscribersController {

  constructor(
    private readonly _vspaceRequestSubscribersService: VspaceRequestSubscribersService
  ) { }

  @Get('getRequestedByMe')
  getRequestedByMe(@Query() query: any, @Req() request: Request) {
    const user: any = request.user;
    let _filter = {};
    try {
      _filter = query.query ? query.query : {};
      _filter['requested_by'] = user._id;
      query.query = _filter;
    } catch (e) {
      // Fallback to basic filter if query parsing fails
      _filter = { requested_by: user._id };
      query.query = _filter;
    }
    return this._vspaceRequestSubscribersService.findAll(query);
  }

  @Get('getRequestedToMe')
  getRequestedToMe(@Query() query: any, @Req() request: Request) {
    const user: any = request.user;
    let _filter = {};
    try {
      _filter = query.query ? query.query : {};
      _filter['requested_to'] = user._id;
      query.query = _filter;
    } catch (e) {
      // Fallback to basic filter if query parsing fails
      _filter = { requested_to: user._id };
      query.query = _filter;
    }
    return this._vspaceRequestSubscribersService.findAll(query);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    const deletedData = this._vspaceRequestSubscribersService.delete(id);
    return deletedData;
  }
}
