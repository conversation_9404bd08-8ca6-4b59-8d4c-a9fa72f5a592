//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { CategoryInterface } from "src/interfaces/category.interface";
import { categories } from "../../data/category";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class CategorySeederService {

  constructor(
    @InjectModel('Category') private categoryModel: Model<CategoryInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<CategoryInterface>> {
    return categories.map(async (cate: any) => {
      return await this.categoryModel
        .findOne({ title: cate.title })
        .exec()
        .then(async dbCategory => {
          // We check if a category already exists.
          // If it does don't create a new one.
          if (dbCategory) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.categoryModel.create(cate),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}