.vspace {
  width: 100%;
  margin: 0px;
  min-width: 100%;
  .page-header {
    padding: 15px 10px;
  }
  .searchbar {
    position: relative;
    .searchInput {
      padding-right: 40px;
    }
    .search_icon {
      color: #6a6a6a;
      position: absolute;
      top: 7px;
      right: 14px;
    }
  }
}
.createVSpace {
  display: flex;
  justify-content: flex-end;
  button {
    background: #2caafd;
    font-weight: 500;
  }
}

.vButton{
  width:150px
}


.subscriptionBlock {
  margin: 0 -23px;
  padding: 10px 20px 30px;
  .header-block {
    padding: 15px 0px 25px;
  }
}

.publicVSpace {
  background: #f5f9fc;
  .header-block h6{
    border: none;
    span {
      background: transparent;
    }
  }
}

.popButton .rki-carousel-card{
  &:hover {
    cursor: pointer;
    transition: all 0.6s;
    .infoCard.card {
      border-top: 3px solid #ddd;
    }
    .hover-btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      visibility: visible;
      background: #fff;
      z-index: 9;
      padding: 12px 20px;
      color: #202020;
      font-size: 17px;
      box-shadow: 0px 0px 15px rgba(97, 97, 97, 0.3);
      border-radius: 2px;
    }
    &:after {
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      background: rgba(201, 202, 206,.7);
      top: 0;
      border-radius: 4px;
    }
  }
  .hover-btn {
    visibility: hidden;
  }
}

.rki-carousel-card {
  margin: 0px 10px;
  z-index: 1;
  max-height: 160px;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  &:hover {
    cursor: pointer;
    .infoCard.card {
      border-top: 3px solid #2ca8ff;
    }
  }
  .infoCard.card {
    min-height: 160px;
    z-index: 0;
    background-color: transparent;
    border-top: 3px solid #ddd;
  }
  .infoCard .card-header {
    color: #444;
    font-weight: 400;
    border-bottom: 1px solid #eee;
    padding: 10px 20px;
    margin: 0 0 8px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .infoCard .card-body p {
    text-decoration: none;
    margin: 0 0 5px;
    color: #666;
    line-height: 22px;
    font-size: 15px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical; 
  }
}

//Vspace Show Page
.vspaceDetails {
  .jEDPQU {
    padding: 0 4px;
  }
}
.nodataFound {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border: 1px solid #f3f3f3;
  border-radius: 4px;
  margin: 25px 0 0;
  box-shadow: 1px 1px 9px rgba(150, 150, 150, 0.08) !important;
}
.vspaceCard {
  border-radius: 4px;
  border: 1px solid #eaeaea;
  box-shadow: 1px 1px 15px rgba(150, 150, 150, 0.2) !important;
  height: 100%;
  .rbc-calendar {
    height: calc(100% - 95px);
    margin: 20px 15px;
  }
}
.vspaceCalendar {
  height: 100%;
  padding: 2px 0 0;
  min-height: 400px;
}
.vspaceTitle {
  display: flex;
  justify-content: space-between;
  padding: 15px 15px 2px;
  border-bottom: 1px solid #dedede;
  margin: 0 0 15px;
}
.vspaceDesc, .vspaceInfo {
  padding: 10px 15px;
}


.vspaceAccordion {
  padding-top: 25px;
  padding-bottom: 25px;
  .card {
    border: none;
    margin: 25px 0;
  }
  .card-body {
    padding: 30px 10px;
  }
  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;
    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }
    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }
    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;
      svg {
        font-size: 16px;
      }
    }
  }
}

// Responsive 
@media screen and (max-width: 992px) {
  .vspaceCalendar {
    .rbc-calendar {
      .fas {
        position: absolute;
        top: 0px;
        &.fa-chevron-left{
          left: 0px;
        }
        &.fa-chevron-right{
          right: 0px;
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .main-container {
    .vspace {
      width: 100%;
      .createVSpace {
        margin: 20px 0 0;
      }
    }
    .vspaceCalendar {
      margin: 20px 0 0;
    }
    .vspaceTitle {
      display: block;
    }
  }
  .vspaceCalendar{
    .rbc-calendar {
      .fas {
        &.fa-chevron-right{
          top: -24px;
        }
      }
    }
  }
}