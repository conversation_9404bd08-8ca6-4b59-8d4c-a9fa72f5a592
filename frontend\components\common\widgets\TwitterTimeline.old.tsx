import dynamic from 'next/dynamic';

// Dynamically import the TwitterTimelineEmbed component, with SSR disabled
const TwitterTimelineEmbed = dynamic(() => import('react-twitter-embed').then(mod => mod.TwitterTimelineEmbed), { ssr: false });

const options = { height: 'calc(44vh - 46px)' };

const TwitterTimeline: React.FunctionComponent<{}> = () => {
  return (
    <div className='twitter__timeline'>
      <div className="timeline-Header timeline-InformationCircle-widgetParent" data-scribe="section:header">
        <h1 className="timeline-Header-title u-inlineBlock" data-scribe="element:title">
          Tweets 
          <span className="timeline-Header-byline" data-scribe="element:byline">&nbsp;by&nbsp;
            <a className="customisable-highlight" href="https://twitter.com/rki_de" target="_blank" title="‎@rki_de on Twitter">
              ‎@rki_de
            </a>
          </span>
        </h1>
      </div>
      <TwitterTimelineEmbed
          sourceType="profile"
  screenName="saurabhnemade"
  options={{height: 400}}
      />
    </div>
  );
};

export default TwitterTimeline;
