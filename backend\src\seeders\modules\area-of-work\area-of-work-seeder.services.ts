//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { AreaOfWorkInterface } from "src/interfaces/area-of-work.interface";
import { areaOfWorks } from "../../data/area-of-work";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class AreaOfWorkSeederService {

  constructor(
    @InjectModel('AreaOfWork') private areaOfWorkModel: Model<AreaOfWorkInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<AreaOfWorkInterface>> {
    return areaOfWorks.map(async (areaOfWork: AreaOfWorkInterface) => {
      return await this.areaOfWorkModel
        .findOne({ title: areaOfWork.title })
        .exec()
        .then(async dbAreaOfWork => {
          // We check if a language already exists.
          // If it does don't create a new one.
          if (dbAreaOfWork) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.areaOfWorkModel.create(areaOfWork),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}