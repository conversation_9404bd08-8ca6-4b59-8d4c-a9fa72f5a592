//Import Library
import { useRouter } from 'next/router';

//Import services/components
import Form from '../updates';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Router = () => {
  const router = useRouter()
  const routes: any = (router.query.routes || []);

  switch (routes[0]) {
    case 'add':
    case 'edit':
      return <Form router={router} />
    default:
      return null;
  }
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Router;
