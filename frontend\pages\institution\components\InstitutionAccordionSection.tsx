//Import Library
import React from "react";
import { Accordion } from "react-bootstrap";

//Import services/components
import DiscussionAccordion from "./DiscussionAccordion";
import { canViewDiscussionUpdate } from "../permission";
import MoreInfoAccordion from "./MoreInfoAccordion";
import MediaGalleryAccordion from "./MediaGalleryAccordion";

interface InstitutionAccordionSectionProps {
  institutionData: any;
  prop: any;
  activeProjects: any[];
  activeOperations: any[];
}

const InstitutionAccordionSection = (props: InstitutionAccordionSectionProps) => {
    const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => (
        <DiscussionAccordion {...props.prop} />
      ));

    return (
        <>
            <Accordion className="countryAccordionNew">
                <MoreInfoAccordion {...props} />
            </Accordion>
            <Accordion className="countryAccordionNew">
               <MediaGalleryAccordion {...props.institutionData} />
            </Accordion>
            <Accordion className="countryAccordionNew">
                <CanViewDiscussionUpdate />
            </Accordion>
        </>
    )
}

export default InstitutionAccordionSection;