//Import Library
import Link from "next/link";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const InstitutionTypeTable = (_props: any) => {
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectInstitutionType, setSelectInstitutionType] = useState({});
    const modalHide = () => setModal(false);
    const { t } = useTranslation('common');

    const columns = [
        {
            name: t("Title"),
            selector: "title",
        },
        {
            name: t("action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_institution_type/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];
    const institutionTypeParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    useEffect(() => {
        getInstitutionTypeData();
    }, []);

    const getInstitutionTypeData = async () => {
        setLoading(true);
        const response = await apiService.get("/institutiontype", institutionTypeParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        institutionTypeParams.limit = perPage;
        institutionTypeParams.page = page;
        getInstitutionTypeData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        institutionTypeParams.limit = newPerPage;
        institutionTypeParams.page = page;
        setLoading(true);
        const response = await apiService.get("/institutiontype", institutionTypeParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/institutiontype/${selectInstitutionType}`);
            getInstitutionTypeData();
            setModal(false);
            toast.success(t("adminsetting.Organisationtypes.Table.orgTypeDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.Organisationtypes.Table.errorDeletingOrgType"));
        }
    };

    const userAction = async (row) => {
        setSelectInstitutionType(row._id);
        setModal(true);
    };

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.Organisationtypes.Delete")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.Organisationtypes.sure")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};
export default InstitutionTypeTable;
