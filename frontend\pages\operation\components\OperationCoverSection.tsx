//Import Library
import React from "react";
import { Col, Row } from "react-bootstrap";

//Import services/components
import OperationHeader from "./OperationInfo";
import ReadMoreContainer from "../../../components/common/readMore/readMore";
import ShowMapContainer from "../../../components/common/maps/ShowMapContainer";
import OperationPartnersAndActivities from "./OperationStats";



interface OperationCoverSectionProps {
  operationData: {
    _id: string;
    title: string;
    description: string;
    country?: {
      coordinates?: Array<{
        latitude: string;
        longitude: string;
      }>;
    };
  };
  routeData: any;
  editAccess: boolean;
}

const OperationCoverSection = (props: OperationCoverSectionProps) => {
    return (
        <>
            <Row>
                <Col className="ps-md-0" md={7}>
                    <OperationHeader operation={props.operationData} routeData={props.routeData} editData={props.editAccess} />
                    <ReadMoreContainer description={props.operationData.description} />
                    <OperationPartnersAndActivities operation={props.operationData} />
                </Col>
                <Col className="pe-md-0" md={5}>
                    <ShowMapContainer mapdata={props.operationData} />
                </Col>
            </Row>
        </>
    )
}

export default OperationCoverSection;