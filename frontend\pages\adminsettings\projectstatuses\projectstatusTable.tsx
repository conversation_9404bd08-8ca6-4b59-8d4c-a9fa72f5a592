//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const ProjectstatusTable = (_props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectProjectstatus, setSelectProjectstatus] = useState({});
    
    const projectstatusParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    const columns = [
        {
            name: t("adminsetting.ProjectStatus.Title"),
            selector: "title",
        },
        {
            name: t("adminsetting.ProjectStatus.Action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_projectstatus/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];

    const getProjectstatusData = async () => {
        setLoading(true);
        const response = await apiService.get("/projectstatus", projectstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        projectstatusParams.limit = perPage;
        projectstatusParams.page = page;
        getProjectstatusData();
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        projectstatusParams.limit = newPerPage;
        projectstatusParams.page = page;
        setLoading(true);
        const response = await apiService.get("/projectstatus", projectstatusParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const userAction = async (row) => {
        setSelectProjectstatus(row._id);
        setModal(true);
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/projectstatus/${selectProjectstatus}`);
            getProjectstatusData();
            setModal(false);
            toast.success(t("adminsetting.ProjectStatus.Table.projectStatusDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.ProjectStatus.Table.errorDeletingProjectStatus"));
        }
    };

    const modalHide = () => setModal(false);

    useEffect(() => {
        getProjectstatusData();
    }, []);

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.ProjectStatus.DeleteProjectstatus")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.ProjectStatus.Areyousurewanttodeletethisprojectstatus")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.ProjectStatus.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.ProjectStatus.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};

export default ProjectstatusTable;
