//Import Library
import React, { useEffect, useState } from "react";
import { Container, <PERSON>, Col, Card, Button, Accordion } from 'react-bootstrap';
import { useRouter } from 'next/router';
import moment from 'moment';
import 'moment/locale/de';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faMinus, faPen, faUserCog } from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";
import _ from "lodash";

//Import services/components
import apiService from "../../services/apiService";
import Discussion from "../../components/common/disussion";
import ReadMoreContainer from "../../components/common/readMore/readMore";
import { useTranslation } from 'next-i18next';
import UpdatePopup from "../../components/updates/UpdatePopup";
import VspaceCalendarEvents from './VirtualspaceCalendarEvents';
import Bookmark from "../../components/common/Bookmark";
import { canEditVspace, canViewDiscussionUpdate} from "./permission";
import VspaceMonitoringMembers from "./VirtualspaceMonitoringMembers";
import VspaceSubscribeRequestUsers from "./VirtualspaceSubscribeRequestUsers";
import VspaceAccordianSection from "./VirtualSpaceAccordionSection";
const visibility = {
  true: 'Public - Accessible to all site users',
  false: 'Private - Accessible only to group members'
};
const columns = [
  {
    name: 'User Name',
    selector: "username",
    sortable: true
  },
  {
    name: 'Email',
    selector: "email",
    sortable: true
  }
];


const ViewVSpace = (props: any) => {
  const { t ,i18n } = useTranslation('common');
  const [vspaceDetails, setVspace] = useState({
    description: "",
    owner: ""
  });
  const currentLang = i18n.language === 'fr' ? 'en' : i18n.language;
  const [vspaceRequest, setVspaceRequest] = useState([]);
  const [subscribers, setSubscribers] = useState([]);
  const [document, setDocument] = useState([]);
  const [docSrc] = useState([]);
  const [ _moment] =  useState(currentLang);
  const [calenderEvents, setEvents]: any = useState([]);
  const [vSpaceDocs, setVspaceDocs]: any = useState([]);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [vSpaceLoading, setVspaceLoading] = useState(false);
  const router = useRouter();
  const routes: any = router.query.routes || [];

  const vSpaceSort = (data: any) => {
    vSpaceParams.sort = {
      [data.columnSelector] : data.sortDirection
    }
    fetchVspaceFiles();
  }

  const updateSort = (data: any) => {
    updateDocParams.sort = {
      [data.columnSelector] : data.sortDirection
    }
    fetchUpdateDoc();
  }

  const vSpaceParams: any = {
    sort: { doc_created_at: "dsc" },
    Doctable: true
  };

  const updateDocParams: any = {
    sort: { doc_created_at: "dsc" },
    collation: "en",
    updateDoctable: true
  };


  const fetchUpdateDoc = async() => {
    const _documents: any[] = [];
    setUpdateLoading(true);
    const _updateDoc = await apiService.get(`/vspace/${routes[1]}`, updateDocParams);
      if(_updateDoc && _updateDoc.data && Array.isArray(_updateDoc.data) && _updateDoc.data.length >= 1){
      _updateDoc.data.forEach((element: any, index: any) => {
        element.document && element.document.length > 0 && element.document.map((ele: any, i: any) => {
          const description = element.document[i].docsrc;
          ele.description = description;
          _documents.push(ele);
        });
      });
    setDocument(_documents);
    }
    setUpdateLoading(false);
  }

  const  fetchVspaceFiles = async() => {
    const _documents: any[] = [];
    setVspaceLoading(true);
    const vSpaceFiles = await apiService.get(`/vspace/${routes[1]}`, vSpaceParams);
    if(vSpaceFiles && vSpaceFiles.data && Array.isArray(vSpaceFiles.data) && vSpaceFiles.data.length >=1) {
    vSpaceFiles.data.forEach((element: any, index: any) => {
      element.document && element.document.length > 0 && element.document.map((ele: any, i: any) => {
          const description = element.document[i].docsrc;
          ele.description = description;
          _documents.push(ele);
        });
      });
      setVspaceDocs(_documents);
    }
    setVspaceLoading(false);
  }

  const fetchVspace = async () => {
    try {
      const dbvspaceDetails = await apiService.get(`/vspace/${routes[1]}`);
      const dbrequestVspace = await apiService.get(`/vspace-request-subscribers/getRequestedToMe`, { query: { vspace: routes[1] }, select: "-vspace -requested_to -created_at -updated_at" });
      if (dbrequestVspace && dbrequestVspace.data && dbrequestVspace.data.length > 0) {
        const requestedUsers = dbrequestVspace.data.map((d: any) => {
          d.requested_by._id = d._id;
          return d.requested_by;
        });
        setVspaceRequest(requestedUsers);
      }
      let notAddedNonMembers = dbvspaceDetails?.nonMembers.filter((x: any) => !dbvspaceDetails?.members.map((x: any) => x.email).includes(x));
      if (notAddedNonMembers.length > 0) {
        const userList = await apiService.get(`/users`, {
          query: {vspace_status: "Approved", is_vspace: true},
          sort: { username: "asc" },
          limit: "~",
        });
        let notAddedNonMembersID = notAddedNonMembers.map((x: any) => userList.data.filter((y: any) => y.email == x).length > 0 ? userList.data.filter((y: any) => y.email == x)[0]._id : "");
        notAddedNonMembersID = notAddedNonMembersID.filter((x: any) => x != "");
        if (notAddedNonMembersID.length > 0) {
          const choosenMembers = notAddedNonMembersID;
          const oldMembers = dbvspaceDetails && dbvspaceDetails.members.map((item: any) => item._id);
          const vspaceRes = await apiService.patch(`/vspace/${dbvspaceDetails._id}`, {
            ...dbvspaceDetails,
            members: [...oldMembers, ...choosenMembers],
            nonMembers: dbvspaceDetails.nonMembers[0] === "" ? "" : dbvspaceDetails.nonMembers,
          });
          let newPlatformUsers = choosenMembers?.map((x: any) => userList?.data.filter((y: any) => y._id == x).length > 0 ? userList.data.filter((y: any) => y._id == x)[0] : [] );
          newPlatformUsers = newPlatformUsers.filter((x: any) => x != '');
          dbvspaceDetails.members = dbvspaceDetails.members.concat(newPlatformUsers);
        }
      }
      setSubscribers(dbvspaceDetails.members);
      setVspace(dbvspaceDetails);
      setEvents([{
        title: "VSpace",
        start: dbvspaceDetails.start_date,
        end: dbvspaceDetails.end_date,
        allDay: true,
        images: dbvspaceDetails.images,
        images_src: dbvspaceDetails.images_src,
        document: dbvspaceDetails.document,
        doc_src: dbvspaceDetails.doc_src

      }]);
    } catch (e) {
      console.log(e);
    }
  }

  useEffect(() => {

    fetchVspace();
    fetchUpdateDoc();
    fetchVspaceFiles();
  }, []);

  const requestedColumns = [
    {
      name: t('vspace.UserName'),
      selector: "username",
      sortable: true
    },
    {
      name: t('vspace.Email'),
      selector: "email",
      sortable: true
    }
  ];

  const [sectionOne, setSectionOne] = useState(true);
  const [sectionTwo, setSectionTwo] = useState(true);
  const [sectionThree, setSectionThree] = useState(true);
  const [sectionFour, setSectionFour] = useState(true);

  const EditVspaceComponent = () => {
    return (
      <Link
        href="/vspace/[...routes]"
        as={`/vspace/edit/${vspaceDetails['_id']}`}
        >
        <Button variant="secondary" size="sm">
          <FontAwesomeIcon icon={faPen} />&nbsp;{t("vspace.Edit")}
        </Button>
      </Link>
    );
  };

  const CaniconEditVSpacesComponent = () => {
    return (
      <Link
        href={{ pathname: "/vspace/[...routes]", query: { id: props.routes[1] } }}
        as={`/vspace/manage?id=${props.routes[1]}`}
        >
        <span><FontAwesomeIcon icon={faUserCog} size="2x" color="#232c3d" className="clickable" /></span>
      </Link>
    );
  }

  const DiscussionComponent = () => {
    return (
      <Accordion.Item eventKey="3">
        <Accordion.Header onClick={() => setSectionFour(!sectionFour)}>
          <div className="cardTitle">{t("vspace.Discussions")}</div>
          <div className="cardArrow">
            {sectionFour ? <FontAwesomeIcon icon={faPlus} color="#fff" /> :
              <FontAwesomeIcon icon={faMinus} color="#fff" />}
          </div>
        </Accordion.Header>
        <Accordion.Body>
          <Discussion
            type="vspace"
            id={props && props.routes ? props.routes[1] : null}
          />
        </Accordion.Body>
      </Accordion.Item>
    )
  };

  const CanViewDiscussionUpdate = canViewDiscussionUpdate(() => <DiscussionComponent />)
  const CanEditVSpace = canEditVspace(() => <EditVspaceComponent />)
  const CanIconEditVSpaces = canEditVspace(() => <CaniconEditVSpacesComponent />)

  const propData = {
    Monitoringmembers: {
      columns: columns,
      subscribers: subscribers
    },
    SubscribeRequestUsers: {
      requestedColumns: requestedColumns,
      vspaceRequest: vspaceRequest
    },
    vSpaceLoading: vSpaceLoading,
    vSpaceSort: vSpaceSort,
    documentAccoirdianProps: {
      vSpaceDocs: vSpaceDocs,
      updateLoading: updateLoading,
      updateSort: updateSort,
      docSrc: docSrc,
    },
    calenderEvents: calenderEvents,
    document: document
  }

  return (
    <Container fluid className="vspaceDetails">
      <UpdatePopup routes={props.routes} />
      <Row style={{ marginTop: "10px", marginBottom: "25px" }}>
        <Col className="ps-md-1 pe-md-1" md={8}>
          {Vspace_func(vspaceDetails, props, CanEditVSpace, t, _moment)}
        </Col>
        <Col className="pe-md-1" md={4}>
          <div className="vspaceCard vspaceCalendar">
            <div className="vspaceTitle">
              <h4>{t('calendar')}</h4>
            </div>
            <VspaceCalendarEvents type='vspace' id={props && props.routes ? props.routes[1] : null} />
          </div>
        </Col>
      </Row>
      <Row>
        <Col className="ps-1 pe-1">
          <div className="d-flex justify-content-between">
            <h4>{t("vspace.Monitoringandevaluationmembers")}</h4>
            {props.routes && props.routes[1] ? (<CanIconEditVSpaces  vspace={vspaceDetails} />) : null}
          </div>
          <VspaceMonitoringMembers {...propData} />
        </Col>
      </Row >

      <Row>
        <Col className="ps-1 pe-1">
          <h4>{t("vspace.SubscribeRequestUsers")}</h4>
          {vspaceRequest && vspaceRequest.length > 0 ?
            <VspaceSubscribeRequestUsers {...propData} />
            : <div className="nodataFound">{t("vspace.Nodataavailable")}</div>}
        </Col>
      </Row>

      <Row>
        <Col className="vspaceAccordion ps-1 pe-1" xs={12}>
          <VspaceAccordianSection {...propData} />
        </Col>
      </Row>
    </Container >
  );
}

export default ViewVSpace;
function Vspace_func(vspaceDetails: any, props: any, CanEditVSpace: any, t: any, _moment: string) {
  return <div className="vspaceCard">
    <div className="vspaceTitle">
      <h4>
        {vspaceDetails['title']}&nbsp;&nbsp;
        {props.routes && props.routes[1] ? (<CanEditVSpace vspace={vspaceDetails} />) : null}
      </h4>
      <section className="d-flex justify-content-between">
        <Bookmark entityId={props.routes[1]} entityType="vspace" />
      </section>
    </div>

    <div className="vspaceDesc">
      <ReadMoreContainer description={vspaceDetails.description} />
    </div>
    <div className="vspaceInfo">
      <p><b>{t("vspace.Ownedby")}</b>: {vspaceDetails["user"] && vspaceDetails["user"].username ? `${vspaceDetails["user"].username}` : ""} </p>
      <p><b>{t("vspace.Groupvisibility")}</b>: {visibility[vspaceDetails['visibility']]}</p>
      <p><b>{t("vspace.Created")}</b>: {moment(vspaceDetails['created_at']).locale(_moment).format('ddd, D MMMM YYYY')}</p>
      <p><b>{t("vspace.LastModified")}</b>: {moment(vspaceDetails['updated_at']).locale(_moment).format('ddd, D MMMM YYYY')}</p>
    </div>
  </div>;
}

