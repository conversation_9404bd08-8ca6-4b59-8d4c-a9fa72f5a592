//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { CountrySeederService } from './country-seeder.services';
// SCHEMAS
import { CountrySchema } from 'src/schemas/country.schemas';
import { WorldRegionSchema } from 'src/schemas/world_region.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Country', schema: CountrySchema },
        { name: 'WorldRegion', schema: WorldRegionSchema },

      ]
    )
  ],
  providers: [CountrySeederService],
  exports: [CountrySeederService],
})
export class CountrySeederModule { }