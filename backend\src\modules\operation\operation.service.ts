//Import Library
import { PaginateModel, Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

//Import services/components
import { OperationInterface } from '../../interfaces/operation.interface';
import { CreateOperationDto } from './dto/create-operation.dto';
import { UpdateOperationDto } from './dto/update-operation.dto';
import { FlagService } from '../flag/flag.service';
import { VspaceInterface } from 'src/interfaces/vspace.interface';
import { UpdateInterface } from 'src/interfaces/update.interface';
const FindOperation = 'Could not find Operation.';
const ArrayIndex = '$arrayIndex';
@Injectable()
export class OperationService {
  constructor(
    @InjectModel('Operation')
    private operationModel: PaginateModel<OperationInterface>,
    @InjectModel('Update') private updateModel: Model<UpdateInterface>,
    @InjectModel('Vspace') private vspaceModel: PaginateModel<VspaceInterface>,
    private readonly _flagService: FlagService,
  ) {}

  async create(
    createOperationDto: CreateOperationDto,
  ): Promise<OperationInterface> {
    const createdOperation = new this.operationModel(createOperationDto);
    createdOperation.save();
    return createdOperation;
  }

  // async getTotalCount(filter: any) {
  //   let _filter = {};
  //   try {
  //     _filter = filter.query ? JSON.parse(filter.query) : {};
  //   } catch (e) {
  //   }
  //   return this.operationModel.count(_filter).exec();
  // }

  async findAll(query): Promise<OperationInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };
    const options = {
      lean: query.lean ? query.lean : false,
      populate: ((typeof query.populate === 'string' ? JSON.parse(query.populate) : query.populate) || ''),
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
      collation: { locale: 'en' },
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }
    const _filter = query.query ? query.query : {};
    if (_filter.title) {
      _filter.title = _filter.title
        .replace('(', '\\(')
        .replace(')', '\\)')
        .replace('&', '\\&');
      const regex = new RegExp(`^${_filter.title}`, 'gi');
      _filter['title'] = regex;
    }

    if (options?.sort?.status) {
      const sortOrder = options.sort.status;
      const sortNumber = sortOrder === 'asc' ? 1 : -1;
      const skipValue = (options.page - 1) * options.limit;
      const sortArray = { 'statusArray.title': sortNumber };
      const list = await this.getSortedList(
        _filter,
        sortArray,
        skipValue,
        options.limit,
        options.page,
      );
      return list;
    } else {
      return this.operationModel.paginate(_filter, options);
    }
  }
  async getSortedList(filter, arr, skip, limit, page) {
    let filterType;
    const worldregionArray = [];
    if (filter['world_region']) {
      filter['world_region'].forEach((element) => {
        const val = element.value ? element.value : element;
        worldregionArray.push(new mongoose.Types.ObjectId(val));
      });
    }

    filterType = this.operationFilter(filter, filterType, worldregionArray);
    const list = await this.operationModel.aggregate([
      {
        $match: filterType,
      },
      {
        $lookup: {
          from: 'operationstatuses',
          localField: 'status',
          foreignField: '_id',
          as: 'statusArray',
        },
      },
      { $unwind: '$statusArray' },
      { $set: { status: '$statusArray' } },
      { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
      { $sort: arr },
      {
        $facet: {
          totalData: [
            { $match: {} },
            { $limit: limit + skip },
            { $skip: skip },
          ],
          totalCount: [
            {
              $group: {
                _id: null,
                count: { $sum: 1 },
              },
            },
          ],
        },
      },
    ]);
    const totalCount = list[0].totalCount[0].count;
    const operationList: any = {};
    operationList.data = list[0].totalData;
    operationList.totalCount = totalCount;
    operationList.limit = limit;
    operationList.totalPages = Math.ceil(totalCount / limit);
    operationList.page = page;
    operationList.pagingCounter = page;
    operationList.hasNextPage = page !== operationList.totalPages;
    operationList.hasPrevPage = !(
      page === 1 && page === operationList.totalPages
    );
    operationList.prevPage =
      page === 1 && page === operationList.totalPages ? null : page - 1;
    operationList.nextPage =
      page === operationList.totalPages ? null : page + 1;
    return operationList;
  }
  private operationFilter(
    filter: any,
    filterType: any,
    worldregionArray: any[],
  ) {
    if (filter.status && filter.title) {
      filterType = {
        $and: [
          { status: new mongoose.Types.ObjectId(filter.status) },
          { world_region: { $in: worldregionArray } },
          { title: filter['title'] },
        ],
      };
    } else if (filter.status && !filter.title) {
      filterType = {
        $and: [
          { status: new mongoose.Types.ObjectId(filter.status) },
          { world_region: { $in: worldregionArray } },
        ],
      };
    } else if (filter.title) {
      filterType = filter.title
        ? {
            $and: [
              { title: filter['title'] },
              { world_region: { $in: worldregionArray } },
            ],
          }
        : {};
    } else {
      filterType = { world_region: { $in: worldregionArray } };
    }
    return filterType;
  }

  async get(operationId): Promise<OperationInterface[]> {
    let result;
    try {
      result = await this.operationModel.findById(operationId).exec();
    } catch (error) {
      throw new NotFoundException(FindOperation);
    }
    if (!result) {
      throw new NotFoundException(FindOperation);
    }
    return result;
  }

  async getSortedDocList(operationId, query): Promise<OperationInterface[]> {
    let _result = [];
    try {
      const options = {
        sort: query.sort ? query.sort : {},
      }
      if ((options.sort.document_title || options.sort.doc_created_at)) {
        const sortOrder = options.sort.document_title ? options.sort.document_title : options.sort.doc_created_at;
        const sortNumber = (sortOrder === 'asc') ? 1 : -1;
        const sortArray = options.sort.document_title ? {  'docArray.original_name' : sortNumber } : { 'docArray.created_at': sortNumber };

        const searchID = new mongoose.Types.ObjectId(operationId);
        const list = await this.operationModel
          .aggregate([
            { $match: { _id: searchID } },
            {
              $unwind: {
                path: '$document',
                preserveNullAndEmptyArrays: true,
                includeArrayIndex: 'arrayIndex',
              },
            },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            { $set: { 'docArray.index': ArrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', ArrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
              },
            },
          ])
          .collation({ locale: 'en', strength: 1 })
          .exec();
        _result = list;
      }
    } catch (error) {
      throw new NotFoundException(FindOperation);
    }
    if (!_result) {
      throw new NotFoundException(FindOperation);
    }
    return _result;
  }

  async getSortedUpdateDocList(
    operationId,
    query,
  ): Promise<OperationInterface[]> {
    let _result = [];
    try {
      const options = {
        sort: query.sort ? query.sort : {},
        collation: query.collation ? query.collation : 'en',
        page: query.page ? Number(query.page) : 1,
        limit: query.limit ? Number(query.limit) : 50,
      };
      if (query.limit === '~') {
        options.limit = Number.MAX_SAFE_INTEGER;
      }
      if (options.sort.document_title || options.sort.doc_created_at) {
        const { sortArray, collation_key } = this.operationSort(options);
        const searchID = new mongoose.Types.ObjectId(operationId);
        const list = await this.updateModel
          .aggregate([
            { $match: { parent_operation: searchID } },
            {
              $unwind: {
                path: '$document',
                preserveNullAndEmptyArrays: true,
                includeArrayIndex: 'arrayIndex',
              },
            },
            {
              $lookup: {
                from: 'files',
                localField: 'document',
                foreignField: '_id',
                as: 'docArray',
              },
            },
            { $set: { 'docArray.index': ArrayIndex } },
            {
              $set: {
                'docArray.docsrc': { $arrayElemAt: ['$doc_src', ArrayIndex] },
              },
            },
            { $unwind: '$docArray' },
            { $set: { document: ['$docArray'] } },
            { $replaceRoot: { newRoot: { $mergeObjects: ['$$ROOT'] } } },
            { $sort: sortArray },
            {
              $project: {
                document: 1,
                doc_src: 1,
              },
            },
          ])
          .collation({ locale: collation_key, strength: 1 })
          .exec();
        _result = list;
      }
    } catch (error) {
      throw new NotFoundException('Could not find Document.');
    }
    if (!_result) {
      throw new NotFoundException('Could not find Document.');
    }
    return _result;
  }

  private operationSort(options: {
    sort: any;
    collation: any;
    page: number;
    limit: number;
  }) {
    const collation_key = options.collation ?? 'en';
    const sortOrder =
      options.sort.document_title ?? options.sort.doc_created_at;
    const sortNumber = sortOrder === 'asc' ? 1 : -1;
    const sortArray = options.sort.document_title
      ? { 'docArray.original_name': sortNumber }
      : { 'docArray.created_at': sortNumber };
    return { sortArray, collation_key };
  }

  async update(operationId: any, updateOperationDto: UpdateOperationDto) {
    const getOperationById: any = await this.operationModel
      .findById(operationId)
      .exec();
    try {
      Object.keys(updateOperationDto).forEach((d) => {
        getOperationById[d] = updateOperationDto[d];
      });
      getOperationById.updated_at = new Date();
      getOperationById.save();
    } catch (e) {
      throw new NotFoundException('Could not update hazard.');
    }
    const languageCode = updateOperationDto['language'] ?? 'en';
    await this._flagService.alertSubscribers(
      getOperationById.title,
      getOperationById._id,
      'operation',
      languageCode,
    );
    return getOperationById;
  }

  async delete(operationId: string) {
    const result = await this.operationModel
      .deleteOne({ _id: operationId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException('Could not find operation.');
    }
  }
}
