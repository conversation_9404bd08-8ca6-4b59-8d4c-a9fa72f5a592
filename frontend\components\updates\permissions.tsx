//Import Library
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

export const canViewUpdateDropDown = connectedAuthWrapper({
  authenticatedSelector: (state: any) => {
    if (state.permissions && state.permissions.update && state.permissions.update['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewUpdateDropDown',
});

export const canViewUpdateRegion = connectedAuthWrapper({
  authenticatedSelector: (state: any) => {
    if (state.permissions && state.permissions.update && state.permissions.update['read:any']) {
      if(state.user && state.user.is_focal_point){
        return state.user.status == "Approved" ? true :false;
      }
      if(state.user && state.user.is_vspace){
        return state.user.vspace_status == "Approved" ? true :false;
      }
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanViewUpdateRegion',
});

export const canEditUpdate = connectedAuthWrapper({
  authenticatedSelector: (state: any, props: any) => {
    if (state.permissions && state.permissions.update) {
      if (state.permissions.update['update:any']) {
        return true;
      } else { //if update:own
        if (state.permissions.update['update:own']) {
          if (props.update && props.update.user && props.update.user._id === state.user._id) {
            return true;
          }
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanEditUpdate',
});


export const canDeleteUpdate = connectedAuthWrapper({
  authenticatedSelector: (state: any, props: any) => {
    if (state.permissions && state.permissions.update) {
      if (state.permissions.update['delete:any']) {
        return true;
      } else { //if delete:own
        if (props.update && props.update.user && props.update.user._id === state.user._id) {
          return true;
        }
      }
    }
    return false;
  },
  wrapperDisplayName: 'CanDeleteUpdate',
});