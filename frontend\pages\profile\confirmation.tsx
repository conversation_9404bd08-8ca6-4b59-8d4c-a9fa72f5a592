//Import Library
import React from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faExclamationTriangle
} from "@fortawesome/free-solid-svg-icons";
import Router from 'next/router';

//Import services/components
import apiService from '../../services/apiService';
import { useTranslation } from 'next-i18next';


const Confirmation = (props) => {
    const { isopen, manageDialog, userId, endpoint } = props;
    const { t } = useTranslation('common');

    const handleClose = () => {
        manageDialog(false)
    }

    const accountDeleteHandlder = async () => {
        let response;
        if (endpoint === "/users") {
            response = await apiService.remove(`${endpoint}/${userId}`);
        } else {
            response = await apiService.post(`${endpoint}`, { code: userId })
        }

        if (response) {
            manageDialog(false);
            Router.push('/home');
        }
    }

    return (
        <Modal show={isopen} onHide={handleClose}>
            <div className="text-center p-2">
                <FontAwesomeIcon
                    icon={faExclamationTriangle}
                    size="5x"
                    color="indianRed"
                    style={{
                        background: "#d6deec",
                        padding: "19px",
                        borderRadius: "50%",
                        width: "100px",
                        height: "100px"
                    }}
                />
            </div>
            <Modal.Body><b>{t("AreyousureyouwishtoleavetheKnowledgePlatform")}</b></Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={handleClose}>
                {t("No")}
          </Button>
                <Button variant="danger" onClick={accountDeleteHandlder}>
                {t("YesDeleteMe")}
          </Button>
            </Modal.Footer>
        </Modal>
    )
}



export default Confirmation;