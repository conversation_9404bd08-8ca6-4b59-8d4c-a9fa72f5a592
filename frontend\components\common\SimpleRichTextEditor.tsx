import React, { useRef, useEffect, useState } from 'react';

interface SimpleRichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
}

const SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Write something...',
  height = 300,
  disabled = false,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Initialize editor with HTML content
  useEffect(() => {
    if (editorRef.current && typeof window !== 'undefined') {
      // Only update if the editor doesn't have focus to prevent cursor jumping
      if (!isFocused && editorRef.current.innerHTML !== value) {
        editorRef.current.innerHTML = value || '';
      }
    }
  }, [value, isFocused]);

  // Handle content changes
  const handleInput = () => {
    if (editorRef.current && onChange) {
      onChange(editorRef.current.innerHTML);
    }
  };

  // Simple toolbar buttons
  const execCommand = (command: string, value?: string) => {
    if (typeof document !== 'undefined') {
      document.execCommand(command, false, value || '');
      handleInput();
      editorRef.current?.focus();
    }
  };

  return (
    <div className="simple-rich-text-editor" style={{ border: '1px solid #ccc' }}>
      {typeof window !== 'undefined' && (
      <>
        <div className="toolbar" style={{ padding: '8px', borderBottom: '1px solid #ccc', background: '#f5f5f5' }}>
            <button
              type="button"
              onClick={() => execCommand('bold')}
              style={{ margin: '0 5px', padding: '3px 8px' }}
            >
              <strong>B</strong>
            </button>
            <button
              type="button"
              onClick={() => execCommand('italic')}
              style={{ margin: '0 5px', padding: '3px 8px' }}
            >
              <em>I</em>
            </button>
            <button
              type="button"
              onClick={() => execCommand('underline')}
              style={{ margin: '0 5px', padding: '3px 8px' }}
            >
              <u>U</u>
            </button>
            <button
              type="button"
              onClick={() => execCommand('insertOrderedList')}
              style={{ margin: '0 5px', padding: '3px 8px' }}
            >
              OL
            </button>
            <button
              type="button"
              onClick={() => execCommand('insertUnorderedList')}
              style={{ margin: '0 5px', padding: '3px 8px' }}
            >
              UL
            </button>
            <button
              type="button"
              onClick={() => {
                const url = prompt('Enter the link URL');
                if (url) execCommand('createLink', url);
              }}
              style={{ margin: '0 5px', padding: '3px 8px' }}
            >
              Link
            </button>
          </div>
          <div
            ref={editorRef}
            contentEditable={!disabled}
            onInput={handleInput}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            style={{
              padding: '15px',
              minHeight: height,
              maxHeight: height * 2,
              overflow: 'auto',
              outline: 'none',
            }}
            data-placeholder={!value ? placeholder : ''}
            suppressContentEditableWarning={true}
          >
          </div>
      </>
      )}
    </div>
  );
};

export default SimpleRichTextEditor;
