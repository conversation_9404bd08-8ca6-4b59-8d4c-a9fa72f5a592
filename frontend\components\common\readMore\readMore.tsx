//Import Library
import { useState } from "react";

//Import services/components
import { useTranslation } from 'next-i18next';

interface ReadMoreContainerProps {
  description: string;
}

const ReadMoreContainer = (props: ReadMoreContainerProps) => {
  const { t } = useTranslation('common');
  const readMoreLength = parseInt(process.env.READ_MORE_LENGTH || '200');
  const [isReadMore, setIsReadMore] = useState(false);

  const createMarkup = (htmlContent: string, isReadMoreInitial: boolean) => {
    const truncateContent = (!isReadMoreInitial && htmlContent.length > readMoreLength) ? htmlContent.substring(0, readMoreLength) + "..." : props.description;
    return { __html: truncateContent };
  };

  return (
    <>
      {
        props.description  ?
        <div
          dangerouslySetInnerHTML={createMarkup(props.description,isReadMore)}
          className="operationDesc"
        >
        </div> : null
      }
      {
        props.description && props.description.length > readMoreLength ?
          <button type="button" className="readMoreText" onClick={() => setIsReadMore(!isReadMore)}>
         {isReadMore ? t("readLess") : t("readMore")}
          </button> : null
      }
    </>
  )
}

export default ReadMoreContainer;
