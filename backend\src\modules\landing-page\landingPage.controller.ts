//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';

//Import services/components
import { CreateLandingPageDto } from './dto/create-landingPage.dto';
import { UpdateLandingPageDto } from './dto/update-landingPage.dto';
import { LandingPageService } from "./landingPage.service";
import { ImageService } from "../image/image.service";
import { SessionGuard } from 'src/auth/session-guard';
import { FilesService } from '../files/files.service';
@Controller('landingPage')
export class LandingPageController {

  constructor(
    private readonly _landingPageService: LandingPageService,
    private readonly _imageService: ImageService,
    private readonly _filesService: FilesService,
  ) { }

  @Post()
  @UseGuards(SessionGuard)
  async create(@Body() createLandingPageDto: CreateLandingPageDto, @Req() request: Request) {
    const user: any = request.user;
    createLandingPageDto['user'] = user._id;
    const _landingPage = await this._landingPageService.create(createLandingPageDto);
    const imageIds = _landingPage['images'] ? _landingPage['images'].map((d) => d._id) : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
    const documentIds = this._landingPageService['document'] ? this._landingPageService['document'].map((d) => d['_id']) : [];
    if (documentIds.length > 0) {
      await this._filesService.bulkUpdate(documentIds);
    }
    return _landingPage;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._landingPageService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') landingPageId: string) {
    return this._landingPageService.get(landingPageId);
  }

  @Patch(':id')
  @UseGuards(SessionGuard)
  async update(@Param('id') landingPageId: string, @Body() updateLandingPageDto: UpdateLandingPageDto) {
    const _landingPage = await this._landingPageService.update(landingPageId, updateLandingPageDto);
    const imageIds = _landingPage['images'] ? _landingPage['images'].map((d) => d._id) : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
    const documentIds = this._landingPageService['document'] ? this._landingPageService['document'].map((d) => d['_id']) : [];
    if (documentIds.length > 0) {
      await this._filesService.bulkUpdate(documentIds);
    }
    return _landingPage;
  }

  @Delete(':id')
  @UseGuards(SessionGuard)
  remove(@Param('id') landingPageId: string) {
    const _landingPage = this._landingPageService.delete(landingPageId);
    return _landingPage;
  }
}
