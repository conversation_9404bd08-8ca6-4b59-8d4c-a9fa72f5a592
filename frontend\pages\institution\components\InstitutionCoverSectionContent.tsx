//Import Library
import React from "react";
import { <PERSON><PERSON>, <PERSON>, Container, <PERSON> } from "react-bootstrap";
import Link from "next/link";
import { faPen } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

//Import services/components
import ReadMoreModal from "../ReadMoreModal";
import Bookmark from "../../../components/common/Bookmark";
import { canEditInstitution, canManageFocalPoints } from "../permission";
import { useTranslation } from 'next-i18next';

interface InstitutionCoverSectionContentProps {
  institutionData: {
    title: string;
    description: string;
    website?: string;
    telephone?: string;
    dial_code?: string;
    address?: {
      line_1?: string;
      line_2?: string;
      city?: string;
      postal_code?: string;
      country?: {
        title: string;
      };
    };
  };
  routeData: {
    routes: string[];
  };
  prop: {
    routes: string[];
  };
  editAccess: boolean;
  focalPoints: any[];
}

const InstitutionCoverSectionContent = (props: InstitutionCoverSectionContentProps) => {
    const { t } = useTranslation('common');

    const EditInstitutionLink = () => {
        return (
            <Link
                href="/institution/[...routes]"
                as={`/institution/edit/${props.prop.routes[1]}`}
                >
                <Button variant="secondary" size="sm">
                    <FontAwesomeIcon icon={faPen} />
                    &nbsp;{t("Edit")}
                </Button>
            </Link>
        );
    };

    const ManageFocalPoints = () => {
        return (
            <li>
                <span className="image-container">
                    <Link
                        href="/institution/[...routes]"
                        as={`/institution/focalpoint/${props?.prop?.routes[1]}`}
                        >
                        <i className="fas fa-plus" />
                    </Link>
                </span>
            </li>
        );
    };

    const CanEditInstitution = canEditInstitution(() => <EditInstitutionLink />);
    const CanManageFocalPoints = canManageFocalPoints(() => <ManageFocalPoints />);

    return (
        <>
            <Container fluid>
                <Row>
                    {institution_request_func(
                        props.institutionData,
                        props.prop,
                        CanEditInstitution,
                        props.editAccess
                    )}

                    <Col xs={6}>
                        <div>
                            <ReadMoreModal
                                description={props.institutionData.description}
                            />
                        </div>
                        {institution_info_func(t, props.institutionData)}
                    </Col>
                    <Col xs={6}>
                        <Row>
                            <Col md={4} className="p-0">
                                {props.focalPoints.length >= 1
                                    ? props.focalPoints.map((item, index) => {
                                        if (item.isPrimary) {
                                            return (
                                                <h6 className="other-focal-points-header">{t("PrimaryFocalPoint")}</h6>
                                            );
                                        }
                                    })
                                    : ""}
                                <ul className="focalPoints primary">
                                    {props.focalPoints.length >= 1
                                        ? props.focalPoints.map((item, index) => {
                                            if (item.isPrimary) {
                                                return (
                                                    <li
                                                        key={index}
                                                        className={
                                                            item.isPrimary ? "isPrimary" : ""
                                                        }
                                                    >
                                                        <span className="image-container">
                                                            {item.image && item.image._id ? (
                                                                <img
                                                                    src={`${process.env.API_SERVER}/image/show/${item.image._id}`}
                                                                />
                                                            ) : (
                                                                <img src="/images/rkiProfile.jpg" />
                                                            )}
                                                        </span>
                                                        <div className="focalpointDetails">
                                                            <p className="fpDetailsFixed">
                                                                <b>{item.username}</b>
                                                                <span>{item.email}</span>
                                                                <span>
                                                                    {item.focal_points_institution}
                                                                </span>
                                                                <span>{item.mobile_number}</span>
                                                            </p>
                                                        </div>
                                                    </li>
                                                );
                                            }
                                        })
                                        : ""}
                                </ul>
                            </Col>
                            {otherFocalpoint_request_func(
                                t,
                                props.focalPoints,
                                props,
                                CanManageFocalPoints,
                                props.institutionData
                            )}
                        </Row>
                    </Col>
                </Row>
            </Container>
        </>
    )
}

export default InstitutionCoverSectionContent;

function institution_request_func(
    institutionData: any,
    props: any,
    CanEditInstitution: any,
    editAccess: boolean
) {
    return (
        <Col xs={12}>
            <div className="d-flex justify-content-between">
                <h4 className="institutionTitle">
                    {institutionData.title} &nbsp;&nbsp;
                    {editAccess && props.routes && props.routes[1] ? (
                        <CanEditInstitution institution={institutionData} />
                    ) : null}
                </h4>
                <Bookmark entityId={props.routes[1]} entityType="institution" />
            </div>
        </Col>
    );
}

function otherFocalpoint_request_func(
    t,
    focalPoints: any[],
    props: any,
    CanManageFocalPoints: any,
    institutionData: any
) {
    return (
        <Col md={8}>
            <h6 className="other-focal-points-header">{t("OtherFocalPoints")}</h6>
            <ul className="focalPoints">
                {focalPoints.length >= 1
                    ? focalPoints.map((item, index) => {
                        return (
                            <li key={index} className={item.isPrimary ? "isPrimary" : ""}>
                                <span className="image-container">
                                    {item.image && item.image._id ? (
                                        <img
                                            src={`${process.env.API_SERVER}/image/show/${item.image._id}`}
                                        />
                                    ) : (
                                        <img src="/images/rkiProfile.jpg" />
                                    )}
                                </span>
                                <div className="focalpointDetails">
                                    <p className="fpDetailsFixed">
                                        <b>{item.username}</b>
                                        <span>{item.email}</span>
                                        <span>{item.focal_points_institution}</span>
                                        <span>{item.mobile_number}</span>
                                    </p>
                                </div>
                            </li>
                        );
                        // }
                    })
                    : ""}
                {props?.prop?.routes && props?.prop?.routes[1] ? (
                    <CanManageFocalPoints institution={institutionData} />
                ) : null}
            </ul>
        </Col>
    );
}

function institution_info_func(t, institutionData: any) {
    return (
        <div className="institutionInfo">
            <ul>
                <li>
                    <label>{t("Telephone")}:</label>
                    <span className="field-value">{`${institutionData.dial_code !== "undefined" &&
                        institutionData.dial_code
                        } ${institutionData.telephone}`}</span>
                </li>
                <li>
                    <label>{t("Address")}:</label>
                    <span className="field-value">
                        {institutionData && institutionData.address
                            ? institutionData.address.line_1
                            : null}{" "}
                        {institutionData && institutionData.address
                            ? institutionData.address.line_2
                            : null}{" "}
                        {institutionData && institutionData.address
                            ? institutionData.address.city
                            : null}
                    </span>
                </li>
                <li>
                    <label>{t("Website")}:</label>
                    <span className="field-value">{institutionData.website}</span>
                </li>
            </ul>
        </div>
    );
}
