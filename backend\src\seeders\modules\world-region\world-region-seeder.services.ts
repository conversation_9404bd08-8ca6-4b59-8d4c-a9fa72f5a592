//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { WorldRegionInterface } from '../../../interfaces/world-region.interface';
import { worldRegions } from "../../data/world-region";

/**
 * Service dealing with world regions based operations.
 *
 * @class
 */
@Injectable()
export class WorldRegionSeederService {
  constructor(
    @InjectModel('WorldRegion') private worldRegionModel: Model<WorldRegionInterface>
  ) {}
  /**
   * Seed all world regions.
   *
   * @function
   */
  create(): Array<Promise<WorldRegionInterface>> {
    return worldRegions.map(async (worldRegion: WorldRegionInterface) => {
      return await this.worldRegionModel
        .findOne({ title: worldRegion.title })
        .exec()
        .then(async dbWorkRegion => {
          // We check if a world regions already exists.
          // If it does don't create a new one.
          if (dbWorkRegion) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.worldRegionModel.create(worldRegion),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}