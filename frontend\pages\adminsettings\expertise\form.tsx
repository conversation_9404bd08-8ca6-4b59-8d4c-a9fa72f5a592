//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { Expertise } from "../../../types";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

interface ExpertiseFormProps {
    [key: string]: any;
}

const ExpertiseForm = (props: ExpertiseFormProps) => {
    const _initialexpertise = {
        title: "",
    };
    const { t } = useTranslation('common');
    const [initialVal, setInitialVal] = useState<Expertise>(_initialexpertise);

    const editform: boolean = props.routes && props.routes[0] === "edit_expertise" && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialexpertise);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.Expertise.Forms.Expertiseisupdatedsuccessfully";
            response = await apiService.patch(`/expertise/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.Expertise.Forms.Expertiseisaddedsuccessfully";
            response = await apiService.post("/expertise", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/expertise");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const expertiseParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getExpertiseData = async () => {
                const response: Expertise = await apiService.get(`/expertise/${props.routes[1]}`, expertiseParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getExpertiseData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.Expertise.Forms.Expertise")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.Expertise.Forms.Expertise")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => String(value || '').trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.Expertise.Forms.PleaseAddtheExpertise"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.Expertise.Forms.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.Expertise.Forms.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/expertise`}
                                        >
                                        <Button variant="secondary">{t("adminsetting.Expertise.Forms.Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default ExpertiseForm;
