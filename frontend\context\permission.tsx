//Import Library
import React, {createContext, useState} from 'react';
import { connect } from "react-redux";

export const PermissionContext: any = createContext({});

interface PermissionContextProviderProps {
  permissions: any;
  children: React.ReactNode;
}

const PermissionContextProvider = (props: PermissionContextProviderProps) => {
  const [permissions,] = useState(props.permissions);

  return (
    <PermissionContext.Provider value={{permissions}}>
      {console.log(props)}
      {props.children}
    </PermissionContext.Provider>
  )
}

export default connect((state) => state)(PermissionContextProvider);