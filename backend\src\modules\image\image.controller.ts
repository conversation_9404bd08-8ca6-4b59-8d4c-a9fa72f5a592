//Import Library
import { <PERSON>, Get, Query, Post, Body, Param, Delete, Patch, Res, UseGuards, UseInterceptors, UploadedFile } from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from  'multer';
import { extname } from  'path';
import * as fs from 'fs';

//Import services/components
import { CreateImageDto } from './dto/create-image.dto';
import { UpdateImageDto } from './dto/update-image.dto';
import { ImageService } from "./image.service";
import { SessionGuard } from 'src/auth/session-guard';
const crypto = require('crypto');

@Controller('image')
export class ImageController {

  constructor(
    private readonly _imageService: ImageService
  ) { }

  @Post()      
  @UseGuards(SessionGuard)
  @UseInterceptors(FileInterceptor('file',
    {
      storage: diskStorage({
        destination: './temp',
        limits: {
          fileSize: 8000000
       },
        filename: (req, file, cb) => {
          const randomVal = crypto.randomInt(30, 500);
          const randomName = Array(32).fill(null).map(() => (Math.round(randomVal * 16)).toString(16)).join('')
          return cb(null, `${randomName}${extname(file.originalname)}`)
        }
      })
    }
  )
  )
 async create(@UploadedFile() file) {
    const createImageDto: CreateImageDto = {
      name: file.filename,
      original_name: file.originalname,
      is_temp: true
    };
    const resp = await this._imageService.create(createImageDto);
    return resp;
  }

  @Get()
  @UseGuards(SessionGuard)
  findAll(@Query() query: any) {
    return this._imageService.findAll(query);
  }

  @Get(':id')
  @UseGuards(SessionGuard)
  findOne(@Param('id') imageId: string) {
    return this._imageService.get(imageId);
  }

  @Get('show/:id')
  async showImage(@Param('id') imageId: string, @Res() response: Response) {
    const imgData:any = await this._imageService.get(imageId);
    let _filepath = `${process.cwd()}/src/assets/img/default.jpg`;
    if (imgData?.name) {
      if (fs.existsSync(`${process.cwd()}/upload/${imgData['name']}`)) {
        _filepath = `${process.cwd()}/upload/${imgData['name']}`;
      }
    }
    const content = fs.readFileSync(_filepath);
    response.writeHead(200, { 'Content-type': 'image/jpg' });
    response.end(content);
  }

  @Patch(':id')
  @UseGuards(SessionGuard)
  update(@Param('id') imageId: string, @Body() updateImageDto: UpdateImageDto) {
    const resp = this._imageService.update(imageId, updateImageDto);
    return resp;
  }

  @Delete(':id')
  @UseGuards(SessionGuard)
  async remove(@Param('id') imageId: string) {
    const deletedData = await this._imageService.delete(imageId);
    return deletedData;
  }

}
