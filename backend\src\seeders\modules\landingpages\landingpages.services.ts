//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { LandingPageInterface } from "src/interfaces/landing-page.interface";
import { landingpages } from "../../data/landingpages";

/**
 * Service dealing with landingpages based operations.
 *
 * @class
 */
@Injectable()
export class LandingpagesSeederService {

  constructor(
    @InjectModel('landingpages') private landingpagesModel: Model<LandingPageInterface>
  ) {}

  /**
   * Seed all landingpages.
   *
   * @function
   */
  create(): Array<Promise<LandingPageInterface>> {
    return landingpages.map(async (landingpages: LandingPageInterface) => {
      return await this.landingpagesModel
        .findOne({ title: landingpages.title })
        .exec()
        .then(async dbLangauge => {
          // We check if a landingpages already exists.
          // If it does don't create a new one.
          if (dbLangauge) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.landingpagesModel.create(landingpages),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}