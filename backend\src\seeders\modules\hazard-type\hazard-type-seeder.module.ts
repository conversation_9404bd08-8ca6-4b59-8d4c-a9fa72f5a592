//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { HazardTypeSeederService } from './hazard-type-seeder.services';
// SCHEMAS
import { HazardTypeSchema } from 'src/schemas/hazard_type.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'HazardType', schema: HazardTypeSchema }
      ]
    )
  ],
  providers: [HazardTypeSeederService],
  exports: [HazardTypeSeederService],
})

export class HazardTypeSeederModule { }
