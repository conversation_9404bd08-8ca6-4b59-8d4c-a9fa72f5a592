//Import Library
import Link from "next/link";
import { Card } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

interface HazardOperationProps {
  t: (key: string) => string;
  hazardOperationData: any[];
}

const HazardOperation = (props: HazardOperationProps) => {
    let hazardOperationData = props.hazardOperationData;
    const { t } = useTranslation('common');
    return (
        <>
            <div className="rki-carousel-card">
                <Card className="infoCard">
                    <Card.Header className="text-center">
                    {t("hazardshow.currentoperations")}
                    </Card.Header>

                    <Card.Body className="hazardBody">
                    {hazardOperationData && hazardOperationData.length > 0 ? (
                        hazardOperationData.map((item, index) => (
                        <ul className="ulItems">
                            <li key={index} className="liItems">
                            <Link
                                key={item._id}
                                href="/operation/[...routes]"
                                as={`/operation/show/${item._id}`}
                            >
                                {item && item.title ? `${item.title}` : ""}
                            </Link>
                            <span>
                                {" "}
                                ({item && item.country ? `${item.country.title}` : ""})
                            </span>
                            </li>
                        </ul>
                        ))
                    ) : (
                        <span className="text-center">{t("noSourceFound")}</span>
                    )}
                    </Card.Body>
                </Card>
            </div>
        </>
    );
};

export default HazardOperation;