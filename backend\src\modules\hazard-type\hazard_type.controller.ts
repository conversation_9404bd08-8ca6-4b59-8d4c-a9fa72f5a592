//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';


//Import services/components
import { CreateHazardTypeDto } from './dto/create_hazard_type.dto';
import { UpdateHazardTypeDto } from './dto/update_hazard_type.dto';
import { HazardTypeService } from './hazard_type.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('hazardtype')
@UseGuards(SessionGuard)
export class HazardTypeController {
  constructor(private readonly _hazardTypeService: HazardTypeService) {}

  @Post()
  async create(@Body() createHazardTypeDto: CreateHazardTypeDto) {
    try {
      const _hazardType = await this._hazardTypeService.create(
        createHazardTypeDto,
      );
      return _hazardType;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  async findAll(@Query() query: any) {
    return this._hazardTypeService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') hazardTypeId: string) {
    return this._hazardTypeService.get(hazardTypeId);
  }

  @Patch(':id')
  async update(
    @Param('id') hazardTypeId: string,
    @Body() updateHazardTypeDto: UpdateHazardTypeDto,
  ) {
    try {
      const _hazardType = await this._hazardTypeService.update(
        hazardTypeId,
        updateHazardTypeDto,
      );
      return _hazardType;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') hazardTypeId: string) {
    const _hazardType = this._hazardTypeService.delete(hazardTypeId);
    return _hazardType;
  }
}
