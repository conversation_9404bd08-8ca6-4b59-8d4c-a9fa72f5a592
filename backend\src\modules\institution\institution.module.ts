//Import Library
import {Module} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { InstitutionController } from './institution.controller';
import { InstitutionService } from './institution.service';
import { UsersService } from './../../users/users.service';
import { FlagService } from '../flag/flag.service';
import { FilesService } from './../files/files.service';
import { ImageService } from './../image/image.service';
import { EmailService } from './../../email.service';
import { CountryService } from "../country/country.service";
import { UpdateService } from '../updates/update.service';
// SCHEMAS
import { InstitutionSchema } from '../../schemas/institution.schemas';
import { UsersSchema } from '../../schemas/users.schemas';
import { ImageSchema } from '../../schemas/image.schemas';
import { ProjectSchema } from '../../schemas/project.schemas';
import { FilesSchema } from '../../schemas/files.schemas';
import {CountrySchema} from "../../schemas/country.schemas";
import { FlagSchema } from "../../schemas/flag.schemas";
import { VspaceSchema } from "../../schemas/vspace.schemas";
import { UpdateSchema } from 'src/schemas/update.schemas';
import { OperationSchema } from 'src/schemas/operation.schemas';
import { HttpModule } from '@nestjs/axios';


@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: 'Institution',
        useFactory: () => {
          const schema = InstitutionSchema;
          schema.post('save', (doc) => console.log(doc._id));
          return schema;
        }
      },
    ]),
    MongooseModule.forFeature([
      { name: 'Institution', schema: InstitutionSchema },
      { name: 'Users', schema: UsersSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Project', schema: ProjectSchema },
      { name: 'Country', schema: CountrySchema },
      { name: 'Flag', schema: FlagSchema},
      { name: 'Files', schema: FilesSchema },
      { name: 'Vspace', schema: VspaceSchema },
      { name: 'Operation', schema: OperationSchema },
      { name: 'Update', schema: UpdateSchema}
    ]), HttpModule
  ],
  controllers: [InstitutionController],
  providers: [InstitutionService, UsersService, ImageService, EmailService, CountryService, FlagService, FilesService, UpdateService],
})

export class InstitutionModule { }