//Import Library
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import { useRef, useState, useEffect } from "react";
import toast from 'react-hot-toast';
import Router from "next/router";
import Link from "next/link";

//Import services/components
import apiService from "../../../services/apiService";
import { HazardType } from "../../../types";
import { useTranslation } from 'next-i18next';
import { EditorComponent } from "../../../shared/quill-editor/quill-editor.component";

interface HazardTypeFormProps {
    [key: string]: any;
}

const HazardTypeForm = (props: HazardTypeFormProps) => {
    const _initialHazardType = {
        title: "",
        code: "",
        description: "",
    };

    const [initialVal, setInitialVal] = useState<HazardType>(_initialHazardType);


    const editform = props.routes && props.routes[0] === "edit_hazard_types" && props.routes[1];
    const { t } = useTranslation('common');

    const handleSubmit = async (event: any, values?: any) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title?.trim(),
            code: initialVal.code?.trim(),
            description: initialVal.description,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.hazardtypes.updatesuccess";
            response = await apiService.patch(`/hazardtype/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.hazardtypes.success";
            response = await apiService.post("/hazardtype", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/hazardTypes");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    const resetHandler = () => {
        setInitialVal(_initialHazardType);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleDescription = (value: string) => {
        setInitialVal((prevState) => ({
            ...prevState,
            description: value,
        }));
    };

    useEffect(() => {
        const hazardTypeParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };

        if (editform) {
            const getHazardTypeData = async () => {
                const response: HazardType = await apiService.get(`/hazardtype/${props.routes[1]}`, hazardTypeParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getHazardTypeData();
        }
    }, []);

    const formRef = useRef(null);

    return (
        <Container className="formCard" fluid>
            <Card
                style={{
                    marginTop: "5px",
                    boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                }}
            >
                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                    <Card.Body>
                        <Row>
                            <Col>
                                <Card.Title>{editform ? t("adminsetting.hazardtypes.EditHazardType") : t("adminsetting.hazardtypes.AddHazardType")}</Card.Title>
                            </Col>
                        </Row>
                        <hr />
                        <Row className="mb-3">
                            <Col md lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field">
                                        {t("adminsetting.hazardtypes.HazardTypeName")}
                                    </Form.Label>
                                    <TextInput
                                        name="title"
                                        id="title"
                                        required
                                        value={initialVal.title}
                                        errorMessage={{
                                            validator: t("adminsetting.hazardtypes.Add")}}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md lg={6} sm={12}>
                                <Form.Group>
                                    <Form.Label className="required-field">
                                        {t("adminsetting.hazardtypes.Code")}
                                    </Form.Label>
                                    <TextInput
                                        name="code"
                                        id="code"
                                        required
                                        value={initialVal.code}
                                        validator={(value) => String(value || '').trim() !== ""}
                                        errorMessage={{
                                            validator: t("adminsetting.hazardtypes.Please"),
                                        }}
                                        onChange={handleChange}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <Form.Group>
                                    <Form.Label>{t("Description")}</Form.Label>
                                    <EditorComponent initContent={initialVal.description} onChange={(evt) => handleDescription(evt)} />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="my-4">
                            <Col>
                                <Button className="me-2" type="submit" variant="primary">
                                    {t("submit")}
                                </Button>
                                <Button className="me-2" onClick={resetHandler} variant="info">
                                    {t("reset")}
                                </Button>
                                <Link
                                    href="/adminsettings/[...routes]"
                                    as={`/adminsettings/hazardTypes`}
                                    >
                                    <Button variant="secondary">{t("Cancel")}</Button>
                                </Link>
                            </Col>
                        </Row>
                    </Card.Body>
                </ValidationFormWrapper>
            </Card>
        </Container>
    );
};

export default HazardTypeForm;
