//Import Library
import React, { useRef, useEffect, useState } from "react";
import Link from "next/link";
import _ from "lodash";
import { Popover, OverlayTrigger } from "react-bootstrap";
import { useRouter } from "next/router";

//Import services/components
import RKITable from "../../components/common/RKITable";
import InstitutionFilter from "./InstitutionsFilter";
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const Networks = ({ networks }) => {
    if (networks && networks.length > 0) {
        return (
            <ul>
                {networks.map((item, index) => {
                    return <li key={index}>{item.title}</li>;
                })}
            </ul>
        );
    }
    return null;
};

function InstitutionsTable(props) {
    const router = useRouter();
    const { setInstitutions, selectedRegions } = props;
    const [filterText, setFilterText] = React.useState("");
    const [filterType, setfilterType] = React.useState([]);
    const [filterNetwork, setFilterNetwork] = React.useState("");
    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);
    const [tabledata, setDataToTable] = useState([]);
    const [loading, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [pageNum, setPageNum] = useState(1);
    const [pageSort, setPageSort] = useState(null);
    const { t } = useTranslation('common');
    const [allRowsCount, setallRowsCount] = useState(0);


    const institutionParams: any = {
        sort: { created_at: "desc" },
        limit: perPage,
        page: 1,
        query: { status: { $not: { $eq: 'Request Pending' } } },
        select: "-contact_name -description -expertisem -hazard_types -hazards -address.region -focal_points -website -telephone -twitter -header -use_default_header -images -email -user -created_at -updated_at -primary_focal_point -partners",
    };

    const [instParams, setInstParams] = useState(institutionParams);

    // For network popover
    const Networkpopover = (
        <Popover id="popover-basic">
            <Popover.Header as="h3" className="text-center">
                NETWORKS
            </Popover.Header>
            <Popover.Body>
                <ul>
                    <li>
                        <b>EMLab</b> - European Mobile Lab
                    </li>
                    <li>
                        <b>EMT</b> -Emergency Medical Teams
                    </li>
                    <li>
                        <b>GHPP</b> - Global Health Protection Program
                    </li>
                    <li>
                        <b>GOARN</b> - Global Outbreak Alert & Response Network
                    </li>
                    <li>
                        <b>IANPHI</b> - International Association of National Public Health Institutes
                    </li>
                    <li>
                        <b>STAKOB</b> - Ständiger Arbeitskreis der Kompetenz-und Behandlungszentren
                    </li>
                    <li>
                        <b>WHOCC</b>- World Health Organization Collaborating Centres
                    </li>
                </ul>
            </Popover.Body>
        </Popover>
    );
    const icons = (
        <OverlayTrigger trigger="click" placement="right" overlay={Networkpopover}>
            <span>
                {t("Network")}&nbsp;&nbsp;&nbsp;
                <i className="fas fa-info-circle" style={{ cursor: "pointer" }} aria-hidden="true"></i>
            </span>
        </OverlayTrigger>
    );
    // End

  const columns = [
  {
    name: t("Organization"),
    selector: "title",
    sortable: true,
    cell: (row) => (
      <Link href="/institution/[...routes]" as={`/institution/show/${row._id}`}>
        {row.title}
      </Link>
    ),
  },
  {
    name: t("Country"),
    selector: "country",
    sortable: true,
    cell: (row) => row.address?.country?.title || "",
  },
  {
    name: t("Type"),
    selector: "type",
    sortable: true,
    cell: (row) => row.type?.title || "",
  },
];



    const setOrgsToTable = (orgs, totalCount) => {
        let filtredOrgs = [];
        filtredOrgs = orgs.filter((org) => org.status != "Request Pending");
        let pendingCount = totalCount - filtredOrgs.length;
        setDataToTable(filtredOrgs);
        setInstitutions(orgs);
        setTotalRows(totalCount - pendingCount);
        setallRowsCount(totalCount);
    };

    const getInstitutionsData = async (institutionParamsinit) => {
        setLoading(true);

        if (router.query && router.query.country) {
            institutionParamsinit.query["address.country"] = router.query.country;
        }

        // Always include selectedRegions if they exist
        if (selectedRegions === null) {
            // On initial load (no filter)
            delete institutionParamsinit.query["address.world_region"];
        } else if (selectedRegions.length === 0) {
            // User cleared all region checkboxes — show nothing
            institutionParamsinit.query["address.world_region"] = "__NO_MATCH__"; // dummy value to ensure no match
        } else {
            // Apply region filter normally
            institutionParamsinit.query["address.world_region"] = selectedRegions;
        }

        // Always make the API call, not just when regions are selected
        const response = await apiService.get("/institution", institutionParamsinit);
        if (response && Array.isArray(response.data)) {
            setOrgsToTable(response.data, response.totalCount);
        }

        setLoading(false);
    };
    const handlePageChange = (page) => {
        // Create a new params object for pagination
        const paginationParams: any = {
            sort: pageSort ? pageSort.sort : { created_at: "desc" },
            limit: perPage,
            page: page,
            query: { status: { $not: { $eq: 'Request Pending' } } },
            select: "-contact_name -description -expertisem -hazard_types -hazards -address.region -focal_points -website -telephone -twitter -header -use_default_header -images -email -user -created_at -updated_at -primary_focal_point -partners",
        };

        // Add filters to query
        if (filterText !== "") {
            paginationParams.query.title = filterText;
        }

        if (filterNetwork) {
            paginationParams.query.networks = filterNetwork;
        }

        const filterType1 = _.map(filterType, "value");
        if (filterType1 && filterType1.length > 0) {
            paginationParams.query.type = filterType1;
        }

        // Add region filter if selected
        if (selectedRegions && selectedRegions.length > 0) {
            paginationParams.query["address.world_region"] = selectedRegions;
        }

        getInstitutionsData(paginationParams);
        setPageNum(page);
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        setLoading(true);

        // Create a new params object for per-page change
        const perPageParams: any = {
            sort: pageSort ? pageSort.sort : { created_at: "desc" },
            limit: newPerPage,
            page: page,
            query: { status: { $not: { $eq: 'Request Pending' } } },
            select: "-contact_name -description -expertisem -hazard_types -hazards -address.region -focal_points -website -telephone -twitter -header -use_default_header -images -email -user -created_at -updated_at -primary_focal_point -partners",
        };

        // Add router country filter if exists
        if (router.query && router.query.country) {
            perPageParams.query["address.country"] = router.query.country;
        }

        // Add region filter if selected
        if (selectedRegions && selectedRegions.length > 0) {
            perPageParams.query["address.world_region"] = selectedRegions;
        }

        // Add other filters
        if (filterText !== "") {
            perPageParams.query.title = filterText;
        }

        if (filterNetwork) {
            perPageParams.query.networks = filterNetwork;
        }

        const filterType1 = _.map(filterType, "value");
        if (filterType1 && filterType1.length > 0) {
            perPageParams.query.type = filterType1;
        }

        const response = await apiService.get("/institution", perPageParams);

        if (response && Array.isArray(response.data)) {
            setOrgsToTable(response.data, response.totalCount);
            setPerPage(newPerPage);
            setLoading(false);
        }
        setPageNum(page);
    };

    useEffect(() => {
        instParams.page = 1;
        getInstitutionsData(instParams);
    }, [selectedRegions, router]);

    useEffect(() => {
        getInstitutionsData(instParams);
    }, [instParams]);

    const handleSort = async (column, sortDirection) => {
        setLoading(true);
        institutionParams.sort = {
            [column.selector]: sortDirection,
        };
        const filterValue = _.map(filterType, "value");
        filterType &&
            filterType.length > 0 &&
            (institutionParams.query = {
                ...institutionParams.query,
                type: filterValue,
            });
        filterNetwork &&
            (institutionParams.query = {
                ...institutionParams.query,
                networks: filterNetwork,
            });
        filterText !== "" &&
            (institutionParams.query = {
                ...institutionParams.query,
                title: filterText,
            });
            console.log("Sorting by:", column.selector);
        await getInstitutionsData(institutionParams);
        setPageSort(institutionParams);
        setLoading(false);
    };

    const sendQuery = (q, page) => {
        if (q) {
            instParams.query["title"] = q;
            instParams.page = page;
            setInstParams({ ...instParams });
        } else {
            delete instParams.query.title;
            setInstParams({ ...instParams });
        }
    };

    const handleSearchTitle = useRef(
        _.debounce((q, page) => sendQuery(q, page), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300)
    ).current;

    const subHeaderComponentMemo = React.useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText("");
            }
        };

        const handleFilterTypeChange = (type) => {
            setfilterType([...type]);
            const typeArray = _.map(type, "value");
            if (typeArray && typeArray.length > 0) {
                instParams.query["type"] = typeArray;
                instParams.page = pageNum;
                setInstParams({ ...instParams });
            } else {
                delete instParams.query.type;
                setInstParams({ ...instParams });
            }
        };

        const handleFilterNetworkChange = (network) => {
            setFilterNetwork(network);
            if (network) {
                instParams.query["networks"] = network;
                instParams.page = pageNum;
                setInstParams({ ...instParams });
            } else {
                delete instParams.query.networks;
                setInstParams({ ...instParams });
            }
        };

        const handleChange = (e) => {
            setFilterText(e.target.value);
            handleSearchTitle(e.target.value, pageNum);
        };

        return (
            <InstitutionFilter
                onFilter={handleChange}
                onFilterTypeChange={handleFilterTypeChange}
                onFilterNetworkChange={(e) => handleFilterNetworkChange(e.target.value)}
                onClear={handleClear}
                filterText={filterText}
                filterType={filterType}
                filterNetwork={filterNetwork}
            />
        );
    }, [filterText, filterType, resetPaginationToggle, filterNetwork, selectedRegions, pageNum]);

    return (
        <div className="institution__table">
            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={allRowsCount}
                loading={loading}
                subheader
                persistTableHead
                onSort={handleSort}
                sortServer
                pagServer={true}
                subHeaderComponent={subHeaderComponentMemo}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
}

export default InstitutionsTable;
