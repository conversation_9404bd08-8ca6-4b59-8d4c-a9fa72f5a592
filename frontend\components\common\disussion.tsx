//Import Library
import React, { useState, useEffect, useContext } from "react";
import moment from "moment";
import "moment/locale/de";
import { <PERSON><PERSON>, Row, Col, Container } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUser } from "@fortawesome/free-solid-svg-icons";
import _ from "lodash";

//Import services/components
import apiService from "../../services/apiService";
import { UpdateContext } from "../../context/update";
import { canAddDiscussionUpdate } from "./permissions";
import AddDiscussion from "./AddDiscussion";
import { useTranslation } from 'next-i18next';
import { EditorComponent } from "../../shared/quill-editor/quill-editor.component";

// Define the discussion item type
interface DiscussionItem {
    _id: string;
    title: string;
    reply: Array<{
        user: string;
        msg: string;
        time: Date;
    }>;
    user: {
        username: string;
    };
    created_at: string;
    [key: string]: any;
}

interface DiscussionProps {
    id: string;
    type: 'operation' | 'event' | 'project' | 'vspace' | 'country' | 'hazard' | 'institution';
}

const Discussion: React.FC<DiscussionProps> = (props) => {
    const { t, i18n } = useTranslation('common');
    const currentLang = i18n.language === "fr" ? "en" : i18n.language;
    const { updates, setUpdates } = useContext(UpdateContext);
    const [discussList, setDiscussList] = useState<DiscussionItem[]>([]);
    const [replyToggle, setReply] = useState(-1);
    const [description, setDescription] = useState("");
    const [username, setUserName] = useState("");
    const [updateTypeId, setUpdateTypeId] = useState("");
    const [_moment] = useState(currentLang);
    const [nodata, setNoData] = useState("");

    const fetchUpdateType = async () => {
        const response = await apiService.get("/updatetype");
        if (response && response.data && response.data.length > 0) {
            const updateType = _.find(response.data, { title: "Conversation" });
            if (updateType && updateType._id) {
                setUpdateTypeId(updateType._id);
                return updateType._id;
            }
        }
    };

    const fetchData = async (type: string, updateTypeIdinitial: string) => {
        if (updateTypeIdinitial) {
            const operationParams = {
                query: { type: type, update_type: updateTypeIdinitial },
                sort: { title: "asc" },
                limit: "~",
            };
            const response = await apiService.get("/updates", operationParams);
            if (response && response.data) {
                const key = `parent_${type}`;
                const _data = _.filter(response.data, { [key]: props.id });
                setDiscussList(_data);
            }
        }
        setNoData(discussList && discussList.length > 0 ? "" : t("NoFilesFound!"));
    };

    useEffect(() => {
        const initialData = async () => {
            const currentUser = await apiService.post("/users/getLoggedUser", {});
            if (currentUser && currentUser.username) {
                setUserName(currentUser.username);
            }
            const updateTypeIdset = await fetchUpdateType();
            await fetchData(props.type, updateTypeIdset);
        };
        initialData();
    }, []);

    const handleDiscussSubmit = async (discussion: string) => {
        if (discussion) {
            const data: any = {
                title: discussion,
                reply: [],
                type: props.type,
                update_type: updateTypeId,
                show_as_announcement: false,
                user: {
                    username: username,
                },
            };
            switch (props.type) {
                case "operation":
                    data["parent_operation"] = props.id;
                    break;
                case "event":
                    data["parent_event"] = props.id;
                    break;
                case "project":
                    data["parent_project"] = props.id;
                    break;
                case "vspace":
                    data["parent_vspace"] = props.id;
                    break;
                case "country":
                    data["parent_country"] = props.id;
                    break;
                case "hazard":
                    data["parent_hazard"] = props.id;
                    break;
                case "institution":
                    data["parent_institution"] = props.id;
                    break;
                default:
                    break;
            }
            const updateresp = await apiService.post("updates", data);
            setDiscussList([...discussList, updateresp]);
            localStorage.setItem("discuss", JSON.stringify([...discussList, data]));
            setUpdates([updateresp, ...updates]);
        }
    };

    const handleDescriptionChange = (val: string) => {
        setDescription(val);
    };

    const showReply = async (key: number) => {
        // Add the reply to the discussion item
        discussList[key].reply.push({
            user: username,
            msg: description,
            time: new Date(),
        });

        // Update the state with the modified discussList
        setDiscussList([...discussList]);
        setReply(-1);
        setDescription("");

        // Update the backend
        await apiService.patch(`/updates/${discussList[key]._id}`, discussList[key]);

        // Update localStorage with the current discussList
        localStorage.setItem("discuss", JSON.stringify(discussList));
    };

    const createMarkup = (htmlContent: string) => {
        return { __html: htmlContent };
    };
    const AddReplyComponent = (AddReplyprops: { i: number }) => {
        return (
            <Col sm={2}>
                <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => {
                        setReply(AddReplyprops.i);
                    }}
                >
                    {t("Reply")}
                </Button>
            </Col>
        );
    };
    const CanAddDiscussion = canAddDiscussionUpdate(() => <AddDiscussion handleDiscussSubmit={handleDiscussSubmit} />);
    const CanReplyToDiscussion = canAddDiscussionUpdate((Discussionprops: { i: number }) => (
        <AddReplyComponent i={Discussionprops.i} />
    ));
    return (
        <Container fluid>
            <CanAddDiscussion />
            {discussList && discussList.length == 0 ? (
                <div>
                    <p className="d-flex d-flex justify-content-center p-2 m-0">{nodata}</p>
                </div>
            ) : (
                <>
                    {discussList.map((cur, i) => {
                        if (cur.title) {
                            return (
                                <div className="discItem" key={i}>
                                    <Row>
                                        <Col sm={10}>
                                            <div className="discThread">
                                                <div className="discAvatar">
                                                    <FontAwesomeIcon icon={faUser} color="#fff" size="lg" />
                                                </div>
                                                <div className="discBody">
                                                    <div className="discUser">
                                                        <span className="discUserName">
                                                            {cur && cur.user && cur.user.username}
                                                        </span>{" "}
                                                        -{" "}
                                                        <span className="discTime">
                                                            {moment.utc(cur.created_at).locale(_moment).fromNow()}
                                                        </span>
                                                    </div>
                                                    <div className="discBodyInnner">{cur.title}</div>
                                                </div>
                                            </div>
                                        </Col>
                                        {replyToggle !== -1 && i === replyToggle ? null : (
                                            <CanReplyToDiscussion i={i} />
                                        )}
                                    </Row>

                                    {cur.reply &&
                                        cur.reply.map((replyData, index) => {
                                            return cur_func(index, replyData, _moment, createMarkup);
                                        })}
                                    {replyToggle !== -1 && i === replyToggle ? (
                                        <div>
                                            <EditorComponent initContent={description} onChange={(evt: string) => handleDescriptionChange(evt)} />{" "}
                                            <br />
                                            <Button
                                                disabled={!description.length}
                                                size="sm"
                                                variant="info"
                                                onClick={() => showReply(i)}
                                            >
                                                {t("Send")}
                                            </Button>{" "}
                                            &nbsp;
                                            <Button
                                                variant="secondary"
                                                size="sm"
                                                onClick={() => {
                                                    setReply(-1);
                                                }}
                                            >
                                                {t("Cancel")}
                                            </Button>
                                        </div>
                                    ) : null}
                                </div>
                            );
                        } else {
                            return null;
                        }
                    })}
                </>
            )}
        </Container>
    );
};

export default Discussion;
function cur_func(index: number, replyData: any, _moment: string, createMarkup: (htmlContent: string) => { __html: string }) {
    return (
        <div className="discReply" key={index}>
            <div style={{ marginLeft: "55px", marginBottom: "10px", marginTop: "10px" }}>
                <div className="discThread">
                    <div className="discAvatar">
                        <FontAwesomeIcon icon={faUser} color="#fff" size="xs" />
                    </div>
                    <div className="discBody">
                        <div className="discUser">
                            <span className="discUserName">{replyData && replyData.user}</span> -{" "}
                            <span className="discTime">
                                {replyData.time
                                    ? moment.utc(replyData.time).locale(_moment).fromNow()
                                    : moment().fromNow()}
                            </span>
                        </div>
                        <div className="discBodyInnner">
                            <div dangerouslySetInnerHTML={createMarkup(replyData.msg)}></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
