//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import RegionTable from "./regionTable";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { canAddRegions } from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";

const RegionIndex = (_props) => {
  const { t } = useTranslation('common');
  const SowRegionIndex = () => {
    return (
      <Container style={{ overflow: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title={t("adminsetting.Regions.Regions")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_region"
              >
              <Button variant="secondary" size="sm">
                {t("adminsetting.Regions.AddRegion")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <RegionTable />
          </Col>
        </Row>
      </Container>
    );
  }

  const ShowAddRegions = canAddRegions(() => <SowRegionIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.region?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddRegions />
  )
};

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default RegionIndex;
