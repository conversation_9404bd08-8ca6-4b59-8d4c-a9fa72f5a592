//Import Library
import { useRouter } from "next/router";

//Import services/components
import EventShow from "./EventShow";
import EventForm from "./Form";
import { canAddEventForm } from "./permission";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Router = () => {
  const router = useRouter();
  const routes: any = router.query.routes || [];
  const CanAccessCreateForm  = canAddEventForm(() => <EventForm routes={routes} />)

  switch (routes[0]) {
    case "create":
      return <CanAccessCreateForm />

    case "edit":
      return <EventForm routes={routes} />;

    case "show":
      return <EventShow routes={routes} />;

    default:
      return null;
  }
};

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Router;
