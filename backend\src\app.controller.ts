//Import Library
import {
  Controller,
  Get,
  Request,
  Post,
  UseGuards,
  Param,
  HttpCode,
  Response,
  HttpStatus,
  Body,
} from '@nestjs/common';
import { InjectRolesBuilder, RolesBuilder } from 'nest-access-control';

//Import services/components
import { SessionAuthGuard } from './auth/session-auth-guard';
import { SessionGuard } from './auth/session-guard';
import { LocalAuthGuard } from './auth/local-auth.guard';
import { AuthService } from './auth/auth.service';
import { ResponseSuccess, ResponseError } from './common/dto/response.dto';
import { ResetPasswordDto } from './users/dto/reset-password.dto';
import { IResponse } from './common/interfaces/response.interface';
import { UsersService } from './users/users.service';


@Controller()
export class AppController {
  constructor(
    private authService: AuthService,
    private readonly userService: UsersService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) {}

  @UseGuards(LocalAuthGuard)
  @UseGuards(SessionAuthGuard)
  @Post('auth/login')
  async login(@Request() req) {
    const payload = await this.authService.login(req.user);
    req.session.user = payload;
    return payload;
  }

  // @UseGuards(LocalAuthGuard)
  // @UseGuards(SessionAuthGuard)
  // @Post('auth/admin/login')
  // async adminLogin(@Request() req) {
  //   const payload = await this.authService.adminLogin(req.user);
  //   req.session.user = payload;
  //   return payload;
  // }

  @Post('auth/logout')
  async logout(@Request() req, @Response() res) {
    res.clearCookie('connect.sid');
    req.session.destroy();
    res.send('logout');
  }

  @Get('email/forgot-password/:email')
  public async sendEmailForgotPassword(@Param() params): Promise<IResponse> {
    try {
      const isEmailSent = await this.authService.forgotpassword(params.email);
      if (isEmailSent) {
        return new ResponseSuccess('LOGIN.EMAIL_RESENT', null);
      } else {
        return new ResponseError('REGISTRATION.ERROR.MAIL_NOT_SENT');
      }
    } catch (error) {
      return new ResponseError('LOGIN.ERROR.SEND_EMAIL', error);
    }
  }

  @Post('email/reset-password')
  @HttpCode(HttpStatus.OK)
  public async setNewPassord(
    @Body() resetPassword: ResetPasswordDto,
  ): Promise<IResponse> {
    try {
      let isNewPasswordChanged: boolean = false;
      if (
        resetPassword?.email &&
        resetPassword?.currentPassword
      ) {
        const isValidPassword = await this.authService.checkPassword(
          resetPassword?.email,
          resetPassword?.currentPassword,
        );
        if (isValidPassword) {
          isNewPasswordChanged = await this.userService.setPassword(
            resetPassword.email,
            resetPassword.newPassword,
          );
        } else {
          return new ResponseError('RESET_PASSWORD.WRONG_CURRENT_PASSWORD');
        }
      } else if (resetPassword.newPasswordToken) {
        const forgottenPasswordModel: any =
          await this.authService.getForgottenPasswordModel(
            resetPassword.newPasswordToken,
          );
        if (forgottenPasswordModel) {
          isNewPasswordChanged = await this.userService.setPassword(
            forgottenPasswordModel.email,
            resetPassword.newPassword,
          );
          if (isNewPasswordChanged) {
            await forgottenPasswordModel.remove();
          }
        } else {
          return new ResponseError('RESET_PASSWORD.INVALID_TOKEN');
        }
      } else {
        return new ResponseError('RESET_PASSWORD.CHANGE_PASSWORD_ERROR');
      }
      return new ResponseSuccess(
        'RESET_PASSWORD.PASSWORD_CHANGED',
        isNewPasswordChanged,
      );
    } catch (error) {
      return new ResponseError('RESET_PASSWORD.CHANGE_PASSWORD_ERROR', error);
    }
  }

  @UseGuards(SessionGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }

  @UseGuards(SessionGuard)
  @Get('permissions')
  async getPermissions(@Request() req) {
    const user: any = req.session.user;
    const permissions = this.roleBuilder.getGrants();
    return permissions[user.roles[0]];
  }
}
