//Import Library
import Router, { useRouter } from "next/router";
import { Button, Col, Row } from "react-bootstrap";

import toast from 'react-hot-toast';
import _ from "lodash";
import { useEffect, useState } from "react";

//Import services/components
import apiService from "../../services/apiService";
import { useTranslation } from 'next-i18next';

const AcceptVspace = (_props: any) => {
    const initialState: any = {
        title: "",
        description: "",
        startDate: null,
        endDate: null,
        searchData: "",
        visibility: true,
        images: [],
        checked: false,
        file_category: "",
        nonMembers: [],
        images_src: [],
        members: [],
        doc_src: [],
        document: [],
    };
    const [disable, setDisable] = useState(false);
    const [vspacedata, setVspaceData] = useState(initialState);
    const [user, setUser] = useState("");
        const { t } = useTranslation('common');
    const router = useRouter();
    const routes: any = router.query.routes || [];

    useEffect(() => {
        GetvSpace();
    }, []);
    const GetvSpace = async () => {
        const vspaceResponse = await apiService.get(`/vspace/${routes[1]}`);
        const _user = await apiService.get(`/users/${routes[3]}`);
        if (vspaceResponse) {
            setDisable(vspaceResponse.visibility);
            setVspaceData(vspaceResponse);
        }
        if (_user) {
            setUser(_user.username);
        }
    };
    const acceptedRequesrtVspace = async () => {
        vspacedata.visibility = true;
        const isSubscribed = _.find(vspacedata.subscribers, { _id: routes[3] });
        isSubscribed == null && vspacedata.subscribers.push(routes[3]);
        const Updateresponse = await apiService.post(`/vspace/acceptSubscriptionRequest/${vspacedata._id}`, vspacedata);
        if (Updateresponse && Updateresponse._id) {
            setDisable(true);
            toast.success(t("Vspaceissuccessfullyaccepted"));
            Router.push("/vspace");
        }
        Updateresponse == "Not authorized" && toast.error(t("Youarenotauthorized"));
    };

    const declineRequestVspace = () => {
        setDisable(true);
        toast.error(t("VspaceissuccessfullyDeclined"));
        Router.push("/vspace");
    };

    return (
        <div>
            <Row className="my-4">
                <Col>
                    <div>Welcome to Robert Koch Institut !</div>
                    <b>{user}</b> has requested to access your private virtual space <b>{vspacedata.title}</b>
                    <br />
                    <Button
                        disabled={disable}
                        className="me-2"
                        type="submit"
                        variant="primary"
                        onClick={acceptedRequesrtVspace}
                    >
                        {t("Accept")}
                    </Button>
                    <Button disabled={disable} className="me-2" variant="info" onClick={declineRequestVspace}>
                        {t("Decline")}
                    </Button>
                </Col>
            </Row>
        </div>
    );
};

export default AcceptVspace;
