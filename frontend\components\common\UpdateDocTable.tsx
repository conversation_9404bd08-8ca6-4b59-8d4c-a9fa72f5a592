//Import Library
import React from "react";
import moment from "moment";

//Import services/components
import RKITable from '../../components/common/RKITable';

interface UpdateDocTableProps {
  docs: any[];
  docsDescription: string;
  sortProps: any;
  loading: boolean;
}

const UpdateDocTable = ({ docs, docsDescription, sortProps, loading }: UpdateDocTableProps) => {
  const normalizedData = Array.isArray(docs) && docs.map((item, index) => ({ description: docsDescription[index] ? docsDescription[index] : "-", ...item, }));


  const handleSort = async (column: any, sortDirection: string) => {
    const objSlect = {
      columnSelector: column.selector,
      sortDirection: sortDirection
    }
    sortProps(objSlect);
  };


  const columns = [
    {
      name: 'File Type',
      width: "15%",
      selector: 'extension',
      cell: (d: any) => d && d.document.extension && d.document.extension,
    },
    {
      name: 'File Name',
      width: "25%",
      selector: "doc_title",
      cell: (d: any) => d && d.document.original_name &&
      <a href={`${process.env.API_SERVER}/files/download/${d._id}`} target="_blank">{d.document.original_name.split('.').slice(0, -1).join('.')}</a>,
      sortable: true
    },
    {
      name: "Description",
      selector: 'description',
      cell: (d: any) => d && d.document.description && d.document.description,
    },
    {
      name: "Uploaded Date",
      width: "25%",
      selector: 'created_at',
      cell: (d: any) => d && d.document.updated_at && moment(d.document.updated_at).format('MM/DD/YYYY'),
      sortable: true
    }
  ];

  return (
    <RKITable
      columns={columns}
      data={normalizedData || []}
      pagServer={true}
      onSort={handleSort}
      persistTableHead
      loading={loading}
    />

  )
}

export default UpdateDocTable;
