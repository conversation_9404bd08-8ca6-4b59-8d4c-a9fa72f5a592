//Import Library
import { connect } from "react-redux";
import {faBookmark, faCheckCircle, faPlusCircle} from "@fortawesome/free-solid-svg-icons";
import {useEffect, useState} from "react";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";

//Import services/components
import apiService from "../../services/apiService";

const onModelOptions = {
  operation: "Operation",
  institution: "Institution",
  event: "Event",
  project: "Project",
  vspace: "Vspace"
} as const;

interface BookMarkProps {
  user?: {
    _id: string;
  };
  entityId: string;
  entityType: keyof typeof onModelOptions;
}

const BookMark: React.FC<BookMarkProps> = (props) => {
  const { user, entityId, entityType } = props;
  const [bookmark, setBookmark] = useState<boolean>(false);
  const [subscribe, setSubscribe] = useState<any>("");

  //Handle bookmark feature
  const fetchIfContentSubscribed = async () => {
    if (!user?._id) return;
    const checkFlag = await apiService.get('/flag', {query: {entity_id: entityId, user: user._id, onModel: onModelOptions[entityType]}});
    if (checkFlag && checkFlag.data && checkFlag.data.length > 0) {
      setSubscribe(checkFlag.data[0]);
      setBookmark(true);
    }
  };

  const bookmarkHandler = async (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (!user?._id) return;
    const flag  = !bookmark;
    const flagPayload = {
      entity_type: entityType,
      entity_id: entityId,
      user: user._id,
      onModel: onModelOptions[entityType]
    }
    if (flag) {
      const flagIt: any = await apiService.post('/flag', flagPayload);
      if (flagIt && flagIt._id) {
        setSubscribe(flagIt);
        setBookmark(flag);
      }
    } else {
      const unFlagIt = await apiService.remove(`/flag/${subscribe._id}`);
      if (unFlagIt && unFlagIt.n) {
        setBookmark(flag);
      }
    }
  }
  //END Bookmark handler
  useEffect(() => {
    fetchIfContentSubscribed();
  },[]);
  return (
    <div className="subscribe-flag">
      <a href="" onClick={bookmarkHandler}>
        <span className="check">
          {bookmark ?
            <FontAwesomeIcon className="clickable checkIcon" icon={faCheckCircle} color="#00CC00" />
            :
            <FontAwesomeIcon className="clickable minusIcon" icon={faPlusCircle} color="#fff"/>
          }
        </span>
        <FontAwesomeIcon className="bookmark" icon={faBookmark} color="#d4d4d4"/>
      </a>
    </div>
  )
}

export default connect((state: any) => state)(BookMark);

