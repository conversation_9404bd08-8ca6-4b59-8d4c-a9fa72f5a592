//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { LanguageInterface } from '../../interfaces/language.interface';
import { CreateLanguageDto } from './dto/create-language.dto';
import { UpdateLanguageDto } from './dto/update-language.dto';
const FindLanguage='Could not find Language.'
@Injectable()
export class LanguageService {
  constructor(
    @InjectModel('language') private languageModel: Model<LanguageInterface>
  ) { }

  async create(createLanguageDto: CreateLanguageDto): Promise<LanguageInterface> {
    const createdLanguage = new this.languageModel(createLanguageDto);
    return createdLanguage.save();
  }

  async findAll(query): Promise<LanguageInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.languageModel.paginate(_filter, options);
  }

  
  async get(languageId): Promise<LanguageInterface[]> {
    let _language;
    try {
      _language = await this.languageModel.findById(languageId).exec();
    } catch (error) {
      throw new NotFoundException(FindLanguage);
    }
    if (!_language) {
      throw new NotFoundException(FindLanguage);
    }
    return _language;
  }

  async update(languageId: any, updateLanguageDto: UpdateLanguageDto) {
    const getLanguageById: any = await this.languageModel.findById(languageId).exec();
    const updatedLanguage = new this.languageModel(updateLanguageDto);
    if (getLanguageById.title) {
      getLanguageById.title = updatedLanguage.title;
    }
    getLanguageById.updatedAt = new Date();
    getLanguageById.save();
    return getLanguageById;
  }

  async delete(languageId: string) {
    const result = await this.languageModel.deleteOne({ _id: languageId }).exec();
    if (result.n === 0) {
      throw new NotFoundException(FindLanguage);
    }
  }
}
