//Import Library
import React from "react";
import { Form, Button, Row, Col } from 'react-bootstrap';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faExclamationTriangle
} from "@fortawesome/free-solid-svg-icons";


const Search = () => {
    return (
        <div>
            <h3><b>RKI Search</b></h3>
            <hr />
            <div style={{ backgroundColor: "#f5f5f5", width: "100%", height: "100px" }}>
                <Form className="mx-3" >
                    <Form.Group controlId="exampleForm.ControlInput1">
                        <Form.Label className="pt-2">Enter Your Keywords</Form.Label>
                        <div className="d-flex">
                            <Form.Control type="email" placeholder="Ex:- Covid-19" />
                            <Button className="ms-3" variant="primary">Search</Button>
                        </div>
                    </Form.Group>
                </Form>
            </div>
            <div className="text-center" style={{ marginTop: "150px" }}>
                <FontAwesomeIcon className="mb-1" icon={faExclamationTriangle} color="#232c3d" size="6x" />
                <h1>Under Construction</h1>
                <p className="lead">Please Visit us Later!</p>
            </div>
        </div>

    )
}



export default Search;

