//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Patch,
  Req,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ACGuard,
  InjectRolesBuilder,
  RolesBuilder,
  UseRoles,
} from 'nest-access-control';

//Import services/components
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';
import { ProjectService } from '../project/project.service';
import { CountryService } from './country.service';
import { SessionGuard } from 'src/auth/session-guard';
import { ResponseError } from '../../common/dto/response.dto';

@Controller('country')
export class CountryController {
  constructor(
    private readonly _countryService: CountryService,
    private readonly _projectService: ProjectService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder,
  ) {}

  @UseGuards(SessionGuard, ACGuard)
  @UseRoles({
    resource: 'country',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(
    @Body() createCountryDto: CreateCountryDto,
    @Req() request: Request,
  ) {
    try {
      const user: any = request.user;
      createCountryDto['user'] = user._id;
      const _country = await this._countryService.create(createCountryDto);
      return _country;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  async findAll(@Query() query: any) {
    return this._countryService.findAll(query);
  }

  @UseGuards(SessionGuard, ACGuard)
  @UseRoles({
    resource: 'country',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') countryId: string, @Req() request: Request) {
    if (request?.query?.instiTable) {
      return this._countryService.getSortedInstiList(countryId, request.query);
    } else if (request?.query && request?.query?.DocTable) {
      return this._countryService.getSortedDocList(countryId, request.query);
    } else if (request?.query && request?.query?.DocUpdateTable) {
      return this._countryService.getSortedUPdateDocList(
        countryId,
        request.query,
      );
    } else {
      return this._countryService.get(countryId);
    }
  }

  @UseGuards(SessionGuard, ACGuard)
  @Patch(':id')
  async update(
    @Param('id') countryId: string,
    @Body() updateCountryDto: UpdateCountryDto,
    @Req() request: Request,
  ) {
    const user: any = request.user;
    const oldCountry: any = await this._countryService.get(countryId);
    const countryUserId = oldCountry.user ? oldCountry.user._id : null;
    const permission =
      user._id === countryUserId
        ? this.roleBuilder.can(user.roles).updateOwn('country').granted
        : this.roleBuilder.can(user.roles).updateAny('country').granted;
    if (permission) {
      try {
        const _country = await this._countryService.update(
          countryId,
          updateCountryDto,
        );
        return _country;
      } catch (error) {
        return {
          message: error.message,
          errorCode: error.code,
        };
      }
    } else {
      throw new HttpException(
        { status: HttpStatus.FORBIDDEN, message: ['Not authorized'] },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  @UseGuards(SessionGuard, ACGuard)
  @Delete(':id')
  async remove(@Param('id') countryId: string, @Req() request: Request) {
    try {
      const country: any = await this._countryService.get(countryId);
      const user: any = request.user;
      const countryUserId = country.user ? country.user._id : null;
      const permission =
        user._id == countryUserId
          ? this.roleBuilder.can(user.roles).deleteOwn('country').granted
          : this.roleBuilder.can(user.roles).deleteAny('country').granted;
      if (permission) {
        const _country = this._countryService.delete(countryId);
        return _country;
      } else {
        return new ResponseError('COUNTRY.ERROR.ACCESS_DENIED');
      }
    } catch (e) {
      return new ResponseError('COUNTRY.ERROR.NOT_FOUND');
    }
  }

  @UseGuards(SessionGuard, ACGuard)
  @UseRoles({
    resource: 'institution',
    action: 'read',
    possession: 'any',
  })
  @Get(':id/institution')
  getInstitution(@Param('id') countryId: string, @Query() query: any) {
    const _country = this._countryService.getInstitution(countryId, query);
    return _country;
  }

  @UseGuards(SessionGuard, ACGuard)
  @UseRoles({
    resource: 'project',
    action: 'read',
    possession: 'any',
  })
  @Get(':id/institutionAndProject')
  async getEvents(@Param('id') countryId: string) {
    const _projects = await this._countryService.getInstitutionAndProject(
      countryId,
    );
    return _projects;
  }
}
