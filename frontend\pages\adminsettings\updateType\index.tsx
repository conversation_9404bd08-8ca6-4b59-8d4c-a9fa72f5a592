//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import UpdateTypeTable from "./updateTypeTable";
import { useTranslation } from 'next-i18next';
import canAddAreaOfWork from "../permissions";
const UpdateTypeIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowUpdateTypeIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title={t("adminsetting.updatestype.UpdateType")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_update_type"
              >
              <Button variant="secondary" size="sm">
              {t("adminsetting.updatestype.AddUpdateType")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <UpdateTypeTable />
          </Col>
        </Row>
      </Container>
    );
  }
  
  const ShowAddUpdateTypes = canAddAreaOfWork(() => <ShowUpdateTypeIndex />);
  return(
    <ShowAddUpdateTypes />
  )
}
export default UpdateTypeIndex;