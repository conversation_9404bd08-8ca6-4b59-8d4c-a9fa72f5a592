//Import Library
import React, { useState } from "react";
import { Form, Modal, Alert } from "react-bootstrap";

//Import services/components
import Confirmation from "./confirmation";
import { useTranslation } from 'next-i18next';

const MyConsent = ({ isOpen, manageClose, id }) => {
  const { t } = useTranslation('common');
  const consentIntialState = {
    consent1: true,
    consent2: true,
    consent3: true,
    consent4: true,
  };
  const [consents, setConsents] = useState(consentIntialState);
  const [warningDialog, setWarningDialog] = useState(false);

  const consentHandler = (e) => {
    const { name, checked } = e.target;
    setConsents((prevState) => ({ ...prevState, [name]: checked }));
    setWarningDialog(!warningDialog);
  };
  const closeHandler = (val) => {
    setConsents(consentIntialState);
    setWarningDialog(val);
  };

  const modalCloseHandler = () => {
    manageClose(false);
  };
  return (
    <>
      <Modal
        show={isOpen}
        onHide={modalCloseHandler}
        size="xl"
        id="main-content"
        className="w-100"
      >
        <Modal.Header closeButton>
          <Modal.Title>{t("declaration.title")}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="p-3 w-100">
            <Alert variant="danger">{t("declaration.info")}</Alert>
            <Form.Check
              className="pb-4"
              type="checkbox"
              name="consent1"
              onChange={consentHandler}
              checked={consents.consent1}
              value="consent1"
              label={t("declaration.consent1")}
            />
             <Form.Check
              className="pb-4"
              name="consent2"
              onChange={consentHandler}
              type="checkbox"
              checked={consents.consent2}
              value="consent2"
              label={t("declaration.consent2")}
            />
            <Form.Check
              className="pb-4"
              name="consent3"
              onChange={consentHandler}
              type="checkbox"
              checked={consents.consent3}
              value="consent3"
              label={t("declaration.consent3")}
            />
            <Form.Check
              className="pb-4"
              type="checkbox"
              name="consent4"
              value="consent4"
              label={t("declaration.consent4")}
              checked={consents.consent4}
              onChange={consentHandler}
            />       
          </div>
        </Modal.Body>
        <Confirmation
          endpoint="/users"
          userId={id ? id : ""}
          isopen={warningDialog}
          manageDialog={(val) => closeHandler(val)}
        />
      </Modal>
    </>
  );
};

export default MyConsent;
