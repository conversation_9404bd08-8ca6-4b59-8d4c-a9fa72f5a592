//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { EventStatusController } from './event-status.controller';
import { EventStatusService } from './event-status.service';
// SCHEMAS
import { EventStatusSchema } from '../../schemas/event_status.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'EventStatus', schema: EventStatusSchema }
    ])
  ],
  controllers: [EventStatusController],
  providers: [EventStatusService],
})

export class EventStatusModule { }