//Import Library
import { useState, useEffect } from "react";
import Link from "next/link";
import { Mo<PERSON>, Button } from "react-bootstrap";

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';


const RoleTable = (_props: any) => {
  const [tabledata, setDataToTable] = useState([]);
  const [, setLoading] = useState(false);
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [isModalShow, setModal] = useState(false);
  const [selectRole, setSelectRole] = useState({});
  const { t } = useTranslation('common');


  const roleParams = {
    "sort": { "title": "asc" },
    "limit": perPage,
    "page": 1,
    "query": {}
  };

  const columns = [
    {
      name: 'Title',
      selector: 'title',
    },
    {
      name: 'Action',
      selector: "",
      cell: d => <div><Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_role/${d._id}`} ><i className="icon fas fa-edit" /></Link>&nbsp;<a onClick={() => userAction(d)}><i className="icon fas fa-trash-alt" /></a> </div>
    }
  ];

  const getRoleData = async () => {
    setLoading(true);
    const response = await apiService.get('/roles', roleParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setTotalRows(response.totalCount);
      setLoading(false);
    }
  };

  const handlePageChange = page => {
    roleParams.limit = perPage;
    roleParams.page = page;
    getRoleData();
  };

  const handlePerRowsChange = async (newPerPage, page) => {
    roleParams.limit = newPerPage;
    roleParams.page = page;
    setLoading(true);
    const response = await apiService.get('/roles', roleParams);
    if (response && response.data && response.data.length > 0) {
      setDataToTable(response.data);
      setPerPage(newPerPage);
      setLoading(false);
    }
  };

  const userAction = async (row) => {
    setSelectRole(row._id);
    setModal(true);
  }

  const modalConfirm = async () => {
    await apiService.remove(`/roles/${selectRole}`);
    getRoleData();
    setModal(false);
  }

  const modalHide = () => setModal(false);

  useEffect(() => {
    getRoleData();
  }, []);

  return (
    <div>
      <Modal show={isModalShow} onHide={modalHide}>
        <Modal.Header closeButton>
          <Modal.Title>{t("DeleteRole")}</Modal.Title>
        </Modal.Header>
        <Modal.Body>{t("Areyousurewanttodeletethisrole")} </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={modalHide}>
          {t("cancel")}
        </Button>
          <Button variant="primary" onClick={modalConfirm}>
          {t("yes")}
        </Button>
        </Modal.Footer>
      </Modal>

      <RKITable
        columns={columns}
        data={tabledata}
        totalRows={totalRows}
        pagServer={true}
        handlePerRowsChange={handlePerRowsChange}
        handlePageChange={handlePageChange}
      />
    </div>
  );
};

export default RoleTable;