//Import Library
import Link from 'next/link';
import ReactPaginate from "react-paginate";

//Import services/components
import { useTranslation } from 'next-i18next';

interface CountriesNamesListingProps {
  countries: {
    data: Array<{
      _id: string;
      title: string;
    }>;
    totalPages?: number;
    page?: number;
    totalCount?: number;
    limit?: number;
  };
  setActivePage: (page: number) => void;
}

const CountriesNamesListing = (props: CountriesNamesListingProps) => {
  const { t } = useTranslation('common');
  const { countries, setActivePage } = props;

  const handlePageChange = (selectedItem: { selected: number }) => {
    const pageNumber = selectedItem.selected + 1; // react-paginate uses 0-based indexing
    setActivePage(pageNumber);
  };
  return (
    <div>
      <div className="alphabetLists">
        {(countries && countries.data && (countries.data.length > 0)) ?
          <ul>
            {countries.data.map((item, i) => {
              return (
                <li className='clearfix' key={i}>
                  <Link href='/country/[...routes]' as={`/country/show/${item._id}`}>
                    <div>{item.title}</div>
                  </Link>
                </li>
              );
            })}
          </ul>
        : t("NoCountriesfound")}
      </div>
      {(countries && countries.data)?
      <div className="countries-pagination">
        <ReactPaginate
          pageCount={Math.ceil(countries.totalCount / countries.limit)}
          pageRangeDisplayed={5}
          marginPagesDisplayed={2}
          onPageChange={handlePageChange}
          forcePage={countries.page - 1} // react-paginate uses 0-based indexing
          containerClassName="pagination"
          pageClassName="page-item"
          pageLinkClassName="page-link"
          previousClassName="page-item"
          previousLinkClassName="page-link"
          nextClassName="page-item"
          nextLinkClassName="page-link"
          activeClassName="active"
          disabledClassName="disabled"
          previousLabel="‹"
          nextLabel="›"
        />
      </div>:null}
    </div>
  );
}

CountriesNamesListing.defaultProps = {
  countries: {
    page: 1,
    limit: 10,
    totalCount: 10,
    data: []
  }
}

export default CountriesNamesListing;