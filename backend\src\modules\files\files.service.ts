//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as fs from 'fs';
const mv = require('mv');

//Import services/components
import { FilesInterface } from '../../interfaces/files.interface';
import { CreateFilesDto } from './dto/create-files.dto';
import { UpdateFilesDto } from './dto/update-files.dto';

const FindFiles = 'Could not find Files.'

@Injectable()
export class FilesService {
  constructor(
    @InjectModel('Files') private filesModel: Model<FilesInterface>
  ) { }

  async create(createFilesDto: CreateFilesDto): Promise<FilesInterface> {
    const createdFiles = new this.filesModel(createFilesDto);
    return createdFiles.save();
  }

  async bulkCreate(createFilesDto: Array<CreateFilesDto>): Promise<FilesInterface[]> {
    const createdFiles = this.filesModel.insertMany(createFilesDto);
    return createdFiles;
  }

  async findAll(query): Promise<FilesInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data'
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : ''
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.filesModel.paginate(_filter, options);
  }

  async get(fileId): Promise<FilesInterface[]> {
    let _result;
    try {
      _result = await this.filesModel.findById(fileId).exec();
    } catch (error) {
      throw new NotFoundException(FindFiles);
    }
    if (!_result) {
      throw new NotFoundException(FindFiles);
    }
    return _result;
  }

  async update(fileId: any, updateFilesDto: UpdateFilesDto) {
    const getById: any = await this.filesModel.findById(fileId).exec();
    const updatedData = new this.filesModel(updateFilesDto);
    try {
      Object.keys(updateFilesDto).forEach((d) => {
        getById[d] = updatedData[d];
      });
      getById.updated_at = new Date();
      getById.save();
    } catch (e) {
      throw new NotFoundException('Could not update Files.');
    }
    return getById;
  }

  async bulkUpdate(fileIds) {
    const foundData = await this.filesModel.find({ _id: { $in: fileIds } });

    if (foundData && foundData.length > 0) {
      foundData.forEach((data) => {
        if (fs.existsSync(`${process.cwd()}/temp/${data.name}`)) {
          mv(
            `${process.cwd()}/temp/${data.name}`,
            `${process.cwd()}/files/${data.name}`,
            function (err) {return err;},
          );
        }
        data.is_temp = false;
        data.save();
      });
    }
    return foundData;
  }

  async delete(fileId: string) {
    const result = await this.filesModel.findOneAndRemove({ _id: fileId }).exec();
    if (!result.is_temp && fs.existsSync(`${process.cwd()}/files/${result.name}`)) {
      fs.unlinkSync(`${process.cwd()}/files/${result.name}`);
    }
    if (result.is_temp && fs.existsSync(`./temp/${result.name}`)) {
      fs.unlinkSync(`${process.cwd()}/temp/${result.name}`);
    }
    if (result.n === 0) {
      throw new NotFoundException(FindFiles);
    }
    return result;
  }

  async bulkDelete(fileIds: Array<string>) {
    const results = await this.filesModel.find({ _id: { $in: fileIds } }).exec();
    if (results && results.length > 0) {
      results.forEach((result) => {
        if (!result.is_temp && fs.existsSync(`${process.cwd()}/files/${result.name}`)) {
          fs.unlinkSync(`${process.cwd()}/files/${result.name}`);
        }
        if (result.is_temp && fs.existsSync(`./temp/${result.name}`)) {
          fs.unlinkSync(`${process.cwd()}/temp/${result.name}`);
        }
        result.remove();
      });
    }
    return results;
  }
}
