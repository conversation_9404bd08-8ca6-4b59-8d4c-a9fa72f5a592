//Import Library
import React, { useState } from "react";
import { Accordion, Card } from "react-bootstrap"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import { useTranslation } from 'next-i18next';
import VspaceTable from "../../../components/common/VspaceTable";

const VirtualSpaceAccordian = (props) => {
    const { t } = useTranslation('common');
    const [section, setSection] = useState(false);
    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("LinkedVirtualSpace")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <VspaceTable
                      id={props.routeData?.routes?.[1] || ''}
                      type="Operation"
                      vspaceData={[]}
                      vspaceDataLoading={false}
                      vspaceDataTotalRows={0}
                      vspaceDataPerPage={10}
                      vspaceDataCurrentPage={1}
                    />
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
}

export default VirtualSpaceAccordian;