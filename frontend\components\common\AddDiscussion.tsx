//Import Library
import React, { useState } from 'react'
import { Button, Col, Form, Row } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

interface AddDiscussionProps {
  handleDiscussSubmit: (discussion: string) => void;
}

const AddDiscussion: React.FC<AddDiscussionProps> = (props) => {
  const [discussion, setDiscussion] = useState<string>("");
  const { t } = useTranslation('common')

  const handleDiscussChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setDiscussion(value);
  };

  const handleKeypress = () => {
    handleDiscussSubmit();

  }
  const handleDiscussSubmit = () => {
    props.handleDiscussSubmit(discussion);
    setDiscussion("");


  };
  return (
    <Form>
      <Row>
        <Col sm={9} lg={10}>
          <Form.Control
            className='mb-2'
            type="text"
            value={discussion}
            onKeyPress={(event: React.KeyboardEvent<HTMLInputElement>) => {
              if (event.key === "Enter") {
                handleKeypress();
                event.preventDefault();
              }
            }}
            onChange={handleDiscussChange} />
        </Col>
        <Col sm={3} lg={2}>
          <Button onClick={handleDiscussSubmit}>{t("submit")}</Button>
        </Col>
      </Row>
    </Form>
  )
};
export default AddDiscussion;
