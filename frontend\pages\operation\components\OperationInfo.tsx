//Import Library
import React from "react";
import { But<PERSON> } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Link from "next/link";
import { faPen } from "@fortawesome/free-solid-svg-icons";

//Import services/components
import Bookmark from "../../../components/common/Bookmark";
import { canEditOperation } from "../permission";
import { useTranslation } from 'next-i18next';


const OperationInfo = (prop) => {
    const { t } = useTranslation('common');
    const EditOperationLink = () => {
        return (
            <>
                {prop.editData ? 
                    <Link
                        href="/operation/[...routes]"
                        as={`/operation/edit/${prop.routeData.routes[1]}`}
                        >
                        <Button variant="secondary" size="sm">
                            <FontAwesomeIcon icon={faPen} />
                            &nbsp;{t("Edit")}
                        </Button>
                    </Link> 
                : ""}
            </>
        );
    };

    const CanEditOperation = canEditOperation(() => <EditOperationLink />);

    return (
        <>
            <section className="d-flex justify-content-between">
                <h4 className="operationTitle">
                    {prop.operation.title}&nbsp;&nbsp;
                    {prop.routeData.routes && prop.routeData.routes[1] ? (
                        <CanEditOperation operation={prop.operation} />
                    ) : null}
                </h4>
                <Bookmark entityId={prop.routeData.routes[1]} entityType="operation" />
            </section>
        </>
    )
}

export default OperationInfo;