//Import Library
import { Contain<PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import CategoryTable from "./categoryTable";
import { useTranslation } from 'next-i18next';


const CategoryIndex = (_props: any) => {
  const { t } = useTranslation('common');

  return (
    <div>
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title="Categories" />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_category"
              >
              <Button variant="secondary" size="sm">
              {t("Addcategory")}
            </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <CategoryTable />
          </Col>
        </Row>
      </Container>
    </div>
  );
}
export default CategoryIndex;