//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";
import HazardTypeTable from "./hazardTypeTable";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import { canAddHazardTypes } from "../permissions";
import NoAccessMessage from "../../rNoAccess";
import { useSelector } from "react-redux";


const HazardTypeIndex = (_props) => {
  const { t } = useTranslation('common');
  const ShowHazardTypeIndex = () => {
    return (
      <Container style={{ overflowX: "hidden" }} fluid className="p-0">
        <Row>
          <Col xs={12}>
            <PageHeading title= {t("adminsetting.hazardtypes.HarzardType")} />
          </Col>
        </Row>
        <Row>
          <Col xs={12}>
            <Link
              href="/adminsettings/[...routes]"
              as="/adminsettings/create_hazard_types"
              >
              <Button variant="secondary" size="sm">
                {t("adminsetting.hazardtypes.type")}
              </Button>
            </Link>
          </Col>
        </Row>
        <Row className="mt-3">
          <Col xs={12}>
            <HazardTypeTable />
          </Col>
        </Row>
      </Container>
    );
  };
  
  const ShowAddHazardTypes = canAddHazardTypes(() => <ShowHazardTypeIndex />);
  const state:any = useSelector((state) => state);
  if (!(state?.permissions?.hazard_type?.['create:any'])) {
    return <NoAccessMessage />
  }
  return(
    <ShowAddHazardTypes />
  );
};

export default HazardTypeIndex;
