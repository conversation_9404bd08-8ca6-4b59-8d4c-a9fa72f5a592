//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { HazardInterface } from "src/interfaces/hazard.interface";
import { operationStatuses } from "../../data/operation-status";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class OperationStatusSeederService {

  constructor(
    @InjectModel('OperationStatus') private operationStatusModel: Model<HazardInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<HazardInterface>> {
    return operationStatuses.map(async (operationStatus: any) => {
      return await this.operationStatusModel
        .findOne({ title: operationStatus.title })
        .exec()
        .then(async dbOperationStatus => {
          // We check if a Operation Status already exists.
          // If it does don't create a new one.
          if (dbOperationStatus) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.operationStatusModel.create(operationStatus),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}