//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import apiService from "../../../services/apiService";
import { InstitutionType } from "../../../types";
import { useTranslation } from 'next-i18next';

interface InstitutionTypeFormProps {
    [key: string]: any;
}

const InstitutionTypeForm = (props: InstitutionTypeFormProps) => {
    const _initialinstitutionType = {
        title: "",
    };

    const [initialVal, setInitialVal] = useState<InstitutionType>(_initialinstitutionType);

    const editform: boolean = props.routes && props.routes[0] === "edit_institution_type" && props.routes[1];
    const { t } = useTranslation('common');

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialinstitutionType);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        const obj = {
            title: initialVal?.title?.trim(),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.Organisationtypes.updatesuccess";
            response = await apiService.patch(`/institutiontype/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.Organisationtypes.success";
            response = await apiService.post("/institutiontype", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/institution_type");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const institutionTypeParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getInstitutionTypeData = async () => {
                const response: InstitutionType = await apiService.get(`/institutiontype/${props.routes[1]}`, institutionTypeParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getInstitutionTypeData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("OrganisationType")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">{t("OrganisationType")}</Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => String(value || '').trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.Organisationtypes.add"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/institution_type`}
                                        >
                                        <Button variant="secondary">{t("Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default InstitutionTypeForm;
