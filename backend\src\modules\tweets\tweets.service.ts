import { Injectable, HttpException, HttpStatus, OnModuleInit } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class TweetsService implements OnModuleInit {
  private readonly twitterApiBaseUrl = 'https://api.twitter.com';
  private readonly bearerToken = process.env.TWITTER_BEARER_TOKEN;
  private readonly username = 'rki_de';
  private readonly maxResults = 1; // Default to fetch just 1 tweet
  private cachedUserId: string | null = null;
  
  // Cache tweets to reduce API calls
  private cachedTweets: any[] = [];
  private lastFetchTime: number = 0;
  private readonly cacheDuration = 15 * 60 * 1000; // 15 minutes in milliseconds

  constructor(private readonly httpService: HttpService) {}

  // Initialize the user ID when the module starts
  async onModuleInit() {
    try {
      this.cachedUserId = await this.fetchUserIdFromApi();
      console.log(`Cached Twitter user ID for @${this.username}: ${this.cachedUserId}`);
      
      // Pre-fetch tweets to populate the cache
      await this.refreshTweetsCache();
    } catch (error) {
      console.error(`Failed to initialize Twitter service:`, error.message);
    }
  }

  async findLatestTweets(query: any): Promise<any> {
    try {
      const count = query?.count || this.maxResults;
      
      // Check if we need to refresh the cache
      const currentTime = Date.now();
      if (this.cachedTweets.length === 0 || 
          currentTime - this.lastFetchTime > this.cacheDuration) {
        await this.refreshTweetsCache();
      }
      
      // Return tweets from cache
      return {
        success: true,
        data: this.cachedTweets.slice(0, count)
      };
    } catch (error) {
      throw new HttpException(
        {
          message: error.message || 'Failed to fetch tweets',
          errorCode: error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async refreshTweetsCache(): Promise<void> {
    try {
      // Get user ID from cache or fetch it if not available
      const userId = await this.getUserId();
      
      // Fetch latest tweets
      const tweets = await this.getTweetsByUserId(userId, 5); // Cache more than we need
      
      // Update cache
      this.cachedTweets = tweets;
      this.lastFetchTime = Date.now();
      console.log(`Refreshed tweets cache at ${new Date().toISOString()}`);
    } catch (error) {
      console.error('Failed to refresh tweets cache:', error.message);
      // Don't throw here, just log the error
      // If cache is empty, we'll return empty array
    }
  }

  private async getUserId(): Promise<string> {
    // Return cached ID if available
    if (this.cachedUserId) {
      return this.cachedUserId;
    }
    
    // Otherwise fetch from API and cache it
    this.cachedUserId = await this.fetchUserIdFromApi();
    return this.cachedUserId;
  }

  private async fetchUserIdFromApi(): Promise<string> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.twitterApiBaseUrl}/2/users/by/username/${this.username}`,
          {
            headers: {
              Authorization: `Bearer ${this.bearerToken}`,
            },
          },
        ),
      );
      
      return response.data.data.id;
    } catch (error) {
      console.error('Error fetching user ID:', error.response?.data || error.message);
      throw new Error('Failed to fetch Twitter user ID');
    }
  }

  private async getTweetsByUserId(userId: string, count: number): Promise<any[]> {
    try {
      // Using the timeline endpoint which is available for free accounts
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.twitterApiBaseUrl}/2/users/${userId}/tweets`,
          {
            headers: {
              Authorization: `Bearer ${this.bearerToken}`,
            },
            params: {
              max_results: count,
              exclude: 'retweets,replies',
              // Minimal fields to reduce data usage
              'tweet.fields': 'created_at,text',
            },
          },
        ),
      );
      
      if (!response.data.data || response.data.data.length === 0) {
        return [];
      }
      
      // Format the tweets to include just the ID for the frontend component
      return response.data.data.map(tweet => ({
        tweetId: tweet.id,
        text: tweet.text,
        createdAt: tweet.created_at
      }));
    } catch (error) {
      console.error('Error fetching tweets:', error.response?.data || error.message);
      throw new Error('Failed to fetch tweets');
    }
  }
}




