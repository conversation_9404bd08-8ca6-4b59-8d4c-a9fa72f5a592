//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreatePageCategoryDto } from './dto/create-page-category.dto';
import { UpdatePageCategoryDto } from './dto/update-page-category..dto';
import { PageCategoryService } from "./page-category.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('pagecategory')
 @UseGuards(SessionGuard)
export class PageCategoryController {

  constructor(
    private readonly _pagecategoryService: PageCategoryService
  ) { }

  @Post()
  @UseGuards(SessionGuard)
  create(@Body() createPageCategoryDto: CreatePageCategoryDto) {
    const resp = this._pagecategoryService.create(createPageCategoryDto);
    return resp;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._pagecategoryService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') pageCategoryId: string) {
    return this._pagecategoryService.get(pageCategoryId);
  }

  @Patch(':id')
  @UseGuards(SessionGuard)
  update(@Param('id') pageCategoryId: string, @Body() updateEventStatusDto: UpdatePageCategoryDto) {
    const resp = this._pagecategoryService.update(pageCategoryId, updateEventStatusDto);
    return resp;
  }

  @Delete(':id')
  @UseGuards(SessionGuard)
  remove(@Param('id') eventstatusId: string) {
    const deletedData = this._pagecategoryService.delete(eventstatusId);
    return deletedData;
  }
}
