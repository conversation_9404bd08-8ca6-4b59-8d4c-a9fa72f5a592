//Import Library
import React, { useState, useEffect } from "react";
import { Modal } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck } from "@fortawesome/free-solid-svg-icons";
import Router from "next/router";

//Import services/components
import { useTranslation } from 'next-i18next';

const VSpaceModal = ({ type, id }: any) => {
  const [time, setTime] = useState(5);
  const { t } = useTranslation('common');

  useEffect(() => {
    if (time > 0) {
      setTimeout(() => setTime(time - 1), 1000);
    } else {
      Router.push({
        pathname: "/vspace/create",
        query: { id: id, source: type },
      });
    }
  });
  return (
    <Modal show={true}>
      <div className="modal--align mt-2">
        <div className="modal--icon">
          <FontAwesomeIcon icon={faCheck} color="#4dc724" size="4x" />
        </div>
      </div>
      <div className="text-center mt-4">
        <p className="lead">
          {type} {t("vspace.formSubmittedSuccessfully")}
          <br />
          <small>{t("vspace.vspaceCreationRedirect")}</small>
          <br />
          <small>
            <b>
              {" "}
              {t("vspace.waitFor")} {time} {t("vspace.waitForSec")}
            </b>
          </small>
        </p>
      </div>
    </Modal>
  );
};

export default VSpaceModal;
