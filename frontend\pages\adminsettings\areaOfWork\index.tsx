//Import Library
import { Container, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import Link from "next/link";

//Import services/components
import PageHeading from "../../../components/common/PageHeading";
import AreaOfWorkTable from "./areaOfWorkTable";
import { useTranslation } from 'next-i18next';
import canAddAreaOfWork from "../permissions";
import { useSelector } from "react-redux";
import NoAccessMessage from "../../rNoAccess";
import { useEffect, useState } from "react";

const AreaOfWorkIndex = (_props: any) => {
  const { t } = useTranslation('common');
  const ShowAreaOfWorkIndex = () => {
    return (
      <div>
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
          <Row>
            <Col xs={12}>
              <PageHeading title={t("adminsetting.areaofwork.Forms.Addareaofwork")} />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <Link
                href="/adminsettings/[...routes]"
                as="/adminsettings/create_area_of_work"
                >
                <Button variant="secondary" size="sm">
                  {t("adminsetting.areaofwork.Forms.Addareaofwork")}
                </Button>
              </Link>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col xs={12}>
              <AreaOfWorkTable />
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  const ShowAddAreaOfWork = canAddAreaOfWork(() => <ShowAreaOfWorkIndex />);
  const state:any = useSelector((state: any) => state);
  if (!(state?.permissions?.area_of_work?.['create:any'])) {
    return <NoAccessMessage />
  }
  return <ShowAddAreaOfWork />
}
export default AreaOfWorkIndex