//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { EmailService } from './../email.service';
import { ImageService } from '../modules/image/image.service';
import { FlagService } from '../modules/flag/flag.service';
import { VspaceService } from '../modules/vspace/vspace.service';
// SCHEMAS
import { UsersSchema } from '../schemas/users.schemas';
import { ImageSchema } from '../schemas/image.schemas';
import { VspaceSchema } from '../schemas/vspace.schemas';
import { FlagSchema } from '../schemas/flag.schemas';
import { UpdateSchema } from 'src/schemas/update.schemas';


@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Users', schema: UsersSchema },
      { name: 'Image', schema: ImageSchema },
      { name: 'Vspace', schema: VspaceSchema },
      { name: 'Flag', schema: FlagSchema },
      { name: 'Update', schema: UpdateSchema}
    ])
  ],
  controllers: [UsersController],
  providers: [UsersService, EmailService, ImageService, VspaceService, FlagService],
  exports: [UsersService],
})
export class UsersModule {}