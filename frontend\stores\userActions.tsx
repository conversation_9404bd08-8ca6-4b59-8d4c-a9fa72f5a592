import { getMenuPlacement } from "react-select/dist/declarations/src/components/Menu"

export const actionTypes = {
  LOAD_DATA: 'LOAD_DATA',
  LOAD_DATA_SUCCESS: 'LOAD_DATA_SUCCESS',
  LOAD_USER_PERMISSIONS: 'LOAD_USER_PERMISSIONS'
}

export function loadLoggedinUserData() { 
  return { type: actionTypes.LOAD_DATA }
}

export function loadDataSuccess(data: any) {
  return {
    type: actionTypes.LOAD_DATA_SUCCESS,
    data,
  }
}

export function loadUserPermissions(data: any) {
  return {
    type: actionTypes.LOAD_USER_PERMISSIONS,
    data,
  }
}
