//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { TaxonomyIconController } from './taxonomy-icon.controller';
import { TaxonomyIconService } from './taxonomy-icon.service';
// SCHEMAS
import { TaxonomyIconSchema } from '../../schemas/taxonomy_icon.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'TaxonomyIcon', schema: TaxonomyIconSchema }
    ])
  ],
  controllers: [TaxonomyIconController],
  providers: [TaxonomyIconService],
})

export class TaxonomyIconModule { }