//Import Library
import Carousel from 'react-bootstrap/Carousel'

interface RKISingleItemCarouselProps {
  list: React.ReactNode[];
}

// TODO: Need to convert this into reusable component
export default function RKISingleItemCarousel(props: RKISingleItemCarouselProps) {
  const { list } = props;
  return (
    <Carousel>
      {list.map((item, index) => {
        return (
          <Carousel.Item key={index}>
            {item}
          </Carousel.Item>
        )
      })}
    </Carousel>
  )
}
