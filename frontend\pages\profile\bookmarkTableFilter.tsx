//Import Library
import React from "react";
import { Form, Col, Container, Row } from "react-bootstrap";
import { MultiSelect } from "react-multi-select-component";

//Import services/components
import { useTranslation } from 'next-i18next';

const BookmarkTableFilter = ({
  filterText,
  onFilter,
  onClear,
  handleGroupHandler,
  groupType,
  options,
}) => {
  const { t } = useTranslation('common');

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={4} md={4} lg={4}>
          <Form.Group style={{ maxWidth: "800px" }}>
            <MultiSelect
              overrideStrings={{
                selectSomeItems: t("ChooseGroup"),
                allItemsAreSelected: "All Groups are Selected",
              }}
              options={options}
              value={groupType}
              onChange={handleGroupHandler}
              className={"choose-group"}
              labelledBy={"Select Network"}
            />
          </Form.Group>
        </Col>
      </Row>
    </Container>
  );
};

export default BookmarkTableFilter;
