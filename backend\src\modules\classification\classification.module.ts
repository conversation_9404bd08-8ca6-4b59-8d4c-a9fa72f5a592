//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ClassificationController } from './classification.controller';
import { ClassificationService } from './classification.service';
// SCHEMAS
import { ClassificationSchema } from '../../schemas/classification.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Classification', schema: ClassificationSchema }
    ])
  ],
  controllers: [ClassificationController],
  providers: [ClassificationService],
})

export class ClassificationModule { }