//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose-paginate-v2';

//Import services/components
import { UpdateTypeInterface } from "src/interfaces/update-type.interface";
import { updateTypes } from "../../data/update-type";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class UpdateTypeSeederService {

  constructor(
    @InjectModel('UpdateType') private updateTypeModel: Model<UpdateTypeInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<UpdateTypeInterface>> {
    return updateTypes.map(async (cate: any) => {
      return await this.updateTypeModel
        .findOne({ title: cate.title })
        .exec()
        .then(async dbUpdateType => {
          // We check if a updateType already exists.
          // If it does don't create a new one.
          if (dbUpdateType) {
            return Promise.resolve(
              await this.updateTypeModel.updateOne({ _id: dbUpdateType._id }, cate).exec()
            );
          }
          return Promise.resolve(
            await this.updateTypeModel.create(cate),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}