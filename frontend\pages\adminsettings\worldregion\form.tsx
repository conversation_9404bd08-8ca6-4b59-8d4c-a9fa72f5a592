//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { ValidationForm, TextInput } from "../../../components/common/FormValidation";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { WorldRegion } from "../../../types";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";

interface WorldregionFormProps {
    [key: string]: any;
}

const WorldregionForm = (props: WorldregionFormProps) => {
    const { t } = useTranslation('common');

    const _initialworldregion = {
        title: "",
        code: "",
    };

    const [initialVal, setInitialVal] = useState<WorldRegion>(_initialworldregion);

    const editform = props.routes && props.routes[0] === "edit_worldregion" && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialworldregion);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        const obj = {
            title: initialVal.title.trim(),
            code: initialVal.code,
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.worldregion.form.Worldregionisupdatedsuccessfully";
            response = await apiService.patch(`/worldregion/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.worldregion.form.Worldregionisaddedsuccessfully";
            response = await apiService.post("/worldregion", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/worldregion");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const worldregionParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getWorldregionData = async () => {
                const response: WorldRegion = await apiService.get(`/worldregion/${props.routes[1]}`, worldregionParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getWorldregionData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.worldregion.form.WorldRegion")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.worldregion.form.WorldRegion")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => String(value || '').trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.worldregion.form.PleaseAddtheWorldRegion"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.worldregion.form.Code")}
                                        </Form.Label>
                                        <TextInput
                                            name="code"
                                            id="code"
                                            required
                                            value={initialVal.code}
                                            validator={(value) => String(value || '').trim() !== ""}
                                            errorMessage={{ validator: t("adminsetting.worldregion.form.PleaseAddthecode")}}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.worldregion.form.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.worldregion.form.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/worldregion`}
                                        >
                                        <Button variant="secondary">{t("adminsetting.worldregion.form.Cancel")}</Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default WorldregionForm;
