//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { InstitutionTypeInterface } from "src/interfaces/institution-type.interface";
import { institutionTypes } from "../../data/institution-type";

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class InstitutionTypeSeederService {

  constructor(
    @InjectModel('InstitutionType') private institutionTypeModel: Model<InstitutionTypeInterface>
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<InstitutionTypeInterface>> {
    return institutionTypes.map(async (insType: any) => {
      return await this.institutionTypeModel
        .findOne({ title: insType.title })
        .exec()
        .then(async dbInsType => {
          // We check if a InstitutionType already exists.
          // If it does don't create a new one.
          if (dbInsType) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.institutionTypeModel.create(insType),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}