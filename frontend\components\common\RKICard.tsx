//Import Library
import React from "react";
import { Card } from "react-bootstrap";
import Modal from "react-bootstrap/Modal";

interface ListModalProps {
  list: {
    heading: string;
    body: React.ReactNode;
  };
  dialogClassName?: string;
  show: boolean;
  onHide: () => void;
}

function ListModal(props: ListModalProps) {
  const { list, dialogClassName } = props;
  return (
    <Modal
      {...props}
      dialogClassName={dialogClassName}
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Modal.Header closeButton>
        <Modal.Title id="contained-modal-title-vcenter">
          {list.heading}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {list.body}
      </Modal.Body>
    </Modal>
  )
}

interface CardFooterProps {
  list?: {
    body: React.ReactNode;
    heading: string;
  };
  dialogClassName?: string;
}

function CardFooter(props: CardFooterProps) {
  const { list } = props;
  const [modalShow, setModalShow] = React.useState(false);
  if (list && list.body) {
    return (
      <>
        <button type="button" onClick={() => setModalShow(true)} style={{ border: 'none', background: 'none', padding: 0 }}>
          <Card.Footer>
            <i className="fas fa-chevron-down" />
          </Card.Footer>
        </button>
        {props.list && <ListModal list={props.list} show={modalShow} onHide={() => setModalShow(false)} dialogClassName={props.dialogClassName} />}
      </>
    )
  }
  return null;
}

interface RKICardProps {
  header: string;
  body: React.ReactNode;
  list?: {
    body: React.ReactNode;
    heading: string;
  };
  dialogClassName?: string;
}

function RKICard(props: RKICardProps) {
  const { header, body } = props

  return (
    <Card className="text-center infoCard">
      <Card.Header>{header}</Card.Header>
      <Card.Body>
        <Card.Text>
          {body}
        </Card.Text>
      </Card.Body>
      <CardFooter {...props} />
    </Card>
  )
}

export default RKICard;
