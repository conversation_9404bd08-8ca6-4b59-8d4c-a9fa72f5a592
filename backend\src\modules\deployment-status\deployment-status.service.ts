//Import Library
import { Model } from 'mongoose-paginate-v2';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

//Import services/components
import { DeploymentStatusInterface } from '../../interfaces/deployment-status.interface';
import { CreateDeploymentStatusDto } from './dto/create-deployment-status.dto';
import { UpdateDeploymentStatusDto } from './dto/update-deployment-status.dto';

const DeploymentStatus = 'Could not find Deployment Status.';
@Injectable()
export class DeploymentStatusService {
  constructor(
    @InjectModel('DeploymentStatus')
    private deploymentStatusModel: Model<DeploymentStatusInterface>,
  ) {}

  async create(
    createDeploymentStatusDto: CreateDeploymentStatusDto,
  ): Promise<DeploymentStatusInterface> {
    const createdDeploymentStatus = new this.deploymentStatusModel(
      createDeploymentStatusDto,
    );
    return createdDeploymentStatus.save();
  }

  async findAll(query): Promise<DeploymentStatusInterface[]> {
    const myCustomLabels = {
      totalDocs: 'totalCount',
      docs: 'data',
    };

    const options = {
      lean: query.lean ? query.lean : false,
      sort: query.sort ? query.sort : {},
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 50,
      customLabels: myCustomLabels,
      select: query.select ? query.select : '',
    };

    if (query.limit === '~') {
      options.limit = Number.MAX_SAFE_INTEGER;
    }

    const _filter = query.query ? query.query : {};
    return await this.deploymentStatusModel.paginate(_filter, options);
  }

  async get(deploymentStatusId): Promise<DeploymentStatusInterface[]> {
    let _result;
    try {
      _result = await this.deploymentStatusModel
        .findById(deploymentStatusId)
        .exec();
    } catch (error) {
      throw new NotFoundException(DeploymentStatus);
    }
    if (!_result) {
      throw new NotFoundException(DeploymentStatus);
    }
    return _result;
  }

  async update(
    deploymentStatusId: any,
    updateDeploymentStatusDto: UpdateDeploymentStatusDto,
  ) {
    const getById: any = await this.deploymentStatusModel
      .findById(deploymentStatusId)
      .exec();
    const updatedData = new this.deploymentStatusModel(
      updateDeploymentStatusDto,
    );
    Object.keys(updateDeploymentStatusDto).forEach((d) => {
      getById[d] = updatedData[d];
    });
    getById.updated_at = new Date();
    return getById.save();
  }

  async delete(deploymentStatusId: string) {
    const result = await this.deploymentStatusModel
      .deleteOne({ _id: deploymentStatusId })
      .exec();
    if (result.n === 0) {
      throw new NotFoundException(DeploymentStatus);
    }
  }
}
