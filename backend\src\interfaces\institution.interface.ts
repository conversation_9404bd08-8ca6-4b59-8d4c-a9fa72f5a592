export interface InstitutionInterface {
  title: string
  description: string,
  type: object,
  networks: object,
  expertise: object,
  hazard_types: object,
  hazards: object,
  address: {
    country: object,
    region: object,
    city: string,
    line_1: string,
    line_2: string,
    coordinates: Object;
  },
  focal_points: [{
    username: string,
    email: string,
    intitution: string,
    phone: string,
    avatar: object,
  }],
  website: string,
  phone: string,
  twitter: string,
  header: object,
  use_default_header: boolean,
  media: object,
  primary_focal_point: object,
  images: Array<any>;
  images_src: Array<any>;
  document: Buffer;
  doc_src: Array<any>;
}
