import React from "react";
import { Accordion } from "react-bootstrap";
import Link from "next/link";
import { useTranslation } from 'next-i18next';

const MoreInfoAccordion = (props) => {
    const { t } = useTranslation('common');
    const { institutionData, activeOperations, activeProjects } = props;

    // Render organization type
    const renderOrganizationType = () => (
        <p>
            <b>{t("OrganisationType")}</b>:
            <span>
                {" "}
                {institutionData.type ? institutionData.type.title : ""}
            </span>
        </p>
    );

    // Render networks
    const renderNetworks = () => (
        <p>
            <b>{t("Network")}</b>:
            <span>
                {institutionData.networks
                    ? institutionData.networks.map((item, index) => (
                        <span key={index}>
                            <li>{item.title}</li>
                        </span>
                    ))
                    : ""}
            </span>
        </p>
    );

    // Render active operations
    const renderActiveOperations = () => (
        <p>
            <b>{t("ActiveOperation")}</b>:
            <span>
                {activeOperations && activeOperations.length > 0 ? (
                    activeOperations.map((item, i) => (
                        <li key={i}>
                            <Link
                                href={"/operation/[...routes]"}
                                as={`/operation/show/${item._id}`}
                            >
                                {item.title}
                            </Link>
                        </li>
                    ))
                ) : (
                    <li>{t("NoActiveoperationsfound")}</li>
                )}
            </span>
        </p>
    );

    // Render expertise
    const renderExpertise = () => (
        <p>
            <b>{t("Expertise")}</b>:
            <span>
                {" "}
                {institutionData.expertise
                    ? institutionData.expertise.map((item, index) => (
                        <li key={index}>
                            {item.title} <br />
                        </li>
                    ))
                    : ""}
            </span>
        </p>
    );

    // Render active projects
    const renderActiveProjects = () => (
        <p>
            <b>{t("ActiveProject")}</b>:
            <span>
                {activeProjects && activeProjects.length > 0 ? (
                    activeProjects.map((item, i) => (
                        <li key={i}>
                            <Link
                                href={"/project/[...routes]"}
                                as={`/project/show/${item._id}`}
                            >
                                {item.title}
                            </Link>
                        </li>
                    ))
                ) : (
                    <li>{t("NoActiveprojectsfound")}</li>
                )}
            </span>
        </p>
    );

    // Render department
    const renderDepartment = () => (
        <p>
            <b>{t("Department")}</b>:
            <span>
                {institutionData && institutionData.department
                    ? institutionData.department
                    : t("Nodepartmentfound")}
            </span>
        </p>
    );

    // Render unit
    const renderUnit = () => (
        <p>
            <b>{t("Unit")}</b>:
            <span>
                {institutionData && institutionData.unit
                    ? institutionData.unit
                    : t("Nounitfound")}
            </span>
        </p>
    );

    return (
        <Accordion defaultActiveKey="0">
            <Accordion.Item eventKey="0">
                <Accordion.Header>
                    <div className="cardTitle">{t("MoreInfo")}</div>
                </Accordion.Header>
                <Accordion.Body className="institutionDetails ps-4">
                    {renderOrganizationType()}
                    {renderNetworks()}
                    {renderActiveOperations()}
                    {renderExpertise()}
                    {renderActiveProjects()}
                    {renderDepartment()}
                    {renderUnit()}
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    );
};

export default MoreInfoAccordion;
