
//Import Library
import React, { useState, useEffect } from 'react';
import { Carousel } from 'react-responsive-carousel';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLink, faDownload
} from "@fortawesome/free-solid-svg-icons";
import { Button } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

// Import CSS for react-responsive-carousel
import "react-responsive-carousel/lib/styles/carousel.min.css";

// Define types for image items
interface ImageItem {
  src: string;
  description: string;
  originalName: string;
  downloadLink: string | false;
}

interface ReactImagesProps {
  gallery?: Array<{
    _id: string;
    name: string;
    original_name?: string;
    src?: string;
    caption?: string;
    alt?: string;
  }>;
  imageSource?: string[];
}

const ReactImages = (props: ReactImagesProps) => {
  const { t } = useTranslation('common');
  const [images, setImages] = useState<ImageItem[]>([]);

  // Render image description and metadata
  const renderImageLegend = (item: ImageItem) => {
    const isValidLink = /(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/.test(item.description);

    return (
      <div className="carousel-legend">
        <p className="lead">
          <b>{t("Filename")}</b> {item.originalName || "No Name found"}
        </p>
        {item.description && (
          <div className="source_link">
            <p><b>{t("Source")}</b></p>
            {isValidLink ? (
              <div>
                <FontAwesomeIcon icon={faLink} size="1x" color="#999" className="me-1" />
                <a className="source_link" href={item.description} target="_blank" rel="noopener noreferrer">
                  {item.description}
                </a>
              </div>
            ) : (
              <div>
                <p className="ps-0 py-0" style={{ wordBreak: "break-all" }}>
                  {item.description}
                </p>
              </div>
            )}
          </div>
        )}
        {item.downloadLink && (
          <Button className="btn btn-success mt-2 btn--download" href={item.downloadLink}>
            {t("Download")}
            <FontAwesomeIcon icon={faDownload} size="1x" className="ms-1" />
          </Button>
        )}
      </div>
    );
  };

  useEffect(() => {
    const carouselImages: ImageItem[] = [];
    props && props.gallery && Array.isArray(props.gallery) && props.gallery.map((item, i) => {
      const fileType = item && item.name.split('.').pop();
      let imgSrc;

      switch (fileType) {
        case "JPG":
        case "jpg":
        case "jpeg":
        case "png":
          imgSrc = `${process.env.API_SERVER}/image/show/${item._id}`;
          break;
        case "pdf":
          imgSrc = "/images/fileIcons/pdfFile.png";
          break;
        case "docx":
          imgSrc = "/images/fileIcons/wordFile.png";
          break;
        case "xls":
        case 'xlsx':
          imgSrc = "/images/fileIcons/xlsFile.png";
          break;
        default:
          imgSrc = "/images/fileIcons/otherFile.png";
      }

      const _link = (fileType === "docx" || fileType === "pdf" || fileType === "xls" || fileType === "xlsx")
        && `${process.env.API_SERVER}/files/download/${item._id}`;
      const _name = `${item && item.original_name ? item.original_name : "No Name found"}`;
      const _description = props.imageSource && Array.isArray(props.imageSource)
        && props.imageSource.length > 0 ? props.imageSource[i] : "";

      carouselImages.push({
        src: imgSrc,
        description: _description,
        originalName: _name,
        downloadLink: _link
      });
    });
    setImages(carouselImages);
  }, [props]);

  return (
    <div>
      {images && images.length === 0 ? (
        <div className="border border-info my-3 mx-0">
          <p className="d-flex d-flex justify-content-center p-2 m-0">{t("NoFilesFound!")}</p>
        </div>
      ) : (
        <Carousel
          showThumbs={true}
          showStatus={true}
          showIndicators={true}
          infiniteLoop={true}
          useKeyboardArrows={true}
          autoPlay={false}
          stopOnHover={true}
          swipeable={true}
          dynamicHeight={false}
          emulateTouch={true}
          renderThumbs={() =>
            images.map((item, index) => (
              <img
                key={index}
                src={item.src}
                alt={`Thumbnail ${index + 1}`}
                style={{ width: '60px', height: '60px', objectFit: 'cover' }}
              />
            ))
          }
        >
          {images.map((item, index) => (
            <div key={index}>
              <img
                src={item.src}
                alt={item.originalName || "Gallery image"}
                style={{ maxHeight: '500px', objectFit: 'contain' }}
              />
              {renderImageLegend(item)}
            </div>
          ))}
        </Carousel>
      )}
    </div>
  );

}

export default ReactImages;
