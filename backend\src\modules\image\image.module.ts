//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ImageController } from './image.controller';
import { ImageService } from './image.service';
// SCHEMAS
import { ImageSchema } from '../../schemas/image.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Image', schema: ImageSchema }
    ])
  ],
  controllers: [ImageController],
  providers: [ImageService],
})

export class ImageModule { }