//Import Library
import { useRouter } from 'next/router';

//Import services/components
import OperationForm from './Form';
import OperationShow from './OperationShow';
import { canAddOperationForm } from "./permission";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Router = () => {
  const router = useRouter()
  const routes:any = router.query.routes || []

  const CanAccessCreateForm = canAddOperationForm(() => <OperationForm routes={routes} />);

  switch (routes[0]) {
    case 'create':
      return <CanAccessCreateForm />

    case 'edit':
      return <OperationForm routes={routes} />

    case 'show':
      return <OperationShow routes={routes}  />

    default:
      return null;
  }
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Router
