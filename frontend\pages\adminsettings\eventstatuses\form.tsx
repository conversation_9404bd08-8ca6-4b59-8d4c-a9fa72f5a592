//Import Library
import { useState, useRef, useEffect } from "react";
import { Container, Card, Row, Col, Form, Button } from "react-bootstrap";
import { TextInput } from "../../../components/common/FormValidation";
import ValidationFormWrapper from "../../../components/common/ValidationFormWrapper";
import Router from "next/router";
import toast from 'react-hot-toast';
import Link from "next/link";

//Import services/components
import { EventstatusInterface } from "../../../components/interfaces/eventstatus.interface";
import apiService from "../../../services/apiService";
import { useTranslation } from 'next-i18next';

const EventstatusForm = (props: any) => {
    const _initialeventstatus = {
        title: "",
    };
    const { t } = useTranslation('common');
    const [initialVal, setInitialVal] = useState<EventstatusInterface>(_initialeventstatus);

    const editform: boolean = props.routes && props.routes[0] === "edit_eventstatus" && props.routes[1];

    const formRef = useRef(null);

    const resetHandler = () => {
        setInitialVal(_initialeventstatus);
        // Reset validation state (Formik handles this automatically)
        window.scrollTo(0, 0);
    };

    const handleChange = (e) => {
        if (e.target) {
            const { name, value } = e.target;
            setInitialVal((prevState) => ({
                ...prevState,
                [name]: value,
            }));
        }
    };

    const handleSubmit = async () => {
        event.preventDefault();

        const obj = {
            title: initialVal.title.trim(),
        };

        let response;
        let toastMsg;
        if (editform) {
            toastMsg = "adminsetting.EventStatus.Forms.Eventstatusisupdatedsuccessfully";
            response = await apiService.patch(`/eventstatus/${props.routes[1]}`, obj);
        } else {
            toastMsg = "adminsetting.EventStatus.Forms.Eventstatusisaddedsuccessfully";
            response = await apiService.post("/eventstatus", obj);
        }
        if (response && response._id) {
            toast.success(t(toastMsg));
            Router.push("/adminsettings/eventstatus");
        } else {
            if (response?.errorCode === 11000) {
                toast.error(t("duplicatesNotAllowed"));
            } else {
                toast.error(response);
            }
        }
    };

    useEffect(() => {
        const eventstatusParams = {
            query: {},
            sort: { title: "asc" },
            limit: "~",
        };
        if (editform) {
            const getEventstatusData = async () => {
                const response = await apiService.get(`/eventstatus/${props.routes[1]}`, eventstatusParams);
                setInitialVal((prevState) => ({ ...prevState, ...response }));
            };
            getEventstatusData();
        }
    }, []);

    return (
        <div>
            <Container className="formCard" fluid>
                <Card
                    style={{
                        marginTop: "5px",
                        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                    }}
                >
                    <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>
                        <Card.Body>
                            <Row>
                                <Col>
                                    <Card.Title>{t("adminsetting.EventStatus.Forms.EventStatus")}</Card.Title>
                                </Col>
                            </Row>
                            <hr />
                            <Row>
                                <Col md lg={6} sm={12}>
                                    <Form.Group>
                                        <Form.Label className="required-field">
                                            {t("adminsetting.EventStatus.Forms.EventStatus")}
                                        </Form.Label>
                                        <TextInput
                                            name="title"
                                            id="title"
                                            required
                                            value={initialVal.title}
                                            validator={(value) => String(value || '').trim() !== ""}
                                            errorMessage={{
                                                validator: t("adminsetting.EventStatus.Forms.PleaseAddtheEventStatus"),
                                            }}
                                            onChange={handleChange}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                            <Row className="my-4">
                                <Col>
                                    <Button className="me-2" type="submit" variant="primary">
                                        {t("adminsetting.EventStatus.Forms.Submit")}
                                    </Button>
                                    <Button className="me-2" onClick={resetHandler} variant="info">
                                        {t("adminsetting.EventStatus.Forms.Reset")}
                                    </Button>
                                    <Link
                                        href="/adminsettings/[...routes]"
                                        as={`/adminsettings/eventstatus`}
                                        >
                                        <Button variant="secondary">
                                            {t("adminsetting.EventStatus.Forms.Cancel")}
                                        </Button>
                                    </Link>
                                </Col>
                            </Row>
                        </Card.Body>
                    </ValidationFormWrapper>
                </Card>
            </Container>
        </div>
    );
};
export default EventstatusForm;
