//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { WorldRegionController } from './world-region.controller';
import { WorldRegionService } from './world-region.service';
// SCHEMAS
import { WorldRegionSchema } from '../../schemas/world_region.schemas';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'WorldRegion', schema: WorldRegionSchema }
    ])
  ],
  controllers: [WorldRegionController],
  providers: [WorldRegionService],
})

export class WorldRegionModule { }