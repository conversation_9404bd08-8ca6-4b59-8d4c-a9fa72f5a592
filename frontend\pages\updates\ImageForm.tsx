//Import Library
import React from "react";
import { Container, Row, Col } from "react-bootstrap";

//Import services/components
import ReactDropZone from "../../components/common/ReactDropZone";
import { useTranslation } from 'next-i18next';


//TOTO refactor
interface ImageFormProps {
  data: any[];
  getId: (id: any[]) => void;
  imgSrc: any[];
  getSourceCollection: (imgSrcArr: any[]) => void;
}

const ImageForm = (props: ImageFormProps): React.ReactElement => {
  const { t } = useTranslation('common');

  const getID = (id: any[]) => {
    props.getId(id)
  }

  const getSourceText = (imgSrcArr: any[]) => {
    props.getSourceCollection(imgSrcArr);
  }

  return (
    <Container className="formCard" fluid>
      <Col>
        <Row className='header-block' lg={12}>
          <h6><span>{t("update.Image")}</span></h6>
        </Row>
        <Row>
          <ReactDropZone datas={props.data} srcText={props.imgSrc} getImgID={(id) => getID(id)} getImageSource={(imgSrcArr) => getSourceText(imgSrcArr)} />
        </Row>
      </Col>
    </Container>
  );
}
export default ImageForm;
