//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { AreaOfWorkSeederService } from './area-of-work-seeder.services';
// SCHEMAS
import { AreaOfWorkSchema } from 'src/schemas/area_of_work.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'AreaOfWork', schema: AreaOfWorkSchema }
      ]
    )
  ],
  providers: [AreaOfWorkSeederService],
  exports: [AreaOfWorkSeederService],
})
export class AreaOfWorkSeederModule { }