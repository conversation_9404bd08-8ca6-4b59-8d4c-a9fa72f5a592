//Import Library
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from 'mongoose';

//Import services/components
import { eventStatuses } from "../../data/event-status";
import { EventStatusInterface } from "src/interfaces/event-status.interface";

/**
 * Service dealing with Event Status.
 *
 * @class
 */
@Injectable()
export class EventStatusSeederService {

  constructor(
    @InjectModel('EventStatus') private eventStatusModel: Model<EventStatusInterface>
  ) {}

  /**
   * Seed all Event Status.
   *
   * @function
   */
  create(): Array<Promise<EventStatusInterface>> {
    return eventStatuses.map(async (classif: any) => {
      return await this.eventStatusModel
        .findOne({ title: classif.title })
        .exec()
        .then(async dbEventStatus => {
          // We check if a Event Status already exists.
          // If it does don't create a new one.
          if (dbEventStatus) {
            return Promise.resolve(null);
          }
          return Promise.resolve(
            await this.eventStatusModel.create(classif),
          );
        })
        .catch(error => Promise.reject(error));
    });
  }
}