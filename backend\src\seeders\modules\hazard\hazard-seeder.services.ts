//Import Library
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { extname } from 'path';
import { copyFileSync } from 'fs';

//Import services/components
import { HazardInterface } from 'src/interfaces/hazard.interface';
import { hazards } from '../../data/hazard';
import { HazardTypeInterface } from 'src/interfaces/hazard_type.interface';
import { hazardTypes } from '../../data/hazard-type';
import { ImageInterface } from 'src/interfaces/image.interface';
import { diseases } from '../../data/disease';
import { facts } from '../../data/facts';

/**
 * Service dealing with language based operations.
 *
 * @class
 */
@Injectable()
export class HazardSeederService {
  constructor(
    @InjectModel('Hazard') private hazardModel: Model<HazardInterface>,
    @InjectModel('HazardType')
    private hazardTypeModel: Model<HazardTypeInterface>,
    @InjectModel('Image') private imageModel: Model<ImageInterface>,
  ) {}

  /**
   * Seed all languages.
   *
   * @function
   */
  create(): Array<Promise<HazardInterface>> {
    // hazardTypes = 
    return hazards.map(async (hazard: any, key: number) => {
      const obj = hazard;


      const diseaseObj = diseases.find((d) => d.name === obj.title);
      if (diseaseObj?.id) {
        const defObj = facts.find(
          (d) => d.id === diseaseObj.id && d.fact === 'Definition',
        );
        obj.description = defObj.description;
      }

      obj.hazard_type = null;
      await this.hazardSeeder(hazard, obj);

      if (obj.image) {
        const timeStamp = Date.now();
        const imgDetails = await this.imageModel.create({
          name: timeStamp + extname(obj.image),
          original_name: obj.image,
          is_temp: false,
          created_at: new Date(),
          updated_at: new Date(),
        });

        copyFileSync(
          process.cwd() + '/src/seeders/data/images/' + obj.image,
          process.cwd() + '/upload/' + (timeStamp + extname(obj.image)),
        );
        obj.picture = imgDetails ? imgDetails._id : null;
      }

      return  this.hazardModel
        .findOne({ title_en: obj.title.en })
        .exec()
        .then(async (dbHazard) => {
          // We check if a hazard already exists.
          // If it does don't create a new one.
          if (dbHazard) {
            const getHazardById: any = await this.hazardModel
              .findById(dbHazard._id)
              .exec();
            const updatedCountry = new this.hazardModel(obj);
            try {
              Object.keys(obj).forEach((d) => {
                getHazardById[d] = updatedCountry[d];
              });
              getHazardById.updated_at = new Date();
              return Promise.resolve(await getHazardById.save());
            } catch (e) {
              throw new NotFoundException('Could not update hazard.');
            }
          } else {
            obj.title_en = obj.title.en;
            return Promise.resolve(await this.hazardModel.create(obj));
          }
        })
        .catch((error) => Promise.reject(error));
    });
  }

  private async hazardSeeder(hazard: any, obj: any) {
    if (hazardTypes[hazard.type]?.title) {
      const hazardType = await this.hazardTypeModel
        .findOne({ title: hazardTypes[hazard.type].title })
        .exec();
      obj.hazard_type = hazardType ? hazardType._id : null;
    }
  }
}
