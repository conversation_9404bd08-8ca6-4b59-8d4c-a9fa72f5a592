//Import Library
import Link from "next/link";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";

import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import { useTranslation } from 'next-i18next';
import apiService from "../../../services/apiService";

const UpdateTypeTable = (_props: any) => {
    const { t } = useTranslation('common');
    const [tabledata, setDataToTable] = useState([]);
    const [, setLoading] = useState(false);
    const [totalRows, setTotalRows] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [isModalShow, setModal] = useState(false);
    const [selectUpdateType, setSelectUpdateType] = useState({});
    const modalHide = () => setModal(false);
    
    const columns = [
        {
            name: t("adminsetting.updatestype.Title"),
            selector: "title",
        },
        {
            name: t("adminsetting.updatestype.Icon"),
            selector: "icon",
            cell: (d) => <i className={`fas ${d.icon}`}></i>,
        },
        {
            name: t("adminsetting.updatestype.Action"),
            selector: "",
            cell: (d) => (
                <div>
                    <Link href="/adminsettings/[...routes]" as={`/adminsettings/edit_update_type/${d._id}`}>

                        <i className="icon fas fa-edit" />

                    </Link>
                    &nbsp;
                    <a onClick={() => userAction(d)}>
                        <i className="icon fas fa-trash-alt" />
                    </a>{" "}
                </div>
            ),
        },
    ];
    const updateTypeParams = {
        sort: { title: "asc" },
        limit: perPage,
        page: 1,
        query: {},
    };

    useEffect(() => {
        getUpdateTypeData(updateTypeParams);
    }, []);

    const getUpdateTypeData = async (updateTypeParams_initials) => {
        setLoading(true);
        const response = await apiService.get("/updatetype", updateTypeParams_initials);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setTotalRows(response.totalCount);
            setLoading(false);
        }
    };

    const handlePageChange = (page) => {
        updateTypeParams.limit = perPage;
        updateTypeParams.page = page;
        getUpdateTypeData(updateTypeParams);
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        updateTypeParams.limit = newPerPage;
        updateTypeParams.page = page;
        setLoading(true);
        const response = await apiService.get("/updatetype", updateTypeParams);
        if (response && response.data && response.data.length > 0) {
            setDataToTable(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/updatetype/${selectUpdateType}`);
            getUpdateTypeData(updateTypeParams);
            setModal(false);
            toast.success(t("adminsetting.updatestype.Table.updateTypeDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.updatestype.Table.errorDeletingUpdateType"));
        }
    };

    const userAction = async (row) => {
        setSelectUpdateType(row._id);
        setModal(true);
    };

    return (
        <div>
            <Modal show={isModalShow} onHide={modalHide}>
                <Modal.Header closeButton>
                    <Modal.Title>{t("adminsetting.updatestype.DeleteUpdateType")}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{t("adminsetting.updatestype.Areyousurewanttodeletethisupdatetype?")}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={modalHide}>
                        {t("adminsetting.updatestype.Cancel")}
                    </Button>
                    <Button variant="primary" onClick={modalConfirm}>
                        {t("adminsetting.updatestype.Yes")}
                    </Button>
                </Modal.Footer>
            </Modal>

            <RKITable
                columns={columns}
                data={tabledata}
                totalRows={totalRows}
                pagServer={true}
                handlePerRowsChange={handlePerRowsChange}
                handlePageChange={handlePageChange}
            />
        </div>
    );
};
export default UpdateTypeTable;
