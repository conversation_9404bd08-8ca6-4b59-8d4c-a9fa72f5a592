//Import Library
import React, {useEffect, useState} from "react";
import Link from 'next/link';
import Button from 'react-bootstrap/Button';
import {Container, Col, Row} from "react-bootstrap";

//Import services/components
import RegionsMultiCheckboxes from "../../components/common/RegionsMultiCheckboxes";
import PageHeading from "../../components/common/PageHeading";
import InstitutionsTable from "./InstitutionsTable";
import InstitutionMapQuickInfo from "./InstitutionMapQuickInfo";
import ListMapContainer from "./ListMapContainer";
import { useTranslation } from 'next-i18next';
import { canAddInstitution } from "./permission";
import apiService from "../../services/apiService";
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const Institutions = () => {
  const [institutions, setInstitutions] = useState([]);
  const [selectedRegions, setSelectedRegions] = useState(null);
  const { t } = useTranslation('common');

  const AddOrganisationComponent = () => {
    return (
      <Link href='/institution/[...routes]' as='/institution/create' >
        <Button variant="secondary" size="sm">
          {t('addOrganisation')}
        </Button>
      </Link>
    );
  };

  const regionHandler = (val) => {
    setSelectedRegions(val);
  }

  const CanAddInstitution = canAddInstitution(() => <AddOrganisationComponent />);

  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={12}>
          <PageHeading title={t('menu.organisations')} />
        </Col>
      </Row>
      <Row>
        <Col xs={12} className="organisationmap_div">
          <div className="organisationMap">
            <ListMapContainer institutions={institutions} />
          </div>
          <div className="organisationInfo">
            <InstitutionMapQuickInfo />
          </div>
        </Col>
      </Row>
      <Row>
        <Col xs={12}>
          <RegionsMultiCheckboxes
            filtreg={(val)=> regionHandler(val)}
            selectedRegions={[]}
            regionHandler={regionHandler}
          />

        </Col>
      </Row>
      <Row>
        <Col xs={12} className="ps-4">
          <CanAddInstitution />
        </Col>
      </Row>
      <Row className="mt-3">
        <Col xs={12}>
          <InstitutionsTable selectedRegions={selectedRegions} setInstitutions={setInstitutions}  />
        </Col>
      </Row>
    </Container>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Institutions;