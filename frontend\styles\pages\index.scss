.landing {
  .header-block {
    width: 100%;
    padding: 15px;
    position: fixed;
    top: 0;
    background: #fff;
    // z-index: 9999;
    z-index: 100;

    .header-container {
      width: 100%;
      max-width: 1170px;
      margin: 0px auto;
    }

    .quick-menu {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: end;
      position: relative;

      li {
        list-style: none;
        display: block;
        padding: 3px 12px;
        position: relative;
        cursor: pointer;

        &::after {
          position: absolute;
          left: 0;
          content: " ";
          width: 1px;
          height: 13px;
          background: #003f97;
          top: 10px;
        }

        &:first-child {
          &::after {
            width: 0;
          }
        }

        a {
          color: #003f97;
          font-size: 13px;
        }

        &.login {
          background: #003f97;
          border-radius: 3px;

          a {
            color: #ffffff;
            font-size: 13px;
          }
        }
      }
    }
  }
  .home {
    width: 100%;
    margin: 20px 0px;
  }

  .horizontal-line {
    width: 100%;
    background-color: rgb(0, 63, 151);
    margin-top: 100px;
    height: 6px;
  }

  .feeds-section {
    width: 100%;
    max-width: 1170px;
    margin: 0px auto;
  }
  .network-news {
    display: block;
    height: auto;
    overflow: hidden;
    clear: both;
    text-align: left;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ededed;
    img {
      width: 200px;
      float: left;
      margin-right: 15px;
      // margin-top: 0.5%;
    }
    .newsContent {
      float: left;
      width: calc(100% - 225px);
      .newsTitle {
        font-weight: bold;
        color: #000000;
        margin-bottom: 5px;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }

  .about-us {
    width: 100%;
    background-color: rgb(0, 63, 151);
    margin-bottom: 25px;

    .aboutus-wrap {
      width: 100%;
      max-width: 1170px;
      margin: 0px auto;
    }

    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .title {
      margin-left: 30px;
      color: #ffffff;
      font-size: 32px;
      font-weight: bold;
    }
    .desc {
      margin-left: 30px;
      color: #ffffff;
      font-size: 16px;
    }
  }

  .feed-news {
    display: block;
    height: auto;
    overflow: hidden;
    clear: both;
    text-align: left;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ededed;
    img {
      width: 115px;
      float: left;
      margin-right: 10px;
      margin-left: 10px;
    }
    .newsContent {
      float: left;
      width: calc(100% - 135px);
      .newsTitle {
        font-weight: bold;
        color: #000000;
        margin-bottom: 5px;
        font-size: 14px;
        float: left;
        span {
          opacity: 0.75;
          font-weight: normal;
        }
      }
      .newsDesc {
        font-size: 13px;
        display: block;
        clear: both;
        line-height: 18px;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }

  .newsDesc a {
    font-size: 16px;
  }

  .block-title {
    text-transform: uppercase;
    text-align: left;
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 15px;
    padding: 5px 10px;
    background: #ffffff;
    position: relative;
    &::after {
      position: absolute;
      height: 1px;
      width: calc(100% - 170px);
      background: #003f97;
      content: " ";
      top: 18px;
      left: 170px;
    }
    &.news-feeds {
      &::after {
        width: calc(100% - 130px);
        left: 140px;
      }
    }
  }

  .welcome {
    width: 100%;
    padding: 60px 0px;

    .about-section {
      width: 100%;
      max-width: 1170px;
      margin: 0px auto;
      text-align: center;

      .title {
        color: #003f97;
        font-size: 21px;
        margin-bottom: 15px;
      }
      p {
        max-width: 960px;
        margin: 0 auto 10px;
        font-size: 16px;
      }
    }
  }
  //  .search  {
  //     height: auto;
  //     overflow: hidden;
  //     margin-top: 10px;
  //     position: relative;

  //     input {
  //       border-radius: 20px;
  //       border: 1px solid #cdd8ea;
  //       float: right;
  //       min-width: 300px;
  //       padding: 3px 14px;
  //       font-size: 14px;
  //       position: relative;
  //       z-index: 1;

  //       &:focus {
  //         outline: none;
  //       }
  //     }

  //     i {
  //       font-size: 14px;
  //       color: rgb(20, 20, 20);
  //       position: absolute;
  //       right: 15px;
  //       z-index: 9;
  //       width: 0.75em;
  //       height: 0.75em;
  //       top: 6px;
  //     }
  //   }

  .slider {
    width: 100%;
    max-width: 1170px;
    margin: 0px auto;

    .myslider {
      position: relative;
      height: 500px;
      overflow: hidden;

      img {
        position: relative;
        z-index: 1;
      }

      .sliderContent {
        position: absolute;
        width: 50%;
        top: 20%;
        z-index: 9;
        left: 5%;

        .sliderTitle {
          background: #003f97;
          padding: 0 15px;
          display: inline-block;
          font-size: 34px;
          color: #ffffff;
          margin-bottom: 15px;
          font-weight: 400;
        }

        .sliderDesc {
          background: rgba(0, 0, 0, 0.75);
          padding: 10px 15px;
          display: inline-block;
          font-size: 16px;
          color: #ffffff;
          margin-bottom: 15px;
          font-weight: 400;
          a {
            font-size: 14px;
          }
        }
      }
    }
  }  

  .landing-menu {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;

    li {
      list-style: none;
      display: block;
      padding: 15px 0;

      a {
        color: #ffffff;
      }
    }
  }

  .contactInfo {
    p {
      color: #ffffff;
      font-size: 12px;
      margin: 0;
      float: left;
      width: calc(100% - 46px);
    }
    h4 {
      color: #ffffff;
      margin-left: 30px;
    }
  }

  .contactNum {
    p {
      color: #ffffff;
      font-size: 12px;
      margin: 0;
      float: left;
      margin-left: 30px;
      width: auto;
    }
    h4 {
      color: #ffffff;
      margin-left: 30px;
    }
  }

  .landing-footer {
    width: 100%;
    background-color: rgb(0, 63, 151);
    padding: 25px 0px;

    .footer-block {
      width: 100%;
      max-width: 1170px;
      margin: 0px auto;
    }
    .footerLeft {
      img {
        float: left;
        width: 36px;
        margin-right: 10px;
      }
      p {
        color: #ffffff;
        font-size: 12px;
        margin: 0;
        float: left;
        width: calc(100% - 46px);
      }
    }
    .footer-links {
      padding: 0 30px;
      border-end: 1px solid rgba(255, 255, 255, 0.1);
      margin: 0;
      min-height: 500px;
      li {
        margin: 12px 0;
        a {
          color: #ffffff;
          font-size: 12px;
        }
      }
    }
  }
}

.response-message-block {
  position: absolute;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ql-editor{
  min-height: 200px;
}