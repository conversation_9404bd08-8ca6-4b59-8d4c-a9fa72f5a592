//Import Library
import { Controller, Get, Query, Post, Body, Param, Delete, Patch, UseGuards } from '@nestjs/common';

//Import services/components
import { CreateTaxonomyIconDto } from './dto/create-taxonomy-icon.dto';
import { UpdateTaxonomyIconDto } from './dto/update-taxonomy-icon.dto';
import { TaxonomyIconService } from "./taxonomy-icon.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('taxonomyicon')
@UseGuards(SessionGuard)
export class TaxonomyIconController {

  constructor(
    private readonly _taxonomyIconService: TaxonomyIconService
  ) { }

  @Post()
  create(@Body() createTaxonomyIconDto: CreateTaxonomyIconDto) {
    const resp = this._taxonomyIconService.create(createTaxonomyIconDto);
    return resp;
  }

  @Get()
  findAll(@Query() query: any) {
    return this._taxonomyIconService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') taxonomyIconId: string) {
    return this._taxonomyIconService.get(taxonomyIconId);
  }

  @Patch(':id')
  update(@Param('id') taxonomyIconId: string, @Body() updateTaxonomyIconDto: UpdateTaxonomyIconDto) {
    const resp = this._taxonomyIconService.update(taxonomyIconId, updateTaxonomyIconDto);
    return resp;
  }

  @Delete(':id')
  remove(@Param('id') taxonomyIconId: string) {
    const deletedData = this._taxonomyIconService.delete(taxonomyIconId);
    return deletedData;
  }
}
