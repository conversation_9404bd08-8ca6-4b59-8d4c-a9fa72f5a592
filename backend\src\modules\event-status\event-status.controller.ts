//Import Library
import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';

//Import services/components
import { CreateEventStatusDto } from './dto/create-event-status.dto';
import { UpdateEventStatusDto } from './dto/update-event-status.dto';
import { EventStatusService } from './event-status.service';
import { SessionGuard } from 'src/auth/session-guard';

@Controller('eventstatus')
@UseGuards(SessionGuard)
export class EventStatusController {
  constructor(private readonly _eventstatusService: EventStatusService) {}

  @Post()
  async create(@Body() createEventStatusDto: CreateEventStatusDto) {
    try {
      const resp = await this._eventstatusService.create(createEventStatusDto);
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Get()
  findAll(@Query() query: any) {
    return this._eventstatusService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') eventstatusId: string) {
    return this._eventstatusService.get(eventstatusId);
  }

  @Patch(':id')
  async update(
    @Param('id') eventstatusId: string,
    @Body() updateEventStatusDto: UpdateEventStatusDto,
  ) {
    try {
      const resp = await this._eventstatusService.update(
        eventstatusId,
        updateEventStatusDto,
      );
      return resp;
    } catch (error) {
      return {
        message: error.message,
        errorCode: error.code,
      };
    }
  }

  @Delete(':id')
  remove(@Param('id') eventstatusId: string) {
    const deletedData = this._eventstatusService.delete(eventstatusId);
    return deletedData;
  }
}
