//Import services/components
import connectedAuthWrapper from 'redux-auth-wrapper/connectedAuthWrapper';

interface AppState {
  permissions?: {
    update?: {
      [key: string]: boolean;
    };
  };
}

export const canAddDiscussionUpdate = connectedAuthWrapper({
  authenticatedSelector: (state: AppState) => {
    if (state.permissions && state.permissions.update && state.permissions.update['create:any']) {
      return true;
    }
    return false;
  },
  wrapperDisplayName: 'CanAddDiscussionUpdate',
});

export default canAddDiscussionUpdate;
