//Import Library
import React, { useState } from "react";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import moment from "moment";
import { Accordion, Card, Col, Row } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';

const OperationDetailsAccordian = (props) => {
    const { t } = useTranslation('common');
    const formatDate = "MM-D-YYYY HH:mm:ss";
    const formatDateWithoutTime = "MM-D-YYYY";
    const [section, setSection] = useState(false);
    return (
        <>
            <Accordion.Item eventKey="0">
                <Accordion.Header onClick={() => setSection(!section)}>
                    <div className="cardTitle">{t("OperationDetails")}</div>
                    <div className="cardArrow">
                        {section ? (
                            <FontAwesomeIcon icon={faMinus} color="#fff" />
                        ) : (
                            <FontAwesomeIcon icon={faPlus} color="#fff" />
                        )}
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                        <Row className="operationData">
                            <Col md lg={6} sm={12} className="ps-0">
                                <p>
                                    <b>{t("HazardType")}</b>:
                                    <span>
                                        {props.operation.hazard_type
                                            ? props.operation.hazard_type.title
                                            : null}
                                    </span>
                                </p>
                                {hazard_separated_func(t, props.operation)}
                                <p>
                                    <b>{t("Syndrome")}</b>:
                                    <span>
                                        {props.operation.syndrome
                                            ? props.operation.syndrome.title
                                            : null}
                                    </span>
                                </p>
                                <p>
                                    <b>{t("Created")}</b>:
                                    <span>
                                        {moment(props.operation.created_at).format(
                                            formatDate
                                        )}
                                    </span>
                                </p>
                                <p>
                                    <b>{t("LastModified")}</b>:
                                    <span>
                                        {moment(props.operation.updated_at).format(
                                            formatDate
                                        )}
                                    </span>
                                </p>
                            </Col>

                            <Col md lg={6} sm={12} className="ps-0">
                                <p>
                                    <b>{t("CountryOrTerritory")}</b>:
                                    <span>
                                        {props.operation.country
                                            ? props.operation.country.title
                                            : null}
                                    </span>
                                </p>
                                <p>
                                    <b>{t("OperationStatus")}</b>:
                                    <span> {props.operation.status.title} </span>
                                </p>
                                <p>
                                    <b>{t("StartDate")}</b>:
                                    <span>
                                        {props.operation.start_date
                                            ? moment(props.operation.start_date).format(
                                                formatDateWithoutTime
                                            )
                                            : null}
                                    </span>
                                </p>
                                <p>
                                    <b>{t("EndDate")}</b>:
                                    <span>
                                        {props.operation.end_date
                                            ? moment(props.operation.end_date).format(
                                                formatDateWithoutTime
                                            )
                                            : null}
                                    </span>
                                </p>
                            </Col>
                        </Row>
                </Accordion.Body>
            </Accordion.Item>
        </>
    )
}

export default OperationDetailsAccordian;
function hazard_separated_func(
    t,
    operationData: {
        title: string;
        timeline: any[];
        description: string;
        hazard_type: { title: string };
        hazard: any[];
        syndrome: { title: string };
        created_at: string;
        updated_at: string;
        country: { title: string };
        status: { title: string };
        start_date: string;
        end_date: string;
        partners: any[];
        images: any[];
        images_src: any[];
        document: any[];
        doc_src: any[];
    }
) {
    return (
        <div className="d-flex mb-2 pb-1">
            <b>{t("Hazard")}</b>:
            <span>
                <ul className="comma-separated">
                    {operationData.hazard && operationData.hazard.length >= 1
                        ? operationData.hazard.map((item, i) => {
                            return <li key={i}>{item.title.en}</li>;
                        })
                        : null}
                </ul>
            </span>
        </div>
    );
}