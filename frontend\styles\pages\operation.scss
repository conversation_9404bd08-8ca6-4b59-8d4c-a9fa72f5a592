.operationDetail {
  padding-top: 15px;
}

.operationAccordion {
  padding-top: 25px;
  padding-bottom: 25px;

  .card {
    border: none;
    margin: 25px 0;
  }

  .card-header {
    background: transparent;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303030;
    cursor: pointer;
    border: none;

    &:before {
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      content: "";
      opacity: 1;
      position: absolute;
      background-color: #ddd;
    }

    .cardTitle {
      display: inline;
      background: #fff;
      z-index: 2;
      position: relative;
      padding: 0 15px 0 0;
    }

    .cardArrow {
      z-index: 2;
      position: absolute;
      margin-right: 0;
      color: #ffffff;
      background: #229adb;
      background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 100%);
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#1e6090', GradientType=1);
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      right: 0;
      top: 0;

      svg {
        font-size: 16px;
      }
    }
  }
}

.opButton{
  width:150px
}


.operationInfo-Items {
  display: flex;

  .operationIcon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 5px solid #ddd;
    border-radius: 50%;
    background: #229adb;
    background: -moz-radial-gradient(center, ellipse cover, #229adb 0%, #1e6090 80%, #174970 100%);
    background: -webkit-radial-gradient(center, ellipse cover, #229adb 0%, #1e6090 80%, #174970 100%);
    background: radial-gradient(ellipse at center, #229adb 0%, #1e6090 80%, #174970 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#229adb', endColorstr='#174970', GradientType=1);
  }
}

.operationInfo {
  color: #1c587a;
  margin: 0 0 0 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  h5 {
    font-size: 18px;
    font-weight: 400;
    margin: 0;
  }

  h4 {
    font-size: 38px;
    font-weight: 400;
    margin: 0;
    line-height: 34px;
  }
}

// Timeline

.operatinTimeline {
  .progressbar-container {
    .progressbar {
      padding: 0 30px;
      li {
        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
}

.timelineIcon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 7px solid #ddd;
  border-radius: 50%;
  background: #d6d6d6;
  background: -moz-radial-gradient(center, ellipse cover, #d6d6d6 0%, #aaaaaa 65%, #848484 80%, #707070 100%);
  background: -webkit-radial-gradient(center, ellipse cover, #d6d6d6 0%, #aaaaaa 65%, #848484 80%, #707070 100%);
  background: radial-gradient(ellipse at center, #d6d6d6 0%, #aaaaaa 65%, #848484 80%, #707070 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#d6d6d6', endColorstr='#707070', GradientType=1);
  margin: 0 auto;

  .svg-inline--fa {
    font-size: 30px
  }
}

.step-label {
  color: #2d3442;
  font-weight: 600;
  font-size: 15px;
  text-transform: capitalize;
  margin: 10px 0 0;
  white-space: break-spaces;
}

.step-text {
  font-size: 15px;
  color: #2d3442;
  text-transform: capitalize;

}

.progress_main_sec {
  display: inline-block;
  position: relative;
  width: 100%;
  margin-top: 50px;
}

.prev {
  position: absolute;
  content: "";
  left: 0;
  top: 27.5%;
  width: 40px;
  height: 100%;
  text-align: center;
  font-size: 28px;
  z-index: 1000;
  background: #fff;
  padding: 20px 0 0;
}

.next {
  position: absolute;
  content: "";
  right: 0;
  top: 27.5%;
  width: 40px;
  height: 100%;
  text-align: center;
  font-size: 28px;
  z-index: 1000;
  background: #fff;
  padding: 20px 0 0;
}

.progressbar-container {
  overflow-x: scroll;
  width: 90%;
  z-index: -1;
  margin: 0 auto;
}

.progressbar-container::-webkit-scrollbar {
  display: none;
}

.progressbar {
  counter-reset: step;
  z-index: 3;
  white-space: nowrap;
  width: 90%;
}

.progressbar li {
  list-style-type: none;
  width: 30%;
  font-size: 12px;
  position: relative;
  text-align: center;
  text-transform: uppercase;
  color: #7d7d7d;
  z-index: 1;
  display: inline-block;
  vertical-align: top;
}

.progressbar li:after {
  width: 100%;
  height: 6px;
  content: "";
  position: absolute;
  background-color: #e5e5e5;
  top: 24%;
  left: -60%;
  z-index: -1;
}

.progressbar li:first-child {
  margin-left: -60px;

  &:after {
    content: none;
  }
}

.progressbar li:last-child {
 padding-right: 50px;
}

.progressbar li.active {
  color: green;
}

.progressbar li.active:before {
  border-color: #55b776;
}

.progressbar li.active+li:after {
  background-color: #55b776;
}

.operationData {
  p {
    margin: 0 0 13px;
    display: flex;
  }

  b {
    width: 155px;
    color:#0b1627;
    display: inline-block;
  }

  span {
    display: inline-block;
    width: calc(100% - 160px);
    margin: 0 0 0 5px;
  }
}

#main .readMoreText {
  color: #2da1ea;
  cursor: pointer;
  text-decoration: underline;
  //top: -13px;
  position: relative;
}

// Responsive 
@media screen and (max-width: 767px) {
  .main-container {
    .operationInfo-Items {
      margin: 0 0 20px;
    }
    .progressbar-container{
      width: 100%;
      .progressbar li {
        width: 85%;
        &:first-child {
          margin-left: 0; 
        }
      }
    }
  }
}


// @media screen and (max-width: 1366px) {
//   .step-label {
//     margin-left: -80px;
//   }
//   .step-text {
//     margin-left: -80px;
//   }
//   .icon-label-institution {
//     margin-left: 30px;
//   }
//   .icon-text-institution {
//     margin-left: 27px;
//   }
//   .progressbar-container {
//     width: 100%;
//   }
// }

// @media screen and (min-width: 1367px) and (max-width: 1536px) {
//   .step-label {
//     margin-left: -118px;
//   }
//   .step-text {
//     margin-left: -118px;
//   }
//   .progressbar li:after {
//     left: -78%;
//   }
//   .icon-label {
//     margin-left: -19px;
//   }
//   .icon-days {
//     margin-left: -19px;
//   }
//   .icon-text {
//     margin-left: -19px;
//   }
//   .icon-label-institution {
//     margin-left: 30px;
//   }
//   .icon-text-institution {
//     margin-left: 27px;
//   }
//   .progressbar-container {
//     width: 90%;
//   }
// }

// @media screen and (min-width: 1537px) and (max-width: 1920px) {
//   .step-label {
//     margin-left: -150px;
//   }
//   .step-text {
//     margin-left: -150px;
//   }
//   .progressbar li:after {
//     left: -78%;
//   }
//   .icon-label {
//     margin-left: -55px;
//   }
//   .icon-days {
//     margin-left: -55px;
//   }
//   .icon-text {
//     margin-left: -55px;
//   }
// }

// @media screen and (min-width: 1921px) {
//   .step-label {
//     margin-left: -216px;
//   }
//   .step-text {
//     margin-left: -216px;
//   }
//   .progressbar li:after {
//     left: -78%;
//   }
//   .icon-label {
//     margin-left: -106px;
//   }
//   .icon-days {
//     margin-left: -106px;
//   }
//   .icon-text {
//     margin-left: -106px;
//   }
// }
