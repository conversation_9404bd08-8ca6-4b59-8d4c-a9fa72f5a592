import React from 'react';
import { GoogleMap, useJsApiLoader } from '@react-google-maps/api';
import RKIMapInfowindow from './RKIMapInfowindow';
import mapStyles from './mapStyles';
import { useRouter } from "next/router";
import { useGoogleMaps } from './GoogleMapsProvider';

interface RKIMap1Props {
  markerInfo?: React.ReactNode;
  activeMarker?: any;
  initialCenter?: { lat: number; lng: number };
  children?: React.ReactNode;
  height?: number | string;
  width?: string;
  language?: string;
  points?: any[];
  zoom?: number;
  minZoom?: number;
  onClose?: () => void
}

const RKIMap1: React.FC<RKIMap1Props> = ({
  markerInfo,
  activeMarker,
  initialCenter,
  children,
  height = 300,
  width = "114%",
  language,
  zoom = 1,
  minZoom = 1,
  onClose
}) => {
  const { locale } = useRouter();
  const { isLoaded, loadError } =  useGoogleMaps();
  const containerStyle = {
    width: width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  const defaultCenter = {
    lat: 52.520017,
    lng: 13.404195,
  };

  const center = initialCenter || defaultCenter;

  const onMapLoad = (map: google.maps.Map) => {
    map.setOptions({
      styles: mapStyles,
    });
  };

  if (loadError) return <div>Error loading maps</div>;
  if (!isLoaded) return <div>Loading Maps...</div>;

  return (
    <div className="map-container">
      <div className="mapprint" style={{ width, height, position: 'relative' }}>
        <GoogleMap
          mapContainerStyle={containerStyle}
          center={center}
          zoom={zoom}
          onLoad={onMapLoad}
          options={{
            minZoom: minZoom,
            draggable: true,
            keyboardShortcuts: false,
            streetViewControl: false,
            panControl: false,
            clickableIcons: false,
            mapTypeControl: false,
            fullscreenControl: true,
          }}
        >
          {children}
          {markerInfo && activeMarker && activeMarker.getPosition && (
            <RKIMapInfowindow
              position={activeMarker.getPosition()}
              onCloseClick={() => {
                // Handle close if needed
                console.log('close click');
                onClose()
              }}
            >
              {markerInfo}
            </RKIMapInfowindow>
          )}
        </GoogleMap>
      </div>
    </div>
  );
};

export default RKIMap1;
