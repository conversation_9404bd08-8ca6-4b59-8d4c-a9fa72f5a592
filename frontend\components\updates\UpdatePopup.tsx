//Import Library
import React, { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  Button,
  Row,
  Col,
  <PERSON><PERSON><PERSON>,
  Spinner,
} from "react-bootstrap";
import moment from "moment";
import Modal from "react-bootstrap/Modal";
import Router from "next/router";

//Import services/components
import apiService from "../../services/apiService";
import ReadMoreContainer from "../common/readMore/readMore";
import Document from "./utils/Document";
import Links from "./utils/Link";
import Image from "./utils/Image";
import { canDeleteUpdate, canEditUpdate } from "./permissions";
import { useTranslation } from 'next-i18next';

const formatDate = "MM-D-YYYY HH:mm:ss";
const Calendar = "Calendar Event"
interface UpdatePopupProps {
  item?: any;
  onRemoveUpdate?: (id: string) => void;
  routes?: string[];
  [key: string]: any;
}

const UpdatePopup = (props: UpdatePopupProps) => {
  const { t } = useTranslation('common');
  const [update, setUpdate] = useState({
    type: "",
    title: "",
    description: "",
    created_at: "",
    updated_at: "",
    link: [],
    reply: [],
    _id: "",
    update_type: "",
    document: [],
    images: [],
    images_src: [],
    contact_details: []
  }); // Initial Schema

  const [popup, setPopup] = useState({ enable: false, event: "close" });
  const [updatetype, setUpdateType] = useState("");
  const [smShow, setSmShow] = useState(false);
  const smhandleClose = () => setSmShow(false);

  const confirm = async (itemInitial: any) => {
    await apiService.remove(`/updates/${itemInitial._id}`);
    setSmShow(false);
    const parentType = `parent_${itemInitial.type}`;
    Router.push(`/${itemInitial.type}/show/${itemInitial[parentType]}`);
  };

  useEffect(() => {
    let _cleatTimeout: NodeJS.Timeout | null = null;
    const fetchUpdateData = async (id: string) => {
      const respData = await apiService.get(`/updates/${id}`);
      if (respData && respData.type) {
        const getUpdateType = await apiService.get(
          `/updateType/${respData.update_type}`
        );

        _cleatTimeout = setTimeout(() => {
          setUpdateType(getUpdateType ? getUpdateType.title : "");
          setUpdate(respData);
          setPopup((prevState) => ({ ...prevState, ...{ enable: true } }));
        }, 500); // timeout for Collapse delay
      }
    };

    if (
      props &&
      props.routes &&
      props.routes[2] === "update" &&
      props.routes[3]
    ) {
      if (_cleatTimeout) clearTimeout(_cleatTimeout);
      setPopup({ enable: false, event: "open" });
      fetchUpdateData(props.routes[3]);
    }
  }, [props.routes]);

  const getComponent = () => {
    switch (updatetype) {
      case "Document":
        return <Document document={update.document} doc_src={update.images_src || []} />;

      case Calendar:
        return <Document document={update.document} doc_src={update.images_src || []} />;

      case "Image":
        return (
          <Image data={update.images} {...props} srcText={update.images_src} />
        );

      case "General / Notice":
        return (<div><Document document={update.document} doc_src={update.images_src || []} />
         <Image data={update.images} {...props} srcText={update.images_src} />
         </div>);


      case "Link":
        return <Links {...update} />;

      default:
        return null;
    }
  };

  const EditLink = () => {
    return (
      <Link
        href={{
          pathname: `/updates/[...routes]`,
          query: {
            parent_type: update.type,
            update_type: update.update_type,
          },
        }}
        as={`/updates/edit/${update._id}?parent_type=${update.type}&update_type=${update.update_type}`}
        >
        <i className="fas fa-pen"></i>
      </Link>
    );
  }

  const DeleteLink = () => {
    return (
      <>
        <i className="fas fa-trash-alt" onClick={() => setSmShow(true)} />
        <Modal
          size="sm"
          show={smShow}
          onHide={() => setSmShow(false)}
          aria-labelledby="example-modal-sizes-title-sm"
        >
          <Modal.Header closeButton>
            <Modal.Title id="example-modal-sizes-title-sm">
              {update.title}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {t("Areyousureyouwanttodelete")} {update.title}?
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={smhandleClose}>
              {t("Cancel")}
            </Button>
            <Button variant="primary" onClick={() => confirm(update)}>
              {t("OK")}
            </Button>
          </Modal.Footer>
        </Modal>
      </>
    )
  }

  const CanEditUpdate = canEditUpdate(() => <EditLink />)

  const CanDeleteUpdate = canDeleteUpdate(() => <DeleteLink />)

  return (
    <div className="updatesPopMain">
      {!popup.enable && popup.event === "open" ? (
        <Spinner animation="border" variant="primary" />
      ) : null}
      {update && update._id && props.routes ? (
        <Collapse in={popup.enable}>
          <div className="updatesPopupBlock">
            <div className="updateActions">
              <CanEditUpdate update={update} />
              <CanDeleteUpdate update={update} />
              <i
                className="fas fa-times"
                onClick={(e) => {
                  setPopup({ enable: false, event: "close" });
                }}
              ></i>
            </div>
            <Row>
              <Card.Body className="mt-4">
                <Row className="operationData">
                  <Col>
                    <h3>{update.title}</h3>
                    <ReadMoreContainer description={update.description} />
                    <Col>{getComponent()}</Col>
                  </Col>
                  <Col lg={5}>
                    <p>
                      <b>{t("UpdateType")}</b>:<span> {updatetype} </span>
                    </p>
                    { updatetype === "Contact" && (update as any).contact_details &&
                     <p>
                     <b>{t("MobileNo")}</b>:<span> {(update as any).contact_details.mobileNo} </span>
                   </p>
                    }
                    { updatetype === "Contact" && (update as any).contact_details &&
                     <p>
                     <b>{t("TelephoneNo")}</b>:<span> {(update as any).contact_details.telephoneNo} </span>
                   </p>
                    }
                    {updatetype === Calendar && (update as any).start_date &&
                      <p>
                        <b>{t("StartDate")}</b>:<span> {moment((update as any).start_date).format(formatDate)}</span>

                      </p>
                    }
                    {updatetype === Calendar && (update as any).end_date &&
                      <p>
                        <b>{t("EndDate")}</b>:<span>{moment((update as any).end_date).format(formatDate)}</span>
                      </p>
                    }
                    <p>
                      <b>{t("Created")}</b>:
                      <span>
                        {moment(update.created_at).format(formatDate)}
                      </span>
                    </p>
                    <p>
                      <b>{t("LastModified")}</b>:
                      <span>
                        {moment(update.updated_at).format(formatDate)}
                      </span>
                    </p>
                  </Col>
                </Row>
              </Card.Body>
            </Row>
          </div>
        </Collapse>
      ) : null}
    </div>
  );
};

export default UpdatePopup;
