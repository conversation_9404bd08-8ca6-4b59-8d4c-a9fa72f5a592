//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { RolesSeederService } from './roles-seeder.services';
// SCHEMAS
import { RolesSchema } from 'src/schemas/roles.schemas';

/**
 * Import and provide seeder classes for Syndrome.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Roles', schema: RolesSchema }
      ]
    )
  ],
  providers: [RolesSeederService],
  exports: [RolesSeederService],
})
export class RolesSeederModule { }
