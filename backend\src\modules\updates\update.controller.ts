//Import Library
import {
  <PERSON>,
  Get,
  Query,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  HttpStatus, HttpException
} from '@nestjs/common';
import {Request} from "express";
import {ACGuard, InjectRolesBuilder, RolesBuilder, UseRoles} from "nest-access-control";

//Import services/components
import { CreateUpdateDto } from './dto/create-update.dto';
import { UpdateUpdateDto } from './dto/update-update.dto';
import { UpdateService } from "./update.service";
import { ImageService } from "./../image/image.service";
import { FilesService } from "./../files/files.service";
import { SessionGuard } from 'src/auth/session-guard';
import {ResponseError} from "../../common/dto/response.dto";

@Controller('updates')
@UseGuards(SessionGuard)
export class UpdateController {
  constructor(
    private readonly _updateService: UpdateService,
    private readonly _imageService: ImageService,
    private readonly _filesService: FilesService,
    @InjectRolesBuilder() private readonly roleBuilder: RolesBuilder
  ) { }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'update',
    action: 'create',
    possession: 'any',
  })
  @Post()
  async create(@Body() createUpdateDto: CreateUpdateDto, @Req() request: Request) {
    const user: any = request.user;
    createUpdateDto['user'] = user._id;
    const _update = await this._updateService.create(createUpdateDto);
    const imageIds = _update['images'] ? _update['images'].map((d) => d._id) : [];
    if (imageIds.length > 0) {
      await this._imageService.bulkUpdate(imageIds);
    }
    const documentIds = _update['document'] ? _update['document'].map((d) => d['_id']) : [];
    const mediaIds = _update['media'] ? _update['media'].map((d) => d['_id']) : [];
    if (documentIds.length > 0 || mediaIds.length > 0) {
      const allFilesIds = [...documentIds, ...mediaIds];
      await this._filesService.bulkUpdate(allFilesIds);
    }
    return _update;
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'update',
    action: 'read',
    possession: 'any',
  })
  @Get()
  findAll(@Query() query: any) {
    return this._updateService.findAll(query);
  }

  @UseGuards(ACGuard)
  @UseRoles({
    resource: 'update',
    action: 'read',
    possession: 'any',
  })
  @Get(':id')
  findOne(@Param('id') updateId: string) {
    return this._updateService.get(updateId);
  }

  @UseGuards(ACGuard)
  @Patch(':id')
  async update(@Param('id') updateId: string, @Body() updateUpdateDto: UpdateUpdateDto, @Req() request: Request) {
    const user: any = request.user;
    const oldUpdate: any = await this._updateService.get(updateId);
    const updateUserid = oldUpdate.user ? oldUpdate.user._id : null;
    const permission = (user._id == updateUserid) ?
      this.roleBuilder.can(user.roles).updateOwn('update').granted :
      this.roleBuilder.can(user.roles).updateAny('update').granted;
    if (permission) {
      const _update = await this._updateService.update(updateId, updateUpdateDto);
      const imageIds = _update['images'] ? _update['images'].map((d) => d._id) : [];
      if (imageIds.length > 0) {
        await this._imageService.bulkUpdate(imageIds);
      }
      const documentIds = _update['document'] ? _update['document'].map((d) => d['_id']) : [];
      const mediaIds = _update['media'] ? _update['media'].map((d) => d['_id']) : [];
      if (documentIds.length > 0 || mediaIds.length > 0) {
        const allFilesIds = [...documentIds, ...mediaIds];
        await this._filesService.bulkUpdate(allFilesIds);
      }
      return _update;  
    } else {
      throw new HttpException({status: HttpStatus.FORBIDDEN,message: ['Not authorized']},
        HttpStatus.FORBIDDEN,
      );
    }
  }

  @UseGuards(ACGuard)
  @Delete(':id')
  async remove(@Param('id') updateId: string, @Req() request: Request) {
    try {
        const permission = await this.updateControl(request, updateId);
      if (permission) {
        const _updateDetails = await this._updateService.delete(updateId);
        if (_updateDetails.images && _updateDetails.images.length > 0) {
          const imageIds = _updateDetails.images.map((d) => d._id);
          await this._imageService.bulkDelete(imageIds);
        }
        const documentIds = _updateDetails.document && _updateDetails.document.length > 0 ? _updateDetails.document.map((d) => d._id) : [];
        const mediaIds = _updateDetails.media && _updateDetails.media.length > 0 ? _updateDetails.media.map((d) => d._id) : [];
        if (documentIds.length > 0 || mediaIds.length > 0) {
          const fileIds = [...documentIds, ...mediaIds];
          await this._filesService.bulkDelete(fileIds);
        }
        return _updateDetails;
      } else {
        return new ResponseError("UPDATE.ERROR.ACCESS_DENIED");
      }
    }catch (e) {
      return new ResponseError("UPDATE.ERROR.NOT_FOUND");
    }
  }

  private async updateControl(request, updateId: string) {
    const user: any = request.user;
    const oldUpdate: any = await this._updateService.get(updateId);
    const updateUserid = oldUpdate.user ? oldUpdate.user._id : null;
    const permission = (user._id == updateUserid) ?
      this.roleBuilder.can(user.roles).deleteOwn('update').granted :
      this.roleBuilder.can(user.roles).deleteAny('update').granted;
    return permission;
  }
}
