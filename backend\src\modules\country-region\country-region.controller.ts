//Import Library
import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';

//Import services/components
import { CountryRegionService } from "./country-region.service";
import { SessionGuard } from 'src/auth/session-guard';

@Controller('country_region')
@UseGuards(SessionGuard)
export class CountryRegionController {
  constructor(
    private readonly _regionService: CountryRegionService
  ) { }

  @Get(':id')
  findOne(@Param('id') regionId: string, @Query() query: any) {
    return this._regionService.get(regionId, query);
  }
}
