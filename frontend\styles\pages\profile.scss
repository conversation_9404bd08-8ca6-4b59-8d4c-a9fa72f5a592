.profile--card {
  box-shadow: 0px 0px 7px 1px #eaeaea;
  border-color: #eaeaea;
}

.rki-table {
  overflow-x: hidden !important;
}

.imgStyle {
  background: #5c88aa;
  padding: 1px;
  border-radius: 50%;
  width: 150px;
  height: 150px;
  margin-left: 10px;
}
.editBtn {
  position: absolute;
  top: 86%;
  left: 1%;
  background-color: #6c757d;
  border-color: #6c757d;
  padding: 0.375rem 0.75rem;
  border-radius: 0.30rem;
  border: 2px solid #ffffff;
}

.clickable {
  cursor: pointer;
}

.form--outline input {
  outline: none;
}

.range-slider__wrap input {
  padding-bottom: 1rem !important;
}

.my-bookmark-table {
  header {
    padding-left: 0;
    padding-right: 0;
    .mb-3 {
      margin-bottom: 0;
    }  
  }
  .rki-table {
    overflow: inherit !important;
  }
}
