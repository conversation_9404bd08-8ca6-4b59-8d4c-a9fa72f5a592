//Import Library
import React, { useState, useEffect } from "react";
import _ from "lodash";

//Import services/components
import RKIMAP1 from "../../components/common/RKIMap1";
import RKIMapMarker from "../../components/common/RKIMapMarker";
import { useTranslation } from 'next-i18next';

const ListMapContainer = (props) => {
    const { i18n } = useTranslation('common');
    const currentLang = i18n.language;
    const { institutions } = props;
    const [activeMarker, setactiveMarker]: any = useState({});
    const [points, setPoints]: any = useState([]);
    const [markerInfo, setMarkerInfo]: any = useState({});

    const MarkerInfo = (Markerprops) => {
        const { info } = Markerprops;
        if (info && info.countryId) {
            let filteredInstitutions = institutions.filter((_institution) => _institution.status != "Request Pending");
            const MarkerInstution = filteredInstitutions.filter(
                (x) => x.address && x.address.country && x.address.country._id == info.countryId
            );
            return (
                <ul>
                    {MarkerInstution.map((item, index) => {
                        return (
                            <li key={index}>
                                <a href={`/${currentLang}/institution/show/${item._id}`}>{item.title}</a>
                            </li>
                        );
                    })}
                </ul>
            );
        }
        return null;
    };

    const resetMarker = () => {
        setactiveMarker(null);
        setMarkerInfo(null);
    };

    const onMarkerClick = (propsinit, marker, e) => {
        resetMarker();
        setactiveMarker(marker);
        setMarkerInfo({
            name: propsinit.name,
            id: propsinit.id,
            countryId: propsinit.countryId,
        });
    };

    const setPointsFromInstitutions = () => {
        const filterinstutionPoints = [];
        let filteredInstitutions = institutions.filter((_institution) => _institution.status != "Request Pending");
        _.forEach(filteredInstitutions, (institution) => {
            filterinstutionPoints.push({
                title: institution.title,
                id: institution._id,
                countryId:
                    institution &&
                    institution.address &&
                    institution.address.country &&
                    institution.address.country._id,
                lat:
                    institution.address &&
                    institution.address.country &&
                    institution.address.country.coordinates &&
                    parseFloat(institution.address.country.coordinates[0].latitude),
                lng:
                    institution.address &&
                    institution.address.country &&
                    institution.address.country.coordinates &&
                    parseFloat(institution.address.country.coordinates[0].longitude),
            });
        });
        setPoints([...filterinstutionPoints]);
    };

    useEffect(() => {
        setPointsFromInstitutions();
    }, [institutions]);

    return (
        <RKIMAP1
            onClose={resetMarker}
            language={currentLang}
            points={points}
            activeMarker={activeMarker}
            markerInfo={<MarkerInfo info={markerInfo} />}
        >
            {points.length >= 1
                ? points.map((item, index) => {
                      return (
                          <RKIMapMarker
                              key={index}
                              name={item.title}
                              id={item.id}
                              countryId={item.countryId}
                              icon={{
                                  url: "/images/map-marker-white.svg",
                              }}
                              onClick={onMarkerClick}
                              position={item}
                          />
                      );
                  })
                : null}
        </RKIMAP1>
    );
};

export default ListMapContainer;
