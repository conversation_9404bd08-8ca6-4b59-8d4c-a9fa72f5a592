//Import Library
import React from "react";
import { Container, Col, Row } from "react-bootstrap";

//Import services/components
import PeopleTable from "./peopleTable";
import PageHeading from "../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

const People = () => {
  const { t } = useTranslation('common');
  return (
    <Container fluid className="p-0">
      <Row>
        <Col xs={12}>
          <PageHeading title={t("menu.people")} />
        </Col>
      </Row>
      <Row className="mt-3">
        <Col xs={12}>
          <PeopleTable />
        </Col>
      </Row>
    </Container>
  );
};

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default People;
