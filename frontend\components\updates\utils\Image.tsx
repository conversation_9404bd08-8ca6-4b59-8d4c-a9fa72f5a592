//Import Library
import React, { useEffect, useState } from "react";
import { Button, Card, Form, Container, Row, Col, Tab, Tabs } from "react-bootstrap";

//Import services/components
import ReactImages from "../../../components/common/ReactImages";

interface ImageProps {
  data: any[];
  srcText: string[];
}

//TOTO refactor
const Image = (props: ImageProps): React.ReactElement => {
  const { data, srcText } = props;
  return (
    <div>
      <ReactImages gallery={data} imageSource={srcText} />
    </div>
  )
}

export default Image;
