//Import Library
import React from "react";
import { Accordion, Col, Container, Row } from "react-bootstrap";

//Import services/components
import OrganizationTable from "../OrganizationTable";
import { useTranslation } from 'next-i18next';

const CountryOrganisationAccordion = (props) => {
    const { t } = useTranslation('common');
    return (
        <Accordion defaultActiveKey="0">
            <Accordion.Item eventKey="0">
                <Accordion.Header>
                    <div className="cardTitle">{t("Organisation")}</div>
                </Accordion.Header>
                <Accordion.Body>
                    <Container fluid>
                        <Row>
                            <Col>
                                <OrganizationTable {...props.prop} />
                            </Col>
                        </Row>
                    </Container>
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    )
}

export default CountryOrganisationAccordion;