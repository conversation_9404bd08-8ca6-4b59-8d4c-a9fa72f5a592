//Import Library
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

//Import services/components
import { ClassificationSeederService } from './classification-seeder.services';
// SCHEMAS
import { ClassificationSchema } from 'src/schemas/classification.schemas';

/**
 * Import and provide seeder classes for languages.
 *
 * @module
 */
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: 'Classification', schema: ClassificationSchema }
      ]
    )
  ],
  providers: [ClassificationSeederService],
  exports: [ClassificationSeederService],
})

export class ClassificationSeederModule { }
