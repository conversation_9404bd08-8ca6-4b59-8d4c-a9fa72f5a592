//Import Library
import Link from "next/link";
import _ from "lodash";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { <PERSON><PERSON>, But<PERSON>, Container, Row, Col } from "react-bootstrap";
import toast from 'react-hot-toast';

//Import services/components
import RKITable from "../../../components/common/RKITable";
import apiService from "../../../services/apiService";
import ContentTableFilter from "./ContentTableFilter";
import PageHeading from "../../../components/common/PageHeading";
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { ContentItem, User, ApiResponse, PaginationParams } from "../../../types";

const getSelectFieldOptions = (type: string, params: PaginationParams) => {
    switch (type) {
        case "operation":
            params.select =
                "-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners -images -document -doc_src -images_src";
            break;
        case "institution":
            params.select =
                "-partners -primary_focal_point -email -status -images -use_default_header -header -twitter -dial_code -telephone -department -unit -contact_name -description -type -networks -expertise -hazard_types -hazards -address -focal_points -website  -document -doc_src -images_src ";
            break;
        case "event":
            params.select =
                "-images -more_info -date -risk_assessment -rki_monitored -officially_validated -laboratory_confirmed -status -syndrome -hazard -hazard_type -country_regions -world_region -country -operation -description -document -doc_src -images_src";
            break;
        case "project":
            params.select =
                "-vspace_visibility -vspace -institution_invites -partner_institutions -area_of_work -region -country -end_date -start_date -status -funded_by -description -website";
            break;
        case "updates":
            params.select =
                " -region -country -end_date -start_date -status -category -contact_details -reply -show_as_announcement -technical_guidance -use_in_media_gallery -field_report -media -images -document -doc_src -images_src -location -link -description  ";
            break;
        case "vspace":
            params.select =
                "-vspace_email_invite -file_category -subscribers -images -members -visibility -topic -end_date -start_date -description -images -document -doc_src -images_src -nonMembers -private_user_invite";
            break;
        default:
            params.select =
                "-status -start_date -images -timeline -country -world_region -region -hazard -description -end_date -syndrome -hazard_type -partners";
            break;
    }
    return params;
};

interface ContentProps {
    [key: string]: any;
}

const Content = (_props: ContentProps) => {
    const [tableData, setTableData] = useState<ContentItem[]>([]);
    const { t } = useTranslation('common');
    const [type, setType] = useState<string>("operation");
    const [resetPaginationToggle, setResetPaginationToggle] = React.useState<boolean>(false);
    const [filterText, setFilterText] = React.useState<string>("");
    const [totalRows, setTotalRows] = useState<number>(0);
    const [perPage, setPerPage] = useState<number>(25);
    const [loading, setLoading] = useState<boolean>(false);
    const [isModalShow, setModal] = useState<boolean>(false);
    const [contentItem, setContentItem] = useState<ContentItem | null>(null);
    const [currentPage, setCurrentPage] = useState<number>(1);

    const [currentUser, setcurrentUser] = useState<User | null>(null);

    let params: any = {
        limit: perPage,
        sort: { created_at: "desc" },
    };

    const columns = [
        {
            name: t("adminsetting.content.table.Title"),
            selector: (row: any) => row.title,
            cell: (d: any) =>
                d.type ? (
                    <Link href={`/${d.type}/[...routes]`} as={`/${d.type}/show/${d[modules_func(d)]}/${type}/${d._id}`}>
                        {d.title}
                    </Link>
                ) : (
                    <Link href={`/${type}/[...routes]`} as={`/${type}/show/${d._id}`}>
                        {d.title}
                    </Link>
                ),
            sortable: true,
        },
        {
            name: t("adminsetting.content.table.Author"),
            selector: (row: ContentItem) => row.user?.username || '',
            cell: (d: ContentItem) => (d.user ? d.user.username : ""),
            sortable: true,
        },
        {
            name: t("adminsetting.content.table.Created"),
            selector: (row: ContentItem) => row.created_at,
            cell: (d: ContentItem) => moment(d.created_at).format("M/D/Y"),
            sortable: true,
        },
        {
            name: t("adminsetting.content.table.Updated"),
            selector: (row: ContentItem) => row.updated_at,
            cell: (d: ContentItem) => moment(d.updated_at).format("M/D/Y"),
            sortable: true,
        },
        {
            name: t("adminsetting.content.table.Action"),
            selector: (row: ContentItem) => row._id,
            sortable: false,
            cell: (d: ContentItem) => (
                <>
                {(currentUser?.roles?.includes('GENERAL_USER') || currentUser?.roles?.includes('PLATFORM_ADMIN')) && currentUser?._id == d?.user?._id ?
                    (<div>
                        <Link href={`/${type}/[...routes]`} as={`/${type}/edit/${d._id}`}>

                            <i className="icon fas fa-edit" />

                        </Link>
                        &nbsp;
                        <Link href="#" onClick={(e) => userAction(d, e)}>

                            <i className="icon fas fa-trash-alt" />

                        </Link>
                    </div>) :
                    (!(currentUser?.roles?.includes('GENERAL_USER') || currentUser?.roles?.includes('PLATFORM_ADMIN')) ?
                    (<div>
                        <Link href={`/${type}/[...routes]`} as={`/${type}/edit/${d._id}`}>

                            <i className="icon fas fa-edit" />

                        </Link>
                        &nbsp;
                        <Link href="#" onClick={(e) => userAction(d, e)}>

                            <i className="icon fas fa-trash-alt" />

                        </Link>
                    </div>): "")
                }
                </>

            ),
        },
    ];

    const fetchData = async (params: PaginationParams) => {
        setLoading(true);
        params = getSelectFieldOptions(type, params);
        const currentUserResponse = await apiService.post("/users/getLoggedUser", {});
        if (currentUserResponse && currentUserResponse.username) {
            setcurrentUser(currentUserResponse);
        }
        const response: ApiResponse<ContentItem[]> = await apiService.get(`/${type}`, params);
        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
            setTableData(response.data);
            setTotalRows(response.totalCount || 0);
        } else {
            setTableData([]);
        }
        setLoading(false);
    };

    const userAction = async (row: ContentItem, e: React.MouseEvent) => {
        e.preventDefault();
        setContentItem({ id: row._id, type: type });
        setModal(true);
    };

    const handlePageChange = (page: number) => {
        params.page = page;
        if (filterText !== "") {
            params.query = { title: filterText };
        }
        setCurrentPage(page);
        fetchData(params);
    };

    const handlePerRowsChange = async (newPerPage: number, page: number) => {
        setLoading(true);
        params.limit = newPerPage;
        params.page = page;
        setCurrentPage(page);
        params = getSelectFieldOptions(type, params);
        const response: ApiResponse<ContentItem[]> = await apiService.get(`/${type}`, params);
        if (response && Array.isArray(response.data)) {
            setTableData(response.data);
            setPerPage(newPerPage);
            setLoading(false);
        }
    };

    const modalHide = () => setModal(false);

    const modalConfirm = async () => {
        try {
            await apiService.remove(`/${contentItem.type}/${contentItem.id}`);
            params.page = currentPage;
            fetchData(params);
            setModal(false);
            toast.success(t("adminsetting.content.table.contentDeletedSuccessfully"));
        } catch (error) {
            toast.error(t("adminsetting.content.table.errorDeletingContent"));
        }
    };

    useEffect(() => {
        fetchData(params);
    }, [type]);

    const handleSort = async (column: any, sortDirection: 'asc' | 'desc') => {
        setLoading(true);
        params.sort = {
            [column.selector]: sortDirection,
        };
        await fetchData(params);
        setLoading(false);
    };

    const subHeaderComponentMemo = React.useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText("");
            }
        };

        const handleFilterTypeChange = (type_initial) => {
            setType(type_initial);
        };

        const sendQuery = (q) => {
            if (q) {
                params.query = { title: q };
            }
            fetchData(params);
        };

        const handleSearchTitle = _.debounce((q: string) => sendQuery(q), Number(process.env.SEARCH_DEBOUNCE_TIME) || 300);

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            setFilterText(e.target.value);
            handleSearchTitle(e.target.value);
        };

        return (
            <ContentTableFilter
                onFilter={handleChange}
                onClear={handleClear}
                filterText={filterText}
                onFilterTypeChange={(e) => handleFilterTypeChange(e.target.value)}
                filterType={type}
            />
        );
    }, [filterText, type, resetPaginationToggle]);

    return (
        <Container style={{ overflowX: "hidden" }} fluid className="p-0">
            <Row>
                <Col xs={12}>
                    <PageHeading title={t("adminsetting.content.table.content")} />
                </Col>
            </Row>
            <Row className="mt-3">
                <Col xs={12}>
                    <Modal show={isModalShow} onHide={modalHide}>
                        <Modal.Header closeButton>
                            <Modal.Title>{t("adminsetting.content.table.DeleteContent")}</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>{t("adminsetting.content.table.Areyousurewanttodeletethiscontent?")}</Modal.Body>
                        <Modal.Footer>
                            <Button variant="secondary" onClick={modalHide}>
                                {t("adminsetting.content.table.Cancel")}
                            </Button>
                            <Button variant="primary" onClick={modalConfirm}>
                                {t("adminsetting.content.table.Yes")}
                            </Button>
                        </Modal.Footer>
                    </Modal>
                    <RKITable
                        columns={columns}
                        loading={loading}
                        data={tableData}
                        totalRows={totalRows}
                        defaultRowsPerPage={perPage}
                        subheader
                        onSort={handleSort}
                        sortServer
                        pagServer={true}
                        subHeaderComponent={subHeaderComponentMemo}
                        persistTableHead
                        resetPaginationToggle={resetPaginationToggle}
                        handlePerRowsChange={handlePerRowsChange}
                        handlePageChange={handlePageChange}
                    />
                </Col>
            </Row>
        </Container>
    );
};

export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

export default Content;

function modules_func(d: any) {
    return `parent_${d.type}`;
}
