//Import Library
import React from "react";
import { Accordion } from "react-bootstrap";

//Import services/components
import { useTranslation } from 'next-i18next';
import DocumentTable from "../../../components/common/DocumentTable";

const CountryDocumentAccordion = (props) => {
    const { t } = useTranslation('common');
    return (
        <Accordion defaultActiveKey="0">
            <Accordion.Item eventKey="0">
                <Accordion.Header>
                    <div className="cardTitle">{t("Documents")}</div>
                </Accordion.Header>
                <Accordion.Body>
                    <DocumentTable
                        loading={props.loading}
                        sortProps={props.updateSort}
                        docs={props.document}
                        docsDescription={props.docSrc}
                    />
                    <h6 className="mt-3">{t("DocumentsfromUpdates")}</h6>
                    <DocumentTable
                        loading={props.loading}
                        sortProps={props.updateDocumentSort}
                        docs={props.updateDocument}
                        docsDescription={props.docSrc}
                    />
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    )
}

export default CountryDocumentAccordion;